"""
Microcapsule Generator for Continuous Live Streaming

Generates content using a microcycle narrative model with buffered real-time generation.
Interface-compatible with existing ScriptGenerator for zero-intrusion integration.
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, AsyncIterator
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from loguru import logger

from .capsule_templates import CapsuleType, CapsuleTemplateLibrary, CapsuleTemplate
from .capsule_selector import CapsuleSelector, SelectionContext, CapsuleState
from .llm_script_generator import LLMScriptGenerator, ScriptSegmentType, LLMScriptSegment
from .script_segment_converter import ScriptSegmentConverter
from ..models.forms import OperationalForm, PersonaConfiguration
from ..models.persona import PersonaConfig
from ..services.factories import ServiceFactory
from ..services.llm_adapters.base import LLMMessage
from ..core.exceptions import ServiceError
from ..core.config import cfg


@dataclass
class GeneratedCapsule:
    """A generated content capsule ready for playback"""
    capsule_id: str
    capsule_type: CapsuleType
    content: str
    estimated_duration_seconds: int
    metadata: Dict[str, Any]
    generated_at: datetime
    llm_stats: Dict[str, Any]


class MicrocapsuleGenerator:
    """
    Microcapsule-based content generator with buffered real-time generation.
    
    Features:
    - Interface-compatible with ScriptGenerator for seamless integration
    - Producer-consumer architecture with asyncio.Queue buffer
    - Intelligent capsule selection with cooldowns and weights
    - State persistence and recovery
    - Graceful shutdown with closing capsules
    """
    
    def __init__(self, 
                 session_id: str,
                 form: OperationalForm,
                 persona_config: Optional[PersonaConfig] = None):
        """
        Initialize the microcapsule generator
        
        Args:
            session_id: Unique session identifier
            form: Operational form with product information
            persona_config: Optional persona configuration
        """
        self.session_id = session_id
        self.form = form
        self.persona_config = persona_config or self._create_default_persona()
        
        # Core components
        self.template_library = CapsuleTemplateLibrary()
        self.capsule_selector = CapsuleSelector(self.template_library)
        self.segment_converter = ScriptSegmentConverter()
        self._llm_adapter = None
        
        # Buffer architecture
        self.content_buffer = asyncio.Queue(maxsize=5)
        self.producer_task: Optional[asyncio.Task] = None
        self.is_running = False
        self.is_shutting_down = False
        
        # State management
        self.pending_capsules: List[str] = []
        self.played_capsules: List[str] = []
        self.recent_summary_short = ""
        self.next_topic_preview = ""
        self.generation_count = 0
        self.first_request = True
        
        # Performance tracking
        self.stats = {
            "capsules_generated": 0,
            "buffer_hits": 0,
            "sync_generations": 0,
            "average_generation_time": 0.0,
            "total_generation_time": 0.0,
            "failures": 0
        }
        
        # State persistence
        self.state_file = Path(f"state_snapshots/session_{session_id}.json")
        self.state_file.parent.mkdir(exist_ok=True)
        
        self.logger = logger.bind(component="microcapsule_generator", session=session_id)
        self.logger.info("MicrocapsuleGenerator initialized")
    
    def _create_default_persona(self) -> PersonaConfig:
        """Create a default persona if none provided"""
        return PersonaConfig(
            persona_id="default_persona",
            name="AI主播",
            description="默认的AI主播形象",
            persona_type="professional",
            voice_type="professional",
            tone="friendly",
            speaking_rate=1.0
        )
    
    async def _initialize_llm(self):
        """Initialize LLM adapter"""
        if self._llm_adapter is None:
            self._llm_adapter = await ServiceFactory.create_llm_adapter()
            self.logger.info(f"LLM adapter initialized: {self._llm_adapter.provider_name}")
    
    async def start_generation(self):
        """Start the buffered generation process"""
        if self.is_running:
            self.logger.warning("Generation already running")
            return
        
        self.is_running = True
        self.producer_task = asyncio.create_task(self._producer_loop())
        self.logger.info("Started buffered content generation")
    
    async def stop_generation(self):
        """Stop the generation process"""
        self.is_running = False
        if self.producer_task:
            self.producer_task.cancel()
            try:
                await self.producer_task
            except asyncio.CancelledError:
                pass
        self.logger.info("Stopped content generation")
    
    async def _producer_loop(self):
        """Producer task: continuously generate capsules for the buffer"""
        self.logger.info("Producer loop started")
        
        while self.is_running and not self.is_shutting_down:
            try:
                if not self.content_buffer.full():
                    # Select next capsule
                    context = self._create_selection_context()
                    selection = self.capsule_selector.select_next_capsule(context)
                    
                    if selection:
                        capsule_id, capsule_state = selection
                        
                        # Generate content for the capsule
                        generated = await self._generate_capsule_content(capsule_state)
                        
                        if generated:
                            # Convert to playable format
                            playable_items = await self._convert_to_playable(generated)
                            
                            # Add to buffer
                            await self.content_buffer.put(playable_items)
                            
                            # Update state
                            self._update_state_after_generation(capsule_id, generated)
                            
                            self.logger.info(f"Added capsule {capsule_id} to buffer (size: {self.content_buffer.qsize()})")
                    else:
                        self.logger.warning("No suitable capsule for selection")
                        await asyncio.sleep(2)
                else:
                    # Buffer full, wait a bit
                    await asyncio.sleep(0.5)
                    
            except Exception as e:
                self.logger.error(f"Error in producer loop: {e}")
                self.stats["failures"] += 1
                await asyncio.sleep(2)
        
        self.logger.info("Producer loop stopped")
    
    def _create_selection_context(self) -> SelectionContext:
        """Create context for capsule selection"""
        return SelectionContext(
            current_time=datetime.utcnow(),
            played_capsules=self.played_capsules[-10:],  # Last 10 played
            pending_capsules=self.pending_capsules[:10],  # Next 10 pending
            recent_summary=self.recent_summary_short,
            next_topic_preview=self.next_topic_preview,
            audience_signals=self._get_audience_signals(),
            is_closing_requested=self.is_shutting_down,
            priority_type=None
        )
    
    def _get_audience_signals(self) -> Dict[str, Any]:
        """Get audience signals for capsule selection"""
        # This could be enhanced with real-time analytics
        return {
            "new_users_spike": self.generation_count < 3,
            "high_engagement": False,
            "questions_pending": 0
        }
    
    async def _generate_capsule_content(self, capsule_state: CapsuleState) -> Optional[GeneratedCapsule]:
        """Generate content for a specific capsule"""
        start_time = datetime.utcnow()
        
        try:
            await self._initialize_llm()
            
            # Build prompt from template
            prompt = self._build_capsule_prompt(capsule_state.template)
            
            # Generate content using LLM
            messages = [
                LLMMessage(role="system", content=capsule_state.template.system_prompt),
                LLMMessage(role="user", content=prompt)
            ]
            
            response = await self._llm_adapter.generate(
                messages=messages,
                temperature=0.7,
                max_tokens=500
            )
            
            if not response.success:
                self.logger.error(f"LLM generation failed: {response.error}")
                return None
            
            # Create generated capsule
            generation_time = (datetime.utcnow() - start_time).total_seconds()
            
            generated = GeneratedCapsule(
                capsule_id=str(uuid.uuid4()),
                capsule_type=capsule_state.capsule_type,
                content=response.content,
                estimated_duration_seconds=self._estimate_duration(response.content),
                metadata={
                    "template_title": capsule_state.template.title,
                    "generation_attempt": 1,
                    "persona": self.persona_config.name
                },
                generated_at=datetime.utcnow(),
                llm_stats={
                    "tokens_used": response.usage.get("total_tokens", 0) if response.usage else 0,
                    "generation_time": generation_time,
                    "model": self._llm_adapter.provider_name
                }
            )
            
            # Update statistics
            self.stats["capsules_generated"] += 1
            self.stats["total_generation_time"] += generation_time
            self.stats["average_generation_time"] = (
                self.stats["total_generation_time"] / self.stats["capsules_generated"]
            )
            
            return generated
            
        except Exception as e:
            self.logger.error(f"Failed to generate capsule content: {e}")
            self.stats["failures"] += 1
            return None
    
    def _build_capsule_prompt(self, template: CapsuleTemplate) -> str:
        """Build prompt from template with variable substitution"""
        prompt = template.user_prompt_template
        
        # Prepare variables for substitution
        variables = {
            "persona": {
                "name": self.persona_config.name,
                "tone": self.persona_config.tone,
                "speaking_rate": self.persona_config.speaking_rate
            },
            "product": self._extract_product_info(),
            "recent_summary_short": self.recent_summary_short or "刚刚开始介绍",
            "next_topic_preview": self.next_topic_preview or "更多精彩内容",
            "session": {
                "key_points": self._get_key_points_summary()
            }
        }
        
        # Replace template variables
        for key, value in self._flatten_dict(variables).items():
            placeholder = "{{" + key + "}}"
            if placeholder in prompt:
                prompt = prompt.replace(placeholder, str(value))
        
        return prompt
    
    def _extract_product_info(self) -> Dict[str, Any]:
        """Extract product information from operational form"""
        product_info = {}
        
        if self.form.basic_information:
            product_info["category"] = self.form.basic_information.product_category
            product_info["target"] = self.form.basic_information.target_audience
        
        if self.form.product_information:
            product_info["description"] = self.form.product_information.product_description
            product_info["specs"] = self.form.product_information.specifications
            product_info["original_price"] = self.form.product_information.original_price
            product_info["current_price"] = self.form.product_information.sale_price
            product_info["discount"] = self.form.product_information.discount_info
        
        if self.form.selling_points_structure:
            key_points = []
            for sp in self.form.selling_points_structure.selling_points[:3]:
                key_points.append(f"{sp.title}: {sp.description[:50]}...")
            product_info["key_points"] = ", ".join(key_points)
            
            # Extract individual selling points
            for i, sp in enumerate(self.form.selling_points_structure.selling_points):
                product_info[f"selling_point_{i}"] = {
                    "title": sp.title,
                    "description": sp.description
                }
        
        return product_info
    
    def _flatten_dict(self, d: Dict, parent_key: str = '') -> Dict[str, Any]:
        """Flatten nested dictionary for template substitution"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}.{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    def _estimate_duration(self, content: str) -> int:
        """Estimate duration in seconds based on content length"""
        # Rough estimation: 150 Chinese characters per minute
        char_count = len(content)
        duration_minutes = char_count / 150
        return int(duration_minutes * 60)
    
    def _get_key_points_summary(self) -> str:
        """Get summary of key points covered"""
        if not self.played_capsules:
            return "即将开始精彩介绍"
        
        # Summarize based on played capsules
        capsule_types = []
        for capsule_id in self.played_capsules[-5:]:
            # Extract type from capsule_id if stored
            if "_" in capsule_id:
                capsule_type = capsule_id.split("_")[0]
                capsule_types.append(capsule_type)
        
        return f"已介绍: {', '.join(set(capsule_types))}"
    
    async def _convert_to_playable(self, capsule: GeneratedCapsule) -> List[Any]:
        """Convert generated capsule to playable format"""
        # Create a script segment from the capsule
        segment = LLMScriptSegment(
            segment_id=capsule.capsule_id,
            segment_type=self._map_capsule_to_segment_type(capsule.capsule_type),
            title=f"{capsule.capsule_type.value}_segment",
            content=capsule.content,
            estimated_duration_seconds=capsule.estimated_duration_seconds,
            priority=self._get_priority_level(capsule.capsule_type),
            triggers=[],
            variables=capsule.metadata,
            generation_metadata=capsule.llm_stats
        )
        
        # Convert to TTS-compatible format
        tts_segments = self.segment_converter.convert([segment])
        
        # Convert to playlist items (SentenceData format)
        from ..models.streaming_models import SentenceData
        
        playable_items = []
        for tts_segment in tts_segments:
            sentence_data = SentenceData(
                text=tts_segment.tts_text,
                audio_url="",  # Will be filled by TTS
                duration=capsule.estimated_duration_seconds / len(tts_segments),
                segment_type=segment.segment_type.value,
                metadata={
                    "capsule_id": capsule.capsule_id,
                    "capsule_type": capsule.capsule_type.value,
                    **capsule.metadata
                }
            )
            playable_items.append(sentence_data)
        
        return playable_items
    
    def _map_capsule_to_segment_type(self, capsule_type: CapsuleType) -> ScriptSegmentType:
        """Map capsule type to script segment type"""
        mapping = {
            CapsuleType.OPENING: ScriptSegmentType.OPENING,
            CapsuleType.SELLING_POINT: ScriptSegmentType.SELLING_POINT,
            CapsuleType.PRICE_VALUE: ScriptSegmentType.PRICE_ANNOUNCEMENT,
            CapsuleType.PROOF_DEMO: ScriptSegmentType.INTERACTION,
            CapsuleType.BRAND_STORY: ScriptSegmentType.PRODUCT_INTRO,
            CapsuleType.HALF_TIME_SUMMARY: ScriptSegmentType.TRANSITION,
            CapsuleType.QA: ScriptSegmentType.INTERACTION,
            CapsuleType.CLOSING: ScriptSegmentType.CLOSING,
            CapsuleType.TRANSITION: ScriptSegmentType.TRANSITION
        }
        return mapping.get(capsule_type, ScriptSegmentType.TRANSITION)
    
    def _get_priority_level(self, capsule_type: CapsuleType):
        """Get priority level for capsule type"""
        from ..models.forms import PriorityLevel
        
        high_priority = [CapsuleType.OPENING, CapsuleType.CLOSING, CapsuleType.PRICE_VALUE]
        if capsule_type in high_priority:
            return PriorityLevel.HIGH
        
        return PriorityLevel.MEDIUM
    
    def _update_state_after_generation(self, capsule_id: str, capsule: GeneratedCapsule):
        """Update state after generating a capsule"""
        self.played_capsules.append(capsule_id)
        self.generation_count += 1
        
        # Update summary (simplified version)
        if len(capsule.content) > 50:
            self.recent_summary_short = capsule.content[:50] + "..."
        else:
            self.recent_summary_short = capsule.content
        
        # Update next topic preview
        remaining_types = set(CapsuleType) - {capsule.capsule_type}
        if remaining_types:
            next_type = list(remaining_types)[0]
            self.next_topic_preview = f"接下来: {next_type.value}"
        
        # Save state
        self._save_state()
    
    # Interface-compatible methods for seamless integration
    
    async def get_next_content(self, force_sync: bool = False) -> Optional[List[Any]]:
        """
        Get next content for playback (interface-compatible with ScriptGenerator)
        
        Args:
            force_sync: Force synchronous generation (for first request)
            
        Returns:
            List of playable items or None
        """
        # Handle first request or empty buffer with synchronous generation
        if self.first_request or force_sync or self.content_buffer.empty():
            self.first_request = False
            self.stats["sync_generations"] += 1
            
            # Generate content synchronously
            content = await self._sync_generate()
            
            # Start background generation if not running
            if not self.is_running:
                await self.start_generation()
            
            return content
        
        # Get from buffer
        try:
            content = await asyncio.wait_for(
                self.content_buffer.get(),
                timeout=2.0
            )
            self.stats["buffer_hits"] += 1
            return content
        except asyncio.TimeoutError:
            self.logger.warning("Buffer timeout, falling back to sync generation")
            return await self._sync_generate()
    
    async def _sync_generate(self) -> Optional[List[Any]]:
        """Synchronously generate content (for first request or fallback)"""
        context = self._create_selection_context()
        
        # For first request, prioritize opening capsule
        if self.generation_count == 0:
            context.priority_type = CapsuleType.OPENING
        
        selection = self.capsule_selector.select_next_capsule(context)
        if not selection:
            self.logger.error("No capsule available for selection")
            return None
        
        capsule_id, capsule_state = selection
        generated = await self._generate_capsule_content(capsule_state)
        
        if generated:
            playable_items = await self._convert_to_playable(generated)
            self._update_state_after_generation(capsule_id, generated)
            return playable_items
        
        return None
    
    async def request_graceful_stop(self) -> bool:
        """
        Request graceful stop with closing capsule
        
        Returns:
            True if closing capsule was generated
        """
        self.logger.info("Graceful stop requested")
        self.is_shutting_down = True
        
        # Stop producer task
        if self.producer_task:
            self.producer_task.cancel()
        
        # Generate closing capsule
        context = self._create_selection_context()
        context.is_closing_requested = True
        
        selection = self.capsule_selector.select_next_capsule(context)
        if selection:
            capsule_id, capsule_state = selection
            generated = await self._generate_capsule_content(capsule_state)
            
            if generated:
                playable_items = await self._convert_to_playable(generated)
                # Put closing content at front of buffer
                temp_queue = asyncio.Queue()
                await temp_queue.put(playable_items)
                
                # Transfer existing buffer content
                while not self.content_buffer.empty():
                    item = await self.content_buffer.get()
                    await temp_queue.put(item)
                
                # Replace buffer
                self.content_buffer = temp_queue
                
                self.logger.info("Closing capsule generated and queued")
                return True
        
        self.logger.warning("Failed to generate closing capsule")
        return False
    
    def _save_state(self):
        """Save current state to file"""
        state = {
            "session_id": self.session_id,
            "played_capsules": self.played_capsules,
            "pending_capsules": self.pending_capsules,
            "recent_summary_short": self.recent_summary_short,
            "next_topic_preview": self.next_topic_preview,
            "generation_count": self.generation_count,
            "stats": self.stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save state: {e}")
    
    def _load_state(self) -> bool:
        """Load state from file"""
        if not self.state_file.exists():
            return False
        
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            self.played_capsules = state.get("played_capsules", [])
            self.pending_capsules = state.get("pending_capsules", [])
            self.recent_summary_short = state.get("recent_summary_short", "")
            self.next_topic_preview = state.get("next_topic_preview", "")
            self.generation_count = state.get("generation_count", 0)
            self.stats.update(state.get("stats", {}))
            
            self.logger.info(f"State loaded from {self.state_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load state: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get generator statistics"""
        selector_stats = self.capsule_selector.get_selection_stats()
        
        return {
            "generator": self.stats,
            "selector": selector_stats,
            "buffer": {
                "current_size": self.content_buffer.qsize(),
                "max_size": self.content_buffer.maxsize
            },
            "state": {
                "generation_count": self.generation_count,
                "played_capsules": len(self.played_capsules),
                "is_running": self.is_running,
                "is_shutting_down": self.is_shutting_down
            }
        }
    
    async def cleanup(self):
        """Clean up resources"""
        await self.stop_generation()
        self._save_state()
        self.logger.info("MicrocapsuleGenerator cleaned up")