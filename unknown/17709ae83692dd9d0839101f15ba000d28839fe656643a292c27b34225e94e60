# AI Live Streamer Configuration
# This file contains detailed configuration for LLM and TTS providers
# Sensitive data (API keys) should be set in .env file

# === 锁管理器配置 ===
lock_manager:
  # 锁策略: "file" (默认) | "redis" | "memory"
  # file: 文件锁，适用于单机多进程
  # redis: Redis分布式锁，需要Redis服务
  # memory: 内存锁，仅单进程有效
  strategy: "file"
  
  # 文件锁配置
  file_lock_directory: "/tmp/ai-live-streamer/locks"
  
  # Redis锁配置 (仅当strategy="redis"时生效)
  # redis_url: "redis://localhost:6379/0"  # 在.env中设置REDIS_URL

# === QA处理策略配置 ===
qa_handling:
  # PAUSED状态下的QA处理策略
  during_pause: "queue"            # 选项: "queue" (排队等恢复) | "process" (暂停时处理) | "ignore" (忽略)
  
  # IDLE状态下的QA处理策略
  during_idle: "process"           # 选项: "process" (直接处理) | "ignore" (忽略)
  
  # QA处理超时设置
  response_timeout_seconds: 30     # QA处理超时时间，防止LLM卡死

# === QA过渡话术配置 ===
transitions:
  enabled: true                    # 启用/禁用过渡话术功能
  
  # 进入QA环节的话术
  to_qa:
    - "正好，这里有个观众提问，我们来看一下。"
    - "哦？有个问题很有意思，我们来回答一下。"
    - "插播一条观众问答，稍等片刻。"
    - "我们暂停一下，回答一个朋友的问题。"
    - "有观众朋友等不及了，我们先来解决一个问题。"
    - "看到有人问了个不错的问题，咱们聊聊。"
    - "来回答一下这位朋友的疑问。"
    - "正巧有人问到了这个，我来解释一下。"

  # 从QA环节回到直播的话术  
  from_qa:
    - "好的，希望这个回答对你有帮助。我们继续刚才的话题。"
    - "问题回答完毕，让我们回到今天的主题。"
    - "OK，让我们言归正传。"
    - "感谢这位观众的提问，我们继续。"
    - "希望我解释清楚了，我们接着往下讲。"
    - "回答完了，咱们继续刚才聊的。"
    - "好了，让我们回到正题。"
    - "解答完毕，继续我们的直播。"

# === QA System v3 Configuration ===
qa_system_v3:
  enabled: false                    # Enable/disable QA v3 system (default: disabled)
  
  # Redis configuration for deduplication and caching
  redis:
    enabled: false                  # Enable/disable Redis (default: disabled)
    host: "localhost"
    port: 6379
    db: 0
    password: null                  # Optional password (set in .env as REDIS_PASSWORD)
    
  # Knowledge base configuration
  knowledge_base:
    db_path: "data/qa_knowledge.db"
    faiss_index_path: "data/qa_knowledge.faiss"
    embedding_model: "BAAI/bge-m3"

# === Knowledge Base Configuration ===
knowledge_base:
  enabled: false                    # Enable/disable knowledge base search (requires Elasticsearch)
  
# === LLM Configuration ===
llm:
  default_provider: "qwen-plus"
  
  # LiteLLM Caching Configuration
  caching:
    enabled: true                  # Enable/disable LiteLLM caching
    type: "local"                  # Cache type: local, redis, s3, disk
    ttl_seconds: 3600             # Cache TTL in seconds (1 hour)
    max_cache_size: 1000          # Maximum number of cached entries
    enable_for_streaming: false   # Enable caching for streaming requests
  
  providers:
    qwen-plus:
      type: "qwen"
      model: "qwen-plus-2025-04-28"
      api_base: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      max_tokens: 4096
      temperature: 0.7
      timeout_seconds: 30
      retry_attempts: 3
      stream: true
      
    qwen-turbo:
      type: "qwen"
      model: "qwen-turbo"
      api_base: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      max_tokens: 2048
      temperature: 0.8
      timeout_seconds: 20
      retry_attempts: 3
      stream: true
      
    openai-gpt4:
      type: "openai"
      model: "gpt-4o-mini"
      api_base: "https://api.openai.com/v1"
      max_tokens: 4096
      temperature: 0.7
      timeout_seconds: 30
      retry_attempts: 3
      stream: true
      
    anthropic-claude:
      type: "anthropic"
      model: "claude-3-haiku-20240307"
      api_base: "https://api.anthropic.com"
      max_tokens: 4096
      temperature: 0.7
      timeout_seconds: 30
      retry_attempts: 3
      stream: true
      
    litellm:
      type: "litellm"
      model: "qwen-plus-2025-04-28"  # Default model for LiteLLM gateway
      api_base: "http://localhost:4000"
      max_tokens: 4096
      temperature: 0.7
      timeout_seconds: 30
      retry_attempts: 3
      stream: true
      cache_enabled: true            # Enable caching for this provider

# === TTS Configuration ===
tts:
  #default_engine: "cosyvoice_unified"  # Use new unified engine by default
  default_engine: "tts_cache_proxy"  # Use new unified engine by default
  
  engines:
    # TTS Cache Proxy Engine - 企业级缓存代理
    tts_cache_proxy:
      type: "tts_cache_proxy"
      cache_service_url: "http://localhost:22243"
      cache_miss_engine: "cosyvoice_unified"  # 缓存未命中时使用的引擎
      timeout_seconds: 10
      retry_attempts: 2
      health_check_interval: 30
      default_voice: "longanran"
      
      # 熔断器配置
      circuit_failure_threshold: 5
      circuit_recovery_timeout: 60
      circuit_success_threshold: 3
      circuit_error_rate_threshold: 0.25
      
      # 流式缓存
      enable_streaming_cache: true
      
      # 缓存雪崩防护
      enable_request_coalescing: true
      
      # 负缓存（防止缓存穿透）
      negative_cache_ttl: 60
      
      # 监控配置
      enable_stats: true
      stats_interval: 60

    # New unified engine - recommended for all deployments
    cosyvoice_unified:
      type: "cosyvoice_unified"
      model: "cosyvoice-v2"
      default_voice: "longanran"        # 龙安燃作为默认
      fallback_voice: "longanxuan"       # 龙安宣作为备选
      sample_rate: 24000                 # CosyVoice v2 固定输出
      streaming_enabled: true
      latency_target_ms: 150
      
      # API 配置
      api_key: "${COSYVOICE_V2_API_KEY}"
      timeout_seconds: 30
      retry_attempts: 3
      
      # 音色配置映射
      voice_mapping:
        default: "longanran"
        female: "longanran"
        male: "longanxuan"
        energetic: "longanran"
        calm: "longanxuan"
        narrator: "longanran"
    
    # Legacy engines - kept for compatibility
    cosyvoice_v2:
      type: "cosyvoice_cloud"
      model: "cosyvoice-v2"
      default_voice: "longanran"        # 龙安燃作为默认
      fallback_voice: "longanxuan"       # 龙安宣作为备选
      sample_rate: 24000                 # CosyVoice v2 固定输出
      streaming_enabled: true
      latency_target_ms: 150
      
      # API 配置
      api_endpoint: "${COSYVOICE_V2_ENDPOINT}"
      timeout_seconds: 30
      retry_attempts: 3
      
      # 音色配置映射
      voice_mapping:
        default: "longanran"
        female: "longanran"
        male: "longanxuan"
        energetic: "longanran"
        calm: "longanxuan"
        narrator: "longanran"

# === TTS Responsive Playback Configuration ===
# 响应式播放配置 - 根治8-10秒停顿问题的核心参数
tts_responsive_playback:
  # 音频静默间隙阈值(秒) - 用于检测句子自然结束
  audio_gap_threshold: 0.8
  
  # 最短句子播放时长(秒) - 防止网络抖动导致的误判
  min_sentence_duration: 0.5
  
  # 启用连接健康检查 - 区分良性静默和真正故障
  connection_health_check: true
  
  # 启用详细调试日志 - 用于监控和故障排查
  debug_logging: true
  
  # 预取队列同步检查 - 确保播放器不会超前于预取器
  prefetch_sync_check: true

# === Playback Timing Configuration ===
# 播放时序估算参数 - 用于UI显示和分析，不用于实际等待
playback_timing:
  # ⚠️ 重要说明：以下参数仅用于估算和显示，绝不用于播放等待！
  # 对于流式播放：TTS完成 = 播放完成，无需任何等待
  
  # 网络传输延迟估算(毫秒) - 估算音频数据从服务器到客户端的传输时间
  # 用途：UI进度显示、QA插入时机估算、用户体验分析
  network_latency_ms: 150
  
  # 客户端缓冲时间估算(毫秒) - 估算客户端音频缓冲和播放准备时间  
  # 用途：估算用户实际听到音频的时间点，UI同步显示
  client_buffer_ms: 400
  
  # 等待检查间隔(毫秒) - 仅用于非流式播放场景的中断检查频率
  # 注意：当前版本流式播放不使用此参数
  wait_check_interval_ms: 100
  
  # 最大等待超时(毫秒) - 安全上限，防止意外的长时间等待
  # 注意：当前版本流式播放不使用此参数
  max_wait_timeout_ms: 30000
  
  # 音频时长回退估算(毫秒/字符) - 当无法获取准确音频时长时的估算
  # 用途：进度条显示、时间预估、性能分析
  fallback_duration_per_char_ms: 80

# === Audio Streaming Configuration ===
audio_streaming:
  # CosyVoice WebSocket 配置
  cosyvoice_websocket:
    api_key: "${COSYVOICE_V2_API_KEY}"
    model: "cosyvoice-v2"
    # v2音色配置 (官方文档验证)
    default_voice: "longanran"  # 使用龙安然作为默认音色
    available_voices:
      - id: "longxiaochun"
        name: "龙小春"
        language: "zh-CN"
        gender: "female"
        type: "standard"
        description: "标准女声，官方推荐"
      - id: "longanran"
        name: "龙安然"
        language: "zh-CN"
        gender: "female"
        type: "energetic"
        description: "活泼女声"
      - id: "longangxuan"
        name: "龙安宣"
        language: "zh-CN"
        gender: "female"
        type: "calm"
        description: "温和女声"
      - id: "longtongxiao"
        name: "龙童晓"
        language: "zh-CN"
        gender: "male"
        type: "child"
        description: "童声"
    
    # 音频格式配置
    default_format: "opus"  # 推荐opus格式，延迟最低
    default_sample_rate: 24000
    supported_formats:
      - "opus"  # 最低延迟
      - "mp3"   # 兼容性好
      - "wav"   # 无损质量
      - "ogg"   # 开源格式
      
    # 🔧 音频格式转换配置 - 基于归档经验优先使用MP3
    format_conversion:
      enabled: true                    # 启用自动格式转换
      output_format: "mp3"             # 目标输出格式 - MP3以获得最佳兼容性
      dashscope_input_format: "pcm"    # DashScope TTS返回格式（PCM 16-bit 24kHz）
      conversion_quality:
        mp3_bitrate: "96k"             # MP3比特率 - 优化流式播放
        webm_bitrate: "96k"            # WebM/Opus比特率（fallback）
        compression_level: 5            # Opus压缩级别 (0-10)
        frame_duration: 20              # Opus帧长度(ms)，适合流式播放
      fallback_format: "webm"          # MP3不支持时的备选格式（罕见）
      enable_performance_logging: true  # 记录转换性能统计
    
    # 音频参数范围
    default_volume: 50    # 0-100
    default_rate: 1.0     # 0.5-2.0
    default_pitch: 1.0    # 0.5-2.0
    
    # WebSocket连接配置
    ws_url: "wss://dashscope.aliyuncs.com/api-ws/v1/inference"
    ping_interval: 20     # 保持连接活跃
    ping_timeout: 10
    max_size: null        # 避免大音频帧被截断
    
    # 防护机制
    keep_alive_interval: 15    # 15秒发送心跳，防止23秒idle超时
    connection_timeout: 10     # 连接超时时间
    
  # 客户端流式播放配置
  client_streaming:
    buffer_size: 3            # 客户端缓冲块数
    max_clients: 10           # 最大客户端连接数
    reconnect_interval: 3000  # 重连间隔(ms)
    max_reconnect_attempts: 5 # 最大重连次数
    heartbeat_interval: 30000 # 心跳间隔(ms)
    
    # 音频队列配置
    audio_queue_size: 150     # 音频队列大小
    max_buffer_duration_ms: 10000  # 最大缓冲时长
    
  # 安全配置
  security:
    token_duration: 60        # Token有效期(秒)
    token_refresh_threshold: 10  # 提前刷新时间(秒)
    token_api_url: "https://dashscope.aliyuncs.com/api/v1/auth/token"
    
  # 性能配置
  performance:
    target_latency_ms: 300    # 目标端到端延迟
    chunk_processing_timeout: 5000  # 音频块处理超时
    stream_start_timeout: 10000     # 流启动超时
    
    # 监控和统计
    metrics_enabled: true
    metrics_interval: 60      # 指标收集间隔(秒)

# === Performance Tuning ===
performance:
  tts:
    max_concurrent_requests: 4
    queue_timeout_seconds: 30
    chunk_size_words: 20
    buffer_size_seconds: 5
    
  llm:
    max_concurrent_requests: 8
    request_timeout_seconds: 30
    retry_backoff_seconds: [1, 2, 4]
    rate_limit_requests_per_minute: 100

# === Feature Flags ===
features:
  tts:
    enable_streaming: true
    enable_voice_cloning: false
    enable_emotion_control: false
    enable_speed_control: true
    
  llm:
    enable_function_calling: true
    enable_multimodal: false
    enable_code_generation: true
    enable_reasoning: true
    
  operational_forms:
    enable_strict_validation: false

# === Logging ===
logging:
  tts:
    log_audio_stats: true
    log_synthesis_time: true
    log_queue_metrics: true
    
  llm:
    log_token_usage: true
    log_response_time: true
    log_model_switching: true

# === Fallback Configuration ===
# REMOVED: Following fail-fast principle from CLAUDE.md
# The system no longer uses fallback mechanisms. Each service either succeeds or fails immediately.
# This ensures predictable behavior and clear error signals for monitoring and debugging.

# === Achievements Dashboard Configuration ===
# 成就仪表盘配置 - 展示系统累计价值和核心资产
achievements_dashboard:
  enabled: true                      # 启用成就仪表盘
  use_mock_data: true               # 使用模拟数据（初始开发阶段）
  
  # Mock数据配置 - 展示成熟系统的状态
  mock_data:
    # 核心成就指标
    total_sessions: 256              # 累计直播场次
    total_duration_hours: 1024.5     # 累计直播时长（小时）
    total_questions_answered: 3842   # 累计回答问题数
    
    # 资产统计
    total_products: 128              # 商品库数量
    total_configs: 42                # 直播配置数
    total_scripts: 85                # 脚本库数量
    
    # 效率指标（计算得出）
    avg_session_duration_hours: 4.0  # 平均直播时长
    avg_questions_per_session: 15    # 场均互动问答
    system_uptime_days: 30           # 系统运行天数
    
  # 数据刷新配置
  refresh_interval_seconds: 60       # 数据刷新间隔
  animation_duration_ms: 2000        # 数字动画持续时间
  
  # 空状态提示文案
  empty_state_messages:
    no_sessions: "开始您的第一场直播"
    no_products: "创建您的第一个商品"
    no_scripts: "生成您的第一个脚本"
    no_configs: "配置您的第一场直播"

# === Environment-Specific Configuration ===
# These values can be overridden by environment variables
COSY_VOICE_ID: "cosyvoice-v2-zh-CN-female"  # Default voice ID for CozyVoice Cloud
COSYVOICE_V2_DEFAULT_VOICE: "longanran"    # 默认音色：龙安燃
COSYVOICE_V2_FALLBACK_VOICE: "longanxuan"   # 备选音色：龙安宣