/**
 * Web Audio Player V2 - 模块化重构版本
 * 整合连接管理、音频播放和协议处理
 */

// 请求管理常量
const REQUEST_TIMEOUT_MS = 10000;         // 请求超时时间（10秒作为合理的平衡点）
const EMERGENCY_UNLOCK_MS = 3000;         // 紧急解锁阈值
const MAX_RETRY_ATTEMPTS = 2;             // 最大重试次数
const RETRY_DELAY_MS = 200;               // 重试延迟

// 播放器状态枚举（增强版）
const PlayerStates = {
    IDLE: 'idle',
    PLAYING: 'playing', 
    BUFFERING: 'buffering',
    RESYNCING: 'resyncing',  // 新增：重新同步状态
    ERROR: 'error'            // 错误状态（用于同步失败）
};

// 状态转换日志装饰器
function logStateTransition(fromState, toState, reason) {
    const timestamp = new Date().toISOString();
    console.log(`🔄 [${timestamp}] 状态转换: ${fromState} → ${toState} | 原因: ${reason}`);
    
    // 可选：发送到监控系统
    if (window.telemetry) {
        window.telemetry.trackEvent('player_state_transition', {
            from: fromState,
            to: toState,
            reason: reason,
            timestamp: timestamp
        });
    }
}

class WebAudioPlayerV2 {
    constructor() {
        console.log('🚀 WebAudioPlayer V2 (Modular) initializing...');
        
        // 状态管理
        this.state = PlayerStates.IDLE;
        this.sessionId = null;
        
        // 播放器状态机（新增）
        this.playerState = 'NORMAL';  // NORMAL | RECONFIGURING | INTERRUPTED
        
        // 播放列表版本管理（新增）
        this.currentPlaylistVersion = 0;
        this.totalPlaylistItems = 0;
        this.pauseContentRequests = false;  // 同步期间暂停请求
        
        // 音频缓存管理（新增）
        this.audioCache = new Map();  // cache_key -> {data, metadata, timestamp}
        this.indexToCacheKey = new Map();  // index -> cache_key
        
        // 严格模式：是否严格执行API契约
        this.strictMode = false;  // 默认关闭，允许一定的容错
        
        // 请求管理状态
        this.isRequestingContent = false;      // 请求锁
        this.requestTimeoutId = null;          // 超时定时器ID
        this.nextIndexToRequest = 0;           // 下一个要请求的索引
        this.currentRequestId = null;          // 当前请求ID
        this.currentRequestIndex = null;       // 当前请求的索引
        this.requestRetryCount = 0;            // 重试计数
        this.requestStartTime = 0;             // 请求开始时间
        
        // 缓冲管理 - 新增
        this.networkBuffer = [];  // 网络接收缓冲队列
        this.pumpingInterval = null;  // 泵循环定时器
        
        // 可调参数配置（从配置文件加载）
        const configProfile = window.location.hostname === 'localhost' ? 'development' : 'production';
        const MAX_NETWORK_BUFFER_SIZE = 20;  // 使用常量
        this.TUNING = window.getAudioConfig ? window.getAudioConfig(configProfile) : {
            PUMP_INTERVAL_MS: 200,
            BUFFER_LOW_WATERMARK: 3,
            BUFFER_HIGH_WATERMARK: 10,
            MAX_NETWORK_BUFFER_SIZE: MAX_NETWORK_BUFFER_SIZE,
            AUDIO_FEED_THRESHOLD: 2
        };
        
        console.log('📋 Audio config loaded:', { profile: configProfile, config: this.TUNING });
        
        // 初始化模块
        this.connection = new ConnectionManager({
            reconnectInterval: 3000,
            maxReconnectAttempts: 5
        });
        
        this.audioPlayer = new AudioPlayer({
            sampleRate: 24000,
            channels: 1,
            bitDepth: 16
        });
        
        this.protocol = new ProtocolHandler();
        
        // 增强监控
        this.monitor = {
            startTime: Date.now(),
            playCount: 0,
            errorCount: 0,
            bufferUnderrun: 0,
            networkBufferPeak: 0,
            totalBytesReceived: 0,
            lastError: null
        };
        
        // UI回调
        this.onStatusUpdate = null;
        this.onError = null;
        
        // 指数退避配置（新增）
        this.syncRetryConfig = {
            baseDelay: 1000,      // 基础延迟 1秒
            maxDelay: 16000,      // 最大延迟 16秒
            maxAttempts: 5,       // 最大尝试次数
            jitterMax: 500        // 最大抖动时间（防止同步风暴）
        };
        
        this.syncRetryState = {
            attempts: 0,
            currentDelay: 1000,
            retryTimer: null
        };
        
        // 设置模块间的连接
        this.setupModuleConnections();
        
        // 初始化音频上下文（需要用户交互）
        this.initPromise = null;
    }
    
    /**
     * 设置模块间的事件连接
     */
    setupModuleConnections() {
        // 设置协议处理器对播放器的引用
        this.protocol.player = this;
        
        // 连接管理器 -> 协议处理器
        this.connection.onMessage = (event) => {
            this.protocol.handleMessage(event);
        };
        
        this.connection.onOpen = () => {
            console.log('🔌 WebSocket connected, resetting all state');
            
            // 完全重置所有状态
            this.nextIndexToRequest = 0;
            this.clearRequestLock();
            this.currentRequestId = null;
            this.currentRequestIndex = null;
            this.requestRetryCount = 0;
            this.networkBuffer = [];
            
            this.updateState(PlayerStates.BUFFERING);
        };
        
        this.connection.onClose = () => {
            console.log('🔌 WebSocket disconnected, clearing request state');
            
            // 清理请求状态
            this.clearRequestLock();
            this.currentRequestId = null;
            this.currentRequestIndex = null;
            
            this.updateState(PlayerStates.IDLE);
        };
        
        this.connection.onError = (error) => {
            this.handleError(error);
        };
        
        // 协议处理器 -> 网络缓冲队列（修改：增强版带请求验证和缓存管理）
        this.protocol.onAudioData = async (arrayBuffer, metadata) => {
            const receivedRequestId = metadata.request_id || 'unknown';
            const receivedIndex = metadata.index;
            
            console.log(`📨 [${receivedRequestId}] Audio data received for index=${receivedIndex}`);
            
            // 🎯 新增：触发当前播放句子的状态更新
            if (metadata && metadata.metadata && metadata.metadata.text) {
                const currentSentenceText = metadata.metadata.text;
                console.log(`📝 Current playing sentence: "${currentSentenceText.substring(0, 50)}..."`);
                
                // 触发外部回调，通知控制面板更新当前播放的句子
                if (this.onCurrentSentenceUpdate) {
                    this.onCurrentSentenceUpdate({
                        text: currentSentenceText,
                        index: receivedIndex,
                        type: metadata.metadata.type || 'main',
                        is_qa: metadata.metadata.is_qa || false,
                        item_id: metadata.metadata.item_id || null
                    });
                }
            }
            
            // 更新音频缓存
            if (receivedIndex !== undefined && receivedIndex !== null) {
                const cacheKey = this.generateCacheKey(metadata);
                this.audioCache.set(cacheKey, {
                    data: arrayBuffer,
                    metadata: metadata,
                    timestamp: Date.now()
                });
                this.indexToCacheKey.set(receivedIndex, cacheKey);
                console.log(`💾 Cached audio for index ${receivedIndex} with key ${cacheKey}`);
            }
            
            // 验证是否是当前请求的响应（防止竞争条件）
            if (this.currentRequestId && receivedRequestId !== this.currentRequestId) {
                // 特殊处理：如果服务端没有返回request_id（违反API契约）
                if (receivedRequestId === 'unknown') {
                    console.error(`❌ Server violated API contract: response missing request_id! Expected: ${this.currentRequestId}`);
                    // 记录违反契约的事件
                    this.monitor.contractViolations = (this.monitor.contractViolations || 0) + 1;
                    
                    // 决定是否继续处理（基于配置或严格模式）
                    if (this.strictMode) {
                        console.error('🚫 Strict mode: rejecting response without proper request_id');
                        return;
                    } else {
                        console.warn('⚠️ Lenient mode: processing response despite missing request_id');
                        // 继续处理，但标记为不可靠
                    }
                } else {
                    console.warn(`⚠️ [${receivedRequestId}] Ignoring stale response (expected ${this.currentRequestId})`);
                    return;
                }
            }
            
            // 验证索引是否存在（防止二进制音频导致的 undefined）
            if (receivedIndex === undefined || receivedIndex === null) {
                console.error(`❌ [${receivedRequestId}] Received audio without index! This violates protocol.`);
                // 记录协议违规
                this.monitor.protocolViolations = (this.monitor.protocolViolations || 0) + 1;
                
                // 在严格模式下拒绝处理
                if (this.strictMode) {
                    console.error('🚫 Strict mode: rejecting response without index');
                    return;
                }
                
                // 宽松模式下，尝试使用当前请求的索引
                if (this.currentRequestIndex !== null) {
                    console.warn(`⚠️ Using fallback index from request: ${this.currentRequestIndex}`);
                    receivedIndex = this.currentRequestIndex;
                } else {
                    console.error('❌ Cannot determine index, skipping this audio chunk');
                    return;
                }
            }
            
            // 验证索引匹配
            if (this.currentRequestIndex !== null && receivedIndex !== this.currentRequestIndex) {
                console.warn(`⚠️ [${receivedRequestId}] Index mismatch: got ${receivedIndex}, expected ${this.currentRequestIndex}`);
                // 根据服务器响应调整索引
                this.nextIndexToRequest = receivedIndex + 1;
            } else {
                // 正常推进索引
                this.nextIndexToRequest = receivedIndex + 1;
            }
            
            // 清除请求锁和重置重试计数
            this.clearRequestLock();
            this.requestRetryCount = 0;
            this.currentRequestId = null;
            this.currentRequestIndex = null;
            
            const elapsed = Date.now() - this.requestStartTime;
            console.log(`✅ [${receivedRequestId}] Request completed in ${elapsed}ms, next index=${this.nextIndexToRequest}`);
            
            // 推入网络缓冲队列
            if (this.networkBuffer.length < this.TUNING.MAX_NETWORK_BUFFER_SIZE) {
                this.networkBuffer.push({
                    data: arrayBuffer,
                    metadata: metadata,
                    timestamp: Date.now()
                });
                
                this.monitor.totalBytesReceived += arrayBuffer.byteLength;
                this.monitor.networkBufferPeak = Math.max(
                    this.monitor.networkBufferPeak, 
                    this.networkBuffer.length
                );
                
                console.log(`📦 Network buffer: ${this.networkBuffer.length}/${this.TUNING.MAX_NETWORK_BUFFER_SIZE}`);
            } else {
                console.warn('⚠️ Network buffer full, dropping audio chunk');
            }
        };
        
        this.protocol.onSessionConfig = async (config) => {
            await this.audioPlayer.updateConfig(config);
        };
        
        this.protocol.onError = (error) => {
            this.handleError(error);
        };
        
        // 处理内容未准备好的情况 (v2.2)
        this.protocol.onContentNotReady = (data) => {
            console.log(`⏱️ Content not ready for index=${data.index}, will retry after ${data.retry_after_ms}ms`);
            
            // 清除当前请求锁，允许重试
            if (data.request_id === this.currentRequestId) {
                this.clearRequestLock();
            }
            
            // 设置重试定时器
            const retryDelay = data.retry_after_ms || 1000;
            setTimeout(() => {
                console.log(`🔄 Retrying content request for index=${data.index}`);
                // 重新请求相同的索引
                this.requestNextContent();
            }, retryDelay);
            
            // 可选：显示用户提示
            this.updateStatus(`正在生成内容，请稍候...`);
        };
        
        this.protocol.onContentNext = (data) => {
            if (data.ready) {
                // 请求下一段内容
                this.requestNextContent();
            }
        };
        
        // WebSocket架构重构：连接状态更新处理器
        this.protocol.onStateUpdate = (stateData) => {
            console.log('📊 Received state update via WebSocket', stateData);
            
            // 触发外部状态更新回调
            if (this.onServerStateUpdate) {
                this.onServerStateUpdate(stateData);
            }
        };
        
        // 音频播放器 -> 主控制器（修改：移除idle时的请求）
        this.audioPlayer.onStateChange = (state) => {
            if (state === 'playing') {
                this.updateState(PlayerStates.PLAYING);
            } else if (state === 'idle') {
                // 仅更新状态，不再请求内容（由pumpingLoop负责）
                this.updateState(PlayerStates.BUFFERING);
            }
        };
        
        this.audioPlayer.onBufferLow = () => {
            this.monitor.bufferUnderrun++;
            console.log('⚠️ AudioPlayer buffer low');
            // 不再直接请求，由pumpingLoop统一管理
        };
        
        this.audioPlayer.onError = (error) => {
            this.handleError(error);
        };
    }
    
    /**
     * 初始化（需要用户交互）
     */
    async initialize() {
        if (this.initPromise) {
            return this.initPromise;
        }
        
        this.initPromise = (async () => {
            try {
                console.log('🎵 Initializing audio context...');
                const success = await this.audioPlayer.initialize();
                
                if (success) {
                    this.updateState(PlayerStates.IDLE);
                    console.log('✅ WebAudioPlayer V2 initialized successfully');
                    return true;
                } else {
                    throw new Error('Failed to initialize audio context');
                }
            } catch (error) {
                this.handleError(error);
                return false;
            }
        })();
        
        return this.initPromise;
    }
    
    /**
     * 启动缓冲泵循环
     */
    startPumpingLoop() {
        if (this.pumpingInterval) {
            console.log('⚠️ Pumping loop already running');
            return;
        }
        
        console.log('🔄 Starting pumping loop...');
        
        this.pumpingInterval = setInterval(() => {
            this.pumpingLoop();
        }, this.TUNING.PUMP_INTERVAL_MS);
        
        // 立即执行一次
        this.pumpingLoop();
    }
    
    /**
     * 停止缓冲泵循环
     */
    stopPumpingLoop() {
        if (this.pumpingInterval) {
            clearInterval(this.pumpingInterval);
            this.pumpingInterval = null;
            console.log('⏹️ Pumping loop stopped');
        }
    }
    
    /**
     * 核心缓冲泵循环逻辑
     */
    async pumpingLoop() {
        // 1. 检查AudioPlayer缓冲状态，喂数据
        const audioStats = this.audioPlayer.getStats();
        
        if (audioStats.activeBuffers < this.TUNING.AUDIO_FEED_THRESHOLD && 
            this.networkBuffer.length > 0) {
            
            // 从网络缓冲取数据喂给AudioPlayer
            const chunk = this.networkBuffer.shift();
            
            try {
                await this.audioPlayer.processPCMChunk(chunk.data);
                this.monitor.playCount++;
                console.log(`🎵 Fed audio to player, network buffer: ${this.networkBuffer.length}`);
            } catch (error) {
                console.error('❌ Failed to feed audio:', error);
            }
        }
        
        // 2. 检查网络缓冲水位，决定是否请求更多数据
        const networkBufferSize = this.networkBuffer.length;
        
        if (networkBufferSize < this.TUNING.BUFFER_LOW_WATERMARK) {
            // 低于低水位线，请求更多内容
            console.log(`📉 Network buffer low (${networkBufferSize}), requesting more...`);
            this.requestNextContent();
        } else if (networkBufferSize > this.TUNING.BUFFER_HIGH_WATERMARK) {
            // 高于高水位线，暂停请求
            console.log(`📈 Network buffer high (${networkBufferSize}), pausing requests`);
        }
        
        // 3. 紧急解锁机制（使用常量）
        if (audioStats.activeBuffers === 0 && 
            this.networkBuffer.length === 0 && 
            this.isRequestingContent) {
            
            const lockDuration = Date.now() - this.requestStartTime;
            if (lockDuration > EMERGENCY_UNLOCK_MS) {
                const requestId = this.currentRequestId || 'unknown';
                console.warn(`🚨 [${requestId}] Emergency unlock after ${lockDuration}ms`);
                this.clearRequestLock();
                // 立即重新请求
                this.requestNextContent();
            }
        }
        
        // 4. 更新监控状态
        if (this.onStatusUpdate) {
            this.onStatusUpdate({
                state: this.state,
                networkBuffer: networkBufferSize,
                audioBuffer: audioStats.activeBuffers,
                stats: this.getStats()
            });
        }
    }
    
    /**
     * 连接并开始播放
     */
    async connectAndPlay(sessionId, options = {}) {
        try {
            // 确保音频上下文已初始化
            const initialized = await this.initialize();
            if (!initialized) {
                throw new Error('Failed to initialize audio player');
            }
            
            this.sessionId = sessionId;
            this.updateState(PlayerStates.BUFFERING);
            
            // 建立WebSocket连接
            // WebSocket 403修复: 传递正确的参数
            await this.connection.connect(sessionId, {
                client_id: this.protocol.clientId,      // 传递protocol的clientId
                client_type: 'web_v2',
                protocol_version: '2.0',                // 修正协议版本参数名和值
                ...options
            });
            
            // 启动缓冲泵循环（关键修改）
            this.startPumpingLoop();
            
            // 初始请求
            setTimeout(() => {
                this.requestNextContent();
            }, 500);
            
        } catch (error) {
            this.handleError(error);
            this.stopPumpingLoop();  // 错误时停止循环
        }
    }
    
    /**
     * 生成请求追踪ID
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
    }
    
    /**
     * 清除请求锁
     */
    clearRequestLock() {
        this.isRequestingContent = false;
        clearTimeout(this.requestTimeoutId);
        this.requestTimeoutId = null;
    }
    
    /**
     * 调整播放索引以适应项目插入
     * @param {number} insertIndex - 插入位置的索引
     * @param {number} itemCount - 插入的项目数量
     */
    adjustPlayIndexForInsertion(insertIndex, itemCount = 1) {
        const beforeAdjust = this.nextIndexToRequest;
        
        if (this.nextIndexToRequest > insertIndex) {
            this.nextIndexToRequest += itemCount;
            console.log(`🎯 播放索引调整: ${beforeAdjust} → ${this.nextIndexToRequest} (在索引 ${insertIndex} 插入了 ${itemCount} 项)`);
            
            // 同时调整当前请求索引（如果正在请求中）
            if (this.currentRequestIndex !== null && this.currentRequestIndex > insertIndex) {
                const oldRequestIndex = this.currentRequestIndex;
                this.currentRequestIndex += itemCount;
                console.log(`🎯 当前请求索引也需调整: ${oldRequestIndex} → ${this.currentRequestIndex}`);
            }
        } else {
            console.log(`📍 播放索引无需调整: ${this.nextIndexToRequest} <= ${insertIndex}`);
        }
        
        // 记录调整事件到监控
        this.monitor.lastAdjustment = {
            timestamp: Date.now(),
            insertIndex: insertIndex,
            itemCount: itemCount,
            beforeIndex: beforeAdjust,
            afterIndex: this.nextIndexToRequest
        };
    }
    
    /**
     * 获取当前播放索引
     */
    getCurrentPlayingIndex() {
        // 返回最后请求的索引减1（假设正在播放）
        return Math.max(0, this.nextIndexToRequest - 1);
    }
    
    /**
     * 获取已缓冲的索引列表
     */
    getBufferedIndices() {
        const indices = [];
        const currentPlaying = this.getCurrentPlayingIndex();
        
        // 网络缓冲中的内容
        for (const item of this.networkBuffer) {
            if (item.metadata && item.metadata.index !== undefined) {
                indices.push(item.metadata.index);
            }
        }
        
        // 音频播放器缓冲中的内容（估算）
        const audioBufferCount = this.audioPlayer.getBufferedSegments ? 
            this.audioPlayer.getBufferedSegments() : 0;
        
        for (let i = 1; i <= audioBufferCount; i++) {
            const idx = currentPlaying + i;
            if (!indices.includes(idx)) {
                indices.push(idx);
            }
        }
        
        return indices.sort((a, b) => a - b);
    }
    
    /**
     * 获取音频缓存键映射
     */
    getAudioCacheKeys() {
        const cacheKeys = {};
        
        // 从缓存映射中获取
        for (const [index, cacheKey] of this.indexToCacheKey) {
            cacheKeys[index] = cacheKey;
        }
        
        return cacheKeys;
    }
    
    /**
     * 暂停缓冲（用于中断处理）
     */
    pauseBuffering() {
        console.log('⏸️ Pausing buffering for reconfiguration');
        this.playerState = 'RECONFIGURING';
        
        // 停止泵循环
        if (this.pumpingInterval) {
            clearInterval(this.pumpingInterval);
            this.pumpingInterval = null;
        }
    }
    
    /**
     * 恢复缓冲
     */
    resumeBuffering() {
        console.log('▶️ Resuming buffering');
        this.playerState = 'NORMAL';
        
        // 重启泵循环
        this.startPumpingLoop();
    }
    
    /**
     * 生成缓存键
     */
    generateCacheKey(metadata) {
        // 基于内容的哈希值或其他唯一标识
        if (metadata.item_id) {
            return `cache_${metadata.item_id}`;
        }
        // 降级：使用索引和内容哈希
        return `cache_idx_${metadata.index}_${metadata.text ? metadata.text.substring(0, 10) : 'unknown'}`;
    }
    
    /**
     * 处理播放列表更新（带缓存复用）
     */
    handlePlaylistUpdate(message) {
        console.log('📋 Playlist update received:', message);
        
        if (this.playerState === 'RECONFIGURING') {
            // 分析新播放列表，识别可复用的缓存
            const reusableCache = this.identifyReusableCache(message.items || []);
            
            // 清理不再需要的缓存
            this.cleanupObsoleteCache(reusableCache);
            
            // 更新索引映射
            this.updateIndexMapping(message.items || []);
            
            // 恢复正常状态
            this.playerState = 'NORMAL';
            this.resumeBuffering();
            
            // 立即请求QA内容
            if (message.qa_info) {
                console.log('🎤 Requesting QA content immediately');
                // 清空网络缓冲，确保QA优先播放
                this.networkBuffer = [];
                this.requestNextContent();
            }
        }
    }
    
    /**
     * 识别可复用的缓存
     */
    identifyReusableCache(newItems) {
        const reusable = new Set();
        let reuseCount = 0;
        
        for (const item of newItems) {
            // 检查是否有匹配的缓存键
            const potentialKeys = [
                `cache_${item.item_id}`,
                `cache_idx_${item.index}_${item.text ? item.text.substring(0, 10) : 'unknown'}`
            ];
            
            for (const key of potentialKeys) {
                if (this.audioCache.has(key)) {
                    reusable.add(key);
                    reuseCount++;
                    console.log(`♻️ Found reusable cache for item ${item.item_id || item.index}`);
                    break;
                }
            }
        }
        
        const totalBuffered = this.audioCache.size;
        const reuseRate = totalBuffered > 0 ? (reuseCount / totalBuffered * 100).toFixed(1) : 0;
        console.log(`♻️ Cache reuse: ${reuseCount}/${totalBuffered} items (${reuseRate}%)`);
        
        return reusable;
    }
    
    /**
     * 清理过时的缓存
     */
    cleanupObsoleteCache(reusableKeys) {
        const keysToDelete = [];
        
        for (const [key] of this.audioCache) {
            if (!reusableKeys.has(key)) {
                keysToDelete.push(key);
            }
        }
        
        for (const key of keysToDelete) {
            this.audioCache.delete(key);
            // 清理索引映射
            for (const [index, cacheKey] of this.indexToCacheKey) {
                if (cacheKey === key) {
                    this.indexToCacheKey.delete(index);
                    break;
                }
            }
        }
        
        if (keysToDelete.length > 0) {
            console.log(`🗑️ Cleaned up ${keysToDelete.length} obsolete cache entries`);
        }
    }
    
    /**
     * 更新索引映射
     */
    updateIndexMapping(items) {
        // 根据新的播放列表更新索引到缓存键的映射
        const newMapping = new Map();
        
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const cacheKey = this.generateCacheKey({
                item_id: item.item_id,
                index: i,
                text: item.text
            });
            
            if (this.audioCache.has(cacheKey)) {
                newMapping.set(i, cacheKey);
            }
        }
        
        this.indexToCacheKey = newMapping;
        console.log(`🔄 Updated index mapping: ${newMapping.size} entries`);
    }
    
    /**
     * 请求下一段内容（增强版带锁和追踪）
     */
    requestNextContent() {
        // 如果正在同步，不发送请求
        if (this.state === PlayerStates.RESYNCING || this.pauseContentRequests) {
            console.log('🚫 跳过内容请求：正在同步中');
            return;
        }
        
        // 防止重复请求
        if (this.isRequestingContent) {
            console.log('⏸️ Request already in flight, skipping');
            return;
        }
        
        // 生成追踪ID
        const requestId = this.generateRequestId();
        const requestIndex = this.nextIndexToRequest;
        
        console.log(`🔄 [${requestId}] Initiating request for index=${requestIndex}`);
        
        // 设置请求状态
        this.isRequestingContent = true;
        this.currentRequestId = requestId;
        this.currentRequestIndex = requestIndex;
        this.requestStartTime = Date.now();
        
        // 设置超时保护（带重试机制）
        this.requestTimeoutId = setTimeout(() => {
            this.handleRequestTimeout(requestId, requestIndex);
        }, REQUEST_TIMEOUT_MS);
        
        // 构建并发送请求（符合API契约v2.0）
        const request = this.protocol.buildContentRequest({
            index: requestIndex,  // 直接使用index字段传递要请求的索引
            request_id: requestId  // 添加追踪ID
        });
        
        if (this.connection.send(request)) {
            console.log(`📤 [${requestId}] Request sent: index=${requestIndex}`);
        } else {
            // 发送失败，立即解锁并可能重试
            console.error(`❌ [${requestId}] Failed to send request`);
            this.clearRequestLock();
            this.attemptRetry(requestId, requestIndex);
        }
    }
    
    /**
     * 处理请求超时（带重试）
     */
    handleRequestTimeout(requestId, requestIndex) {
        console.warn(`⚠️ [${requestId}] Request timeout after ${REQUEST_TIMEOUT_MS}ms`);
        
        // 清除锁
        this.clearRequestLock();
        
        // 尝试重试
        this.attemptRetry(requestId, requestIndex);
    }
    
    /**
     * 重试机制（修复版：确保状态转换的原子性）
     */
    attemptRetry(originalRequestId, requestIndex) {
        // 检查是否已经收到了响应（避免竞态条件）
        if (this.nextIndexToRequest > requestIndex) {
            console.log(`✅ [${originalRequestId}] Response already received during timeout, index advanced to ${this.nextIndexToRequest}`);
            this.requestRetryCount = 0;
            return;  // 不需要重试
        }
        
        if (this.requestRetryCount < MAX_RETRY_ATTEMPTS) {
            this.requestRetryCount++;
            console.log(`🔁 [${originalRequestId}] Retry ${this.requestRetryCount}/${MAX_RETRY_ATTEMPTS} for index=${requestIndex}`);
            
            // 清理之前的状态
            this.currentRequestId = null;
            this.currentRequestIndex = null;
            
            setTimeout(() => {
                // 双重检查：确保在延迟期间没有收到响应
                if (this.nextIndexToRequest === requestIndex) {
                    this.requestNextContent();
                } else {
                    console.log(`✅ Index already advanced to ${this.nextIndexToRequest}, skip retry`);
                    this.requestRetryCount = 0;
                }
            }, RETRY_DELAY_MS);
        } else {
            // 达到最大重试次数
            console.error(`❌ [${originalRequestId}] Max retries reached for index=${requestIndex}`);
            this.requestRetryCount = 0;
            
            // 跳过这个有问题的索引，继续下一个
            this.nextIndexToRequest = requestIndex + 1;
            console.warn(`⚠️ Skipping problematic index ${requestIndex}, moving to ${this.nextIndexToRequest}`);
            
            // 立即请求下一个
            this.requestNextContent();
        }
    }
    
    /**
     * 发送健康报告
     */
    sendHealthReport() {
        const stats = this.audioPlayer.getStats();
        
        const report = this.protocol.buildHealthReport({
            bufferSize: stats.activeBuffers,
            droppedFrames: stats.droppedChunks,
            latency: Date.now() - this.monitor.startTime
        });
        
        this.connection.send(report);
    }
    
    /**
     * 停止播放
     */
    stop() {
        console.log('⏹️ Stopping playback...');
        
        this.stopPumpingLoop();  // 停止泵循环
        this.audioPlayer.stop();
        this.connection.disconnect();
        this.networkBuffer = [];  // 清空网络缓冲
        
        // 使用 SessionManager 清理会话状态
        if (window.sessionManager && this.sessionId) {
            const currentState = window.sessionManager.getState();
            // 只有在会话还活跃时才调用 endSession
            if (currentState.isActive || currentState.isConnecting) {
                window.sessionManager.endSession('normal');
            }
        }
        
        this.sessionId = null;
        this.updateState(PlayerStates.IDLE);
    }
    
    /**
     * 更新状态
     */
    updateState(newState, reason = 'unknown') {
        const oldState = this.state;
        this.state = newState;
        
        // 使用状态转换日志
        logStateTransition(oldState, newState, reason);
        
        if (this.onStatusUpdate) {
            this.onStatusUpdate({
                state: newState,
                stats: this.getStats()
            });
        }
    }
    
    /**
     * 更新状态文本（用于UI显示）
     * 保持与旧版本的兼容性
     */
    updateStatus(status) {
        console.log(`📊 Status update: ${status}`);
        if (this.onStatusUpdate) {
            this.onStatusUpdate({
                statusText: status,
                state: this.state,
                stats: this.getStats()
            });
        }
    }
    
    /**
     * 处理错误（增强版带完整状态清理）
     */
    handleError(error) {
        const requestId = this.currentRequestId || 'none';
        console.error(`❌ [${requestId}] Error:`, error);
        
        // 清理所有请求相关状态
        this.clearRequestLock();
        this.currentRequestId = null;
        this.currentRequestIndex = null;
        this.requestRetryCount = 0;
        
        // 更新监控统计
        this.monitor.errorCount++;
        this.monitor.lastError = error.message;
        
        this.updateState(PlayerStates.ERROR, error.message);
        
        if (this.onError) {
            this.onError(error);
        }
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        const audioStats = this.audioPlayer.getStats();
        
        return {
            state: this.state,
            sessionId: this.sessionId,
            connected: this.connection.isConnected,
            audio: audioStats,
            networkBuffer: {
                current: this.networkBuffer.length,
                peak: this.monitor.networkBufferPeak,
                lowWatermark: this.TUNING.BUFFER_LOW_WATERMARK,
                highWatermark: this.TUNING.BUFFER_HIGH_WATERMARK
            },
            monitor: this.monitor,
            uptime: Date.now() - this.monitor.startTime
        };
    }
    
    /**
     * 发送QA请求
     */
    sendQARequest(question) {
        const request = this.protocol.buildQARequest(question);
        
        if (this.connection.send(request)) {
            console.log('💬 QA request sent:', question);
            return true;
        }
        return false;
    }
    
    /**
     * 进入重新同步状态
     */
    enterResyncState(reason = 'unknown') {
        const previousState = this.state;
        this.state = PlayerStates.RESYNCING;
        logStateTransition(previousState, PlayerStates.RESYNCING, reason);
        
        // 暂停所有内容请求
        this.pauseContentRequests = true;
        this.clearRequestLock();
        
        console.log(`🔄 进入重新同步状态 (原因: ${reason})`);
    }
    
    /**
     * 请求完整播放列表同步
     */
    async requestFullPlaylistSync(reason, triggerMessage) {
        try {
            const request = {
                type: 'playlist_info_request',
                request_id: `sync_${Date.now()}`,
                client_version: this.currentPlaylistVersion || 0,
                sync_reason: reason,
                current_playing_index: this.nextIndexToRequest - 1
            };
            
            console.log('📤 请求完整播放列表同步:', request);
            
            // 发送请求并等待响应
            const response = await this.sendAndWaitForResponse(request, 'playlist_info_response', 5000);
            
            if (response) {
                // 同步成功，重置重试状态
                this.resetSyncRetryState();
                this.applyPlaylistSync(response);
            } else {
                throw new Error('播放列表同步超时');
            }
        } catch (error) {
            console.error('❌ 播放列表同步失败:', error);
            this.handleSyncFailureWithRetry(reason, triggerMessage);
        }
    }
    
    /**
     * 发送请求并等待响应
     */
    async sendAndWaitForResponse(request, responseType, timeout = 5000) {
        return new Promise((resolve) => {
            const requestId = request.request_id;
            let timeoutId;
            
            // 设置超时
            timeoutId = setTimeout(() => {
                console.warn(`⚠️ 请求超时: ${requestId}`);
                resolve(null);
            }, timeout);
            
            // 设置响应处理器
            const originalHandler = this.protocol.onPlaylistInfo;
            this.protocol.onPlaylistInfo = (response) => {
                if (response.request_id === requestId) {
                    clearTimeout(timeoutId);
                    this.protocol.onPlaylistInfo = originalHandler;
                    resolve(response);
                }
            };
            
            // 发送请求
            if (!this.connection.send(request)) {
                clearTimeout(timeoutId);
                this.protocol.onPlaylistInfo = originalHandler;
                resolve(null);
            }
        });
    }
    
    /**
     * 应用播放列表同步
     */
    applyPlaylistSync(playlistInfo) {
        console.log('📥 应用播放列表同步:', playlistInfo);
        
        // 无条件替换本地状态
        this.currentPlaylistVersion = playlistInfo.version;
        this.totalPlaylistItems = playlistInfo.total_items;
        
        // 智能计算下一个请求索引
        const suggestedIndex = playlistInfo.suggested_resume_index;
        if (suggestedIndex !== undefined) {
            this.nextIndexToRequest = suggestedIndex;
            console.log(`✅ 使用服务器建议的恢复索引: ${suggestedIndex}`);
        } else {
            // 保持当前索引，但验证边界
            this.nextIndexToRequest = Math.min(
                this.nextIndexToRequest,
                this.totalPlaylistItems
            );
        }
        
        // 退出同步状态，恢复播放
        this.exitResyncState();
    }
    
    /**
     * 退出同步状态
     */
    exitResyncState() {
        const previousState = this.state;
        this.pauseContentRequests = false;
        this.state = PlayerStates.PLAYING;
        logStateTransition(previousState, PlayerStates.PLAYING, '同步完成');
        
        console.log(`✅ 同步完成，恢复播放 (下一索引: ${this.nextIndexToRequest})`);
        
        // 立即请求下一个内容
        this.requestNextContent();
    }
    
    /**
     * 处理同步失败（指数退避重试）
     */
    handleSyncFailureWithRetry(reason, originalTrigger) {
        const previousState = this.state;
        this.state = PlayerStates.ERROR;
        logStateTransition(previousState, PlayerStates.ERROR, `同步失败: ${reason}`);
        
        this.syncRetryState.attempts++;
        
        if (this.syncRetryState.attempts > this.syncRetryConfig.maxAttempts) {
            console.error('❌ 达到最大重试次数，同步彻底失败');
            this.handleCriticalSyncFailure();
            return;
        }
        
        // 计算下次重试延迟（指数退避）
        const baseDelay = Math.min(
            this.syncRetryConfig.baseDelay * Math.pow(2, this.syncRetryState.attempts - 1),
            this.syncRetryConfig.maxDelay
        );
        
        // 添加随机抖动（防止同步风暴）
        const jitter = Math.random() * this.syncRetryConfig.jitterMax;
        const totalDelay = baseDelay + jitter;
        
        console.log(`⏱️ 将在 ${Math.round(totalDelay)}ms 后重试同步 (尝试 ${this.syncRetryState.attempts}/${this.syncRetryConfig.maxAttempts})`);
        
        // 清除之前的重试定时器
        if (this.syncRetryState.retryTimer) {
            clearTimeout(this.syncRetryState.retryTimer);
        }
        
        // 设置重试定时器
        this.syncRetryState.retryTimer = setTimeout(() => {
            console.log(`🔄 执行第 ${this.syncRetryState.attempts} 次同步重试`);
            this.requestFullPlaylistSync(reason, originalTrigger);
        }, totalDelay);
    }
    
    /**
     * 重置同步重试状态
     */
    resetSyncRetryState() {
        this.syncRetryState.attempts = 0;
        this.syncRetryState.currentDelay = this.syncRetryConfig.baseDelay;
        if (this.syncRetryState.retryTimer) {
            clearTimeout(this.syncRetryState.retryTimer);
            this.syncRetryState.retryTimer = null;
        }
    }
    
    /**
     * 处理关键同步失败
     */
    handleCriticalSyncFailure() {
        // 关键失败处理：停止播放，等待用户干预
        this.stop();
        
        if (this.onError) {
            this.onError({
                type: 'CRITICAL_SYNC_FAILURE',
                message: '无法与服务器同步播放列表，播放已停止',
                action: 'USER_INTERVENTION_REQUIRED'
            });
        }
    }
    
    /**
     * 清理资源
     */
    async cleanup() {
        this.stopPumpingLoop();  // 确保泵循环停止
        this.stop();
        await this.audioPlayer.cleanup();
        this.networkBuffer = [];
        console.log('🧹 WebAudioPlayer V2 cleaned up');
    }
}

// 保持向后兼容
window.WebAudioPlayer = WebAudioPlayerV2;

// 简单的监控输出（可选）
if (typeof window !== 'undefined') {
    setInterval(() => {
        if (window.audioPlayer && window.audioPlayer.state === 'playing') {
            const stats = window.audioPlayer.getStats();
            console.table({
                '状态': stats.state,
                '已播放': stats.audio.playedChunks,
                '已丢弃': stats.audio.droppedChunks,
                '错误数': stats.monitor.errorCount,
                '缓冲不足': stats.monitor.bufferUnderrun
            });
        }
    }, 30000); // 每30秒输出一次
}