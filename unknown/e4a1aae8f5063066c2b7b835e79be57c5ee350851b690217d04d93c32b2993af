"""
Form Processing Service

Advanced form data processing, validation, and transformation pipeline
for operational forms submitted through the frontend interface.
"""

from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
import asyncio
from decimal import Decimal
from loguru import logger

from ..models.forms import (
    OperationalForm, BasicInformation, ProductInformation, 
    SellingPointsStructure, PersonaConfiguration, AdvancedSettings,
    ReviewAndValidation, ValidationRule, ValidationStatus, PriorityLevel
)
from ..models.content import QuestionPriority
from ..models.persona import PersonaManager, PersonaConfig, PersonaType, VoiceStyle
from ..core.exceptions import ValidationError, ServiceError
from ..core.config import cfg


@dataclass
class ProcessingResult:
    """Result of form processing operation"""
    success: bool
    processed_form_id: str
    content_generated: bool
    persona_configured: bool
    validation_passed: bool
    error_messages: List[str]
    processing_time_seconds: float
    generated_content_preview: Optional[str] = None


@dataclass 
class ValidationContext:
    """Context information for validation rules"""
    form_id: str
    section_being_validated: int
    user_id: str
    submission_timestamp: datetime
    previous_validations: List[ValidationStatus]


class FormProcessor:
    """
    Advanced form processing service that handles validation,
    content generation, and persona configuration.
    """
    
    def __init__(self):
        self.persona_manager = PersonaManager()
        self.validation_rules = self._initialize_validation_rules()
        self.processing_stats = {
            "forms_processed": 0,
            "validation_failures": 0,
            "content_generation_failures": 0,
            "average_processing_time": 0.0
        }
    
    async def process_form_submission(self, form: OperationalForm) -> ProcessingResult:
        """
        Process a complete form submission through the full pipeline
        
        Args:
            form: The operational form to process
            
        Returns:
            ProcessingResult with detailed processing information
        """
        start_time = datetime.utcnow()
        result = ProcessingResult(
            success=False,
            processed_form_id=form.form_id,
            content_generated=False,
            persona_configured=False,
            validation_passed=False,
            error_messages=[],
            processing_time_seconds=0.0
        )
        
        try:
            logger.info(f"Starting form processing pipeline for {form.form_id}")
            
            # Step 1: Comprehensive validation
            validation_context = ValidationContext(
                form_id=form.form_id,
                section_being_validated=0,  # Complete form
                user_id=form.created_by,
                submission_timestamp=datetime.utcnow(),
                previous_validations=[]
            )
            
            validation_result = await self._comprehensive_validation(form, validation_context)
            if not validation_result.success:
                result.error_messages.extend(validation_result.error_messages)
                return result
            
            result.validation_passed = True
            logger.info(f"Form {form.form_id} passed comprehensive validation")
            
            # Step 2: Configure persona (simplified)
            try:
                persona_id = form.persona_configuration.selected_persona_id
                if not self.persona_manager.switch_persona(persona_id):
                    # Create default persona if not exists
                    default_persona = PersonaConfig(
                        persona_id=persona_id,
                        name=f"Form Persona {persona_id}",
                        description="Auto-generated persona from form configuration",
                        persona_type=PersonaType.TEMPLATE_BASED,
                        speaking_rate=form.persona_configuration.speaking_rate,
                        energy_level=form.persona_configuration.energy_level,
                        voice_style=form.persona_configuration.voice_style or VoiceStyle.WARM
                    )
                    self.persona_manager.add_persona(default_persona)
                    self.persona_manager.switch_persona(persona_id)
                result.persona_configured = True
                logger.info(f"Persona {persona_id} configured for form {form.form_id}")
            except Exception as e:
                logger.warning(f"Persona configuration skipped for {form.form_id}: {str(e)}")
                result.persona_configured = False
            
            # Step 3: Generate content (simplified)
            try:
                preview_text = f"表单 {form.form_id} 内容已准备：{form.basic_information.stream_title}"
                result.content_generated = True
                result.generated_content_preview = preview_text
                logger.info(f"Content prepared for form {form.form_id}")
            except Exception as e:
                logger.warning(f"Content generation skipped for {form.form_id}: {str(e)}")
                result.content_generated = False
            
            # Step 4: Finalize processing
            form.is_processed = True
            form.processed_at = datetime.utcnow()
            logger.info(f"Form {form.form_id} processing finalized")
            
            result.success = True
            self.processing_stats["forms_processed"] += 1
            
            logger.info(f"Form {form.form_id} processed successfully")
            
        except Exception as e:
            logger.error(f"Form processing failed for {form.form_id}: {str(e)}")
            result.error_messages.append(f"Processing error: {str(e)}")
            
        finally:
            # Calculate processing time
            end_time = datetime.utcnow()
            result.processing_time_seconds = (end_time - start_time).total_seconds()
            
            # Update stats
            self._update_processing_stats(result)
            
        return result
    
    async def validate_section(
        self, 
        form: OperationalForm, 
        section_number: int,
        validation_context: Optional[ValidationContext] = None
    ) -> ProcessingResult:
        """
        Validate a specific form section with enhanced validation logic
        
        Args:
            form: The form to validate
            section_number: Section number (1-6)
            validation_context: Optional context for validation
            
        Returns:
            ProcessingResult with validation details
        """
        start_time = datetime.utcnow()
        
        if validation_context is None:
            validation_context = ValidationContext(
                form_id=form.form_id,
                section_being_validated=section_number,
                user_id=form.created_by,
                submission_timestamp=datetime.utcnow(),
                previous_validations=[]
            )
        
        result = ProcessingResult(
            success=False,
            processed_form_id=form.form_id,
            content_generated=False,
            persona_configured=False,
            validation_passed=False,
            error_messages=[],
            processing_time_seconds=0.0
        )
        
        try:
            validation_errors = []
            
            if section_number == 1:
                validation_errors = await self._validate_basic_information(
                    form.basic_information, validation_context
                )
            elif section_number == 2:
                validation_errors = await self._validate_product_information(
                    form.product_information, validation_context
                )
            elif section_number == 3:
                validation_errors = await self._validate_selling_points(
                    form.selling_points_structure, validation_context
                )
            elif section_number == 4:
                validation_errors = await self._validate_persona_configuration(
                    form.persona_configuration, validation_context
                )
            elif section_number == 5:
                validation_errors = await self._validate_advanced_settings(
                    form.advanced_settings, validation_context
                )
            elif section_number == 6:
                validation_errors = await self._validate_review_and_validation(
                    form.review_and_validation, validation_context
                )
            else:
                validation_errors = [f"Invalid section number: {section_number}"]
            
            if validation_errors:
                result.error_messages = validation_errors
            else:
                result.validation_passed = True
                result.success = True
                
            logger.info(f"Section {section_number} validation for {form.form_id}: {'PASS' if result.success else 'FAIL'}")
            
        except Exception as e:
            logger.error(f"Section validation error for {form.form_id}, section {section_number}: {str(e)}")
            result.error_messages.append(f"Validation error: {str(e)}")
            
        finally:
            end_time = datetime.utcnow()
            result.processing_time_seconds = (end_time - start_time).total_seconds()
            
        return result
    
    async def _comprehensive_validation(self, form: OperationalForm, context: ValidationContext) -> ProcessingResult:
        """Perform comprehensive validation across all sections"""
        result = ProcessingResult(
            success=True,
            processed_form_id=form.form_id,
            content_generated=False,
            persona_configured=False,
            validation_passed=False,
            error_messages=[],
            processing_time_seconds=0.0
        )
        
        # Check if strict validation is enabled
        strict_validation = cfg.get_yaml_config('features.operational_forms.enable_strict_validation', False)
        
        if not strict_validation:
            # Skip validation, mark as passed
            result.validation_passed = True
            logger.info(f"Skipping strict validation for form {form.form_id} (disabled in config)")
            return result
        
        # Validate each section
        for section_num in range(1, 7):
            section_result = await self.validate_section(form, section_num, context)
            if not section_result.success:
                result.success = False
                result.error_messages.extend([
                    f"Section {section_num}: {error}" for error in section_result.error_messages
                ])
        
        # Cross-section validation
        cross_validation_errors = await self._cross_section_validation(form, context)
        if cross_validation_errors:
            result.success = False
            result.error_messages.extend(cross_validation_errors)
        
        if result.success:
            result.validation_passed = True
            
        return result
    
    async def _validate_basic_information(self, basic_info: BasicInformation, context: ValidationContext) -> List[str]:
        """Enhanced validation for basic information section"""
        errors = []
        
        # Title validation
        if len(basic_info.stream_title.strip()) < 10:
            errors.append("Stream title must be at least 10 characters long")
        
        # Only check for test terms in production environment
        from ..core.config import cfg
        if hasattr(cfg, 'environment') and cfg.environment == 'production':
            if "测试" in basic_info.stream_title or "test" in basic_info.stream_title.lower():
                errors.append("Stream title should not contain test-related terms for production")
        
        # Duration validation
        if basic_info.planned_duration_minutes < 30:
            errors.append("Stream duration must be at least 30 minutes")
        elif basic_info.planned_duration_minutes > 180:
            errors.append("Stream duration should not exceed 180 minutes")
        
        # Schedule validation
        if basic_info.start_time:
            if basic_info.start_time < datetime.utcnow():
                errors.append("Scheduled start time cannot be in the past")
        
        return errors
    
    async def _validate_product_information(self, product_info: ProductInformation, context: ValidationContext) -> List[str]:
        """Enhanced validation for product information section"""
        errors = []
        
        # SKU validation
        if product_info.primary_sku.upper() in ["TBD", "TODO", "TEMP"]:
            errors.append("Primary SKU must be a valid product identifier")
        
        # Price validation
        if product_info.current_price <= 0:
            errors.append("Product price must be greater than 0")
        
        if product_info.original_price and product_info.original_price <= product_info.current_price:
            errors.append("Original price must be higher than current price when specified")
        
        # Name validation
        if product_info.product_name.startswith("待"):
            errors.append("Product name must be finalized (remove placeholder text)")
        
        # Specifications validation
        if len(product_info.key_specifications) < 1:
            errors.append("At least one key specification is required")
        
        return errors
    
    async def _validate_selling_points(self, selling_points: SellingPointsStructure, context: ValidationContext) -> List[str]:
        """Enhanced validation for selling points section"""
        errors = []
        
        # Value proposition validation
        if selling_points.primary_value_proposition.startswith("待"):
            errors.append("Primary value proposition must be finalized")
        
        # Selling points validation
        if len(selling_points.selling_points) < 3:
            errors.append("At least 3 selling points are required")
        
        high_priority_count = sum(1 for sp in selling_points.selling_points if sp.priority == PriorityLevel.HIGH)
        if high_priority_count == 0:
            errors.append("At least one selling point must be marked as high priority")
        
        # Verify supporting facts
        for i, sp in enumerate(selling_points.selling_points):
            if len(sp.supporting_facts) < 1:
                errors.append(f"Selling point '{sp.title}' needs at least 1 supporting fact")
        
        return errors
    
    async def _validate_persona_configuration(self, persona_config: PersonaConfiguration, context: ValidationContext) -> List[str]:
        """Enhanced validation for persona configuration section"""
        errors = []
        
        # Persona selection validation
        if persona_config.selected_persona_id == "default":
            errors.append("A specific persona must be selected (not default)")
        
        # Voice settings validation
        if persona_config.speaking_rate < 0.5 or persona_config.speaking_rate > 2.0:
            errors.append("Speaking rate must be between 0.5 and 2.0")
        
        return errors
    
    async def _validate_advanced_settings(self, advanced_settings: AdvancedSettings, context: ValidationContext) -> List[str]:
        """Enhanced validation for advanced settings section"""
        errors = []
        
        # Words per minute validation
        if advanced_settings.words_per_minute_override:
            if advanced_settings.words_per_minute_override < 100 or advanced_settings.words_per_minute_override > 300:
                errors.append("Words per minute override must be between 100 and 300")
        
        # Question timeout validation
        if advanced_settings.question_response_timeout_seconds < 60:
            errors.append("Question timeout must be at least 60 seconds")
        
        return errors
    
    async def _validate_review_and_validation(self, review: ReviewAndValidation, context: ValidationContext) -> List[str]:
        """Enhanced validation for review and validation section"""
        errors = []
        
        # Check that all mandatory validations are completed
        content_rules_passed = all(
            rule.validation_status == ValidationStatus.VALID 
            for rule in review.content_validation_rules
            if rule.is_mandatory
        )
        
        compliance_rules_passed = all(
            rule.validation_status == ValidationStatus.VALID
            for rule in review.compliance_validation_rules  
            if rule.is_mandatory
        )
        
        technical_rules_passed = all(
            rule.validation_status == ValidationStatus.VALID
            for rule in review.technical_validation_rules
            if rule.is_mandatory
        )
        
        if not content_rules_passed:
            errors.append("All mandatory content validation rules must pass")
        
        if not compliance_rules_passed:
            errors.append("All mandatory compliance validation rules must pass")
        
        if not technical_rules_passed:
            errors.append("All mandatory technical validation rules must pass")
        
        return errors
    
    async def _cross_section_validation(self, form: OperationalForm, context: ValidationContext) -> List[str]:
        """Perform cross-section validation checks"""
        errors = []
        
        # Check consistency between stream type and content
        if form.basic_information.stream_type == "flash_sale":
            if not form.product_information.original_price:
                errors.append("Flash sale streams require original price to show discount")
        
        # Check persona and content alignment
        if form.persona_configuration.energy_level == "high":
            if form.basic_information.planned_duration_minutes > 120:
                errors.append("High energy personas work best with shorter streams (≤120 minutes)")
        
        # Check selling points and product alignment
        total_selling_points = len(form.selling_points_structure.selling_points)
        stream_duration = form.basic_information.planned_duration_minutes
        
        if total_selling_points > 0:
            time_per_point = stream_duration / total_selling_points
            if time_per_point < 10:
                errors.append("Too many selling points for stream duration (minimum 10 minutes per point)")
        
        return errors
    
    async def _configure_persona(self, form: OperationalForm) -> ProcessingResult:
        """Configure persona based on form settings"""
        result = ProcessingResult(
            success=False,
            processed_form_id=form.form_id,
            content_generated=False,
            persona_configured=False,
            validation_passed=False,
            error_messages=[],
            processing_time_seconds=0.0
        )
        
        try:
            persona_id = form.persona_configuration.selected_persona_id
            
            # Configure persona with form-specific settings
            persona_config = PersonaConfig(
                persona_id=persona_id,
                speaking_rate=form.persona_configuration.speaking_rate,
                energy_level=form.persona_configuration.energy_level,
                custom_greetings=form.persona_configuration.custom_greetings or [],
                voice_style=form.persona_configuration.voice_style or "natural"
            )
            
            # Apply persona configuration - switch to the selected persona
            if not self.persona_manager.switch_persona(persona_id):
                # If persona doesn't exist, add it first
                persona_config_obj = PersonaConfig(
                    persona_id=persona_id,
                    name=f"Form Persona {persona_id}",
                    description="Auto-generated persona from form configuration",
                    persona_type=PersonaType.TEMPLATE_BASED,
                    speaking_rate=form.persona_configuration.speaking_rate,
                    energy_level=form.persona_configuration.energy_level,
                    voice_style=form.persona_configuration.voice_style or VoiceStyle.WARM
                )
                self.persona_manager.add_persona(persona_config_obj)
                self.persona_manager.switch_persona(persona_id)
            
            result.success = True
            result.persona_configured = True
            
            logger.info(f"Persona {persona_id} configured for form {form.form_id}")
            
        except Exception as e:
            logger.error(f"Persona configuration failed for {form.form_id}: {str(e)}")
            result.error_messages.append(f"Persona configuration error: {str(e)}")
        
        return result
    
    async def _generate_content_pipeline(self, form: OperationalForm) -> ProcessingResult:
        """Generate content pipeline based on form data"""
        result = ProcessingResult(
            success=False,
            processed_form_id=form.form_id,
            content_generated=False,
            persona_configured=False,
            validation_passed=False,
            error_messages=[],
            processing_time_seconds=0.0
        )
        
        try:
            # Generate opening script
            opening_script = await self._generate_opening_script(form)
            
            # Generate selling point scripts
            selling_point_scripts = await self._generate_selling_point_scripts(form)
            
            # Generate interaction prompts
            interaction_prompts = await self._generate_interaction_prompts(form)
            
            # Create content preview
            preview_parts = [
                f"Opening: {opening_script[:100]}...",
                f"Selling Points: {len(selling_point_scripts)} scripts generated",
                f"Interactions: {len(interaction_prompts)} prompts ready"
            ]
            
            result.generated_content_preview = " | ".join(preview_parts)
            result.success = True
            result.content_generated = True
            
            logger.info(f"Content pipeline generated for form {form.form_id}")
            
        except Exception as e:
            logger.error(f"Content generation failed for {form.form_id}: {str(e)}")
            result.error_messages.append(f"Content generation error: {str(e)}")
        
        return result
    
    async def _generate_opening_script(self, form: OperationalForm) -> str:
        """Generate opening script based on form data"""
        basic_info = form.basic_information
        product_info = form.product_information
        
        # Template-based generation
        opening_script = f"""
        大家好！欢迎来到{basic_info.stream_title}！
        我是你们的AI主播，今天要为大家介绍{product_info.brand}的{product_info.product_name}。
        这是一款{product_info.category}产品，现在的优惠价格是{product_info.current_price}元。
        """
        
        return opening_script.strip()
    
    async def _generate_selling_point_scripts(self, form: OperationalForm) -> List[str]:
        """Generate scripts for each selling point"""
        scripts = []
        
        for sp in form.selling_points_structure.selling_points:
            script = f"""
            现在让我们来看看{sp.title}：
            {sp.description}
            
            支撑这个卖点的事实包括：
            {' | '.join(sp.supporting_facts)}
            """
            scripts.append(script.strip())
        
        return scripts
    
    async def _generate_interaction_prompts(self, form: OperationalForm) -> List[str]:
        """Generate interaction prompts for viewer engagement"""
        prompts = [
            f"有人对{form.product_information.product_name}有什么问题吗？",
            "大家觉得这个价格怎么样？",
            "想了解更多规格的朋友请在评论区告诉我",
            "现在下单的朋友可以享受特别优惠！"
        ]
        
        return prompts
    
    async def _finalize_processing(self, form: OperationalForm) -> None:
        """Finalize form processing and cleanup"""
        # Mark form as processed
        form.is_processed = True
        form.processed_at = datetime.utcnow()
        
        # Log processing completion
        logger.info(f"Form {form.form_id} processing finalized")
    
    def _update_processing_stats(self, result: ProcessingResult) -> None:
        """Update processing statistics"""
        if result.success:
            # Update average processing time
            current_avg = self.processing_stats["average_processing_time"]
            forms_count = self.processing_stats["forms_processed"]
            
            if forms_count > 0:
                new_avg = ((current_avg * forms_count) + result.processing_time_seconds) / (forms_count + 1)
                self.processing_stats["average_processing_time"] = new_avg
            else:
                self.processing_stats["average_processing_time"] = result.processing_time_seconds
        else:
            if not result.validation_passed:
                self.processing_stats["validation_failures"] += 1
            if not result.content_generated:
                self.processing_stats["content_generation_failures"] += 1
    
    def _initialize_validation_rules(self) -> Dict[str, List[ValidationRule]]:
        """Initialize validation rules for different categories"""
        return {
            "content": [
                ValidationRule(
                    rule_id="content_title_quality",
                    rule_name="Stream Title Quality Check",
                    description="Ensures stream title is descriptive and professional",
                    is_mandatory=True
                ),
                ValidationRule(
                    rule_id="content_selling_points_verified", 
                    rule_name="Selling Points Verification",
                    description="All selling points must have verified supporting facts",
                    is_mandatory=True
                )
            ],
            "compliance": [
                ValidationRule(
                    rule_id="compliance_advertising_standards",
                    rule_name="Advertising Standards Compliance",
                    description="Content must comply with advertising regulations",
                    is_mandatory=True
                ),
                ValidationRule(
                    rule_id="compliance_truth_in_advertising",
                    rule_name="Truth in Advertising",
                    description="All product claims must be substantiated and truthful",
                    is_mandatory=True
                )
            ],
            "technical": [
                ValidationRule(
                    rule_id="technical_persona_settings",
                    rule_name="Persona Technical Validation", 
                    description="Persona settings must be technically implementable",
                    is_mandatory=True
                ),
                ValidationRule(
                    rule_id="technical_duration_feasibility",
                    rule_name="Stream Duration Feasibility",
                    description="Stream duration must be realistic for content volume",
                    is_mandatory=False
                )
            ]
        }
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get current processing statistics"""
        return self.processing_stats.copy()