"""锁管理器工厂 - 显式策略选择

设计原则:
- 显式配置: 根据环境变量选择锁策略
- Fail-Fast: 初始化失败时立即终止
- 不提供自动降级

Author: Claude Code
Date: 2025-01-08
"""

import os
import sys
from typing import Optional
from loguru import logger

from .lock_manager import AbstractLockManager
from .file_lock_manager import FileLockManager, WindowsFileLockManager
from ..core.config import cfg


# 全局锁管理器实例
_global_lock_manager: Optional[AbstractLockManager] = None


async def get_lock_manager() -> AbstractLockManager:
    """获取全局锁管理器实例
    
    根据LOCK_STRATEGY环境变量选择实现:
    - "file" (默认): 文件锁
    - "redis": Redis分布式锁
    - "memory": 内存锁（单进程）
    
    Returns:
        AbstractLockManager: 锁管理器实例
        
    Raises:
        RuntimeError: 锁管理器初始化失败
    """
    global _global_lock_manager
    
    if _global_lock_manager is not None:
        return _global_lock_manager
    
    # 从环境变量或配置文件读取锁策略
    lock_strategy = cfg.get('lock_manager.strategy', 'file').lower()
    
    logger.info(f"🔧 初始化锁管理器: strategy={lock_strategy}")
    
    try:
        if lock_strategy == 'file':
            # 文件锁 - 默认策略
            lock_dir = cfg.get('lock_manager.file_lock_directory', '/tmp/ai-live-streamer/locks')
            
            # Windows平台使用专门的实现
            if os.name == 'nt':
                _global_lock_manager = WindowsFileLockManager(lock_dir)
            else:
                _global_lock_manager = FileLockManager(lock_dir)
            
            logger.info(f"✅ 文件锁管理器初始化成功: {lock_dir}")
            
        elif lock_strategy == 'redis':
            # Redis分布式锁 - 需要确保Redis可用
            try:
                from .redis_lock_manager import RedisLockManager
            except ImportError as e:
                logger.error("❌ Redis库未安装，无法使用Redis锁策略")
                raise RuntimeError(
                    "LOCK_STRATEGY设置为'redis'但Redis库未安装。"
                    "请安装: pip install redis>=5.0.0"
                ) from e
            
            redis_url = cfg.get('REDIS_URL', 'redis://localhost:6379/0')
            _global_lock_manager = RedisLockManager(redis_url)
            
            # 初始化连接 - 失败时会抛出异常
            await _global_lock_manager.initialize()
            
            logger.info(f"✅ Redis锁管理器初始化成功: {redis_url}")
            
        elif lock_strategy == 'memory':
            # 内存锁 - 仅单进程有效
            from .memory_lock_manager import MemoryLockManager
            
            _global_lock_manager = MemoryLockManager()
            logger.warning("⚠️ 使用内存锁管理器 - 仅在单进程模式下有效")
            
        else:
            raise ValueError(f"不支持的锁策略: {lock_strategy}")
            
    except Exception as e:
        logger.error(f"❌ 锁管理器初始化失败: {e}")
        logger.error("程序无法继续运行，请检查配置")
        
        # Fail-Fast: 初始化失败时立即终止程序
        sys.exit(1)
    
    return _global_lock_manager


def reset_lock_manager():
    """重置全局锁管理器（仅用于测试）"""
    global _global_lock_manager
    
    if _global_lock_manager:
        # 清理资源
        if hasattr(_global_lock_manager, 'cleanup'):
            _global_lock_manager.cleanup()
    
    _global_lock_manager = None
    logger.debug("🔄 锁管理器已重置")


# 锁上下文管理器
class Lock:
    """通用锁上下文管理器
    
    使用示例:
        async with Lock("my_resource", timeout=30) as lock:
            # 执行需要锁保护的操作
            pass
    """
    
    def __init__(
        self,
        lock_key: str,
        holder_id: Optional[str] = None,
        timeout_seconds: int = 60
    ):
        self.lock_key = lock_key
        self.holder_id = holder_id or f"holder_{os.getpid()}_{id(self)}"
        self.timeout_seconds = timeout_seconds
        self.lock_manager: Optional[AbstractLockManager] = None
        self.acquired = False
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.lock_manager = await get_lock_manager()
        
        self.acquired = await self.lock_manager.acquire_lock(
            self.lock_key,
            self.holder_id,
            self.timeout_seconds
        )
        
        if not self.acquired:
            raise RuntimeError(
                f"无法获取锁: {self.lock_key} "
                f"(超时: {self.timeout_seconds}秒)"
            )
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.acquired and self.lock_manager:
            await self.lock_manager.release_lock(self.lock_key, self.holder_id)