"""System status API endpoints

Provides global system status endpoint for frontend compatibility.
Simplified version based on archived status.py implementation.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException
from loguru import logger

# Router setup
router = APIRouter(prefix="/api", tags=["status"])

# Track start time for uptime calculation
_system_start_time = datetime.utcnow()


@router.get("/status")
async def get_system_status() -> Dict[str, Any]:
    """Get overall system status
    
    Simplified system status endpoint for monitoring purposes.
    No longer bound by API contract since frontend uses /api/control/status instead.
    """
    try:
        # Import here to avoid circular dependency
        from .control import _active_sessions
        
        # Calculate system uptime
        uptime = (datetime.utcnow() - _system_start_time).total_seconds()
        
        # Check if we have any active sessions
        main_session = _active_sessions.get("main")
        
        if not main_session:
            return {
                "system_status": "idle",
                "message": "System ready, no active stream",
                "uptime_seconds": int(uptime),
                "active_sessions": 0,
                "timestamp": datetime.utcnow().isoformat()
            }
        
        # Get basic status from main session
        is_running = main_session.get("is_running", False)
        
        # Simplified response for monitoring
        response = {
            "system_status": "active" if is_running else "paused",
            "uptime_seconds": int(uptime),
            "active_sessions": len(_active_sessions),
            "main_session_running": is_running,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.debug(f"System status retrieved: {response['system_status']}")
        return response
        
    except Exception as e:
        logger.error(f"Failed to get system status: {str(e)}")
        
        # Return simplified error status
        return {
            "system_status": "error",
            "message": f"Status check failed: {str(e)}",
            "uptime_seconds": int((datetime.utcnow() - _system_start_time).total_seconds()),
            "active_sessions": 0,
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """Simple health check endpoint"""
    try:
        from .control import _active_sessions
        
        return {
            "status": "healthy",
            "service": "system_status",
            "active_sessions": len(_active_sessions),
            "uptime_seconds": int((datetime.utcnow() - _system_start_time).total_seconds()),
            "checked_at": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "system_status",
            "error": str(e),
            "checked_at": datetime.utcnow().isoformat()
        }