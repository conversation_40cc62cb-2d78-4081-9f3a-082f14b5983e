"""Knowledge base data processing pipeline

Handles extraction from various document formats, text processing,
embedding generation, and loading into Elasticsearch indices.
"""

import os
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path
import hashlib
import mimetypes
from loguru import logger

# Document processing imports
import PyPDF2
import docx
import markdown
from bs4 import BeautifulSoup

from .base import ETLBase, ETLTask, ETLTaskResult, PipelineStatus, create_etl_flow
from ...services.search import ElasticsearchService
from ...utils.index_manager import IndexManager
from ...core.exceptions import ServiceError, ValidationError
from ...core.config import cfg


class DocumentProcessor:
    """Handles different document format processing"""
    
    @staticmethod
    async def extract_text_from_pdf(file_path: Path) -> str:
        """Extract text from PDF file
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Extracted text content
        """
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_content = []
                
                for page in pdf_reader.pages:
                    text_content.append(page.extract_text())
                
                return '\n'.join(text_content)
                
        except Exception as e:
            raise ServiceError(
                f"Failed to extract text from PDF {file_path}: {str(e)}",
                "document_processor",
                "PDF_EXTRACTION_ERROR"
            )
    
    @staticmethod
    async def extract_text_from_docx(file_path: Path) -> str:
        """Extract text from DOCX file
        
        Args:
            file_path: Path to DOCX file
            
        Returns:
            Extracted text content
        """
        try:
            doc = docx.Document(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n'.join(text_content)
            
        except Exception as e:
            raise ServiceError(
                f"Failed to extract text from DOCX {file_path}: {str(e)}",
                "document_processor", 
                "DOCX_EXTRACTION_ERROR"
            )
    
    @staticmethod
    async def extract_text_from_markdown(file_path: Path) -> str:
        """Extract text from Markdown file
        
        Args:
            file_path: Path to Markdown file
            
        Returns:
            Extracted text content
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                md_content = file.read()
            
            # Convert markdown to HTML then extract text
            html = markdown.markdown(md_content)
            soup = BeautifulSoup(html, 'html.parser')
            
            return soup.get_text(separator='\n', strip=True)
            
        except Exception as e:
            raise ServiceError(
                f"Failed to extract text from Markdown {file_path}: {str(e)}",
                "document_processor",
                "MARKDOWN_EXTRACTION_ERROR"
            )
    
    @staticmethod
    async def extract_text_from_txt(file_path: Path) -> str:
        """Extract text from plain text file
        
        Args:
            file_path: Path to text file
            
        Returns:
            File content
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
                
        except Exception as e:
            raise ServiceError(
                f"Failed to read text file {file_path}: {str(e)}",
                "document_processor",
                "TEXT_EXTRACTION_ERROR"
            )


class TextChunker:
    """Handles text chunking with overlap for better context preservation"""
    
    def __init__(
        self,
        chunk_size: int = 1000,
        overlap_size: int = 200,
        min_chunk_size: int = 100
    ) -> None:
        """Initialize text chunker
        
        Args:
            chunk_size: Maximum characters per chunk
            overlap_size: Overlap between chunks
            min_chunk_size: Minimum chunk size to keep
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size
        self.min_chunk_size = min_chunk_size
    
    async def chunk_text(self, text: str, document_metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Split text into overlapping chunks
        
        Args:
            text: Text to chunk
            document_metadata: Metadata to include with each chunk
            
        Returns:
            List of text chunks with metadata
        """
        if not text or len(text) < self.min_chunk_size:
            return []
        
        chunks = []
        start = 0
        chunk_index = 0
        
        while start < len(text):
            # Calculate chunk end position
            end = min(start + self.chunk_size, len(text))
            
            # Try to break at sentence boundary
            if end < len(text):
                # Look for sentence endings within the last 200 characters
                search_start = max(end - 200, start)
                sentence_endings = ['.', '!', '?', '。', '！', '？']
                
                best_break = end
                for i in range(end - 1, search_start - 1, -1):
                    if text[i] in sentence_endings and i + 1 < len(text) and text[i + 1].isspace():
                        best_break = i + 1
                        break
                
                end = best_break
            
            # Extract chunk text
            chunk_text = text[start:end].strip()
            
            if len(chunk_text) >= self.min_chunk_size:
                chunk_data = {
                    "chunk_text": chunk_text,
                    "chunk_index": chunk_index,
                    "char_start": start,
                    "char_end": end,
                    "chunk_length": len(chunk_text),
                    **document_metadata
                }
                chunks.append(chunk_data)
                chunk_index += 1
            
            # Move to next chunk with overlap
            if end >= len(text):
                break
                
            start = max(end - self.overlap_size, start + 1)
        
        logger.info(f"Created {len(chunks)} chunks from {len(text)} characters")
        return chunks


class KnowledgeBaseProcessor(ETLBase):
    """Knowledge base ETL processor for document ingestion"""
    
    def __init__(self) -> None:
        """Initialize knowledge base processor"""
        super().__init__("knowledge_base_processor")
        self.document_processor = DocumentProcessor()
        self.text_chunker = TextChunker()
        self.es_service: Optional[ElasticsearchService] = None
        self.index_manager: Optional[IndexManager] = None
        
        # Supported file types
        self.supported_extensions = {
            '.pdf': self.document_processor.extract_text_from_pdf,
            '.docx': self.document_processor.extract_text_from_docx,
            '.doc': self.document_processor.extract_text_from_docx,
            '.md': self.document_processor.extract_text_from_markdown,
            '.txt': self.document_processor.extract_text_from_txt,
        }
    
    async def initialize_services(self) -> None:
        """Initialize Elasticsearch services"""
        if not self.es_service:
            self.es_service = ElasticsearchService()
            await self.es_service.initialize()
            
        if not self.index_manager:
            self.index_manager = IndexManager(self.es_service)
    
    async def extract(self, source_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract documents from source directory
        
        Args:
            source_config: Configuration with 'input_directory', 'document_types', etc.
            
        Returns:
            List of extracted document data
        """
        input_directory = Path(source_config.get("input_directory", "data/documents"))
        document_types = source_config.get("document_types", ["spec", "faq", "policy", "story"])
        recursive = source_config.get("recursive", True)
        
        if not input_directory.exists():
            raise ServiceError(
                f"Input directory does not exist: {input_directory}",
                "knowledge_processor",
                "DIRECTORY_NOT_FOUND"
            )
        
        extracted_docs = []
        
        # Scan for supported files
        if recursive:
            file_pattern = "**/*"
        else:
            file_pattern = "*"
        
        for file_path in input_directory.glob(file_pattern):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                try:
                    doc_data = await self._extract_single_document(file_path, document_types)
                    if doc_data:
                        extracted_docs.append(doc_data)
                        
                except Exception as e:
                    self.logger.error(f"Failed to extract {file_path}: {str(e)}")
                    continue
        
        self.logger.info(f"Extracted {len(extracted_docs)} documents from {input_directory}")
        return extracted_docs
    
    async def _extract_single_document(
        self, 
        file_path: Path, 
        document_types: List[str]
    ) -> Optional[Dict[str, Any]]:
        """Extract single document
        
        Args:
            file_path: Path to document file
            document_types: List of valid document types
            
        Returns:
            Document data or None if extraction fails
        """
        file_extension = file_path.suffix.lower()
        extractor = self.supported_extensions.get(file_extension)
        
        if not extractor:
            self.logger.warning(f"Unsupported file type: {file_extension}")
            return None
        
        try:
            # Extract text content
            text_content = await extractor(file_path)
            
            if not text_content or len(text_content.strip()) < 50:
                self.logger.warning(f"Insufficient content in {file_path}")
                return None
            
            # Generate document metadata
            file_stats = file_path.stat()
            doc_metadata = {
                "file_path": str(file_path),
                "file_name": file_path.name,
                "file_size": file_stats.st_size,
                "file_modified": datetime.fromtimestamp(file_stats.st_mtime),
                "file_type": file_extension,
                "content_hash": hashlib.md5(text_content.encode()).hexdigest(),
                "original_length": len(text_content),
            }
            
            # Infer document type from path or filename
            doc_type = self._infer_document_type(file_path, document_types)
            
            # Infer SKU and brand from path/filename if possible
            sku, brand = self._infer_sku_brand(file_path)
            
            return {
                "text_content": text_content,
                "doc_type": doc_type,
                "sku": sku,
                "brand": brand,
                "metadata": doc_metadata,
                "extracted_at": datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to extract {file_path}: {str(e)}")
            return None
    
    def _infer_document_type(self, file_path: Path, valid_types: List[str]) -> str:
        """Infer document type from file path
        
        Args:
            file_path: Path to document
            valid_types: List of valid document types
            
        Returns:
            Inferred document type
        """
        path_lower = str(file_path).lower()
        
        # Check for type keywords in path
        for doc_type in valid_types:
            if doc_type in path_lower:
                return doc_type
        
        # Check for common patterns
        if any(keyword in path_lower for keyword in ['faq', 'question', '问答']):
            return 'faq'
        elif any(keyword in path_lower for keyword in ['spec', 'specification', '规格']):
            return 'spec'
        elif any(keyword in path_lower for keyword in ['policy', 'rule', '政策', '规则']):
            return 'policy'
        elif any(keyword in path_lower for keyword in ['story', 'case', '案例', '故事']):
            return 'story'
        
        # Default to 'spec'
        return 'spec'
    
    def _infer_sku_brand(self, file_path: Path) -> tuple[Optional[str], Optional[str]]:
        """Infer SKU and brand from file path
        
        Args:
            file_path: Path to document
            
        Returns:
            Tuple of (sku, brand) or (None, None)
        """
        path_parts = file_path.parts
        file_name = file_path.stem
        
        # Simple heuristics - can be enhanced based on naming conventions
        sku = None
        brand = None
        
        # Look for SKU pattern (alphanumeric, often with numbers)
        import re
        sku_pattern = r'[A-Z0-9]{3,}[-_]?[A-Z0-9]*'
        sku_match = re.search(sku_pattern, file_name.upper())
        if sku_match:
            sku = sku_match.group()
        
        # Look for brand in path (typically in directory structure)
        if len(path_parts) > 1:
            # Check if any path part looks like a brand name
            for part in path_parts[:-1]:  # Exclude filename
                if part and len(part) > 2 and part.isalpha():
                    brand = part
                    break
        
        return sku, brand
    
    async def transform(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform extracted documents into chunks with embeddings
        
        Args:
            data: List of extracted document data
            
        Returns:
            List of processed chunks ready for indexing
        """
        await self.initialize_services()
        
        processed_chunks = []
        
        for doc_data in data:
            try:
                # Create document metadata for chunks
                chunk_metadata = {
                    "doc_type": doc_data["doc_type"],
                    "sku": doc_data["sku"] or "UNKNOWN",
                    "brand": doc_data["brand"] or "UNKNOWN", 
                    "title": doc_data["metadata"]["file_name"],
                    "version": 1,
                    "created_at": doc_data["extracted_at"].isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "source_metadata": doc_data["metadata"]
                }
                
                # Chunk the document text
                chunks = await self.text_chunker.chunk_text(
                    doc_data["text_content"],
                    chunk_metadata
                )
                
                # Generate embeddings and finalize chunks
                for chunk in chunks:
                    try:
                        # Generate embedding for chunk text
                        embedding = self.es_service.generate_embedding(chunk["chunk_text"])
                        
                        # Create final chunk document
                        chunk_doc = {
                            "_id": f"{doc_data['metadata']['content_hash']}_{chunk['chunk_index']}",
                            "doc_id": f"{doc_data['metadata']['content_hash']}_{chunk['chunk_index']}",
                            "type": chunk["doc_type"],
                            "sku": chunk["sku"],
                            "brand": chunk["brand"],
                            "title": chunk["title"],
                            "chunk_text": chunk["chunk_text"],
                            "embedding": embedding,
                            "metadata": {
                                "chunk_index": chunk["chunk_index"],
                                "char_start": chunk["char_start"],
                                "char_end": chunk["char_end"],
                                "chunk_length": chunk["chunk_length"],
                                "source_file": chunk_metadata["source_metadata"]["file_path"]
                            },
                            "created_at": chunk["created_at"],
                            "updated_at": chunk["updated_at"],
                            "version": chunk["version"]
                        }
                        
                        processed_chunks.append(chunk_doc)
                        
                    except Exception as e:
                        self.logger.error(f"Failed to process chunk {chunk['chunk_index']}: {str(e)}")
                        continue
                
            except Exception as e:
                self.logger.error(f"Failed to transform document: {str(e)}")
                continue
        
        self.logger.info(f"Transformed {len(data)} documents into {len(processed_chunks)} chunks")
        return processed_chunks
    
    async def load(self, data: List[Dict[str, Any]], target_config: Dict[str, Any]) -> int:
        """Load processed chunks into Elasticsearch
        
        Args:
            data: List of processed chunks
            target_config: Target configuration with index settings
            
        Returns:
            Number of successfully loaded chunks
        """
        await self.initialize_services()
        
        index_version = target_config.get("index_version", None)
        create_new_version = target_config.get("create_new_version", True)
        
        # Create new index version if requested
        if create_new_version:
            if index_version is None:
                current_version = await self.index_manager.get_current_version("knowledge_base")
                index_version = (current_version or 0) + 1
            
            index_name = await self.index_manager.create_versioned_index("knowledge_base", index_version)
            self.logger.info(f"Created new knowledge base index: {index_name}")
        else:
            # Use existing active index
            index_name = self.index_manager._get_alias_name("knowledge_base")
        
        # Bulk load documents
        if data:
            loaded_count = await self.es_service.bulk_index_documents(index_name, data)
            
            # Switch alias to new index if we created a new version
            if create_new_version and loaded_count > 0:
                await self.index_manager.switch_alias_to_version("knowledge_base", index_version)
                self.logger.info(f"Switched knowledge base alias to version {index_version}")
            
            return loaded_count
        
        return 0
    
    async def _validate_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Validate individual chunk item
        
        Args:
            item: Chunk data to validate
            
        Returns:
            Validated item
            
        Raises:
            ValidationError: If validation fails
        """
        required_fields = ["doc_id", "type", "sku", "brand", "chunk_text", "embedding"]
        
        for field in required_fields:
            if field not in item:
                raise ValidationError(f"Missing required field: {field}")
            
            if field == "chunk_text" and len(item[field].strip()) < 10:
                raise ValidationError("Chunk text too short")
            
            if field == "embedding" and (not isinstance(item[field], list) or len(item[field]) == 0):
                raise ValidationError("Invalid embedding format")
        
        return item


# Prefect ETL tasks and flows

@ETLTask("extract_knowledge_documents", retries=2, timeout_seconds=1800)
async def extract_knowledge_documents(source_config: Dict[str, Any]) -> ETLTaskResult:
    """Extract documents from source directory"""
    processor = KnowledgeBaseProcessor()
    started_at = datetime.utcnow()
    result = processor.create_result("extract_knowledge_documents", started_at)
    
    try:
        documents = await processor.extract(source_config)
        
        result.processed_count = len(documents)
        result.input_data_size = sum(len(doc.get("text_content", "")) for doc in documents)
        result.metadata["source_directory"] = source_config.get("input_directory")
        result.metadata["documents"] = documents
        
        return result
        
    except Exception as e:
        result.status = PipelineStatus.FAILED
        result.error_count = 1
        result.errors.append({
            "error": str(e),
            "component": "document_extraction"
        })
        raise


@ETLTask("transform_knowledge_documents", retries=1, timeout_seconds=3600)
async def transform_knowledge_documents(extract_result: ETLTaskResult) -> ETLTaskResult:
    """Transform documents into searchable chunks"""
    processor = KnowledgeBaseProcessor()
    started_at = datetime.utcnow()
    result = processor.create_result("transform_knowledge_documents", started_at)
    
    try:
        documents = extract_result.metadata.get("documents", [])
        chunks = await processor.transform(documents)
        
        result.processed_count = len(chunks)
        result.input_data_size = extract_result.output_data_size or extract_result.input_data_size
        result.output_data_size = sum(len(chunk.get("chunk_text", "")) for chunk in chunks)
        result.metadata["chunks"] = chunks
        
        return result
        
    except Exception as e:
        result.status = PipelineStatus.FAILED
        result.error_count = 1
        result.errors.append({
            "error": str(e),
            "component": "document_transformation"
        })
        raise


@ETLTask("load_knowledge_chunks", retries=2, timeout_seconds=1800)
async def load_knowledge_chunks(transform_result: ETLTaskResult, target_config: Dict[str, Any]) -> ETLTaskResult:
    """Load processed chunks into Elasticsearch"""
    processor = KnowledgeBaseProcessor()
    started_at = datetime.utcnow()
    result = processor.create_result("load_knowledge_chunks", started_at)
    
    try:
        chunks = transform_result.metadata.get("chunks", [])
        loaded_count = await processor.load(chunks, target_config)
        
        result.processed_count = loaded_count
        result.input_data_size = transform_result.output_data_size
        result.metadata["loaded_chunks"] = loaded_count
        result.metadata["target_index"] = target_config.get("index_version")
        
        return result
        
    except Exception as e:
        result.status = PipelineStatus.FAILED
        result.error_count = 1
        result.errors.append({
            "error": str(e),
            "component": "chunk_loading"
        })
        raise


@create_etl_flow(
    name="knowledge_base_etl_pipeline",
    description="Complete ETL pipeline for knowledge base documents",
    timeout_minutes=120
)
async def knowledge_base_etl_pipeline(
    source_config: Dict[str, Any],
    target_config: Dict[str, Any]
) -> Dict[str, ETLTaskResult]:
    """Complete knowledge base ETL pipeline
    
    Args:
        source_config: Source configuration for document extraction
        target_config: Target configuration for Elasticsearch loading
        
    Returns:
        Dictionary of task results
    """
    # Execute ETL pipeline
    extract_result = await extract_knowledge_documents(source_config)
    transform_result = await transform_knowledge_documents(extract_result)
    load_result = await load_knowledge_chunks(transform_result, target_config)
    
    return {
        "extract": extract_result,
        "transform": transform_result,
        "load": load_result
    }