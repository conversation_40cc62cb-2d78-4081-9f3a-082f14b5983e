/**
 * 音频播放器配置文件
 * 可根据网络环境调整参数
 */
const AUDIO_PLAYER_CONFIG = {
    // 生产环境配置
    production: {
        // 缓冲泵循环参数
        PUMP_INTERVAL_MS: 200,        // 泵循环检查间隔（毫秒）
        BUFFER_LOW_WATERMARK: 3,      // 网络缓冲低水位线（触发请求）
        BUFFER_HIGH_WATERMARK: 10,    // 网络缓冲高水位线（暂停请求）
        MAX_NETWORK_BUFFER_SIZE: 20,  // 最大网络缓冲大小
        AUDIO_FEED_THRESHOLD: 2,      // AudioPlayer缓冲阈值
        
        // AudioPlayer参数
        MAX_CONCURRENT_SOURCES: 3,    // 最大并发音频源数量
        MIN_BUFFER_QUEUE_SIZE: 2,     // 最小缓冲队列大小
        
        // 音频默认参数
        DEFAULT_SAMPLE_RATE: 24000,   // 默认采样率
        DEFAULT_CHANNELS: 1,          // 默认声道数
        DEFAULT_BIT_DEPTH: 16,        // 默认位深度
        
        // 连接参数
        RECONNECT_INTERVAL: 3000,     // 重连间隔（毫秒）
        MAX_RECONNECT_ATTEMPTS: 5,    // 最大重连次数
        HEARTBEAT_INTERVAL: 30000,    // 心跳间隔（毫秒）
        
        // 初始请求延迟
        INITIAL_REQUEST_DELAY: 500    // 连接后首次请求延迟（毫秒）
    },
    
    // 慢速网络配置（3G）
    slow3G: {
        PUMP_INTERVAL_MS: 150,        // 更频繁检查
        BUFFER_LOW_WATERMARK: 5,      // 更高的低水位线
        BUFFER_HIGH_WATERMARK: 15,    // 更高的高水位线
        MAX_NETWORK_BUFFER_SIZE: 30,  // 更大的缓冲
        AUDIO_FEED_THRESHOLD: 3,      // 更保守的播放阈值
        MAX_CONCURRENT_SOURCES: 2,    // 减少并发
        MIN_BUFFER_QUEUE_SIZE: 3,     // 更大的最小缓冲
        INITIAL_REQUEST_DELAY: 1000   // 更长的初始延迟
    },
    
    // 快速网络配置（WiFi/4G）
    fast: {
        PUMP_INTERVAL_MS: 250,        // 较低频率检查
        BUFFER_LOW_WATERMARK: 2,      // 较低的低水位线
        BUFFER_HIGH_WATERMARK: 8,     // 较低的高水位线
        MAX_NETWORK_BUFFER_SIZE: 15,  // 较小的缓冲
        AUDIO_FEED_THRESHOLD: 1,      // 激进的播放阈值
        MAX_CONCURRENT_SOURCES: 4,    // 更多并发
        MIN_BUFFER_QUEUE_SIZE: 1,     // 最小缓冲
        INITIAL_REQUEST_DELAY: 300    // 较短的初始延迟
    },
    
    // 开发/调试配置
    development: {
        PUMP_INTERVAL_MS: 100,        // 高频检查便于调试
        BUFFER_LOW_WATERMARK: 2,      
        BUFFER_HIGH_WATERMARK: 5,     
        MAX_NETWORK_BUFFER_SIZE: 10,  
        AUDIO_FEED_THRESHOLD: 1,      
        MAX_CONCURRENT_SOURCES: 2,    
        MIN_BUFFER_QUEUE_SIZE: 1,
        INITIAL_REQUEST_DELAY: 200,
        
        // 调试特有
        ENABLE_VERBOSE_LOGGING: true, // 详细日志
        LOG_BUFFER_STATS: true,       // 记录缓冲统计
        LOG_INTERVAL_MS: 5000         // 统计日志间隔
    }
};

/**
 * 获取当前配置
 * @param {string} profile - 配置档案名称
 * @returns {object} 配置对象
 */
function getAudioConfig(profile = 'production') {
    const config = AUDIO_PLAYER_CONFIG[profile] || AUDIO_PLAYER_CONFIG.production;
    
    // 可以根据网络状况动态调整
    if (typeof navigator !== 'undefined' && navigator.connection) {
        const connection = navigator.connection;
        
        // 根据网络类型自动选择配置
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
            return { ...AUDIO_PLAYER_CONFIG.slow3G, AUTO_SELECTED: true };
        } else if (connection.effectiveType === '3g') {
            return { ...AUDIO_PLAYER_CONFIG.slow3G, AUTO_SELECTED: true };
        } else if (connection.effectiveType === '4g') {
            return { ...AUDIO_PLAYER_CONFIG.fast, AUTO_SELECTED: true };
        }
    }
    
    return config;
}

/**
 * 网络质量监测器
 */
class NetworkQualityMonitor {
    constructor() {
        this.samples = [];
        this.maxSamples = 10;
    }
    
    /**
     * 添加延迟样本
     */
    addLatencySample(latencyMs) {
        this.samples.push(latencyMs);
        if (this.samples.length > this.maxSamples) {
            this.samples.shift();
        }
    }
    
    /**
     * 获取平均延迟
     */
    getAverageLatency() {
        if (this.samples.length === 0) return 0;
        const sum = this.samples.reduce((a, b) => a + b, 0);
        return sum / this.samples.length;
    }
    
    /**
     * 推荐配置档案
     */
    recommendProfile() {
        const avgLatency = this.getAverageLatency();
        
        if (avgLatency > 500) {
            return 'slow3G';
        } else if (avgLatency > 200) {
            return 'production';
        } else {
            return 'fast';
        }
    }
}

// 导出
window.AUDIO_PLAYER_CONFIG = AUDIO_PLAYER_CONFIG;
window.getAudioConfig = getAudioConfig;
window.NetworkQualityMonitor = NetworkQualityMonitor;