"""简化的资源管理

符合新架构设计原则：
- 单一职责：专注于基本资源跟踪
- 简单并发：最小化状态管理
- 明确控制流：清晰的资源管理逻辑

Author: Claude Code  
Date: 2025-08-05
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from loguru import logger


class SimpleTaskManager:
    """简化的任务管理器（兼容性）"""
    
    def __init__(self, parent_tracker):
        self._parent = parent_tracker
    
    async def register_resource(self, task, resource_id: str, task_type: Optional[str] = None, 
                               session_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
        """注册任务资源"""
        # 简化实现：只记录资源ID，不管理实际的task对象
        combined_metadata = {
            "task_name": getattr(task, '__name__', str(task)),
            **(metadata or {})
        }
        if session_id:
            combined_metadata["session_id"] = session_id
            
        self._parent.register_resource(resource_id, task_type or "task", combined_metadata)
    
    async def release_resource(self, resource_id: str):
        """释放任务资源"""
        return self._parent.release_resource(resource_id)


class SimpleResourceTracker:
    """简化的资源跟踪器
    
    职责：
    1. 基本的资源注册和跟踪
    2. 简单的资源清理
    3. 资源使用统计
    """
    
    def __init__(self):
        self.resources: Dict[str, Dict[str, Any]] = {}
        self.stats = {
            "total_registered": 0,
            "total_released": 0,
            "current_active": 0
        }
        # 添加兼容性属性
        self.task_manager = SimpleTaskManager(self)
        logger.debug("🗂️ SimpleResourceTracker initialized")
    
    def register_resource(self, resource_id: str, resource_type: str, metadata: Optional[Dict[str, Any]] = None):
        """注册资源"""
        self.resources[resource_id] = {
            "type": resource_type,
            "created_at": datetime.utcnow(),
            "metadata": metadata or {}
        }
        self.stats["total_registered"] += 1
        self.stats["current_active"] += 1
        logger.debug(f"📝 Resource registered: {resource_id} ({resource_type})")
    
    def release_resource(self, resource_id: str) -> bool:
        """释放资源"""
        if resource_id in self.resources:
            del self.resources[resource_id]
            self.stats["total_released"] += 1
            self.stats["current_active"] -= 1
            logger.debug(f"🗑️ Resource released: {resource_id}")
            return True
        return False
    
    def get_active_resources(self) -> Dict[str, Dict[str, Any]]:
        """获取活跃资源"""
        return self.resources.copy()
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        return self.stats.copy()
    
    def cleanup_all(self):
        """清理所有资源"""
        count = len(self.resources)
        self.resources.clear()
        self.stats["total_released"] += count
        self.stats["current_active"] = 0
        logger.info(f"🧹 Cleaned up {count} resources")


# 全局实例
_resource_tracker: Optional[SimpleResourceTracker] = None


def get_resource_manager() -> SimpleResourceTracker:
    """获取全局资源跟踪器实例（保持接口兼容性）"""
    global _resource_tracker
    if _resource_tracker is None:
        _resource_tracker = SimpleResourceTracker()
    return _resource_tracker