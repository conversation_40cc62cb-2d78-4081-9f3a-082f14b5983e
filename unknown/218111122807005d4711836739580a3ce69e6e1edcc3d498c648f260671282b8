"""会话Token管理器

提供安全的会话Token生成和验证，作为官方临时Token的替代方案。
实现60秒有效期、HMAC签名防伪造等安全特性。
"""

import time
import uuid
import hmac
import hashlib
import secrets
import asyncio
from typing import Dict, Optional, Any
from loguru import logger

from ...core.exceptions import ServiceError


class SessionTokenManager:
    """会话Token管理器
    
    安全特性：
    - 60秒严格有效期
    - HMAC-SHA256签名防伪造
    - 自动清理过期Token
    - 线程安全操作
    """
    
    def __init__(self, api_key: str):
        """初始化会话Token管理器
        
        Args:
            api_key: CosyVoice API密钥，用于最终的WebSocket连接
        """
        if not api_key:
            raise ValueError("API key cannot be empty")
            
        self.api_key = api_key
        self.sessions: Dict[str, dict] = {}
        self.session_lock = asyncio.Lock()
        
        # 生成用于签名的密钥（每次启动随机生成）
        self.signing_key = secrets.token_bytes(32)
        
        # Token配置
        self.token_duration = 60  # 60秒有效期，模拟官方临时Token
        self.cleanup_interval = 30  # 30秒清理一次过期Token
        
        # 启动后台清理任务
        self.cleanup_task = None
        self._start_cleanup_task()
        
        logger.info("Session Token Manager initialized")
    
    def _start_cleanup_task(self):
        """启动后台Token清理任务"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(self.cleanup_interval)
                    await self._cleanup_expired_sessions()
                except Exception as e:
                    logger.error(f"Session cleanup error: {e}")
        
        self.cleanup_task = asyncio.create_task(cleanup_loop())
    
    async def create_session_token(self, client_id: Optional[str] = None) -> str:
        """创建新的会话Token
        
        Args:
            client_id: 可选的客户端标识
            
        Returns:
            会话Token字符串
        """
        async with self.session_lock:
            # 生成会话信息
            session_id = str(uuid.uuid4())
            current_time = time.time()
            expires_at = current_time + self.token_duration
            
            # 构造Token载荷
            payload = f"{session_id}:{int(expires_at)}"
            
            # 生成HMAC签名
            signature = hmac.new(
                self.signing_key,
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()[:16]  # 取前16位作为签名
            
            # 完整Token格式：session_id:expires_at:signature
            session_token = f"{payload}:{signature}"
            
            # 存储会话信息
            self.sessions[session_id] = {
                "session_token": session_token,
                "client_id": client_id,
                "created_at": current_time,
                "expires_at": expires_at,
                "api_key": self.api_key
            }
            
            logger.debug(f"Created session token for client: {client_id}, expires at: {expires_at}")
            return session_token
    
    async def validate_session_token(self, session_token: str) -> Optional[Dict[str, Any]]:
        """验证会话Token并返回会话信息
        
        Args:
            session_token: 要验证的会话Token
            
        Returns:
            会话信息字典，如果Token无效则返回None
        """
        async with self.session_lock:
            try:
                # 解析Token格式：session_id:expires_at:signature
                parts = session_token.split(':')
                if len(parts) != 3:
                    logger.warning("Invalid session token format")
                    return None
                
                session_id, expires_at_str, provided_signature = parts
                expires_at = int(expires_at_str)
                
                # 检查会话是否存在
                if session_id not in self.sessions:
                    logger.warning(f"Session not found: {session_id}")
                    return None
                
                session_data = self.sessions[session_id]
                
                # 验证Token是否匹配
                if session_data["session_token"] != session_token:
                    logger.warning(f"Session token mismatch for: {session_id}")
                    return None
                
                # 检查是否过期
                current_time = time.time()
                if current_time > expires_at:
                    logger.info(f"Session token expired: {session_id}")
                    # 移除过期的会话
                    del self.sessions[session_id]
                    return None
                
                # 验证签名
                expected_payload = f"{session_id}:{expires_at}"
                expected_signature = hmac.new(
                    self.signing_key,
                    expected_payload.encode('utf-8'),
                    hashlib.sha256
                ).hexdigest()[:16]
                
                if not hmac.compare_digest(provided_signature, expected_signature):
                    logger.warning(f"Invalid signature for session: {session_id}")
                    return None
                
                logger.debug(f"Session token validated successfully: {session_id}")
                return session_data
                
            except (ValueError, IndexError) as e:
                logger.warning(f"Failed to parse session token: {e}")
                return None
    
    async def get_api_key_for_session(self, session_token: str) -> Optional[str]:
        """根据会话Token获取API Key
        
        Args:
            session_token: 会话Token
            
        Returns:
            API Key字符串，如果Token无效则返回None
        """
        session_data = await self.validate_session_token(session_token)
        if session_data:
            return session_data["api_key"]
        return None
    
    async def revoke_session_token(self, session_token: str) -> bool:
        """撤销会话Token
        
        Args:
            session_token: 要撤销的会话Token
            
        Returns:
            是否成功撤销
        """
        async with self.session_lock:
            try:
                session_id = session_token.split(':')[0]
                if session_id in self.sessions:
                    del self.sessions[session_id]
                    logger.info(f"Session token revoked: {session_id}")
                    return True
            except (ValueError, IndexError):
                pass
            
            return False
    
    async def _cleanup_expired_sessions(self):
        """清理过期的会话Token"""
        async with self.session_lock:
            current_time = time.time()
            expired_sessions = []
            
            for session_id, session_data in self.sessions.items():
                if current_time > session_data["expires_at"]:
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                del self.sessions[session_id]
            
            if expired_sessions:
                logger.debug(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息
        
        Returns:
            包含会话统计的字典
        """
        current_time = time.time()
        active_sessions = 0
        
        for session_data in self.sessions.values():
            if current_time <= session_data["expires_at"]:
                active_sessions += 1
        
        return {
            "total_sessions": len(self.sessions),
            "active_sessions": active_sessions,
            "token_duration": self.token_duration,
            "cleanup_interval": self.cleanup_interval
        }
    
    async def cleanup(self):
        """清理资源"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        async with self.session_lock:
            self.sessions.clear()
        
        logger.info("Session Token Manager cleaned up")


# 全局会话Token管理器实例
_session_token_manager: Optional[SessionTokenManager] = None


def get_session_token_manager() -> SessionTokenManager:
    """获取全局会话Token管理器实例
    
    Returns:
        SessionTokenManager实例
        
    Raises:
        ServiceError: 初始化失败
    """
    global _session_token_manager
    
    if _session_token_manager is None:
        try:
            import os
            
            # 从环境变量读取API Key
            api_key = os.getenv('COSYVOICE_V2_API_KEY')
            
            if not api_key:
                raise ServiceError(
                    "COSYVOICE_V2_API_KEY environment variable not set", 
                    "session_token_manager", 
                    "CONFIG_ERROR"
                )
            
            # 验证API Key格式
            if not api_key.startswith('sk-'):
                raise ServiceError(
                    f"Invalid CosyVoice API key format: '{api_key[:10]}...'. API key must start with 'sk-'", 
                    "session_token_manager", 
                    "INVALID_API_KEY_FORMAT"
                )
            
            _session_token_manager = SessionTokenManager(api_key)
            logger.info("Global Session Token Manager created")
            
        except Exception as e:
            if isinstance(e, ServiceError):
                raise
            
            raise ServiceError(
                f"Failed to initialize session token manager: {str(e)}", 
                "session_token_manager", 
                "INIT_ERROR"
            )
    
    return _session_token_manager


async def cleanup_session_token_manager():
    """清理全局会话Token管理器"""
    global _session_token_manager
    
    if _session_token_manager:
        await _session_token_manager.cleanup()
        _session_token_manager = None
        logger.info("Session token manager cleaned up")