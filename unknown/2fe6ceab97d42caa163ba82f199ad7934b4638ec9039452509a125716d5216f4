#!/usr/bin/env python3
"""
Database Migration Utility

Handles database schema migrations and version management for the AI Live Streamer application.
This utility provides safe migration capabilities with backup and rollback support.
"""

import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from loguru import logger

from ..services.persistence import DatabaseManager
from ..core.exceptions import ServiceError, ConfigError


class DatabaseMigration:
    """Database migration utility with version management"""
    
    # Migration definitions
    MIGRATIONS = {
        "1.0": {
            "description": "Initial schema with operational forms and sessions",
            "tables": ["operational_forms", "form_sections", "form_sessions", "schema_version"]
        },
        "1.1": {
            "description": "Add script previews persistence",
            "tables": ["script_previews"],
            "sql": [
                """
                CREATE TABLE IF NOT EXISTS script_previews (
                    form_id TEXT PRIMARY KEY,
                    preview_data TEXT NOT NULL,
                    preview_html TEXT NOT NULL,
                    estimated_metrics TEXT NULL,
                    warnings TEXT NULL,
                    generation_time_seconds REAL DEFAULT 0.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (form_id) REFERENCES operational_forms(id) ON DELETE CASCADE
                )
                """,
                "CREATE INDEX IF NOT EXISTS idx_script_previews_created_at ON script_previews(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_script_previews_updated_at ON script_previews(updated_at)"
            ]
        }
    }
    
    def __init__(self, db_path: Optional[str] = None):
        """Initialize database migration utility
        
        Args:
            db_path: Path to database file, uses default if None
        """
        self.db_manager = DatabaseManager(db_path)
        self.db_path = self.db_manager.db_path
        logger.info(f"Database migration utility initialized for: {self.db_path}")
    
    def get_current_version(self) -> Optional[str]:
        """Get current database schema version
        
        Returns:
            Current schema version or None if not set
        """
        try:
            return self.db_manager.get_schema_version()
        except Exception as e:
            logger.error(f"Failed to get current version: {e}")
            return None
    
    def get_available_versions(self) -> List[str]:
        """Get list of available migration versions
        
        Returns:
            Sorted list of available versions
        """
        versions = list(self.MIGRATIONS.keys())
        versions.sort(key=lambda x: tuple(map(int, x.split('.'))))
        return versions
    
    def create_backup(self) -> str:
        """Create database backup before migration
        
        Returns:
            Path to backup file
            
        Raises:
            ServiceError: If backup creation fails
        """
        if not os.path.exists(self.db_path):
            raise ServiceError(f"Database file not found: {self.db_path}")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{self.db_path}.backup_{timestamp}"
        
        try:
            shutil.copy2(self.db_path, backup_path)
            logger.info(f"Database backup created: {backup_path}")
            return backup_path
        except Exception as e:
            raise ServiceError(f"Failed to create backup: {e}")
    
    def validate_migration_path(self, target_version: str) -> Tuple[bool, List[str], str]:
        """Validate migration path from current to target version
        
        Args:
            target_version: Target schema version
            
        Returns:
            Tuple of (is_valid, migration_steps, error_message)
        """
        current_version = self.get_current_version()
        available_versions = self.get_available_versions()
        
        if target_version not in available_versions:
            return False, [], f"Target version {target_version} not available"
        
        if current_version == target_version:
            return True, [], "Already at target version"
        
        if not current_version:
            # Fresh database, need to apply all migrations up to target
            target_index = available_versions.index(target_version)
            migration_steps = available_versions[:target_index + 1]
            return True, migration_steps, ""
        
        # Find migration path
        current_index = available_versions.index(current_version) if current_version in available_versions else -1
        target_index = available_versions.index(target_version)
        
        if current_index >= target_index:
            return False, [], f"Cannot downgrade from {current_version} to {target_version}"
        
        migration_steps = available_versions[current_index + 1:target_index + 1]
        return True, migration_steps, ""
    
    def apply_migration(self, version: str, create_backup: bool = True) -> bool:
        """Apply migration to specific version
        
        Args:
            version: Target version to migrate to
            create_backup: Whether to create backup before migration
            
        Returns:
            True if migration successful, False otherwise
        """
        try:
            logger.info(f"Starting migration to version {version}")
            
            # Validate migration
            is_valid, steps, error = self.validate_migration_path(version)
            if not is_valid:
                logger.error(f"Migration validation failed: {error}")
                return False
            
            if not steps:
                logger.info(f"Already at target version {version}")
                return True
            
            # Create backup if requested
            backup_path = None
            if create_backup and os.path.exists(self.db_path):
                backup_path = self.create_backup()
            
            # Apply migration steps
            try:
                for step_version in steps:
                    if not self._apply_version_migration(step_version):
                        raise ServiceError(f"Failed to apply migration for version {step_version}")
                
                logger.info(f"Migration to version {version} completed successfully")
                return True
                
            except Exception as e:
                logger.error(f"Migration failed: {e}")
                
                # Attempt to restore backup
                if backup_path and os.path.exists(backup_path):
                    try:
                        shutil.copy2(backup_path, self.db_path)
                        logger.info(f"Database restored from backup: {backup_path}")
                    except Exception as restore_error:
                        logger.error(f"Failed to restore backup: {restore_error}")
                
                return False
                
        except Exception as e:
            logger.error(f"Migration error: {e}")
            return False
    
    def _apply_version_migration(self, version: str) -> bool:
        """Apply migration for specific version
        
        Args:
            version: Version to migrate to
            
        Returns:
            True if successful, False otherwise
        """
        migration = self.MIGRATIONS.get(version)
        if not migration:
            logger.error(f"No migration definition for version {version}")
            return False
        
        try:
            logger.info(f"Applying migration {version}: {migration['description']}")
            
            # Execute migration SQL if provided
            if "sql" in migration:
                with self.db_manager.get_connection() as conn:
                    for sql_statement in migration["sql"]:
                        conn.execute(sql_statement.strip())
                        logger.debug(f"Executed SQL: {sql_statement.strip()[:50]}...")
            
            # Update schema version
            with self.db_manager.get_connection() as conn:
                conn.execute(
                    "INSERT OR REPLACE INTO schema_version (version) VALUES (?)",
                    (version,)
                )
            
            logger.info(f"Migration {version} applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply migration {version}: {e}")
            return False
    
    def get_migration_status(self) -> Dict[str, any]:
        """Get current migration status
        
        Returns:
            Dictionary with migration status information
        """
        current_version = self.get_current_version()
        available_versions = self.get_available_versions()
        latest_version = available_versions[-1] if available_versions else None
        
        # Get database stats
        try:
            db_stats = self.db_manager.get_database_stats()
        except Exception as e:
            logger.warning(f"Failed to get database stats: {e}")
            db_stats = {}
        
        status = {
            "current_version": current_version,
            "latest_version": latest_version,
            "available_versions": available_versions,
            "needs_migration": current_version != latest_version,
            "database_path": self.db_path,
            "database_exists": os.path.exists(self.db_path),
            "database_stats": db_stats
        }
        
        # Check if migration is needed
        if current_version and latest_version:
            try:
                current_index = available_versions.index(current_version)
                latest_index = available_versions.index(latest_version)
                status["versions_behind"] = latest_index - current_index
            except ValueError:
                status["versions_behind"] = 0
        else:
            status["versions_behind"] = 0
        
        return status
    
    def migrate_to_latest(self, create_backup: bool = True) -> bool:
        """Migrate database to latest version
        
        Args:
            create_backup: Whether to create backup before migration
            
        Returns:
            True if migration successful, False otherwise
        """
        available_versions = self.get_available_versions()
        if not available_versions:
            logger.error("No migration versions available")
            return False
        
        latest_version = available_versions[-1]
        return self.apply_migration(latest_version, create_backup)


def main():
    """Command-line interface for database migration"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Database Migration Utility")
    parser.add_argument("--db-path", help="Path to database file")
    parser.add_argument("--status", action="store_true", help="Show migration status")
    parser.add_argument("--migrate", help="Migrate to specific version")
    parser.add_argument("--migrate-latest", action="store_true", help="Migrate to latest version")
    parser.add_argument("--no-backup", action="store_true", help="Skip backup creation")
    
    args = parser.parse_args()
    
    try:
        migration = DatabaseMigration(args.db_path)
        
        if args.status:
            status = migration.get_migration_status()
            print("📊 Database Migration Status")
            print("=" * 40)
            print(f"Database Path: {status['database_path']}")
            print(f"Database Exists: {status['database_exists']}")
            print(f"Current Version: {status['current_version']}")
            print(f"Latest Version: {status['latest_version']}")
            print(f"Needs Migration: {status['needs_migration']}")
            print(f"Versions Behind: {status['versions_behind']}")
            print(f"Available Versions: {', '.join(status['available_versions'])}")
            
        elif args.migrate:
            success = migration.apply_migration(args.migrate, not args.no_backup)
            if success:
                print(f"✅ Migration to version {args.migrate} completed successfully")
            else:
                print(f"❌ Migration to version {args.migrate} failed")
                exit(1)
                
        elif args.migrate_latest:
            success = migration.migrate_to_latest(not args.no_backup)
            if success:
                print("✅ Migration to latest version completed successfully")
            else:
                print("❌ Migration to latest version failed")
                exit(1)
        else:
            parser.print_help()
            
    except Exception as e:
        print(f"❌ Migration utility error: {e}")
        exit(1)


if __name__ == "__main__":
    main()