"""Stream Configuration Extractor

Extracts and transforms operational form data into live stream initialization parameters.
Provides manual extraction of stream configuration from completed operational forms.
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from loguru import logger

from ..models.forms import OperationalForm, StreamType, PriorityLevel
from ..models.state import LiveStreamState, StreamStatus, PaceConfig, PaceLevel
from ..models.persona import PersonaManager, PersonaConfig
from ..core.exceptions import ServiceError, ValidationError
from ..core.config import cfg


class StreamConfigurationResult:
    """Result of stream configuration extraction"""
    
    def __init__(
        self,
        success: bool,
        live_stream_state: Optional[LiveStreamState] = None,
        extracted_config: Optional[Dict[str, Any]] = None,
        error_messages: Optional[List[str]] = None,
        warnings: Optional[List[str]] = None,
        extraction_time_seconds: float = 0.0
    ):
        self.success = success
        self.live_stream_state = live_stream_state
        self.extracted_config = extracted_config or {}
        self.error_messages = error_messages or []
        self.warnings = warnings or []
        self.extraction_time_seconds = extraction_time_seconds
        
        # Metadata
        self.extracted_at = datetime.utcnow()
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for serialization"""
        return {
            "success": self.success,
            "extracted_config": self.extracted_config,
            "error_messages": self.error_messages,
            "warnings": self.warnings,
            "extraction_time_seconds": self.extraction_time_seconds,
            "extracted_at": self.extracted_at.isoformat(),
            "has_live_stream_state": self.live_stream_state is not None
        }


class StreamConfigurationExtractor:
    """
    Extracts live stream initialization parameters from operational forms.
    
    Transforms operational form data into LiveStreamState configuration
    with proper validation and error handling.
    """
    
    def __init__(self, persona_manager: Optional[PersonaManager] = None):
        self.logger = logger.bind(component="stream_configuration_extractor")
        self.persona_manager = persona_manager
        
        # Stream type to pace mapping
        self.stream_type_pace_mapping = {
            StreamType.PRODUCT_LAUNCH: PaceLevel.HIGH,
            StreamType.DAILY_STREAM: PaceLevel.MEDIUM,
            StreamType.FLASH_SALE: PaceLevel.VERY_HIGH,
            StreamType.SPECIAL_EVENT: PaceLevel.HIGH,
            StreamType.Q_AND_A: PaceLevel.LOW
        }
        
        # Default viewer count estimates by stream type
        self.stream_type_viewer_estimates = {
            StreamType.PRODUCT_LAUNCH: 150,
            StreamType.DAILY_STREAM: 80,
            StreamType.FLASH_SALE: 300,
            StreamType.SPECIAL_EVENT: 200,
            StreamType.Q_AND_A: 50
        }
    
    async def extract_stream_configuration(
        self, 
        form: OperationalForm,
        initial_viewer_count: Optional[int] = None
    ) -> StreamConfigurationResult:
        """Extract complete stream configuration from operational form
        
        Args:
            form: Completed operational form
            initial_viewer_count: Initial viewer count estimate (optional)
            
        Returns:
            StreamConfigurationResult with extracted configuration
        """
        start_time = datetime.utcnow()
        errors = []
        warnings = []
        
        try:
            self.logger.info(f"Extracting stream configuration from form: {form.form_id}")
            
            # Validate form completeness
            validation_errors = self._validate_form_for_extraction(form)
            if validation_errors:
                errors.extend(validation_errors)
                return StreamConfigurationResult(
                    success=False,
                    error_messages=errors,
                    extraction_time_seconds=(datetime.utcnow() - start_time).total_seconds()
                )
            
            # Extract configuration components
            content_config = self._extract_content_configuration(form)
            interaction_config = self._extract_interaction_configuration(form)
            context_config = await self._extract_context_configuration(form)
            control_config = self._extract_control_configuration(form, initial_viewer_count)
            
            # Create LiveStreamState
            live_stream_state = LiveStreamState(
                # Content category
                plan_outline=content_config["plan_outline"],
                current_segment_idx=0,
                key_points_covered=[],
                pending_topics=content_config["pending_topics"],
                
                # Interaction category  
                question_queue=[],
                conversation_flow=[],
                last_interaction_time=None,
                
                # Context category
                current_persona=context_config["current_persona"],
                time_context=context_config["time_context"],
                pace_config=control_config["pace_config"],
                
                # Control category
                stream_status=StreamStatus.INITIALIZING,
                break_until=None,
                segments_played_count=0
            )
            
            # Compile complete extracted configuration
            extracted_config = {
                "form_metadata": {
                    "form_id": form.form_id,
                    "created_by": form.created_by,
                    "stream_title": form.basic_information.stream_title,
                    "stream_type": form.basic_information.stream_type.value,
                    "planned_duration_minutes": form.basic_information.planned_duration_minutes
                },
                "content_configuration": content_config,
                "interaction_configuration": interaction_config,
                "context_configuration": context_config,
                "control_configuration": control_config,
                "persona_configuration": {
                    "persona_id": context_config["current_persona"],
                    "persona_name": form.persona_configuration.persona_name if form.persona_configuration else "default"
                }
            }
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            self.logger.info(
                f"Successfully extracted stream configuration from form {form.form_id} "
                f"in {processing_time:.2f}s"
            )
            
            return StreamConfigurationResult(
                success=True,
                live_stream_state=live_stream_state,
                extracted_config=extracted_config,
                warnings=warnings,
                extraction_time_seconds=processing_time
            )
            
        except Exception as e:
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            error_msg = f"Stream configuration extraction failed: {e}"
            self.logger.error(error_msg)
            errors.append(error_msg)
            
            return StreamConfigurationResult(
                success=False,
                error_messages=errors,
                warnings=warnings,
                extraction_time_seconds=processing_time
            )
    
    def _validate_form_for_extraction(self, form: OperationalForm) -> List[str]:
        """Validate that form has required data for stream configuration extraction"""
        errors = []
        
        # Basic validation
        if not form.basic_information:
            errors.append("Basic information section is missing")
        elif not form.basic_information.stream_title.strip():
            errors.append("Stream title is required")
        
        if not form.product_information:
            errors.append("Product information section is missing")
        elif not form.product_information.primary_sku.strip():
            errors.append("Primary SKU is required")
        
        if not form.selling_points_structure:
            errors.append("Selling points structure is missing")
        elif len(form.selling_points_structure.selling_points) == 0:
            errors.append("At least one selling point is required")
        
        # Check if form is processed
        if not form.is_processed:
            errors.append("Form must be processed before configuration extraction")
        
        return errors
    
    def _extract_content_configuration(self, form: OperationalForm) -> Dict[str, Any]:
        """Extract content-related configuration from form"""
        
        # Generate plan outline from selling points
        plan_outline = []
        if form.selling_points_structure:
            # Add opening
            plan_outline.append(f"欢迎观看{form.basic_information.stream_title}")
            
            # Add product introduction
            plan_outline.append(f"介绍{form.product_information.product_name}")
            
            # Add selling points as outline items
            for point in form.selling_points_structure.selling_points:
                plan_outline.append(f"重点介绍: {point.title}")
            
            # Add call to actions
            if form.selling_points_structure.call_to_actions:
                for cta in form.selling_points_structure.call_to_actions:
                    plan_outline.append(f"行动召唤: {cta}")
            
            # Add closing
            plan_outline.append("直播总结和感谢")
        
        # Generate pending topics list
        pending_topics = []
        if form.selling_points_structure:
            for point in form.selling_points_structure.selling_points:
                pending_topics.append(point.title)
                # Add supporting facts as sub-topics
                pending_topics.extend(point.supporting_facts)
            
            # Add competitive advantages
            pending_topics.extend(form.selling_points_structure.competitive_advantages)
        
        return {
            "plan_outline": plan_outline,
            "pending_topics": pending_topics,
            "primary_value_proposition": form.selling_points_structure.primary_value_proposition if form.selling_points_structure else "",
            "selling_points_count": len(form.selling_points_structure.selling_points) if form.selling_points_structure else 0,
            "estimated_content_segments": len(plan_outline)
        }
    
    def _extract_interaction_configuration(self, form: OperationalForm) -> Dict[str, Any]:
        """Extract interaction-related configuration from form"""
        
        basic_info = form.basic_information
        
        return {
            "auto_interaction_enabled": basic_info.enable_auto_interaction,
            "proactive_engagement_enabled": basic_info.enable_proactive_engagement,
            "max_concurrent_questions": basic_info.max_concurrent_questions,
            "interaction_frequency_target": "medium",  # Default medium frequency
            "question_priority_handling": True
        }
    
    async def _extract_context_configuration(self, form: OperationalForm) -> Dict[str, Any]:
        """Extract context-related configuration from form"""
        
        # Determine current persona
        current_persona = "default"
        if form.persona_configuration and form.persona_configuration.selected_persona_id:
            current_persona = form.persona_configuration.selected_persona_id
        
        # Extract time context
        time_context = self._generate_time_context(form)
        
        return {
            "current_persona": current_persona,
            "time_context": time_context,
            "target_timezone": form.basic_information.target_timezone,
            "primary_language": form.basic_information.language
        }
    
    def _extract_control_configuration(
        self, 
        form: OperationalForm, 
        initial_viewer_count: Optional[int] = None
    ) -> Dict[str, Any]:
        """Extract control-related configuration from form"""
        
        # Determine initial viewer count
        if initial_viewer_count is None:
            initial_viewer_count = self.stream_type_viewer_estimates.get(
                form.basic_information.stream_type, 100
            )
        
        # Create pace configuration based on stream type and viewer count
        pace_level = self.stream_type_pace_mapping.get(
            form.basic_information.stream_type, PaceLevel.MEDIUM
        )
        
        pace_config = self._create_pace_config(pace_level, initial_viewer_count)
        
        return {
            "pace_config": pace_config,
            "planned_duration_minutes": form.basic_information.planned_duration_minutes,
            "initial_viewer_count": initial_viewer_count,
            "stream_type": form.basic_information.stream_type.value,
            "auto_pace_adjustment": True
        }
    
    def _generate_time_context(self, form: OperationalForm) -> Dict[str, Any]:
        """Generate time-based context from form data"""
        
        current_time = datetime.utcnow()
        hour = current_time.hour
        
        # Determine time period
        if 6 <= hour < 12:
            time_period = "morning"
            greeting_modifier = "早上好"
        elif 12 <= hour < 18:
            time_period = "afternoon"  
            greeting_modifier = "下午好"
        else:
            time_period = "evening"
            greeting_modifier = "晚上好"
        
        return {
            "current_time": current_time.isoformat(),
            "time_period": time_period,
            "greeting_modifier": greeting_modifier,
            "timezone": form.basic_information.target_timezone,
            "is_weekend": current_time.weekday() >= 5,
            "scheduled_start": form.basic_information.start_time.isoformat() if form.basic_information.start_time else None
        }
    
    def _create_pace_config(self, pace_level: PaceLevel, viewer_count: int) -> PaceConfig:
        """Create pace configuration for the given pace level and viewer count"""
        
        return PaceConfig.from_viewer_count(viewer_count)
    
    def get_extraction_summary(self, form: OperationalForm) -> Dict[str, Any]:
        """Get summary of what would be extracted from form without full processing
        
        Args:
            form: Operational form to summarize
            
        Returns:
            Dictionary with extraction summary
        """
        try:
            summary = {
                "form_id": form.form_id,
                "form_readiness": {
                    "is_complete": form.is_complete,
                    "is_processed": form.is_processed,
                    "completion_percentage": form.calculate_completion_percentage()
                },
                "extractable_data": {
                    "has_basic_info": form.basic_information is not None,
                    "has_product_info": form.product_information is not None,
                    "has_selling_points": form.selling_points_structure is not None,
                    "has_persona_config": form.persona_configuration is not None,
                    "selling_points_count": len(form.selling_points_structure.selling_points) if form.selling_points_structure else 0
                },
                "stream_parameters": {
                    "stream_type": form.basic_information.stream_type.value if form.basic_information else "unknown",
                    "planned_duration": form.basic_information.planned_duration_minutes if form.basic_information else 0,
                    "estimated_viewer_count": self.stream_type_viewer_estimates.get(form.basic_information.stream_type, 100) if form.basic_information else 100
                },
                "validation_status": {
                    "ready_for_extraction": len(self._validate_form_for_extraction(form)) == 0,
                    "validation_errors": self._validate_form_for_extraction(form)
                }
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to generate extraction summary: {e}")
            return {
                "error": f"Summary generation failed: {e}",
                "form_id": form.form_id if form else "unknown"
            }


# Global extractor instance
_stream_config_extractor: Optional[StreamConfigurationExtractor] = None


def get_stream_configuration_extractor(persona_manager: Optional[PersonaManager] = None) -> StreamConfigurationExtractor:
    """Get global stream configuration extractor instance"""
    global _stream_config_extractor
    if _stream_config_extractor is None:
        _stream_config_extractor = StreamConfigurationExtractor(persona_manager=persona_manager)
    return _stream_config_extractor


async def extract_stream_config_from_form(
    form: OperationalForm,
    initial_viewer_count: Optional[int] = None,
    persona_manager: Optional[PersonaManager] = None
) -> StreamConfigurationResult:
    """Convenience function to extract stream configuration from form
    
    Args:
        form: Operational form to extract from
        initial_viewer_count: Initial viewer count estimate
        persona_manager: Persona manager instance
        
    Returns:
        StreamConfigurationResult with extracted configuration
    """
    extractor = get_stream_configuration_extractor(persona_manager)
    return await extractor.extract_stream_configuration(form, initial_viewer_count)