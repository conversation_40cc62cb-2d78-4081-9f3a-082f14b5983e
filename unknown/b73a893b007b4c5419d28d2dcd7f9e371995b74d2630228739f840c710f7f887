#!/usr/bin/env python3
"""
生产环境 TTS Cache 部署脚本（纯缓存服务）
支持后台运行、日志管理和进程监控

注意：此服务为纯缓存服务，不进行 TTS 合成
所有合成由主应用的 cache_miss_engine 处理
"""

import subprocess
import time
import sys
import os
import requests
from pathlib import Path
import json
import signal

class ProductionTTSCacheDeployer:
    """生产环境 TTS Cache 部署管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.log_dir = self.project_root / "logs"
        self.pid_dir = self.project_root / "run"
        
        # 创建必要的目录
        self.log_dir.mkdir(exist_ok=True)
        self.pid_dir.mkdir(exist_ok=True)
        
    def check_docker(self):
        """检查 Docker 是否可用"""
        try:
            subprocess.run(["docker", "--version"], check=True, capture_output=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def check_service_health(self, url, timeout=30):
        """检查服务健康状态"""
        for i in range(timeout):
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    return True
            except requests.RequestException:
                pass
            time.sleep(1)
        return False
    
    def start_minio(self):
        """启动 MinIO 服务"""
        print("🗄️  启动 MinIO 存储服务...")
        
        # 创建数据目录
        minio_data_dir = self.project_root / "data" / "minio_data"
        minio_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查是否已经运行
        try:
            if self.check_service_health("http://localhost:9000/minio/health/live", timeout=3):
                print("✅ MinIO 服务已在运行")
                return True
        except:
            pass
        
        # 停止可能存在的旧容器
        try:
            subprocess.run(["docker", "stop", "minio-standalone"], 
                         capture_output=True, check=False)
            subprocess.run(["docker", "rm", "minio-standalone"], 
                         capture_output=True, check=False)
        except:
            pass
        
        # 启动 MinIO 容器
        cmd = [
            "docker", "run", "-d", "--name", "minio-standalone",
            "--restart", "unless-stopped",  # 自动重启策略
            "-p", "9000:9000", "-p", "9001:9001",
            "-e", "MINIO_ROOT_USER=minioadmin",
            "-e", "MINIO_ROOT_PASSWORD=minioadmin123",
            "-v", f"{minio_data_dir}:/data",
            "minio/minio:latest", "server", "/data", "--console-address", ":9001"
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"✅ MinIO 容器启动成功: {result.stdout.strip()}")
            
            # 等待 MinIO 启动
            print("⏳ 等待 MinIO 服务启动...")
            if self.check_service_health("http://localhost:9000/minio/health/live"):
                print("✅ MinIO 服务启动成功")
                return True
            else:
                print("❌ MinIO 服务启动超时")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ MinIO 启动失败: {e.stderr}")
            return False
    
    def start_tts_cache_daemon(self):
        """以守护进程方式启动 TTS Cache 服务"""
        print("🎤 启动 TTS Cache 服务（守护进程模式）...")
        
        # 检查是否已经运行
        if self.is_tts_cache_running():
            print("✅ TTS Cache 服务已在运行")
            return True
        
        # 准备环境变量
        env = os.environ.copy()
        env.update({
            "MINIO_ENDPOINT": "localhost:9000",
            "MINIO_ACCESS_KEY": "minioadmin",
            "MINIO_SECRET_KEY": "minioadmin123",
            "BUCKET_NAME": "tts-cache",
            "CACHE_EXPIRY_DAYS": "365",
            "MONTHLY_CHAR_LIMIT": "1000000",
            "CIRCUIT_FAILURE_THRESHOLD": "5",
            "CIRCUIT_RECOVERY_TIMEOUT": "60"
        })
        
        # 纯缓存服务不需要 API Key
        # 移除 API Key 相关配置，因为缓存服务不进行 TTS 合成
        
        # 创建日志文件
        log_file = self.log_dir / "tts_cache.log"
        pid_file = self.pid_dir / "tts_cache.pid"
        
        # 启动命令
        cmd = [
            sys.executable, "-m", "uvicorn", "src.main:app",
            "--host", "0.0.0.0",
            "--port", "22243",
            "--workers", "1"
        ]
        
        try:
            # 切换到 TTS Cache 目录
            tts_cache_dir = self.project_root / "services" / "tts-cache-cosyvoice"
            
            # 启动进程
            with open(log_file, "w") as log_f:
                process = subprocess.Popen(
                    cmd,
                    cwd=str(tts_cache_dir),
                    env=env,
                    stdout=log_f,
                    stderr=subprocess.STDOUT,
                    start_new_session=True  # 创建新的进程组
                )
            
            # 保存 PID
            pid_file.write_text(str(process.pid))
            
            print(f"✅ TTS Cache 服务已启动 (PID: {process.pid})")
            print(f"📋 日志文件: {log_file}")
            print(f"📋 PID 文件: {pid_file}")
            
            # 等待服务启动
            print("⏳ 等待 TTS Cache 服务启动...")
            if self.check_service_health("http://localhost:22243/metrics"):
                print("✅ TTS Cache 服务启动成功")
                return True
            else:
                print("❌ TTS Cache 服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ TTS Cache 启动异常: {e}")
            return False
    
    def is_tts_cache_running(self):
        """检查 TTS Cache 是否在运行"""
        try:
            response = requests.get("http://localhost:22243/metrics", timeout=3)
            return response.status_code == 200
        except:
            return False
    
    def get_tts_cache_pid(self):
        """获取 TTS Cache 进程 PID"""
        pid_file = self.pid_dir / "tts_cache.pid"
        if pid_file.exists():
            try:
                return int(pid_file.read_text().strip())
            except:
                pass
        return None
    
    def stop_tts_cache(self):
        """停止 TTS Cache 服务"""
        print("🛑 停止 TTS Cache 服务...")
        
        pid = self.get_tts_cache_pid()
        if pid:
            try:
                os.kill(pid, signal.SIGTERM)
                print(f"✅ 已发送停止信号给进程 {pid}")
                
                # 等待进程结束
                for i in range(10):
                    try:
                        os.kill(pid, 0)  # 检查进程是否存在
                        time.sleep(1)
                    except OSError:
                        break
                else:
                    # 强制终止
                    try:
                        os.kill(pid, signal.SIGKILL)
                        print(f"⚠️  强制终止进程 {pid}")
                    except OSError:
                        pass
                
                # 清理 PID 文件
                pid_file = self.pid_dir / "tts_cache.pid"
                if pid_file.exists():
                    pid_file.unlink()
                
                print("✅ TTS Cache 服务已停止")
                
            except OSError:
                print("⚠️  进程可能已经停止")
        else:
            print("ℹ️  未找到 TTS Cache 进程")
    
    def stop_minio(self):
        """停止 MinIO 服务"""
        print("🛑 停止 MinIO 服务...")
        try:
            subprocess.run(["docker", "stop", "minio-standalone"], 
                         capture_output=True, check=False)
            subprocess.run(["docker", "rm", "minio-standalone"], 
                         capture_output=True, check=False)
            print("✅ MinIO 服务已停止")
        except:
            print("⚠️  MinIO 停止时出现问题")
    
    def show_status(self):
        """显示服务状态"""
        print("📊 TTS Cache 生产环境状态")
        print("=" * 40)
        
        # MinIO 状态
        if self.check_service_health("http://localhost:9000/minio/health/live", timeout=3):
            print("✅ MinIO 服务: 运行中")
        else:
            print("❌ MinIO 服务: 未运行")
        
        # TTS Cache 状态
        if self.is_tts_cache_running():
            print("✅ TTS Cache 服务: 运行中")
            pid = self.get_tts_cache_pid()
            if pid:
                print(f"   📋 进程 PID: {pid}")
            
            # 获取指标
            try:
                response = requests.get("http://localhost:22243/metrics", timeout=2)
                metrics = response.json()
                print(f"   📊 缓存命中率: {metrics.get('cache_hit_rate', 'N/A')}")
                print(f"   📈 总请求数: {metrics.get('total_requests', 'N/A')}")
            except:
                pass
        else:
            print("❌ TTS Cache 服务: 未运行")
        
        # 日志文件状态
        log_file = self.log_dir / "tts_cache.log"
        if log_file.exists():
            print(f"📋 日志文件: {log_file} ({log_file.stat().st_size} bytes)")
        else:
            print("📋 日志文件: 不存在")
    
    def deploy(self):
        """部署所有服务"""
        print("🚀 生产环境 TTS Cache 部署")
        print("=" * 40)
        
        if not self.check_docker():
            print("❌ Docker 不可用")
            return False
        
        # 启动 MinIO
        if not self.start_minio():
            return False
        
        # 启动 TTS Cache
        if not self.start_tts_cache_daemon():
            return False
        
        print("\n🎉 TTS Cache 生产环境部署完成！")
        print("📊 服务地址:")
        print("  - TTS Cache API: http://localhost:22243")
        print("  - MinIO 控制台: http://localhost:9001")
        print("\n📋 管理命令:")
        print("  - 查看状态: python scripts/deploy_tts_cache_production.py status")
        print("  - 停止服务: python scripts/deploy_tts_cache_production.py stop")
        print("  - 查看日志: tail -f logs/tts_cache.log")
        return True
    
    def stop_all(self):
        """停止所有服务"""
        self.stop_tts_cache()
        self.stop_minio()

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="TTS Cache 生产环境部署管理器")
    parser.add_argument("action", choices=["deploy", "stop", "status", "restart"],
                       help="操作类型")
    
    args = parser.parse_args()
    deployer = ProductionTTSCacheDeployer()
    
    if args.action == "deploy":
        if deployer.deploy():
            print("\n✅ 部署成功！服务正在后台运行")
        else:
            print("\n❌ 部署失败")
            sys.exit(1)
    
    elif args.action == "stop":
        deployer.stop_all()
        print("✅ 所有服务已停止")
    
    elif args.action == "status":
        deployer.show_status()
    
    elif args.action == "restart":
        print("🔄 重启服务...")
        deployer.stop_all()
        time.sleep(3)
        if deployer.deploy():
            print("✅ 重启成功")
        else:
            print("❌ 重启失败")
            sys.exit(1)

if __name__ == "__main__":
    main()
