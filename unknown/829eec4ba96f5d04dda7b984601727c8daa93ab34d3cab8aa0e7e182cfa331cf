"""Index management utilities for Elasticsearch

Handles index versioning, rollback capabilities, and health monitoring
for knowledge base and Style Corpus indices.
"""

from typing import Dict, List, Optional, Any
import asyncio
from datetime import datetime
from loguru import logger

from ..services.search import ElasticsearchService, DocumentSchema
from ..core.exceptions import ServiceError


class IndexManager:
    """Manages Elasticsearch indices with versioning and rollback support"""
    
    def __init__(self, es_service: ElasticsearchService) -> None:
        """Initialize index manager
        
        Args:
            es_service: Elasticsearch service instance
        """
        self.es_service = es_service
        self.index_configs = {
            "knowledge_base": {
                "prefix": "ai_live_streamer_kb",
                "mapping": DocumentSchema.get_knowledge_base_mapping(),
                "description": "Knowledge base documents with product info, specs, FAQs"
            },
            "style_corpus": {
                "prefix": "ai_live_streamer_style",
                "mapping": DocumentSchema.get_style_corpus_mapping(),
                "description": "Style Corpus with script sections and examples"
            }
        }
    
    def _get_versioned_index_name(self, index_type: str, version: int) -> str:
        """Get versioned index name
        
        Args:
            index_type: Type of index (knowledge_base, style_corpus)
            version: Version number
            
        Returns:
            Versioned index name
        """
        prefix = self.index_configs[index_type]["prefix"]
        return f"{prefix}_v{version}"
    
    def _get_alias_name(self, index_type: str) -> str:
        """Get alias name for index type
        
        Args:
            index_type: Type of index
            
        Returns:
            Alias name for active index
        """
        prefix = self.index_configs[index_type]["prefix"]
        return f"{prefix}_active"
    
    async def create_versioned_index(self, index_type: str, version: int) -> str:
        """Create a new versioned index
        
        Args:
            index_type: Type of index to create
            version: Version number
            
        Returns:
            Created index name
            
        Raises:
            ServiceError: If index creation fails
        """
        if index_type not in self.index_configs:
            raise ServiceError(
                f"Unknown index type: {index_type}",
                "index_manager",
                "INVALID_INDEX_TYPE"
            )
        
        index_name = self._get_versioned_index_name(index_type, version)
        mapping = self.index_configs[index_type]["mapping"]
        
        # Add version metadata to mapping
        mapping["mappings"]["properties"]["index_version"] = {"type": "integer"}
        mapping["mappings"]["properties"]["index_created_at"] = {"type": "date"}
        
        try:
            success = await self.es_service.create_index(index_name, mapping)
            if success:
                logger.info(f"Created versioned index: {index_name}")
                
                # Add metadata document
                metadata_doc = {
                    "doc_id": "_index_metadata",
                    "index_version": version,
                    "index_type": index_type,
                    "index_created_at": datetime.utcnow().isoformat(),
                    "description": self.index_configs[index_type]["description"]
                }
                
                await self.es_service.index_document(index_name, "_metadata", metadata_doc)
                
            return index_name
            
        except Exception as e:
            raise ServiceError(
                f"Failed to create versioned index {index_name}: {str(e)}",
                "index_manager",
                "CREATE_INDEX_ERROR"
            )
    
    async def get_current_version(self, index_type: str) -> Optional[int]:
        """Get current version of index type
        
        Args:
            index_type: Type of index
            
        Returns:
            Current version number or None if no indices exist
        """
        try:
            prefix = self.index_configs[index_type]["prefix"]
            pattern = f"{prefix}_v*"
            
            response = await self.es_service.client.cat.indices(
                index=pattern,
                format="json",
                h="index"
            )
            
            if not response:
                return None
            
            # Extract version numbers
            versions = []
            for index_info in response:
                index_name = index_info["index"]
                if "_v" in index_name:
                    version_str = index_name.split("_v")[-1]
                    try:
                        versions.append(int(version_str))
                    except ValueError:
                        continue
            
            return max(versions) if versions else None
            
        except Exception as e:
            logger.error(f"Failed to get current version for {index_type}: {str(e)}")
            return None
    
    async def switch_alias_to_version(self, index_type: str, version: int) -> bool:
        """Switch alias to point to specific version
        
        Args:
            index_type: Type of index
            version: Version to switch to
            
        Returns:
            True if switch successful
        """
        try:
            index_name = self._get_versioned_index_name(index_type, version)
            alias_name = self._get_alias_name(index_type)
            
            # Check if target index exists
            if not await self.es_service.client.indices.exists(index=index_name):
                raise ServiceError(
                    f"Target index {index_name} does not exist",
                    "index_manager",
                    "INDEX_NOT_FOUND"
                )
            
            # Get current alias
            current_indices = []
            try:
                alias_response = await self.es_service.client.indices.get_alias(name=alias_name)
                current_indices = list(alias_response.keys())
            except Exception:
                # Alias doesn't exist yet
                pass
            
            # Prepare alias actions
            actions = []
            
            # Remove alias from current indices
            for current_index in current_indices:
                actions.append({
                    "remove": {
                        "index": current_index,
                        "alias": alias_name
                    }
                })
            
            # Add alias to new index
            actions.append({
                "add": {
                    "index": index_name,
                    "alias": alias_name
                }
            })
            
            # Execute alias update
            await self.es_service.client.indices.update_aliases(body={"actions": actions})
            
            logger.info(f"Switched alias {alias_name} to {index_name}")
            return True
            
        except Exception as e:
            raise ServiceError(
                f"Failed to switch alias: {str(e)}",
                "index_manager",
                "ALIAS_SWITCH_ERROR"
            )
    
    async def rollback_to_version(self, index_type: str, version: int) -> bool:
        """Rollback to previous version
        
        Args:
            index_type: Type of index
            version: Version to rollback to
            
        Returns:
            True if rollback successful
        """
        logger.info(f"Rolling back {index_type} to version {version}")
        return await self.switch_alias_to_version(index_type, version)
    
    async def list_versions(self, index_type: str) -> List[Dict[str, Any]]:
        """List all versions of an index type
        
        Args:
            index_type: Type of index
            
        Returns:
            List of version information
        """
        try:
            prefix = self.index_configs[index_type]["prefix"]
            pattern = f"{prefix}_v*"
            
            response = await self.es_service.client.cat.indices(
                index=pattern,
                format="json",
                h="index,docs.count,store.size,creation.date.string"
            )
            
            versions = []
            for index_info in response:
                index_name = index_info["index"]
                if "_v" in index_name:
                    version_str = index_name.split("_v")[-1]
                    try:
                        version_num = int(version_str)
                        versions.append({
                            "version": version_num,
                            "index_name": index_name,
                            "document_count": int(index_info.get("docs.count", 0)),
                            "size": index_info.get("store.size", "0b"),
                            "created_at": index_info.get("creation.date.string", "unknown")
                        })
                    except ValueError:
                        continue
            
            # Sort by version number
            versions.sort(key=lambda x: x["version"], reverse=True)
            return versions
            
        except Exception as e:
            logger.error(f"Failed to list versions for {index_type}: {str(e)}")
            return []
    
    async def delete_old_versions(self, index_type: str, keep_versions: int = 3) -> int:
        """Delete old versions keeping only the most recent ones
        
        Args:
            index_type: Type of index
            keep_versions: Number of versions to keep
            
        Returns:
            Number of versions deleted
        """
        try:
            versions = await self.list_versions(index_type)
            
            if len(versions) <= keep_versions:
                logger.info(f"No old versions to delete for {index_type}")
                return 0
            
            # Get current active version
            alias_name = self._get_alias_name(index_type)
            active_index = None
            try:
                alias_response = await self.es_service.client.indices.get_alias(name=alias_name)
                active_index = list(alias_response.keys())[0] if alias_response else None
            except Exception:
                pass
            
            # Identify versions to delete
            versions_to_delete = versions[keep_versions:]
            deleted_count = 0
            
            for version_info in versions_to_delete:
                index_name = version_info["index_name"]
                
                # Don't delete active index
                if index_name == active_index:
                    logger.info(f"Skipping deletion of active index: {index_name}")
                    continue
                
                try:
                    await self.es_service.client.indices.delete(index=index_name)
                    logger.info(f"Deleted old version: {index_name}")
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"Failed to delete {index_name}: {str(e)}")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to delete old versions: {str(e)}")
            return 0
    
    async def get_index_health(self, index_type: str) -> Dict[str, Any]:
        """Get health information for index type
        
        Args:
            index_type: Type of index
            
        Returns:
            Health information dictionary
        """
        try:
            alias_name = self._get_alias_name(index_type)
            
            # Get active index
            active_index = None
            try:
                alias_response = await self.es_service.client.indices.get_alias(name=alias_name)
                active_index = list(alias_response.keys())[0] if alias_response else None
            except Exception:
                pass
            
            if not active_index:
                return {
                    "status": "no_active_index",
                    "active_index": None,
                    "document_count": 0,
                    "size": "0b"
                }
            
            # Get index stats
            stats_response = await self.es_service.client.indices.stats(index=active_index)
            index_stats = stats_response["indices"][active_index]
            
            return {
                "status": "healthy",
                "active_index": active_index,
                "document_count": index_stats["total"]["docs"]["count"],
                "size": f"{index_stats['total']['store']['size_in_bytes']} bytes",
                "primaries": index_stats["primaries"]["docs"]["count"],
                "replicas": index_stats["total"]["docs"]["count"] - index_stats["primaries"]["docs"]["count"]
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "active_index": None,
                "document_count": 0
            }