"""产品管理控制台

提供产品管理的Web界面。
"""

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from pathlib import Path

# 创建路由器
console_router = APIRouter(prefix="/console", tags=["console"])


@console_router.get("/products", response_class=HTMLResponse)
async def product_console(request: Request):
    """产品管理控制台页面"""
    html_content = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品管理中心</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            height: fit-content;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }
        
        .btn-secondary:hover {
            background: #d5dbdd;
        }
        
        .btn-success {
            background: #2ecc71;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .product-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .product-sku {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .product-category {
            display: inline-block;
            background: #ecf0f1;
            color: #7f8c8d;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            margin-bottom: 10px;
        }
        
        .product-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ecf0f1;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 12px;
            color: #95a5a6;
            margin-top: 2px;
        }
        
        .search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 40px;
            width: 90%;
            max-width: 600px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .tags-input {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            min-height: 50px;
        }
        
        .tag {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .tag-remove {
            cursor: pointer;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-radius: 50%;
            border-top: 3px solid #667eea;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 60px;
            color: #95a5a6;
        }
        
        .empty-state img {
            width: 150px;
            opacity: 0.5;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ 产品管理中心</h1>
            <p>管理产品信息、QA话术和直播配置</p>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">快速操作</h3>
                <button class="btn btn-primary" style="width: 100%; margin-bottom: 10px;" onclick="showAddProductModal()">
                    ➕ 添加新产品
                </button>
                <button class="btn btn-secondary" style="width: 100%; margin-bottom: 10px;" onclick="refreshProducts()">
                    🔄 刷新列表
                </button>
                
                <hr style="margin: 20px 0; border: none; border-top: 1px solid #ecf0f1;">
                
                <h3 style="margin-bottom: 15px; color: #2c3e50;">分类筛选</h3>
                <select id="categoryFilter" class="form-control" onchange="filterByCategory()">
                    <option value="">所有分类</option>
                    <option value="electronics">电子产品</option>
                    <option value="clothing">服装</option>
                    <option value="food">食品</option>
                    <option value="beauty">美妆</option>
                    <option value="home">家居</option>
                    <option value="sports">运动</option>
                    <option value="books">图书</option>
                    <option value="toys">玩具</option>
                    <option value="other">其他</option>
                </select>
                
                <hr style="margin: 20px 0; border: none; border-top: 1px solid #ecf0f1;">
                
                <h3 style="margin-bottom: 15px; color: #2c3e50;">统计信息</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <div style="margin-bottom: 10px;">
                        <div style="color: #7f8c8d; font-size: 12px;">产品总数</div>
                        <div style="font-size: 24px; font-weight: 700; color: #667eea;" id="totalProducts">-</div>
                    </div>
                    <div>
                        <div style="color: #7f8c8d; font-size: 12px;">QA条目总数</div>
                        <div style="font-size: 24px; font-weight: 700; color: #764ba2;" id="totalQA">-</div>
                    </div>
                </div>
            </div>
            
            <div class="content-area">
                <div class="search-bar">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索产品名称、SKU或描述...">
                    <button class="btn btn-primary" onclick="searchProducts()">搜索</button>
                    <button class="btn btn-secondary" onclick="clearSearch()">清空</button>
                </div>
                
                <div id="loadingIndicator" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p style="margin-top: 20px;">正在加载产品列表...</p>
                </div>
                
                <div id="productGrid" class="product-grid">
                    <!-- 产品卡片将动态加载到这里 -->
                </div>
                
                <div id="emptyState" class="empty-state" style="display: none;">
                    <p style="font-size: 18px; margin-bottom: 20px;">暂无产品数据</p>
                    <button class="btn btn-primary" onclick="showAddProductModal()">添加第一个产品</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑产品模态框 -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <h2 id="modalTitle" style="margin-bottom: 30px; color: #2c3e50;">添加产品</h2>
            
            <form id="productForm" onsubmit="saveProduct(event)">
                <div class="form-group">
                    <label>SKU *</label>
                    <input type="text" id="skuInput" class="form-control" required 
                           pattern="[A-Za-z0-9_-]+" placeholder="例如: PROD-001">
                </div>
                
                <div class="form-group">
                    <label>产品名称 *</label>
                    <input type="text" id="nameInput" class="form-control" required 
                           placeholder="请输入产品名称">
                </div>
                
                <div class="form-group">
                    <label>产品分类 *</label>
                    <select id="categoryInput" class="form-control" required>
                        <option value="">请选择分类</option>
                        <option value="electronics">电子产品</option>
                        <option value="clothing">服装</option>
                        <option value="food">食品</option>
                        <option value="beauty">美妆</option>
                        <option value="home">家居</option>
                        <option value="sports">运动</option>
                        <option value="books">图书</option>
                        <option value="toys">玩具</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>产品描述</label>
                    <textarea id="descriptionInput" class="form-control" rows="3" 
                              placeholder="请输入产品描述"></textarea>
                </div>
                
                <div class="form-group">
                    <label>价格</label>
                    <input type="number" id="priceInput" class="form-control" 
                           step="0.01" min="0" placeholder="0.00">
                </div>
                
                <div class="form-group">
                    <label>库存</label>
                    <input type="number" id="stockInput" class="form-control" 
                           min="0" placeholder="0">
                </div>
                
                <div class="form-group">
                    <label>标签</label>
                    <input type="text" id="tagInput" class="form-control" 
                           placeholder="输入标签后按Enter添加">
                    <div id="tagsContainer" class="tags-input" style="margin-top: 10px;"></div>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 30px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        let products = [];
        let currentEditId = null;
        let currentTags = [];
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            
            // 标签输入处理
            document.getElementById('tagInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addTag(this.value);
                    this.value = '';
                }
            });
        });
        
        // 加载产品列表
        async function loadProducts() {
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('productGrid').style.display = 'none';
            
            try {
                const response = await fetch('/api/v1/products?size=100');
                const data = await response.json();
                
                products = data.items || [];
                renderProducts(products);
                updateStats();
                
            } catch (error) {
                console.error('加载产品失败:', error);
                alert('加载产品列表失败');
            } finally {
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('productGrid').style.display = 'grid';
            }
        }
        
        // 渲染产品卡片
        function renderProducts(productList) {
            const grid = document.getElementById('productGrid');
            const emptyState = document.getElementById('emptyState');
            
            if (productList.length === 0) {
                grid.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            grid.style.display = 'grid';
            emptyState.style.display = 'none';
            
            grid.innerHTML = productList.map(product => `
                <div class="product-card" onclick="viewProduct(${product.id})">
                    <div class="product-sku">${product.sku}</div>
                    <div class="product-name">${product.name}</div>
                    <div class="product-category">${getCategoryName(product.category)}</div>
                    ${product.description ? `<p style="color: #7f8c8d; font-size: 14px; margin: 10px 0;">${product.description.substring(0, 100)}...</p>` : ''}
                    <div class="product-stats">
                        <div class="stat-item">
                            <div class="stat-value">${product.qa_count || 0}</div>
                            <div class="stat-label">QA条目</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${product.price || 0}</div>
                            <div class="stat-label">价格</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${product.stock || 0}</div>
                            <div class="stat-label">库存</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // 查看产品详情
        function viewProduct(productId) {
            window.location.href = `/console/products/${productId}/qa`;
        }
        
        // 显示添加产品模态框
        function showAddProductModal() {
            currentEditId = null;
            currentTags = [];
            document.getElementById('modalTitle').textContent = '添加产品';
            document.getElementById('productForm').reset();
            document.getElementById('tagsContainer').innerHTML = '';
            document.getElementById('productModal').style.display = 'block';
        }
        
        // 保存产品
        async function saveProduct(event) {
            event.preventDefault();
            
            const productData = {
                sku: document.getElementById('skuInput').value,
                name: document.getElementById('nameInput').value,
                category: document.getElementById('categoryInput').value,
                description: document.getElementById('descriptionInput').value || null,
                price: parseFloat(document.getElementById('priceInput').value) || null,
                stock: parseInt(document.getElementById('stockInput').value) || null,
                tags: currentTags
            };
            
            try {
                const response = await fetch('/api/v1/products', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(productData)
                });
                
                if (response.ok) {
                    closeModal();
                    loadProducts();
                    alert('产品创建成功');
                } else {
                    const error = await response.json();
                    alert('创建失败: ' + (error.detail || '未知错误'));
                }
            } catch (error) {
                console.error('保存产品失败:', error);
                alert('保存失败');
            }
        }
        
        // 添加标签
        function addTag(tag) {
            tag = tag.trim();
            if (tag && !currentTags.includes(tag)) {
                currentTags.push(tag);
                renderTags();
            }
        }
        
        // 渲染标签
        function renderTags() {
            const container = document.getElementById('tagsContainer');
            container.innerHTML = currentTags.map(tag => `
                <div class="tag">
                    ${tag}
                    <span class="tag-remove" onclick="removeTag('${tag}')">×</span>
                </div>
            `).join('');
        }
        
        // 移除标签
        function removeTag(tag) {
            currentTags = currentTags.filter(t => t !== tag);
            renderTags();
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('productModal').style.display = 'none';
        }
        
        // 刷新产品列表
        function refreshProducts() {
            loadProducts();
        }
        
        // 搜索产品
        async function searchProducts() {
            const query = document.getElementById('searchInput').value;
            if (!query) {
                loadProducts();
                return;
            }
            
            try {
                const response = await fetch(`/api/v1/products?search=${encodeURIComponent(query)}&size=100`);
                const data = await response.json();
                renderProducts(data.items || []);
            } catch (error) {
                console.error('搜索失败:', error);
            }
        }
        
        // 清空搜索
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            loadProducts();
        }
        
        // 按分类筛选
        async function filterByCategory() {
            const category = document.getElementById('categoryFilter').value;
            
            try {
                let url = '/api/v1/products?size=100';
                if (category) {
                    url += `&category=${category}`;
                }
                
                const response = await fetch(url);
                const data = await response.json();
                renderProducts(data.items || []);
            } catch (error) {
                console.error('筛选失败:', error);
            }
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('totalProducts').textContent = products.length;
            
            // TODO: 获取QA总数
            let totalQA = products.reduce((sum, p) => sum + (p.qa_count || 0), 0);
            document.getElementById('totalQA').textContent = totalQA;
        }
        
        // 获取分类名称
        function getCategoryName(category) {
            const names = {
                'electronics': '电子产品',
                'clothing': '服装',
                'food': '食品',
                'beauty': '美妆',
                'home': '家居',
                'sports': '运动',
                'books': '图书',
                'toys': '玩具',
                'other': '其他'
            };
            return names[category] || category;
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('productModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
'''
    return HTMLResponse(content=html_content)


@console_router.get("/products/{product_id}/qa", response_class=HTMLResponse)
async def product_qa_console(product_id: int):
    """产品QA管理页面"""
    html_content = f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品QA管理</title>
    <style>
        /* 使用与产品管理相同的样式 */
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }}
        
        .breadcrumb {{
            margin-bottom: 20px;
            color: #7f8c8d;
        }}
        
        .breadcrumb a {{
            color: #667eea;
            text-decoration: none;
        }}
        
        .btn {{
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .btn-primary {{
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }}
        
        .btn-success {{
            background: #2ecc71;
            color: white;
        }}
        
        .btn-danger {{
            background: #e74c3c;
            color: white;
        }}
        
        .qa-table {{
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
        }}
        
        th {{
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
        }}
        
        td {{
            padding: 15px;
            border-top: 1px solid #ecf0f1;
        }}
        
        tr:hover {{
            background: #f8f9fa;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="breadcrumb">
                <a href="/console/products">产品管理</a> / 产品QA管理
            </div>
            <h1 id="productName">加载中...</h1>
            <p>产品ID: {product_id}</p>
        </div>
        
        <div style="margin-bottom: 20px;">
            <button class="btn btn-primary" onclick="showAddQAModal()">➕ 添加QA</button>
            <button class="btn btn-success" onclick="loadQAList()">🔄 刷新</button>
        </div>
        
        <div class="qa-table">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>问题</th>
                        <th>答案</th>
                        <th>命中次数</th>
                        <th>置信度</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="qaTableBody">
                    <tr>
                        <td colspan="6" style="text-align: center;">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        const productId = {product_id};
        
        // 加载产品信息
        async function loadProductInfo() {{
            try {{
                const response = await fetch(`/api/v1/products/${{productId}}`);
                const product = await response.json();
                document.getElementById('productName').textContent = product.name;
            }} catch (error) {{
                console.error('加载产品信息失败:', error);
            }}
        }}
        
        // 加载QA列表
        async function loadQAList() {{
            try {{
                const response = await fetch(`/api/v1/products/${{productId}}/qa`);
                const data = await response.json();
                
                const tbody = document.getElementById('qaTableBody');
                if (data.items.length === 0) {{
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无QA数据</td></tr>';
                    return;
                }}
                
                tbody.innerHTML = data.items.map(qa => `
                    <tr>
                        <td>${{qa.id}}</td>
                        <td>${{qa.question}}</td>
                        <td>${{qa.answer.substring(0, 100)}}...</td>
                        <td>${{qa.hit_count}}</td>
                        <td>${{qa.confidence_score.toFixed(2)}}</td>
                        <td>
                            <button class="btn btn-danger" onclick="deleteQA(${{qa.id}})">删除</button>
                        </td>
                    </tr>
                `).join('');
                
            }} catch (error) {{
                console.error('加载QA列表失败:', error);
            }}
        }}
        
        // 删除QA
        async function deleteQA(qaId) {{
            if (!confirm('确定要删除这条QA吗？')) return;
            
            try {{
                const response = await fetch(`/api/v1/products/${{productId}}/qa/${{qaId}}`, {{
                    method: 'DELETE'
                }});
                
                if (response.ok) {{
                    loadQAList();
                }} else {{
                    alert('删除失败');
                }}
            }} catch (error) {{
                console.error('删除QA失败:', error);
            }}
        }}
        
        // 显示添加QA模态框
        function showAddQAModal() {{
            // TODO: 实现添加QA的模态框
            alert('添加QA功能待实现');
        }}
        
        // 页面加载
        document.addEventListener('DOMContentLoaded', function() {{
            loadProductInfo();
            loadQAList();
        }});
    </script>
</body>
</html>
'''
    return HTMLResponse(content=html_content)