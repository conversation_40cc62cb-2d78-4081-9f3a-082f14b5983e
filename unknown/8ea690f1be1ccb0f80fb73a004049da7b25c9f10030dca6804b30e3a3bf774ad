"""知识库管理器模块"""

import sqlite3
import numpy as np
import faiss
from typing import List, Tu<PERSON>, Dict, Optional
from sentence_transformers import SentenceTransformer
from dataclasses import dataclass
import json
import logging
import os
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


@dataclass
class QAEntry:
    """QA条目数据结构"""
    question: str
    answer: str
    category: str
    keywords: List[str]
    confidence: float
    metadata: Optional[Dict] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class SearchResult:
    """搜索结果数据结构"""
    question: str
    answer: str
    score: float
    category: str
    metadata: Dict
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class KnowledgeBaseError(Exception):
    """知识库操作异常"""
    pass


class KnowledgeBaseManager:
    """统一管理SQLite和Faiss的知识库服务"""
    
    def __init__(self, db_path: str, faiss_index_path: str, 
                 embedding_model: str = "BAAI/bge-m3"):
        self.db_path = db_path
        self.faiss_index_path = faiss_index_path
        self.db_connection = None
        self.faiss_index = None
        self.embedding_model = None
        self.embedding_model_name = embedding_model
        self._executor = ThreadPoolExecutor(max_workers=2)
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """初始化知识库"""
        try:
            # 确保数据目录存在
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            Path(self.faiss_index_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 连接数据库
            self.db_connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.db_connection.row_factory = sqlite3.Row
            
            # 加载或创建Faiss索引
            self.faiss_index = self._load_or_create_index()
            
            # 异步加载嵌入模型
            await self._load_embedding_model()
            
            logger.info(f"知识库初始化完成")
            
        except Exception as e:
            logger.error(f"知识库初始化失败: {e}")
            raise
            
    async def _load_embedding_model(self):
        """异步加载嵌入模型"""
        try:
            logger.info(f"开始加载嵌入模型: {self.embedding_model_name}")
            
            # 在线程池中加载模型
            self.embedding_model = await asyncio.get_event_loop().run_in_executor(
                self._executor,
                SentenceTransformer,
                self.embedding_model_name
            )
            
            logger.info(f"嵌入模型加载完成")
        except Exception as e:
            logger.error(f"嵌入模型加载失败: {e}")
            raise RuntimeError(f"无法加载嵌入模型 {self.embedding_model_name}: {e}")
        
    def _load_or_create_index(self) -> faiss.Index:
        """加载或创建Faiss索引"""
        try:
            if os.path.exists(self.faiss_index_path):
                index = faiss.read_index(self.faiss_index_path)
                logger.info(f"加载现有Faiss索引: {self.faiss_index_path}, 条目数: {index.ntotal}")
                return index
        except Exception as e:
            logger.warning(f"加载Faiss索引失败: {e}")
            
        # 创建新索引 - 固定维度，与BGE-M3模型匹配
        dimension = 1024  # BGE-M3 模型的固定维度
        # 使用IndexFlatIP (Inner Product) 作为余弦相似度的替代
        # 注意：需要对向量进行L2归一化以获得余弦相似度
        index = faiss.IndexFlatIP(dimension)
        logger.info(f"创建新的Faiss索引 (维度: {dimension}, 类型: IndexFlatIP)")
        return index
        
    async def hybrid_search(self, question: str, top_k: int = 5,
                           bm25_weight: float = 0.3, 
                           vector_weight: float = 0.7) -> List[Tuple[int, float, str, str]]:
        """混合检索：BM25 + 向量检索"""
        if not question.strip():
            return []
            
        try:
            # 1. BM25关键词召回
            bm25_results = await self._bm25_search(question, top_k * 2)
            
            # 2. 向量相似度检索
            vector_results = await self._vector_search(question, top_k * 2)
            
            # 3. 融合评分
            return self._merge_search_results(bm25_results, vector_results, 
                                            bm25_weight, vector_weight, top_k)
            
        except Exception as e:
            logger.error(f"混合检索失败: {e}")
            raise
            
    async def _bm25_search(self, question: str, limit: int) -> List[Dict]:
        """BM25关键词搜索"""
        cursor = self.db_connection.cursor()
        
        try:
            bm25_query = """
                SELECT qa.id, qa.question, qa.answer, 
                       bm25(qa_entries_fts) as score,
                       qa.hit_count, qa.confidence_score
                FROM qa_entries qa
                JOIN qa_entries_fts fts ON qa.id = fts.rowid
                WHERE qa_entries_fts MATCH ?
                ORDER BY score DESC
                LIMIT ?
            """
            cursor.execute(bm25_query, (question, limit))
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                results.append({
                    'id': row['id'],
                    'question': row['question'],
                    'answer': row['answer'],
                    'bm25_score': float(row['score']) if row['score'] else 0.0,
                    'hit_count': row['hit_count'],
                    'confidence_score': row['confidence_score']
                })
                
            return results
            
        except Exception as e:
            logger.warning(f"BM25搜索失败: {e}")
            return []
            
    async def _vector_search(self, question: str, limit: int) -> List[Dict]:
        """向量相似度搜索"""
        if not self.embedding_model or self.faiss_index.ntotal == 0:
            return []
            
        try:
            # 异步计算嵌入向量
            question_embedding = await asyncio.get_event_loop().run_in_executor(
                self._executor,
                lambda: self.embedding_model.encode([question])
            )
            
            # L2归一化以获得余弦相似度
            question_embedding = question_embedding / np.linalg.norm(question_embedding, axis=1, keepdims=True)
            
            # 搜索最相似的向量
            distances, indices = self.faiss_index.search(
                question_embedding.astype(np.float32), min(limit, self.faiss_index.ntotal)
            )
            
            # 获取向量检索结果
            results = []
            cursor = self.db_connection.cursor()
            
            for idx, dist in zip(indices[0], distances[0]):
                if idx >= 0:  # Faiss返回-1表示没有结果
                    cursor.execute("""
                        SELECT id, question, answer, hit_count, confidence_score 
                        FROM qa_entries 
                        WHERE embedding_id = ?
                    """, (int(idx),))
                    row = cursor.fetchone()
                    if row:
                        results.append({
                            'id': row['id'],
                            'question': row['question'],
                            'answer': row['answer'],
                            'vector_score': float(dist),
                            'hit_count': row['hit_count'],
                            'confidence_score': row['confidence_score']
                        })
                        
            return results
            
        except Exception as e:
            logger.warning(f"向量搜索失败: {e}")
            return []
            
    def _merge_search_results(self, bm25_results: List[Dict], vector_results: List[Dict],
                            bm25_weight: float, vector_weight: float, 
                            top_k: int) -> List[Tuple[int, float, str, str]]:
        """合并BM25和向量搜索结果"""
        results_dict = {}
        
        # 处理BM25结果
        max_bm25_score = max([r['bm25_score'] for r in bm25_results], default=1.0)
        for result in bm25_results:
            results_dict[result['id']] = {
                'id': result['id'],
                'question': result['question'],
                'answer': result['answer'],
                'bm25_score': result['bm25_score'] / (max_bm25_score + 1e-6),
                'vector_score': 0.0,
                'hit_count': result['hit_count']
            }
            
        # 处理向量检索结果
        max_vector_score = max([r['vector_score'] for r in vector_results], default=1.0)
        for result in vector_results:
            entry_id = result['id']
            if entry_id in results_dict:
                results_dict[entry_id]['vector_score'] = result['vector_score'] / (max_vector_score + 1e-6)
            else:
                results_dict[entry_id] = {
                    'id': entry_id,
                    'question': result['question'],
                    'answer': result['answer'],
                    'bm25_score': 0.0,
                    'vector_score': result['vector_score'] / (max_vector_score + 1e-6),
                    'hit_count': result['hit_count']
                }
                
        # 计算融合分数
        final_results = []
        for item in results_dict.values():
            # 加权融合，考虑命中次数加成
            hit_bonus = min(item['hit_count'] * 0.01, 0.1)  # 命中次数加成，最大10%
            final_score = (bm25_weight * item['bm25_score'] + 
                          vector_weight * item['vector_score'] + 
                          hit_bonus)
            
            final_results.append((
                item['id'],
                final_score,
                item['question'],
                item['answer']
            ))
            
        # 排序并返回top_k结果
        final_results.sort(key=lambda x: x[1], reverse=True)
        return final_results[:top_k]
        
    async def add_entry(self, question: str, answer: str, 
                       metadata: Optional[Dict] = None) -> int:
        """添加QA条目"""
        if not question or not answer:
            raise ValueError("问题和答案不能为空")
            
        metadata = metadata or {}
        
        async with self._lock:
            try:
                cursor = self.db_connection.cursor()
                
                # 1. 存储到SQLite
                cursor.execute("""
                    INSERT INTO qa_entries (question, answer, category, tags)
                    VALUES (?, ?, ?, ?)
                """, (
                    question,
                    answer,
                    metadata.get('category'),
                    json.dumps(metadata.get('tags', []))
                ))
                entry_id = cursor.lastrowid
                
                # 2. 计算嵌入向量并添加到Faiss索引
                if self.embedding_model:
                    await self._add_to_faiss_index(question, entry_id, cursor)
                
                # 3. 更新FTS索引
                cursor.execute("""
                    INSERT INTO qa_entries_fts (rowid, question, answer) 
                    VALUES (?, ?, ?)
                """, (entry_id, question, answer))
                
                self.db_connection.commit()
                
                # 4. 保存Faiss索引
                await self._save_faiss_index()
                
                logger.info(f"添加QA条目成功: ID={entry_id}")
                return entry_id
                
            except Exception as e:
                self.db_connection.rollback()
                logger.error(f"添加QA条目失败: {e}")
                raise
                
    async def _add_to_faiss_index(self, question: str, entry_id: int, cursor):
        """添加条目到Faiss索引"""
        try:
            # 异步计算嵌入向量
            embedding = await asyncio.get_event_loop().run_in_executor(
                self._executor,
                lambda: self.embedding_model.encode([question])
            )
            
            # L2归一化以获得余弦相似度
            embedding = embedding / np.linalg.norm(embedding, axis=1, keepdims=True)
            
            # 添加到Faiss索引
            embedding_id = self.faiss_index.ntotal
            self.faiss_index.add(embedding.astype(np.float32))
            
            # 更新embedding_id
            cursor.execute(
                "UPDATE qa_entries SET embedding_id = ? WHERE id = ?",
                (embedding_id, entry_id)
            )
            
        except Exception as e:
            logger.warning(f"添加到Faiss索引失败: {e}")
            
    async def _save_faiss_index(self):
        """保存Faiss索引"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                lambda: faiss.write_index(self.faiss_index, self.faiss_index_path)
            )
        except Exception as e:
            logger.warning(f"保存Faiss索引失败: {e}")
        
    async def batch_update_index(self, entries: List[Dict]) -> None:
        """批量更新索引"""
        if not entries:
            return
            
        async with self._lock:
            try:
                cursor = self.db_connection.cursor()
                
                # 批量计算嵌入
                questions = [e['question'] for e in entries]
                if self.embedding_model:
                    embeddings = await asyncio.get_event_loop().run_in_executor(
                        self._executor,
                        lambda: self.embedding_model.encode(questions)
                    )
                    
                    # L2归一化以获得余弦相似度
                    embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
                    
                    # 批量添加到Faiss
                    start_idx = self.faiss_index.ntotal
                    self.faiss_index.add(embeddings.astype(np.float32))
                else:
                    start_idx = 0
                    embeddings = None
                
                # 批量更新数据库
                for i, entry in enumerate(entries):
                    cursor.execute("""
                        INSERT INTO qa_entries 
                        (question, answer, category, tags, embedding_id)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        entry['question'],
                        entry['answer'],
                        entry.get('category'),
                        json.dumps(entry.get('tags', [])),
                        start_idx + i if embeddings is not None else None
                    ))
                    
                    # 更新FTS
                    entry_id = cursor.lastrowid
                    cursor.execute("""
                        INSERT INTO qa_entries_fts (rowid, question, answer) 
                        VALUES (?, ?, ?)
                    """, (entry_id, entry['question'], entry['answer']))
                    
                self.db_connection.commit()
                
                # 保存Faiss索引
                await self._save_faiss_index()
                
                logger.info(f"批量更新{len(entries)}条记录成功")
                
            except Exception as e:
                self.db_connection.rollback()
                logger.error(f"批量更新失败: {e}")
                raise
                
    async def update_hit_count(self, entry_id: int):
        """更新条目的命中次数"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute(
                "UPDATE qa_entries SET hit_count = hit_count + 1 WHERE id = ?",
                (entry_id,)
            )
            self.db_connection.commit()
        except Exception as e:
            logger.error(f"更新命中次数失败: {e}")
            
    async def get_entry_by_id(self, entry_id: int) -> Optional[Dict]:
        """根据ID获取QA条目"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT id, question, answer, category, tags, hit_count, 
                       confidence_score, created_at, updated_at
                FROM qa_entries WHERE id = ?
            """, (entry_id,))
            row = cursor.fetchone()
            
            if row:
                return {
                    'id': row['id'],
                    'question': row['question'],
                    'answer': row['answer'],
                    'category': row['category'],
                    'tags': json.loads(row['tags']) if row['tags'] else [],
                    'hit_count': row['hit_count'],
                    'confidence_score': row['confidence_score'],
                    'created_at': row['created_at'],
                    'updated_at': row['updated_at']
                }
            return None
        except Exception as e:
            logger.error(f"获取QA条目失败: {e}")
            return None
            
    async def delete_entry(self, entry_id: int) -> bool:
        """删除QA条目"""
        async with self._lock:
            try:
                cursor = self.db_connection.cursor()
                
                # 删除主表记录
                cursor.execute("DELETE FROM qa_entries WHERE id = ?", (entry_id,))
                deleted_count = cursor.rowcount
                
                if deleted_count > 0:
                    # 删除FTS索引记录
                    cursor.execute("DELETE FROM qa_entries_fts WHERE rowid = ?", (entry_id,))
                    self.db_connection.commit()
                    logger.info(f"删除QA条目成功: ID={entry_id}")
                    return True
                else:
                    logger.warning(f"QA条目不存在: ID={entry_id}")
                    return False
                    
            except Exception as e:
                self.db_connection.rollback()
                logger.error(f"删除QA条目失败: {e}")
                return False
                
    async def get_stats(self) -> Dict:
        """获取知识库统计信息"""
        try:
            cursor = self.db_connection.cursor()
            
            # 总条目数
            cursor.execute("SELECT COUNT(*) FROM qa_entries")
            total_entries = cursor.fetchone()[0]
            
            # 今日新增
            cursor.execute("""
                SELECT COUNT(*) FROM qa_entries 
                WHERE DATE(created_at) = DATE('now')
            """)
            today_count = cursor.fetchone()[0]
            
            # 待审核数
            cursor.execute("SELECT COUNT(*) FROM pending_qa_entries WHERE status = 'pending'")
            pending_count = cursor.fetchone()[0]
            
            # 热门问题
            cursor.execute("""
                SELECT question, answer, hit_count 
                FROM qa_entries 
                ORDER BY hit_count DESC 
                LIMIT 5
            """)
            hot_questions = [
                {
                    'question': row['question'],
                    'answer': row['answer'][:100] + '...' if len(row['answer']) > 100 else row['answer'],
                    'hit_count': row['hit_count']
                }
                for row in cursor.fetchall()
            ]
            
            return {
                'total_entries': total_entries,
                'today_count': today_count,
                'pending_count': pending_count,
                'hot_questions': hot_questions,
                'faiss_entries': self.faiss_index.ntotal if self.faiss_index else 0
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
            
    async def warmup_embedding_model(self):
        """预热嵌入模型"""
        if not self.embedding_model:
            logger.warning("嵌入模型未加载，跳过预热")
            return
            
        try:
            test_texts = ["测试文本", "预热模型", "embedding test"]
            await asyncio.get_event_loop().run_in_executor(
                self._executor,
                lambda: self.embedding_model.encode(test_texts)
            )
            logger.info("嵌入模型预热完成")
        except Exception as e:
            logger.warning(f"嵌入模型预热失败: {e}")
            
    def __del__(self):
        """析构函数，清理资源"""
        if self.db_connection:
            self.db_connection.close()
        if self._executor:
            self._executor.shutdown(wait=False)