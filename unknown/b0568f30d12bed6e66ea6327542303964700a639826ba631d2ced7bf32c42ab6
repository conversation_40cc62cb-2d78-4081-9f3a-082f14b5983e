"""TTS-Cache Service

纯缓存服务，只负责音频的存储和查询
不进行任何 TTS 合成，所有合成由主应用处理
"""

import asyncio
import hashlib
import json
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from pathlib import Path
import os
import io
import base64

from fastapi import FastAPI, HTTPException, Response, Request, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import aiohttp
from minio import Minio
from minio.error import S3Error
from loguru import logger

# 配置日志
logger.add("logs/tts-cache.log", rotation="500 MB", retention="7 days")

# 应用实例
app = FastAPI(
    title="TTS-Cache-Service",
    description="纯缓存服务，负责音频的存储和查询",
    version="2.0.0"
)

# CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置
class Config:
    """服务配置"""
    # MinIO 配置
    MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "minio:9000")
    MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
    MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")
    MINIO_SECURE = os.getenv("MINIO_SECURE", "false").lower() == "true"
    BUCKET_NAME = os.getenv("BUCKET_NAME", "tts-cache")
    
    # 缓存配置
    CACHE_EXPIRY_DAYS = int(os.getenv("CACHE_EXPIRY_DAYS", "365"))
    MONTHLY_CHAR_LIMIT = int(os.getenv("MONTHLY_CHAR_LIMIT", "1000000"))

config = Config()

# MinIO 客户端
minio_client = Minio(
    config.MINIO_ENDPOINT,
    access_key=config.MINIO_ACCESS_KEY,
    secret_key=config.MINIO_SECRET_KEY,
    secure=config.MINIO_SECURE
)

# 统计信息
class Stats:
    def __init__(self):
        self.total_requests = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_uploads = 0
        self.errors = 0
        self.total_bytes_cached = 0
        self.response_times = []
        
    def add_response_time(self, time_ms: float):
        """记录响应时间"""
        self.response_times.append(time_ms)
        
        # 保留最近1000个样本
        if len(self.response_times) > 1000:
            self.response_times = self.response_times[-1000:]
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取统计指标"""
        cache_hit_rate = (self.cache_hits / self.total_requests * 100) if self.total_requests > 0 else 0
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
        
        return {
            "total_requests": self.total_requests,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_uploads": self.cache_uploads,
            "cache_hit_rate": f"{cache_hit_rate:.2f}%",
            "errors": self.errors,
            "total_bytes_cached": self.total_bytes_cached,
            "avg_response_time_ms": f"{avg_response_time:.2f}"
        }

stats = Stats()

# 请求模型
class TTSRequest(BaseModel):
    text: str = Field(..., description="要转换的文本")
    language: str = Field(default="zh", description="语言代码")
    voice: str = Field(default="longanran", description="音色 ID")
    format: str = Field(default="pcm", description="音频格式")
    sample_rate: int = Field(default=24000, description="采样率")

class TTSResponse(BaseModel):
    status: str
    message: str
    cache_hit: bool
    audio_size: Optional[int] = None
    duration_ms: Optional[float] = None

# 纯缓存服务不调用任何 TTS API
# 所有音频合成由主应用的 cosyvoice_unified_engine 负责

@app.on_event("startup")
async def startup_event():
    """启动时初始化"""
    logger.info("Starting TTS-Cache-CosyVoice service...")
    
    # 确保 MinIO bucket 存在
    try:
        if not minio_client.bucket_exists(config.BUCKET_NAME):
            minio_client.make_bucket(config.BUCKET_NAME)
            logger.info(f"Created MinIO bucket: {config.BUCKET_NAME}")
        
        # 设置 bucket 生命周期策略（跳过，因为 MinIO 版本兼容性问题）
        try:
            from minio.lifecycleconfig import LifecycleConfig, Rule, Expiration
            lifecycle_config = LifecycleConfig(
                [Rule(
                    rule_id="expire-old-cache",
                    status="Enabled",
                    expiration=Expiration(days=config.CACHE_EXPIRY_DAYS)
                )]
            )
            minio_client.set_bucket_lifecycle(config.BUCKET_NAME, lifecycle_config)
            logger.info(f"Set bucket lifecycle: {config.CACHE_EXPIRY_DAYS} days")
        except Exception as e:
            logger.warning(f"Could not set bucket lifecycle policy: {e}. Cache will not auto-expire.")
        
    except Exception as e:
        logger.error(f"MinIO initialization error: {e}")
        raise
    
    logger.info("TTS-Cache-CosyVoice service started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """关闭时清理"""
    logger.info("Shutting down TTS-Cache-CosyVoice service...")

@app.get("/", response_model=Dict[str, Any])
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "tts-cache-service",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "minio_connected": True  # 可以添加实际的 MinIO 连接检查
    }

@app.get("/metrics", response_model=Dict[str, Any])
async def get_metrics():
    """获取服务指标"""
    return stats.get_metrics()

@app.post("/speech/", response_class=Response)
async def synthesize_speech(request: TTSRequest, raw_request: Request):
    """缓存查询接口 - 只查询，不合成"""
    start_time = time.time()
    stats.total_requests += 1
    
    # 生成缓存键
    cache_key = hashlib.sha256(
        f"{request.text}:{request.voice}:{request.format}:{request.sample_rate}".encode()
    ).hexdigest()
    object_name = f"{cache_key}.{request.format}"
    
    # 检查缓存
    try:
        # 尝试从 MinIO 获取缓存
        response = minio_client.get_object(config.BUCKET_NAME, object_name)
        audio_data = response.read()
        response.close()
        response.release_conn()
        
        stats.cache_hits += 1
        
        # 计算响应时间
        response_time = (time.time() - start_time) * 1000
        stats.add_response_time(response_time)
        
        logger.info(f"Cache hit for text: {request.text[:50]}... (key: {cache_key})")
        
        return Response(
            content=audio_data,
            media_type=f"audio/{request.format}",
            headers={
                "X-Cache-Hit": "true",
                "X-Response-Time": f"{response_time:.2f}ms",
                "X-Audio-Format": request.format,
                "X-Sample-Rate": str(request.sample_rate)
            }
        )
        
    except S3Error as e:
        if e.code != "NoSuchKey":
            logger.error(f"MinIO error: {e}")
            stats.errors += 1
            raise HTTPException(status_code=500, detail="Cache storage error")
    
    # 缓存未命中，作为纯缓存服务，返回 404
    stats.cache_misses += 1
    logger.info(f"Cache miss for text: {request.text[:50]}... (key: {cache_key})")
    
    # 纯缓存服务不负责合成，返回 404 让主应用处理
    response_time = (time.time() - start_time) * 1000
    stats.add_response_time(response_time)
    
    raise HTTPException(
        status_code=404,
        detail="Cache miss - audio not found in cache",
        headers={
            "X-Cache-Hit": "false",
            "X-Response-Time": f"{response_time:.2f}ms",
            "X-Cache-Key": cache_key
        }
    )

from fastapi import UploadFile, File

@app.put("/cache/", response_model=Dict[str, Any])
async def upload_cache(
    text: str = Form(...),
    voice: str = Form(...),
    format: str = Form("pcm"),
    sample_rate: int = Form(24000),
    audio_data: UploadFile = File(...)
):
    """上传音频到缓存 - 供主应用在合成后写入"""
    # 读取上传文件的内容
    audio_bytes = await audio_data.read()
    
    # 生成缓存键
    cache_key = hashlib.sha256(
        f"{text}:{voice}:{format}:{sample_rate}".encode()
    ).hexdigest()
    object_name = f"{cache_key}.{format}"
    
    try:
        # 存入 MinIO
        minio_client.put_object(
            config.BUCKET_NAME,
            object_name,
            io.BytesIO(audio_bytes),
            len(audio_bytes),
            content_type=f"audio/{format}",
            metadata={
                "text_hash": cache_key,
                "voice": voice,
                "created_at": datetime.now().isoformat(),
                "text_length": str(len(text))
            }
        )
        
        # 更新统计
        stats.cache_uploads += 1
        stats.total_bytes_cached += len(audio_bytes)
        
        logger.info(f"Uploaded audio to cache: {cache_key}, size: {len(audio_bytes)} bytes")
        
        return {
            "status": "success",
            "cache_key": cache_key,
            "size": len(audio_bytes)
        }
    except Exception as e:
        logger.error(f"Failed to upload cache: {e}")
        raise HTTPException(status_code=500, detail=f"Cache upload failed: {str(e)}")

@app.delete("/cache/{cache_key}")
async def delete_cache_entry(cache_key: str):
    """删除特定缓存条目"""
    try:
        # 列出所有匹配的对象
        objects = minio_client.list_objects(config.BUCKET_NAME, prefix=cache_key)
        deleted_count = 0
        
        for obj in objects:
            minio_client.remove_object(config.BUCKET_NAME, obj.object_name)
            deleted_count += 1
            logger.info(f"Deleted cache object: {obj.object_name}")
        
        if deleted_count == 0:
            raise HTTPException(status_code=404, detail="Cache entry not found")
        
        return {
            "status": "success",
            "message": f"Deleted {deleted_count} cache entries",
            "cache_key": cache_key
        }
        
    except Exception as e:
        logger.error(f"Cache deletion error: {e}")
        raise HTTPException(status_code=500, detail="Cache deletion failed")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=22243)