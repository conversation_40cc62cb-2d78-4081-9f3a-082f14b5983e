"""播放列表管理器

服务器端权威管理的智能播放列表，支持动态QA插入和版本控制。

Author: Claude Code
Date: 2025-08-08
"""

import asyncio
import hashlib
import time
import uuid
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from datetime import datetime
from loguru import logger

from ..core.streaming_config import StreamingConfig
from ..core.config import cfg
from ..core.semantic_sentence_splitter import SemanticSentenceSplitter, SemanticSentence
from ..monitoring.qa_performance_monitor import get_qa_performance_monitor, QAInsertionResult as MonitorResult
from ..models.playlist_models import (
    PlaylistItem, ItemType, PlaylistStats, PlaylistModification,
    QAInsertionRequest, QAInsertionResult, QAInsertionStrategy,
    calculate_playlist_checksum, estimate_total_duration,
    group_qa_sequences, find_qa_sequence_bounds
)
from ..models.playable_interface import IPlayableItem, PlaybackStatus
from ..exceptions import InvalidPlaylistStateError

if TYPE_CHECKING:
    from .client_state_tracker import ClientStateTracker


class PlaylistManager:
    """
    服务器端播放列表管理器 - 唯一修改权威
    
    核心职责：
    1. 维护播放列表的完整性和一致性
    2. 处理QA插入请求，使用配置的策略计算插入位置
    3. 提供版本控制和修改历史追踪
    4. 支持原子性操作，确保并发安全
    """
    
    def __init__(self, config: StreamingConfig, main_content_player, semantic_splitter=None):
        """初始化播放列表管理器
        
        Args:
            config: 流媒体配置
            main_content_player: MainContentPlayer实例（显式依赖注入）
            semantic_splitter: SemanticSentenceSplitter实例（可选，用于避免重复初始化）
        """
        self.config = config
        self._main_content_player = main_content_player  # 显式依赖注入
        self._semantic_splitter = semantic_splitter  # 依赖注入的语义拆分器
        
        # 核心数据
        self._playlist: List[PlaylistItem] = []
        self._original_script: List[Any] = []  # 原始脚本引用
        
        # 并发控制
        self._lock = asyncio.Lock()
        
        # 版本控制
        self._version = 0
        self._modification_log: List[PlaylistModification] = []
        self._max_log_entries = 1000
        
        # 统计信息
        self._creation_time = datetime.utcnow()
        self._last_modified = datetime.utcnow()
        
        # 决策者模式新增状态
        self._current_playback_index = 0  # 当前播放位置
        self._last_completed_item_id: Optional[str] = None  # 最后完成的项目ID
        self._playback_history: List[tuple] = []  # 播放历史记录
        self._pending_qa_items: List[PlaylistItem] = []  # 待插入的QA项目
        self._has_pending_qa_flag = False  # 是否有待处理的QA
        
        # 🔥 暂停状态管理
        self._paused_state = False  # 是否暂停
        self._pause_reason: Optional[str] = None  # 暂停原因
        self._paused_at: Optional[datetime] = None  # 暂停时间
        
        # 支持的暂停状态枚举
        self.PAUSE_REASON_NO_CLIENTS = "NO_CLIENTS_CONNECTED"
        self.PAUSE_REASON_USER_REQUEST = "USER_REQUEST"
        self.PAUSE_REASON_QA_PRIORITY = "QA_PRIORITY"
        
        logger.info("播放列表管理器初始化完成")
        
    @property
    def version(self) -> int:
        """当前版本号"""
        return self._version
        
    @property
    def length(self) -> int:
        """播放列表长度"""
        return len(self._playlist)
        
    async def initialize_from_script(self, sentences: List[Any]) -> bool:
        """
        从原始脚本初始化播放列表，智能拆分长段落为短句
        
        Args:
            sentences: 原始脚本句子列表 (SentenceData objects or compatible)
            
        Returns:
            是否初始化成功
        """
        try:
            async with self._lock:
                if self._playlist:
                    logger.warning("播放列表已初始化，将覆盖现有内容")
                    
                self._original_script = sentences.copy()
                self._playlist = []
                
                # 使用注入的句子拆分器（避免重复初始化）
                splitter = self._semantic_splitter
                
                # 如果没有注入拆分器，则按需创建（保持向后兼容）
                if splitter is None:
                    # 从依赖注入获取单例
                    try:
                        from ..core.dependencies import get_semantic_splitter
                        splitter = get_semantic_splitter()
                        logger.info("📝 从依赖注入获取 SemanticSentenceSplitter 单例")
                    except ImportError:
                        # 降级处理：如果依赖注入不可用，创建新实例（仅用于测试）
                        logger.warning("⚠️ 依赖注入不可用，创建新的 SemanticSentenceSplitter 实例")
                        try:
                            splitter = SemanticSentenceSplitter()
                            logger.info("📝 句子拆分器初始化成功，将拆分长段落")
                        except Exception as e:
                            logger.warning(f"句子拆分器初始化失败: {e}，将使用原始内容")
                            splitter = False  # 标记为失败，避免重复尝试
                
                # 转换句子为播放列表项
                for i, sentence in enumerate(sentences):
                    # 验证输入对象是否有必要的属性
                    if not hasattr(sentence, 'text'):
                        logger.error(f"❌ 第{i}个对象缺少'text'属性: {type(sentence)}")
                        raise ValueError(f"Object at index {i} missing 'text' attribute")
                    
                    # 获取文本内容
                    text_content = sentence.text
                    if not text_content or not text_content.strip():
                        logger.warning(f"跳过第{i}个空内容项")
                        continue
                    
                    # 智能判断是否需要拆分（长度阈值：100字符）
                    SPLIT_THRESHOLD = 100
                    
                    if len(text_content) > SPLIT_THRESHOLD:
                        
                        # 如果拆分器可用，进行语义拆分
                        if splitter and splitter is not False:
                            try:
                                logger.debug(f"🔄 拆分长内容（{len(text_content)}字符）: {text_content[:50]}...")
                                semantic_sentences = splitter.split_sentences_semantic(text_content)
                                
                                # 为每个拆分后的句子创建播放项
                                for j, sem_sentence in enumerate(semantic_sentences):
                                    item = PlaylistItem.create_script_item(
                                        content=sem_sentence.text,
                                        original_index=getattr(sentence, 'index', i),
                                        sentence_type=getattr(sentence, 'type', 'normal')
                                    )
                                    
                                    # 添加语义分析的元数据
                                    item.metadata['split_from_original'] = True
                                    item.metadata['split_index'] = j
                                    item.metadata['semantic_confidence'] = sem_sentence.confidence
                                    item.metadata['estimated_duration_ms'] = sem_sentence.estimated_duration_ms
                                    
                                    # 如果原始句子有元数据，继承过来
                                    if hasattr(sentence, 'metadata') and isinstance(sentence.metadata, dict):
                                        item.metadata.update(sentence.metadata)
                                    
                                    self._playlist.append(item)
                                
                                logger.info(f"✅ 成功拆分为 {len(semantic_sentences)} 个语义句子")
                                continue  # 跳过原始内容的添加
                                
                            except Exception as e:
                                logger.warning(f"句子拆分失败: {e}，使用原始内容")
                                # 继续执行，使用原始内容
                    
                    # 对于短内容或拆分失败的情况，直接创建播放列表项
                    item = PlaylistItem.create_script_item(
                        content=text_content,
                        original_index=getattr(sentence, 'index', i),
                        sentence_type=getattr(sentence, 'type', 'normal')
                    )
                    
                    # 如果有额外的元数据，添加到项目中
                    if hasattr(sentence, 'metadata') and isinstance(sentence.metadata, dict):
                        item.metadata.update(sentence.metadata)
                    
                    self._playlist.append(item)
                    
                # 更新版本和日志
                self._version += 1
                self._last_modified = datetime.utcnow()
                
                # 统计拆分效果
                original_count = len(sentences)
                final_count = len(self._playlist)
                split_items = sum(1 for item in self._playlist if item.metadata.get('split_from_original', False))
                
                self._log_modification("initialize", {
                    "original_items_count": original_count,
                    "final_items_count": final_count,
                    "split_items_count": split_items,
                    "total_characters": sum(len(item.content) for item in self._playlist),
                    "estimated_duration_ms": estimate_total_duration(self._playlist),
                    "average_item_length": sum(len(item.content) for item in self._playlist) // final_count if final_count > 0 else 0
                })
                
                # 检查是否成功添加了内容
                if not self._playlist:
                    logger.error("❌ 播放列表初始化后为空")
                    return False
                
                logger.info(f"✅ 播放列表初始化完成: "
                          f"原始 {original_count} 项 → 最终 {final_count} 项 "
                          f"(拆分了 {split_items} 项), "
                          f"平均长度 {sum(len(item.content) for item in self._playlist) // final_count if final_count > 0 else 0} 字符, "
                          f"版本: {self._version}")
                return True
                
        except Exception as e:
            logger.error(f"播放列表初始化失败: {e}")
            return False
            
    async def handle_qa_event(self, qa_request: QAInsertionRequest, 
                            client_tracker: 'ClientStateTracker') -> QAInsertionResult:
        """
        处理QA事件 - 使用安全距离插入策略
        
        采用简化的稳健插入策略：
        - 直接在 current_playing_index + 配置偏移量 位置插入QA内容
        - 所有后续内容自然后移，确保无内容丢失
        - 避免复杂的客户端状态同步逻辑，确保系统稳定性
        
        Args:
            qa_request: QA插入请求
            client_tracker: 客户端状态追踪器（保留向后兼容，但不使用）
            
        Returns:
            QAInsertionResult: 包含插入的items和相关信息的结果对象
        """
        try:
            offset = cfg.qa_insertion_offset
            logger.info(f"收到QA事件: qa_id={qa_request.qa_id}, "
                      f"策略=安全距离插入, 偏移量={offset}")
            
            # 直接使用稳健的安全距离插入策略
            return await self._simple_insert_qa(qa_request)
            
        except Exception as e:
            logger.error(f"QA事件处理失败: {e}")
            return QAInsertionResult(
                items_inserted=[],
                items_for_pre_synthesis=[],
                insertion_index=-1,
                success=False,
                qa_id=qa_request.qa_id,
                error_message=f"QA事件处理失败: {str(e)}"
            )
            
    async def _smart_insert_with_reports(self, qa_request: QAInsertionRequest, 
                                       client_reports: List[Dict[str, Any]]) -> QAInsertionResult:
        """基于客户端报告执行智能插入"""
        try:
            # 🎯 调试日志：分析客户端报告
            logger.info(f"[QA智能插播] 收到{len(client_reports)}个客户端报告")
            for report in client_reports:
                logger.debug(f"  客户端{report['client_id']}: "
                           f"播放={report.get('playing_index')}, "
                           f"缓冲={report.get('buffered_indices', [])}")
            
            # 构建QA项目
            qa_items = qa_request.build_items()
            
            if not qa_items:
                logger.warning("QA请求没有生成有效的项目")
                return QAInsertionResult(
                    items_inserted=[],
                    items_for_pre_synthesis=[],
                    insertion_index=-1,
                    success=False,
                    qa_id=qa_request.qa_id,
                    error_message="QA请求没有生成有效的项目"
                )
            
            async with self._lock:
                # 分析所有客户端报告，确定"飞行中"内容
                in_flight_indices = self._analyze_in_flight_content(client_reports)
                logger.info(f"[QA智能插播] 识别出飞行中内容: {in_flight_indices}")
                
                # 确定插入位置（最保守的策略）
                if client_reports:
                    # 找到所有客户端中最小的播放索引
                    min_playing_index = min(
                        report.get('playing_index', 0) 
                        for report in client_reports
                    )
                    insert_after = min_playing_index + 1
                else:
                    # 没有客户端报告，使用当前播放索引
                    insert_after = self._current_playback_index + 1
                
                logger.info(f"[QA智能插播] 插入位置: {insert_after}")
                
                # 提取需要重新调度的内容
                items_to_reschedule = []
                indices_to_remove = []
                
                for idx in sorted(in_flight_indices):
                    if 0 <= idx < len(self._playlist):
                        item = self._playlist[idx]
                        items_to_reschedule.append(item)
                        indices_to_remove.append(idx)
                        logger.debug(f"[QA智能插播] 将重新调度: index={idx}, content={item.content[:50]}...")
                
                # 执行三明治式插入
                # 1. 先移除需要重新调度的项目（从后往前移除，避免索引变化）
                for idx in reversed(indices_to_remove):
                    del self._playlist[idx]
                    # 调整插入位置
                    if idx < insert_after:
                        insert_after -= 1
                
                # 2. 插入QA内容
                for i, qa_item in enumerate(qa_items):
                    self._playlist.insert(insert_after + i, qa_item)
                    logger.debug(f"[QA智能插播] 插入QA项目[{i}]: {qa_item.content}")
                
                # 3. 将"飞行中"内容插入到QA后面
                qa_end_position = insert_after + len(qa_items)
                for item in items_to_reschedule:
                    self._playlist.insert(qa_end_position, item)
                    logger.debug(f"[QA智能插播] 重新调度: {item.content[:50]}... 到位置 {qa_end_position}")
                    qa_end_position += 1
                
                # 更新版本
                self._version += 1
                
                logger.info(f"[QA智能插播] 完成: 插入{len(qa_items)}个QA项目, "
                          f"重新调度{len(items_to_reschedule)}个项目, 新版本={self._version}")
                
                # 广播播放列表更新
                try:
                    from ..core.dependencies import get_state_broadcaster
                    broadcaster = get_state_broadcaster()
                    if broadcaster:
                        await broadcaster.broadcast_item_inserted(
                            insert_index=insert_after,
                            items=[item.to_dict() for item in qa_items],
                            playlist_version=self._version,
                            reason="qa_with_reschedule",
                            qa_info={
                                "qa_id": qa_request.qa_id,
                                "question": qa_request.question,
                                "answer_preview": qa_request.answer[:100] + "..." if len(qa_request.answer) > 100 else qa_request.answer
                            },
                            rescheduled_items=[item.item_id for item in items_to_reschedule]
                        )
                        logger.info("📡 智能QA插入事件已广播到所有客户端")
                except Exception as broadcast_error:
                    logger.error(f"❌ 广播失败: {broadcast_error}")
                
                # 返回成功结果
                return QAInsertionResult(
                    items_inserted=qa_items,
                    items_for_pre_synthesis=qa_items,
                    insertion_index=insert_after,
                    success=True,
                    qa_id=qa_request.qa_id,
                    playlist_version=self._version
                )
                
        except Exception as e:
            logger.error(f"智能插入失败: {e}")
            return QAInsertionResult(
                items_inserted=[],
                items_for_pre_synthesis=[],
                insertion_index=-1,
                success=False,
                qa_id=qa_request.qa_id,
                error_message=f"智能插入失败: {str(e)}"
            )
    
    def _analyze_in_flight_content(self, client_reports: List[Dict[str, Any]]) -> set:
        """分析客户端报告，确定"飞行中"的内容索引"""
        in_flight_indices = set()
        
        for report in client_reports:
            # 获取每个客户端已缓冲的索引
            buffered = report.get('buffered_indices', [])
            in_flight_indices.update(buffered)
        
        return in_flight_indices
    
    async def _simple_insert_qa(self, qa_request: QAInsertionRequest) -> QAInsertionResult:
        """使用安全距离插入策略的QA插入方法，集成性能监控"""
        # 启动性能监控
        monitor = get_qa_performance_monitor()
        metric = monitor.start_qa_tracking(
            qa_id=qa_request.qa_id,
            question_text=qa_request.question,
            insertion_strategy=f"safe_distance_offset_{cfg.qa_insertion_offset}",
            session_id="",  # 可以后续集成session管理
            client_id=""    # 可以后续集成client管理
        )
        
        try:
            monitor.record_backend_processing_start(qa_request.qa_id)
            
            qa_items = qa_request.build_items()
            
            if not qa_items:
                # 记录失败结果
                monitor.finish_qa_tracking(
                    qa_request.qa_id, 
                    MonitorResult.FAILED,
                    error_message="QA请求没有生成有效的项目"
                )
                return QAInsertionResult(
                    items_inserted=[],
                    items_for_pre_synthesis=[],
                    insertion_index=-1,
                    success=False,
                    qa_id=qa_request.qa_id,
                    error_message="QA请求没有生成有效的项目"
                )
            
            async with self._lock:
                # 计算插入位置
                insert_index = self._calculate_qa_insertion_index()
                
                # 记录位置计算和潜在冲突监控
                potential_conflict = self._analyze_insertion_conflict(insert_index)
                monitor.record_position_calculation(
                    qa_request.qa_id,
                    requested_position=-1,  # 在安全距离策略中没有特定请求位置
                    calculated_position=insert_index,
                    final_position=insert_index,
                    confidence=0.95 if not potential_conflict else 0.7
                )
                
                # 记录插入开始时间
                playlist_insertion_start = time.time() * 1000
                
                # 直接插入
                for i, qa_item in enumerate(qa_items):
                    self._playlist.insert(insert_index + i, qa_item)
                
                # 更新版本
                self._version += 1
                
                # 记录插入时间
                playlist_insertion_end = time.time() * 1000
                monitor.record_playlist_insertion_timing(
                    qa_request.qa_id, 
                    playlist_insertion_end - playlist_insertion_start
                )
                
                logger.info(f"✅ QA安全距离插入完成: 位置={insert_index}, "
                          f"项目数={len(qa_items)}, 新版本={self._version}, "
                          f"潜在冲突={potential_conflict}")
                
                # 记录成功结果
                monitor.record_backend_processing_end(qa_request.qa_id)
                monitor.finish_qa_tracking(
                    qa_request.qa_id,
                    MonitorResult.SUCCESS,
                    sentences_inserted=len(qa_items),
                    playback_interruption=potential_conflict
                )
                
                # 返回结果
                return QAInsertionResult(
                    items_inserted=qa_items,
                    items_for_pre_synthesis=qa_items,
                    insertion_index=insert_index,
                    success=True,
                    qa_id=qa_request.qa_id,
                    playlist_version=self._version
                )
                
        except Exception as e:
            logger.error(f"安全距离插入失败: {e}")
            # 记录异常到监控系统
            monitor.finish_qa_tracking(
                qa_request.qa_id,
                MonitorResult.FAILED,
                error_message=f"安全距离插入失败: {str(e)}"
            )
            return QAInsertionResult(
                items_inserted=[],
                items_for_pre_synthesis=[],
                insertion_index=-1,
                success=False,
                qa_id=qa_request.qa_id,
                error_message=f"安全距离插入失败: {str(e)}"
            )
            
    async def _calculate_final_insertion_position(self, insertion_points: List[int], 
                                                 strategy: QAInsertionStrategy) -> int:
        """计算最终插入位置"""
        if not insertion_points:
            # 无活跃客户端，插入到末尾
            return len(self._playlist)
            
        if strategy == QAInsertionStrategy.MIN_POSITION:
            # 保守策略：所有客户端都能及时听到
            position = min(insertion_points)
            logger.debug(f"使用MIN策略: {insertion_points} -> {position}")
            
        elif strategy == QAInsertionStrategy.MAX_POSITION:
            # 激进策略：为最快客户端优化
            position = max(insertion_points)
            logger.debug(f"使用MAX策略: {insertion_points} -> {position}")
            
        elif strategy == QAInsertionStrategy.AVERAGE_POSITION:
            # 均衡策略：使用平均值
            position = int(sum(insertion_points) / len(insertion_points))
            logger.debug(f"使用AVERAGE策略: {insertion_points} -> {position}")
            
        elif strategy == QAInsertionStrategy.IMMEDIATE:
            # 立即插入策略
            position = min(insertion_points) + 1  # 下一个位置
            logger.debug(f"使用IMMEDIATE策略: {insertion_points} -> {position}")
            
        else:
            # 默认使用配置的策略
            if self.config.qa_insertion_strategy == "min":
                position = min(insertion_points)
            else:
                position = max(insertion_points)
                
            logger.debug(f"使用配置策略({self.config.qa_insertion_strategy}): "
                       f"{insertion_points} -> {position}")
            
        return position
        
    def _validate_insertion_position(self, position: int) -> int:
        """验证并调整插入位置"""
        # 确保位置在合法范围内
        position = max(0, min(position, len(self._playlist)))
        
        # 检查是否会插入到QA序列中间
        if 0 < position < len(self._playlist):
            item_before = self._playlist[position - 1]
            item_after = self._playlist[position] if position < len(self._playlist) else None
            
            # 如果前后都是QA相关项目且属于同一个qa_id，寻找更好的位置
            if (item_before.type.is_qa_related and item_after and 
                item_after.type.is_qa_related and
                item_before.metadata.get("qa_id") == item_after.metadata.get("qa_id")):
                
                # 寻找QA序列的边界
                qa_id = item_before.metadata.get("qa_id")
                start_idx, end_idx = find_qa_sequence_bounds(self._playlist, qa_id)
                
                if start_idx != -1:
                    # 插入到QA序列之后
                    position = end_idx + 1
                    logger.info(f"调整插入位置以避免分割QA序列: {position}")
                    
        return position
        
    async def _execute_insertion(self, position: int, items: List[PlaylistItem], 
                               details: Dict[str, Any]) -> bool:
        """执行原子性插入操作 - 🎯 使用预演-提交模式确保完整原子性"""
        # 🚀 Phase 2.2: Preview-Commit Pattern for Playlist Modifications
        
        # 第一阶段：预演 (Preview)
        preview_result = await self._preview_playlist_insertion(position, items, details)
        if not preview_result['success']:
            logger.error(f"播放列表插入预演失败: {preview_result['error']}")
            return False
        
        # 第二阶段：提交 (Commit) - 原子性执行所有操作
        try:
            # 2.1 播放列表插入
            original_playlist = self._playlist.copy()  # 备份原始播放列表
            original_version = self._version
            original_modified_time = self._last_modified
            
            self._playlist[position:position] = items
            self._version += 1
            self._last_modified = datetime.utcnow()
            
            logger.info(f"✅ 播放列表插入成功: 位置={position}, 项目数={len(items)}, 新版本={self._version}")
            
            # 2.2 MainContentPlayer插入（关键的原子性操作）
            player_success = await self._notify_main_content_player_insertion(items, details)
            
            if not player_success:
                # 🔄 MainContentPlayer插入失败，回滚播放列表
                logger.error("MainContentPlayer插入失败，执行播放列表回滚")
                self._playlist = original_playlist
                self._version = original_version
                self._last_modified = original_modified_time
                
                return False
            
            # 2.3 记录修改日志（只有在所有操作成功后才记录）
            self._log_modification("qa_insertion_atomic", {
                **details,
                "playlist_success": True,
                "player_success": True,
                "atomic_operation": True,
                "preview_validated": True
            })
            
            logger.info(f"🎯 原子性QA插入完全成功: qa_id={details.get('qa_id')}, "
                       f"播放列表+播放器都已更新")
            return True
            
        except Exception as e:
            logger.error(f"❌ 原子性插入操作失败: {e}")
            # 这里应该已经有了回滚逻辑，但为了安全再确认一次
            return False
    
    async def _preview_playlist_insertion(
        self, 
        position: int, 
        items: List[PlaylistItem], 
        details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """播放列表插入预演 - 🎭 Phase 2.2核心实现
        
        验证插入操作的可行性，不实际修改数据
        
        Args:
            position: 插入位置
            items: 要插入的项目
            details: 插入详情
            
        Returns:
            预演结果字典
        """
        preview_result = {
            "success": False,
            "error": None,
            "validations": [],
            "warnings": [],
            "estimated_impact": {}
        }
        
        try:
            logger.debug(f"🎭 开始播放列表插入预演: 位置={position}, 项目数={len(items)}")
            
            # 验证1: 插入位置有效性
            if position < 0 or position > len(self._playlist):
                preview_result["error"] = f"插入位置无效: {position} (有效范围: 0-{len(self._playlist)})"
                return preview_result
            
            preview_result["validations"].append("插入位置有效")
            
            # 验证2: 项目内容有效性
            valid_items = 0
            text_items = 0
            for item in items:
                if item and item.content:
                    valid_items += 1
                    if item.type == ItemType.QA_ANSWER:
                        text_items += 1
            
            if valid_items == 0:
                preview_result["error"] = "没有有效的插入项目"
                return preview_result
            
            preview_result["validations"].append(f"项目有效性检查通过: {valid_items}/{len(items)}项有效")
            
            # 验证3: MainContentPlayer可用性检查
            if self._main_content_player is None:
                preview_result["error"] = "MainContentPlayer不可用"
                return preview_result
            
            preview_result["validations"].append("MainContentPlayer可用")
            
            # 验证4: 提取句子内容验证
            sentences = []
            for item in items:
                if item.type == ItemType.QA_ANSWER:
                    if item.content and item.content.strip():
                        sentences.append(item.content.strip())
            
            if text_items > 0 and len(sentences) == 0:
                preview_result["warnings"].append("存在文本类型项目但无有效句子内容")
            
            # 计算影响估算
            preview_result["estimated_impact"] = {
                "new_playlist_length": len(self._playlist) + len(items),
                "items_to_insert": len(items),
                "text_sentences": len(sentences),
                "new_version": self._version + 1,
                "insertion_position": position,
                "existing_items_after_insertion": len(self._playlist) - position
            }
            
            logger.debug(f"🎭 播放列表插入预演成功: {len(preview_result['validations'])} 个验证通过, "
                        f"{len(preview_result['warnings'])} 个警告")
            
            preview_result["success"] = True
            return preview_result
            
        except Exception as e:
            preview_result["error"] = f"预演过程异常: {str(e)}"
            logger.error(f"❌ 播放列表插入预演异常: {e}")
            return preview_result
    
    async def _notify_main_content_player_insertion(
        self, 
        items: List[PlaylistItem], 
        details: Dict[str, Any]
    ) -> bool:
        """通知MainContentPlayer执行QA句子插入 - 🚀 使用增强Command Queue
        
        Args:
            items: 插入的播放列表项目
            details: 插入操作详情
            
        Returns:
            是否插入成功
        """
        try:
            # 提取文本句子（过滤掉非文本项目）
            sentences = []
            qa_metadata = {
                "qa_id": details.get("qa_id"),
                "insertion_strategy": details.get("strategy"),
                "playlist_position": details.get("final_position"),
                "timestamp": self._last_modified.timestamp()
            }
            
            for item in items:
                if item.type == ItemType.QA_ANSWER:
                    if item.content and item.content.strip():
                        sentences.append(item.content.strip())
                        logger.debug(f"提取QA句子: {item.type.value} - {item.content[:50]}...")
            
            if not sentences:
                logger.warning("没有找到可播放的QA文本内容")
                return True  # 不是错误，可能是纯音频或其他类型的QA
            
            logger.info(f"准备通知MainContentPlayer插入 {len(sentences)} 个QA句子")
            
            # 🚀 使用注入的MainContentPlayer实例并调用增强插入方法
            if self._main_content_player is None:
                logger.error("MainContentPlayer实例未注入")
                return False
            
            # 确定插入策略（映射到播放器的策略）
            insertion_strategy = "safe_after_current"  # 默认策略
            if details.get("strategy") == "immediate":
                insertion_strategy = "after_current_sentence"
            elif details.get("strategy") == "min":
                insertion_strategy = "safe_after_current"
            
            # 🎯 调用MainContentPlayer的增强QA插入接口
            result = await self._main_content_player.insert_qa_sentences(
                sentences=sentences,
                insertion_strategy=insertion_strategy,
                metadata=qa_metadata
            )
            
            logger.info(f"✅ MainContentPlayer QA插入完成: command_id={result['command_id']}, "
                       f"sentences={result['sentences_count']}, strategy={result['insertion_strategy']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ MainContentPlayer QA插入通知失败: {e}")
            return False
    
            
    async def get_item_at(self, index: int) -> Optional[PlaylistItem]:
        """
        获取指定索引的播放项
        
        Args:
            index: 索引位置
            
        Returns:
            播放项或None
        """
        async with self._lock:
            if 0 <= index < len(self._playlist):
                return self._playlist[index]
            return None
            
    async def get_playlist_segment(self, start_index: int, count: int) -> List[PlaylistItem]:
        """
        获取播放列表片段
        
        Args:
            start_index: 起始索引
            count: 获取数量
            
        Returns:
            播放项列表
        """
        async with self._lock:
            end_index = min(start_index + count, len(self._playlist))
            return self._playlist[start_index:end_index].copy()
    
    def _validate_playlist_state(self) -> None:
        """验证播放列表状态的前置条件（快速失败策略）
        
        Raises:
            InvalidPlaylistStateError: 当播放列表处于无效状态时
        """
        state_info = {
            "playlist_is_none": self._playlist is None,
            "playlist_type": type(self._playlist).__name__,
            "version": getattr(self, '_version', None),
            "last_modified": getattr(self, '_last_modified', None),
            "playlist_length": len(self._playlist) if self._playlist is not None else None
        }
        
        # 检查播放列表是否已初始化
        if self._playlist is None:
            raise InvalidPlaylistStateError(
                "Playlist is not initialized (playlist is None)",
                state_info=state_info
            )
        
        # 检查播放列表类型
        if not isinstance(self._playlist, list):
            raise InvalidPlaylistStateError(
                f"Playlist must be a list, got {type(self._playlist)}",
                state_info=state_info
            )
        
        # 检查版本号
        if not hasattr(self, '_version') or self._version <= 0:
            raise InvalidPlaylistStateError(
                f"Version must be positive, got {getattr(self, '_version', None)}",
                state_info=state_info
            )
        
        # 检查最后修改时间
        if not hasattr(self, '_last_modified') or self._last_modified is None:
            raise InvalidPlaylistStateError(
                "Last modified time must be set",
                state_info=state_info
            )
            
    async def get_playlist_info(self) -> PlaylistStats:
        """获取播放列表统计信息"""
        async with self._lock:
            # 统一前置条件验证（快速失败策略）
            self._validate_playlist_state()
            
            # 防御性检查 - 处理预期的运行时边界条件
            if not self._playlist:
                logger.warning("播放列表为空，返回默认统计信息")
                return PlaylistStats(
                    version=self._version,
                    total_items=0,
                    script_items=0,
                    qa_sequences=0,
                    total_estimated_duration_ms=0,
                    checksum="",
                    last_modified=self._last_modified
                )
            
            # 使用防御性调用辅助函数，确保任何一个失败都不影响整体
            try:
                qa_sequences_count = len(group_qa_sequences(self._playlist))
            except Exception as e:
                logger.error(f"计算QA序列数量失败: {e}", exc_info=True)
                qa_sequences_count = 0
                
            try:
                total_duration = estimate_total_duration(self._playlist)
            except Exception as e:
                logger.error(f"计算总时长失败: {e}", exc_info=True)
                total_duration = 0
                
            try:
                checksum = calculate_playlist_checksum(self._playlist)
            except Exception as e:
                logger.error(f"计算校验和失败: {e}", exc_info=True)
                checksum = f"error_at_{int(time.time())}"
                
            return PlaylistStats(
                version=self._version,
                total_items=len(self._playlist),
                script_items=len([item for item in self._playlist 
                                if item.type == ItemType.SCRIPT]),
                qa_sequences=qa_sequences_count,
                total_estimated_duration_ms=total_duration,
                checksum=checksum,
                last_modified=self._last_modified
            )
            
    async def get_current_length(self) -> int:
        """获取当前播放列表长度"""
        return len(self._playlist)
        
    async def find_qa_sequences(self) -> Dict[str, Dict[str, Any]]:
        """查找所有QA序列"""
        async with self._lock:
            qa_groups = group_qa_sequences(self._playlist)
            
            sequences = {}
            for qa_id, items in qa_groups.items():
                start_idx, end_idx = find_qa_sequence_bounds(self._playlist, qa_id)
                
                sequences[qa_id] = {
                    "qa_id": qa_id,
                    "items_count": len(items),
                    "start_index": start_idx,
                    "end_index": end_idx,
                    "items": [item.to_dict() for item in items],
                    "total_duration_ms": sum(item.estimate_duration_ms() for item in items)
                }
                
            return sequences
            
    async def remove_qa_sequence(self, qa_id: str) -> bool:
        """
        移除指定的QA序列
        
        Args:
            qa_id: QA标识符
            
        Returns:
            是否移除成功
        """
        try:
            async with self._lock:
                start_idx, end_idx = find_qa_sequence_bounds(self._playlist, qa_id)
                
                if start_idx == -1:
                    logger.warning(f"未找到QA序列: {qa_id}")
                    return False
                    
                # 移除序列
                removed_items = self._playlist[start_idx:end_idx + 1]
                del self._playlist[start_idx:end_idx + 1]
                
                # 更新版本和日志
                self._version += 1
                self._last_modified = datetime.utcnow()
                
                self._log_modification("qa_removal", {
                    "qa_id": qa_id,
                    "start_index": start_idx,
                    "end_index": end_idx,
                    "items_removed": len(removed_items)
                })
                
                logger.info(f"QA序列已移除: {qa_id}, 项目数: {len(removed_items)}")
                return True
                
        except Exception as e:
            logger.error(f"移除QA序列失败: {e}")
            return False
            
    async def get_modification_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取修改历史"""
        async with self._lock:
            recent_logs = self._modification_log[-limit:] if self._modification_log else []
            return [log.to_dict() for log in reversed(recent_logs)]
            
    async def export_playlist(self) -> Dict[str, Any]:
        """导出完整播放列表"""
        async with self._lock:
            return {
                "version": self._version,
                "created_at": self._creation_time.isoformat(),
                "last_modified": self._last_modified.isoformat(),
                "items": [item.to_dict() for item in self._playlist],
                "stats": (await self.get_playlist_info()).to_dict(),
                "qa_sequences": await self.find_qa_sequences(),
                "modification_history": await self.get_modification_history(20)
            }
            
    async def validate_integrity(self) -> Dict[str, Any]:
        """验证播放列表完整性"""
        async with self._lock:
            errors = []
            warnings = []
            
            # 检查基本结构
            if not self._playlist:
                warnings.append("播放列表为空")
                
            # 检查索引连续性
            script_indices = []
            for item in self._playlist:
                if item.type == ItemType.SCRIPT and item.original_index is not None:
                    script_indices.append(item.original_index)
                    
            script_indices.sort()
            for i in range(len(script_indices) - 1):
                if script_indices[i + 1] != script_indices[i] + 1:
                    warnings.append(f"脚本索引不连续: {script_indices[i]} -> {script_indices[i + 1]}")
                    
            # 检查QA序列完整性
            qa_sequences = group_qa_sequences(self._playlist)
            for qa_id, items in qa_sequences.items():
                has_answer = any(item.type == ItemType.QA_ANSWER for item in items)
                if not has_answer:
                    errors.append(f"QA序列缺少回答: {qa_id}")
                    
            # 检查内容哈希
            calculated_checksum = calculate_playlist_checksum(self._playlist)
            current_stats = await self.get_playlist_info()
            
            if calculated_checksum != current_stats.checksum:
                errors.append("播放列表校验和不匹配")
                
            return {
                "is_valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "stats": current_stats.to_dict(),
                "validation_time": datetime.utcnow().isoformat()
            }
            
    def _log_modification(self, action: str, details: Dict[str, Any]) -> None:
        """记录修改日志（在锁内调用）"""
        modification = PlaylistModification(
            timestamp=datetime.utcnow(),
            version=self._version,
            action=action,
            details=details
        )
        
        self._modification_log.append(modification)
        
        # 保持日志大小合理
        if len(self._modification_log) > self._max_log_entries:
            self._modification_log = self._modification_log[-self._max_log_entries//2:]
            
        logger.debug(f"记录修改日志: {action}, 版本: {self._version}")
        
    async def reset(self) -> None:
        """重置播放列表管理器"""
        async with self._lock:
            self._playlist.clear()
            self._original_script.clear()
            self._modification_log.clear()
            self._version = 0
            self._last_modified = datetime.utcnow()
            
        logger.info("播放列表管理器已重置")
        
    async def get_debug_info(self) -> Dict[str, Any]:
        """获取调试信息"""
        async with self._lock:
            return {
                "playlist_length": len(self._playlist),
                "version": self._version,
                "creation_time": self._creation_time.isoformat(),
                "last_modified": self._last_modified.isoformat(),
                "modification_log_size": len(self._modification_log),
                "original_script_size": len(self._original_script),
                "memory_usage_estimate_bytes": self._estimate_memory_usage(),
                "recent_items": [
                    item.get_display_info() 
                    for item in self._playlist[-5:] if self._playlist
                ],
                "qa_sequences_count": len(group_qa_sequences(self._playlist))
            }
            
    def _estimate_memory_usage(self) -> int:
        """估算内存使用量（字节）"""
        # 粗略估算
        base_size = 0
        for item in self._playlist:
            base_size += len(item.content.encode('utf-8'))
            base_size += len(str(item.metadata))
            base_size += 200  # 对象开销
            
        return base_size
    
    # ========== 决策者模式新增方法 ==========
    
    async def get_next_item_to_play(self, status: Optional[PlaybackStatus] = None) -> Optional[IPlayableItem]:
        """根据播放状态决定下一个要播放的项目
        
        这是决策者模式的核心方法。根据当前播放状态、待处理的QA、
        以及配置的策略，决定下一个应该播放的内容。
        
        Args:
            status: 上一个项目的播放状态（可选）
            
        Returns:
            下一个要播放的项目，如果播放列表结束则返回 None
        """
        async with self._lock:
            # 🔥 1. 首先检查暂停状态
            if self._paused_state:
                logger.debug(f"⏸️ 播放列表暂停中，原因: {self._pause_reason}")
                return None
            
            # 2. 更新内部播放指针（如果有状态）
            if status:
                await self._update_playback_pointer(status)
            
            # 3. 检查是否有高优先级QA需要插入
            if self._has_pending_qa() and await self._is_good_insertion_point():
                qa_item = self._get_next_qa_item()
                if qa_item:
                    logger.info(f"🎤 决策：插入QA项目 {qa_item.item_id}")
                    return qa_item
            
            # 4. 获取下一个主内容项
            if self._current_playback_index < len(self._playlist):
                main_item = self._playlist[self._current_playback_index]
                logger.debug(f"📄 决策：播放主内容 {main_item.item_id} (位置 {self._current_playback_index})")
                return main_item
            
            # 5. 播放列表结束
            logger.info("🏁 播放列表已结束")
            return None
    
    async def notify_playback_completed(self, item: IPlayableItem) -> None:
        """通知播放完成
        
        记录项目播放成功完成，更新内部状态。
        
        Args:
            item: 已完成播放的项目
        """
        async with self._lock:
            self._last_completed_item_id = item.item_id
            self._playback_history.append((item.item_id, 'completed', datetime.utcnow()))
            
            # 如果是主内容，更新播放索引
            if item.item_type == "script":
                self._current_playback_index += 1
            
            # 如果是QA项目，从待处理列表中移除
            if item.item_type == "qa_answer":
                self._pending_qa_items = [
                    qa for qa in self._pending_qa_items 
                    if qa.item_id != item.item_id
                ]
                # 如果QA序列播放完成，清除标志
                if not self._pending_qa_items:
                    self._has_pending_qa_flag = False
            
            logger.info(f"✅ 播放完成通知: {item.item_id} (类型: {item.item_type})")
    
    async def notify_playback_failed(self, item: IPlayableItem, error: Exception) -> None:
        """通知播放失败
        
        记录播放失败，可以实现重试逻辑或跳过策略。
        
        Args:
            item: 播放失败的项目
            error: 失败原因
        """
        async with self._lock:
            self._playback_history.append((item.item_id, 'failed', datetime.utcnow(), str(error)))
            logger.error(f"❌ 播放失败通知: {item.item_id}, 错误: {error}")
            
            # 根据配置决定是否跳过失败的项目
            # 这里简单处理：主内容失败则跳过，QA失败则重试
            if item.item_type == "script":
                self._current_playback_index += 1
                logger.warning(f"跳过失败的主内容项目: {item.item_id}")
    
    async def _update_playback_pointer(self, status: PlaybackStatus) -> None:
        """根据播放状态更新内部指针"""
        if status.is_success and status.last_played_item_id:
            # 查找刚播放完的项目在列表中的位置
            for i, item in enumerate(self._playlist):
                if item.item_id == status.last_played_item_id:
                    # 只有主内容才更新索引
                    if item.type == ItemType.SCRIPT:
                        self._current_playback_index = i + 1
                    break
    
    async def update_playback_index(self, index: int) -> None:
        """更新当前播放索引（由 StreamingContentProvider 调用）
        
        Args:
            index: 当前正在请求/播放的内容索引
        """
        async with self._lock:
            old_index = self._current_playback_index
            self._current_playback_index = index
            logger.info(f"📍 Playback index updated: {old_index} → {index}")
            
            # # 🎯 调试日志：播放索引更新时打印详细信息
            # if 0 <= index < len(self._playlist):
            #     current_item = self._playlist[index]
            #     logger.debug(f"[QA调试-索引更新] 播放索引更新: {old_index} → {index}")
            #     logger.debug(f"[QA调试-索引更新] 新播放句子: {current_item.content[:100]}...")
            #     logger.debug(f"[QA调试-索引更新] 句子类型: {current_item.type.value}")
    
    def _has_pending_qa(self) -> bool:
        """检查是否有待处理的QA"""
        return self._has_pending_qa_flag and len(self._pending_qa_items) > 0
    
    async def _is_good_insertion_point(self) -> bool:
        """判断当前是否是好的QA插入时机
        
        考虑因素：
        1. 当前不在QA序列中间
        2. 根据策略判断时机（immediate, min, max等）
        3. 客户端缓冲状态
        """
        # 检查是否在QA序列中间
        if self._current_playback_index > 0 and self._current_playback_index < len(self._playlist):
            current = self._playlist[self._current_playback_index]
            if self._current_playback_index > 0:
                prev = self._playlist[self._current_playback_index - 1]
                # 如果前一个是QA项目且属于同一个qa_id，说明在序列中间
                if (prev.type.is_qa_related and current.type.is_qa_related and
                    prev.metadata.get("qa_id") == current.metadata.get("qa_id")):
                    return False
        
        # 根据策略判断
        if self.config.qa_insertion_strategy == "immediate":
            return True  # 立即插入
        elif self.config.qa_insertion_strategy == "min":
            # 保守策略：确保所有客户端都准备好
            # 这里简化处理，实际需要考虑客户端状态
            return True
        else:
            return True
    
    def _get_next_qa_item(self) -> Optional[PlaylistItem]:
        """获取下一个待播放的QA项目"""
        if self._pending_qa_items:
            return self._pending_qa_items[0]
        return None
    
    def _calculate_qa_insertion_index(self) -> int:
        """
        计算QA插入位置的索引
        
        插入规则：
        - 标准情况：current_playing_index + 配置偏移量（安全距离插入）
        - 播放结束：插入到列表末尾
        - 播放未开始：插入到开头（成为首播项）
        
        配置偏移量说明：
        - offset=1: 激进模式，可能需要客户端丢弃已缓冲内容
        - offset=2: 稳定模式，确保安全距离，无内容丢失风险
        
        Returns:
            QA应该插入的索引位置
        """
        # 获取配置的偏移量
        offset = cfg.qa_insertion_offset
        
        # # 🎯 调试日志：打印计算插入位置时的状态
        # logger.debug(f"[QA调试-计算] 开始计算QA插入位置...")
        # logger.debug(f"[QA调试-计算] 当前播放索引: {self._current_playback_index}")
        # logger.debug(f"[QA调试-计算] 播放列表长度: {len(self._playlist)}")
        # logger.debug(f"[QA调试-计算] 配置偏移量: {offset}")
        
        # 如果播放列表为空，插入到开头
        if len(self._playlist) == 0:
            logger.debug("[QA调试-计算] 空播放列表，QA插入到索引0")
            return 0
        
        # 如果播放未开始（索引为0且未开始播放）
        if self._current_playback_index == 0:
            logger.debug(f"[QA调试-计算] 播放未开始(index=0)，QA插入到索引1（保留开场）")
            return 1
        
        # 如果播放即将结束或已结束
        if self._current_playback_index >= len(self._playlist):
            logger.debug(f"[QA调试-计算] 播放已结束(index>={len(self._playlist)})，QA插入到末尾（索引{len(self._playlist)}）")
            return len(self._playlist)
        
        # 标准情况：插入到当前播放位置 + 配置偏移量（安全距离）
        insert_index = self._current_playback_index + offset
        logger.debug(f"[QA调试-计算] 标准插入：当前索引={self._current_playback_index}, 偏移量={offset}, QA将插入到索引{insert_index}")
        return insert_index
    
    def _analyze_insertion_conflict(self, insert_index: int) -> bool:
        """
        分析插入点是否可能与客户端缓冲冲突
        
        Args:
            insert_index: 计算出的插入位置
            
        Returns:
            bool: True if 可能存在冲突, False if 相对安全
        """
        offset = cfg.qa_insertion_offset
        current_index = self._current_playback_index
        
        # 分析冲突可能性
        # offset=1: 插入到 current+1，客户端很可能已缓冲该位置
        # offset=2: 插入到 current+2，相对安全，但仍有小概率冲突
        
        if offset <= 1:
            # 激进模式，高冲突概率
            potential_conflict = True
            logger.debug(f"[冲突分析] 激进模式 offset={offset}, 高冲突概率")
        else:
            # 稳定模式，低冲突概率，但取决于客户端缓冲策略
            gap = insert_index - current_index
            # 当gap >= offset时认为相对安全，gap < offset时有冲突风险
            potential_conflict = gap < offset
            logger.debug(f"[冲突分析] 稳定模式 offset={offset}, gap={gap}, "
                       f"冲突概率={'高' if potential_conflict else '低'}")
        
        # 记录分析结果
        logger.info(f"🔍 插入冲突分析: current={current_index}, "
                  f"insert={insert_index}, offset={offset}, "
                  f"potential_conflict={potential_conflict}")
        
        return potential_conflict
    
    async def set_pending_qa(self, qa_items: List[PlaylistItem]) -> None:
        """设置待处理的QA项目（用于QA事件处理）"""
        async with self._lock:
            self._pending_qa_items = qa_items
            self._has_pending_qa_flag = True
            logger.info(f"设置待处理QA: {len(qa_items)} 个项目")
    
    # ========== 暂停/恢复机制 ==========
    
    async def pause(self, reason: str = "USER_REQUEST") -> bool:
        """暂停播放列表
        
        Args:
            reason: 暂停原因
            
        Returns:
            是否成功暂停
        """
        async with self._lock:
            if self._paused_state:
                logger.warning(f"播放列表已经暂停，原因: {self._pause_reason}")
                return False
            
            self._paused_state = True
            self._pause_reason = reason
            self._paused_at = datetime.utcnow()
            
            logger.info(f"⏸️ 播放列表已暂停，原因: {reason}")
            return True
    
    async def resume(self) -> bool:
        """恢复播放列表
        
        Returns:
            是否成功恢复
        """
        async with self._lock:
            if not self._paused_state:
                logger.warning("播放列表未暂停，无需恢复")
                return False
            
            pause_duration = (datetime.utcnow() - self._paused_at).total_seconds() if self._paused_at else 0
            logger.info(f"▶️ 播放列表恢复，暂停时长: {pause_duration:.1f}秒，原因: {self._pause_reason}")
            
            self._paused_state = False
            self._pause_reason = None
            self._paused_at = None
            
            return True
    
    async def pause_due_to_no_clients(self) -> bool:
        """因无客户端连接而暂停
        
        这是专门为"幽灵播放"问题设计的接口。
        当所有客户端断开时，AudioStreamingProxy 会调用此方法。
        
        Returns:
            是否成功暂停
        """
        return await self.pause(self.PAUSE_REASON_NO_CLIENTS)
    
    def is_paused(self) -> bool:
        """检查是否处于暂停状态
        
        Returns:
            是否暂停
        """
        return self._paused_state
    
    def get_pause_info(self) -> Dict[str, Any]:
        """获取暂停信息
        
        Returns:
            暂停相关信息
        """
        if not self._paused_state:
            return {"paused": False}
        
        return {
            "paused": True,
            "reason": self._pause_reason,
            "paused_at": self._paused_at.isoformat() if self._paused_at else None,
            "duration_seconds": (datetime.utcnow() - self._paused_at).total_seconds() if self._paused_at else 0
        }


# 工厂函数已废弃 - 请使用 core.dependencies.get_playlist_manager()