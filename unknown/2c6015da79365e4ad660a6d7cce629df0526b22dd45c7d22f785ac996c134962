"""Redis分布式锁管理器 - QA并发冲突解决方案

实现带有看门狗续期机制的分布式锁，用于防止QA请求的并发竞争。
重要: 不提供任何fallback机制，Redis不可用时直接失败。

Author: Claude Code  
Date: 2025-01-08 (重构移除fallback)
"""

import asyncio
import time
from typing import Optional, Dict, Any
from loguru import logger

from .lock_manager import AbstractLockManager
from ..core.config import cfg

# Redis导入失败时直接抛出异常
try:
    import redis.asyncio as redis
except ImportError:
    raise ImportError(
        "Redis库未安装。如需使用RedisLockManager，请安装: pip install redis>=5.0.0"
    )


class RedisLockManager(AbstractLockManager):
    """Redis分布式锁管理器 - 带看门狗续期机制"""
    
    def __init__(self, redis_url: str = None):
        """初始化Redis锁管理器
        
        Args:
            redis_url: Redis连接URL，如果为None则从配置中获取
        """
        self.redis_url = redis_url or cfg.get('redis_url', 'redis://localhost:6379/0')
        self.redis_client: Optional[redis.Redis] = None
        self.active_locks: Dict[str, Dict[str, Any]] = {}
        self.watchdog_tasks: Dict[str, asyncio.Task] = {}
        
        # 锁配置
        self.default_timeout = 60  # 60秒默认超时
        self.watchdog_interval = 20  # 20秒续期间隔
        self.renewal_duration = 30  # 每次续期30秒
        
        logger.info(f"🔒 RedisLockManager initialized with URL: {self.redis_url}")
    
    async def initialize(self) -> None:
        """初始化Redis连接
        
        Raises:
            RuntimeError: Redis连接失败时抛出异常
        """
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("✅ Redis连接成功建立")
            
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            raise RuntimeError(f"无法连接到Redis服务器 {self.redis_url}: {e}")
    
    async def acquire_lock(
        self,
        lock_key: str,
        holder_id: str,
        timeout_seconds: int = None,
        enable_watchdog: bool = True
    ) -> bool:
        """获取分布式锁
        
        Args:
            lock_key: 锁的唯一标识
            holder_id: 锁持有者ID
            timeout_seconds: 锁超时时间
            enable_watchdog: 是否启用看门狗自动续期
            
        Returns:
            bool: 是否成功获取锁
        """
        timeout_seconds = timeout_seconds or self.default_timeout
        
        if not self.redis_client:
            raise RuntimeError("Redis客户端未初始化")
        
        try:
            # 使用Redis分布式锁
            success = await self._acquire_redis_lock(lock_key, holder_id, timeout_seconds)
            
            if success and enable_watchdog:
                # 启动看门狗任务
                await self._start_watchdog(lock_key, holder_id)
                logger.debug(f"🐕 看门狗已启动: lock_key={lock_key}")
            
            if success:
                logger.info(f"🔒 锁获取成功: {lock_key} (holder: {holder_id}, timeout: {timeout_seconds}s)")
            else:
                logger.warning(f"⏰ 锁获取失败，已被占用: {lock_key}")
                
            return success
            
        except Exception as e:
            logger.error(f"❌ 锁获取异常: {lock_key}, error: {e}")
            return False
    
    async def release_lock(self, lock_key: str, holder_id: str) -> bool:
        """释放分布式锁
        
        Args:
            lock_key: 锁的唯一标识
            holder_id: 锁持有者ID
            
        Returns:
            bool: 是否成功释放锁
        """
        try:
            # 停止看门狗
            await self._stop_watchdog(lock_key)
            
            if not self.redis_client:
                raise RuntimeError("Redis客户端未初始化")
            
            success = await self._release_redis_lock(lock_key, holder_id)
            
            if success:
                logger.info(f"🔓 锁释放成功: {lock_key} (holder: {holder_id})")
            else:
                logger.warning(f"⚠️ 锁释放失败: {lock_key} (holder: {holder_id})")
                
            return success
            
        except Exception as e:
            logger.error(f"❌ 锁释放异常: {lock_key}, error: {e}")
            return False
    
    async def _acquire_redis_lock(self, lock_key: str, holder_id: str, timeout_seconds: int) -> bool:
        """Redis分布式锁实现"""
        lua_script = """
        if redis.call("get", KEYS[1]) == false then
            return redis.call("setex", KEYS[1], ARGV[2], ARGV[1])
        else
            return false
        end
        """
        
        result = await self.redis_client.eval(
            lua_script,
            1,  # 键数量
            lock_key,  # KEYS[1]
            holder_id,  # ARGV[1] 
            timeout_seconds  # ARGV[2]
        )
        
        if result:
            self.active_locks[lock_key] = {
                'holder_id': holder_id,
                'acquired_at': time.time(),
                'timeout_seconds': timeout_seconds
            }
            
        return bool(result)
    
    async def _release_redis_lock(self, lock_key: str, holder_id: str) -> bool:
        """Redis锁释放实现"""
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        
        result = await self.redis_client.eval(
            lua_script,
            1,  # 键数量
            lock_key,  # KEYS[1]
            holder_id   # ARGV[1]
        )
        
        if result:
            self.active_locks.pop(lock_key, None)
            
        return bool(result)
    
    
    async def _start_watchdog(self, lock_key: str, holder_id: str):
        """启动看门狗任务"""
        if lock_key in self.watchdog_tasks:
            # 已有看门狗任务，先停止
            await self._stop_watchdog(lock_key)
        
        async def watchdog_loop():
            """看门狗循环"""
            try:
                while lock_key in self.active_locks:
                    await asyncio.sleep(self.watchdog_interval)
                    
                    # 检查锁是否仍然活跃
                    if lock_key not in self.active_locks:
                        break
                    
                    # 续期锁
                    success = await self._renew_lock(lock_key, holder_id)
                    if success:
                        logger.debug(f"🐕 看门狗续期成功: {lock_key}")
                    else:
                        logger.warning(f"⚠️ 看门狗续期失败: {lock_key}")
                        break
                        
            except asyncio.CancelledError:
                logger.debug(f"🐕 看门狗任务被取消: {lock_key}")
            except Exception as e:
                logger.error(f"❌ 看门狗任务异常: {lock_key}, error: {e}")
            finally:
                # 清理
                self.watchdog_tasks.pop(lock_key, None)
        
        task = asyncio.create_task(watchdog_loop())
        self.watchdog_tasks[lock_key] = task
    
    async def _stop_watchdog(self, lock_key: str):
        """停止看门狗任务"""
        if lock_key in self.watchdog_tasks:
            task = self.watchdog_tasks.pop(lock_key)
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
    
    async def _renew_lock(self, lock_key: str, holder_id: str) -> bool:
        """续期锁"""
        if not self.redis_client:
            raise RuntimeError("Redis客户端未初始化")
            
        try:
            # Redis续期
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("expire", KEYS[1], ARGV[2])
            else
                return 0
            end
            """
            
            result = await self.redis_client.eval(
                lua_script,
                1,  # 键数量
                lock_key,  # KEYS[1]
                holder_id,  # ARGV[1]
                self.renewal_duration  # ARGV[2]
            )
            
            return bool(result)
                
        except Exception as e:
            logger.error(f"❌ 锁续期失败: {lock_key}, error: {e}")
            return False
    
    def get_lock_info(self, lock_key: str) -> Optional[Dict[str, Any]]:
        """获取锁信息"""
        return self.active_locks.get(lock_key)
    
    def get_active_locks_count(self) -> int:
        """获取活跃锁数量"""
        return len(self.active_locks)
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 停止所有看门狗任务
            for lock_key in list(self.watchdog_tasks.keys()):
                await self._stop_watchdog(lock_key)
            
            # 释放所有活跃锁
            for lock_key, lock_info in list(self.active_locks.items()):
                await self.release_lock(lock_key, lock_info['holder_id'])
            
            # 关闭Redis连接
            if self.redis_client:
                await self.redis_client.close()
                
            logger.info("🧹 RedisLockManager cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ RedisLockManager cleanup error: {e}")


# 全局锁管理器实例
_lock_manager: Optional[RedisLockManager] = None


async def get_redis_lock_manager() -> RedisLockManager:
    """获取全局Redis锁管理器实例
    
    注意: 调用者必须确保Redis可用，否则会抛出异常
    """
    global _lock_manager
    
    if _lock_manager is None:
        _lock_manager = RedisLockManager()
        await _lock_manager.initialize()  # 失败时会抛出异常
    
    return _lock_manager


class DistributedLock:
    """分布式锁上下文管理器"""
    
    def __init__(
        self,
        lock_key: str,
        holder_id: str = None,
        timeout_seconds: int = 60,
        enable_watchdog: bool = True
    ):
        self.lock_key = lock_key
        self.holder_id = holder_id or f"holder_{int(time.time() * 1000)}"
        self.timeout_seconds = timeout_seconds
        self.enable_watchdog = enable_watchdog
        self.lock_manager: Optional[RedisLockManager] = None
        self.acquired = False
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.lock_manager = await get_redis_lock_manager()
        
        self.acquired = await self.lock_manager.acquire_lock(
            self.lock_key,
            self.holder_id,
            self.timeout_seconds,
            self.enable_watchdog
        )
        
        if not self.acquired:
            raise RuntimeError(f"Failed to acquire lock: {self.lock_key}")
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.acquired and self.lock_manager:
            await self.lock_manager.release_lock(self.lock_key, self.holder_id)


# 使用示例
async def example_usage():
    """分布式锁使用示例"""
    
    # 方式1：使用上下文管理器（推荐）
    try:
        async with DistributedLock("qa_processing:session_123", timeout_seconds=60) as lock:
            logger.info("🔒 获取到QA处理锁，开始处理...")
            # 执行需要加锁的QA处理逻辑
            await asyncio.sleep(2)  # 模拟处理时间
            logger.info("✅ QA处理完成")
    except RuntimeError as e:
        logger.error(f"❌ 获取锁失败: {e}")
    
    # 方式2：手动管理
    lock_manager = await get_redis_lock_manager()
    lock_key = "qa_processing:session_456"
    holder_id = "qa_handler_001"
    
    if await lock_manager.acquire_lock(lock_key, holder_id):
        try:
            logger.info("🔒 手动获取锁成功")
            # 处理逻辑
            await asyncio.sleep(1)
        finally:
            await lock_manager.release_lock(lock_key, holder_id)
    else:
        logger.warning("⏰ 锁被占用，跳过处理")


if __name__ == "__main__":
    asyncio.run(example_usage())