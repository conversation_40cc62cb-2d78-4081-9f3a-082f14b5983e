#!/usr/bin/env python3
"""
SemanticSentenceSplitter - 基于语义分析的智能句子拆分器

使用先进的NLP工具进行语义感知的句子分割，专门优化中文文本处理：
- spaCy中文模型进行精确句子边界检测
- jieba结合语义上下文的智能分词
- 严格Fail-Fast原则，无简单正则表达式fallback
- 针对直播TTS场景优化的句子长度和语义完整性平衡

Author: Claude Code
Date: 2025-08-04
"""

import re
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import jieba
import spacy
from loguru import logger


class SentenceType(Enum):
    """句子类型枚举"""
    DECLARATIVE = "declarative"  # 陈述句
    INTERROGATIVE = "interrogative"  # 疑问句  
    EXCLAMATORY = "exclamatory"  # 感叹句
    IMPERATIVE = "imperative"  # 祈使句


@dataclass
class SemanticSentence:
    """语义句子数据结构"""
    text: str
    sentence_type: SentenceType
    confidence: float  # 语义分析置信度
    word_count: int
    char_count: int
    estimated_duration_ms: int  # 预估播放时长(毫秒)
    semantic_break_strength: float  # 语义断句强度 (0.0-1.0)


class SemanticSentenceSplitter:
    """基于语义分析的智能句子拆分器 - 严格Fail-Fast，无简单fallback"""
    
    # 语义分析配置常量 - 调整后的参数
    MIN_SENTENCE_CHARS = 5  # 最小句子字符数（降低最小长度要求）
    MAX_SENTENCE_CHARS = 120  # 最大句子字符数（适当提高，给 spaCy 更多空间）
    OPTIMAL_SENTENCE_CHARS = 50  # 最优句子字符数（保持不变）
    MIN_SEMANTIC_CONFIDENCE = 0.3  # 最小语义置信度阈值（降低置信度要求）
    
    def __init__(self):
        """初始化语义分析器 - Fail-Fast初始化"""
        logger.info("🧠 Initializing Semantic Sentence Splitter with NLP models...")
        
        # 初始化spaCy中文模型
        try:
            self.nlp = spacy.load("zh_core_web_sm")
            logger.info("✅ spaCy Chinese model loaded successfully")
        except OSError as e:
            raise RuntimeError(f"Failed to load spaCy Chinese model 'zh_core_web_sm': {e}. Please run: python -m spacy download zh_core_web_sm") from e
        except Exception as e:
            raise RuntimeError(f"spaCY model initialization failed: {e}") from e
        
        # 初始化jieba分词器
        try:
            # 预加载jieba词典以提高性能
            jieba.initialize()
            logger.info("✅ Jieba tokenizer initialized successfully")
        except Exception as e:
            raise RuntimeError(f"Jieba tokenizer initialization failed: {e}") from e
        
        # 语义分析缓存
        self._sentence_cache: Dict[str, List[SemanticSentence]] = {}
        
        logger.info("🧠 Semantic Sentence Splitter ready with advanced NLP capabilities")
    
    def split_sentences_semantic(self, content: str) -> List[SemanticSentence]:
        """基于语义分析的智能句子拆分 - 核心方法
        
        Args:
            content: 需要拆分的文本内容
            
        Returns:
            List[SemanticSentence]: 语义句子列表
            
        Raises:
            ValueError: 内容为空或无效时抛出
            RuntimeError: 语义分析失败时抛出
        """
        if not content or not content.strip():
            raise ValueError("Content cannot be empty for semantic analysis")
        
        content = content.strip()
        
        # 检查缓存
        cache_key = self._generate_cache_key(content)
        if cache_key in self._sentence_cache:
            logger.debug(f"🎯 Cache hit for content semantic analysis")
            return self._sentence_cache[cache_key]
        
        logger.info(f"🧠 Starting semantic sentence analysis for {len(content)} characters")
        
        try:
            # Step 1: spaCy句子边界检测
            spacy_sentences = self._extract_spacy_sentences(content)
            logger.debug(f"📝 spaCy detected {len(spacy_sentences)} initial sentences")
            
            # Step 2: 语义优化和重新分割
            optimized_sentences = self._optimize_sentence_boundaries(spacy_sentences)
            logger.debug(f"🎯 Optimized to {len(optimized_sentences)} semantic sentences")
            
            # Step 3: 语义句子对象构建
            semantic_sentences = self._build_semantic_sentences(optimized_sentences)
            
            # Step 4: 质量验证
            self._validate_semantic_sentences(semantic_sentences)
            
            # 缓存结果
            self._sentence_cache[cache_key] = semantic_sentences
            
            logger.info(f"✅ Semantic analysis completed: {len(semantic_sentences)} high-quality sentences")
            return semantic_sentences
            
        except Exception as e:
            logger.error(f"❌ Semantic sentence splitting failed: {e}")
            raise RuntimeError(f"Semantic analysis failed: {e}") from e
    
    def _extract_spacy_sentences(self, content: str) -> List[str]:
        """使用spaCy进行句子边界检测 - 增强版"""
        try:
            doc = self.nlp(content)
            sentences = []
            
            for sent in doc.sents:
                text = sent.text.strip()
                if not text:
                    continue
                
                # 只对超长句子或包含多个逗号的复杂句子进行分割
                if len(text) > self.MAX_SENTENCE_CHARS or (len(text) > 80 and text.count('，') >= 3):
                    sub_sentences = self._intelligent_comma_split(text)
                    sentences.extend(sub_sentences)
                else:
                    sentences.append(text)
            
            if not sentences:
                raise ValueError("spaCy failed to detect any sentences")
            
            return sentences
            
        except Exception as e:
            raise RuntimeError(f"spaCy sentence detection failed: {e}") from e
    
    def _intelligent_comma_split(self, sentence: str) -> List[str]:
        """基于连词和语境的智能逗号分割"""
        
        # 强分割信号：这些连词前的逗号几乎总是句子边界
        STRONG_SPLIT_CONJUNCTIONS = [
            "但是", "但", "然而", "可是", "不过", "只是",
            "所以", "因此", "因而", "于是",
            "而且", "并且", "另外", "此外", "况且",
            "总之", "总而言之", "最后"
        ]
        
        # 弱分割信号：需要结合上下文
        WEAK_SPLIT_CONJUNCTIONS = ["而", "也", "还", "又"]
        
        # 代词开头（如果前面部分够长）
        PRONOUN_STARTS = ["你", "我", "他", "她", "它", "这", "那"]
        
        # 查找所有逗号位置
        parts = sentence.split('，')
        if len(parts) <= 1:
            return [sentence]
        
        result = []
        current = parts[0]
        
        for i in range(1, len(parts)):
            part = parts[i].strip()
            if not part:
                continue
            
            # 检查是否应该分割
            should_split = False
            split_confidence = 0.0
            
            # 获取逗号后的第一个词
            words = list(jieba.cut(part, cut_all=False))
            first_word = words[0] if words else ""
            
            # 规则1：强连词 - 高优先级分割
            for conj in STRONG_SPLIT_CONJUNCTIONS:
                if part.startswith(conj):
                    should_split = True
                    split_confidence = 0.9
                    break
            
            # 规则2：代词开头且前文足够长
            if not should_split and first_word in PRONOUN_STARTS:
                if len(current) >= 10:  # 前文至少10字符（降低阈值）
                    should_split = True
                    split_confidence = 0.7
            
            # 规则3：弱连词且前文很长
            if not should_split and first_word in WEAK_SPLIT_CONJUNCTIONS:
                if len(current) >= 40:  # 前文至少40字符
                    should_split = True
                    split_confidence = 0.6
            
            # 规则4：疑问句结尾的独立分割（新增）
            if not should_split and current.strip().endswith(('吗', '呢', '没有', '不是')):
                if len(current) >= 5:  # 前文至少5字符（降低阈值）
                    should_split = True
                    split_confidence = 0.65
            
            # 执行分割决策
            if should_split and split_confidence >= 0.6:
                result.append(current.strip())
                current = part
            else:
                current += '，' + part
        
        # 添加最后一部分
        if current.strip():
            result.append(current.strip())
        
        return result
    
    def _optimize_sentence_boundaries(self, sentences: List[str]) -> List[str]:
        """最小化干预版本 - 只处理真正的碎片"""
        optimized = []
        buffer = ""
        
        for sentence in sentences:
            if not sentence.strip():
                continue
            
            sentence = sentence.strip()
            
            # 只合并极少数情况
            if self._is_fragment(sentence) and buffer:
                # 碎片，尝试附加到前句
                if len(buffer + sentence) <= self.MAX_SENTENCE_CHARS:
                    buffer = buffer.rstrip('。！？') + sentence
                else:
                    optimized.append(buffer)
                    buffer = sentence
            else:
                # 正常句子
                if buffer:
                    optimized.append(buffer)
                buffer = sentence
        
        if buffer:
            optimized.append(buffer)
        
        return [s for s in optimized if s.strip()]
    
    def _is_fragment(self, text: str) -> bool:
        """判断是否是需要合并的碎片"""
        text = text.strip().rstrip('。！？')  # 移除标点符号进行判断
        
        # 先检查独立的有意义短句 - 这些不是碎片
        INDEPENDENT_PHRASES = {"是的", "好的", "谢谢", "不是", "没有", "可以", "知道", "对的", "当然", "没错"}
        if text in INDEPENDENT_PHRASES:
            return False
        
        # 单个语气词
        if text in {"啊", "哦", "呢", "吧", "呀", "啦", "哇", "嘛", "哈"}:
            return True
        
        # 极短的其他内容
        if len(text) <= 1:  # 只有单个字符才考虑为碎片
            return True
        
        return False
    
    def _analyze_sentence_semantics(self, sentence: str) -> Dict[str, Any]:
        """分析句子的语义特征"""
        # 使用jieba进行分词分析
        words = list(jieba.cut(sentence, cut_all=False))
        
        # 语义特征计算
        features = {
            "word_count": len(words),
            "char_count": len(sentence),
            "avg_word_length": sum(len(w) for w in words) / len(words) if words else 0,
            "has_particles": any(w in ["的", "了", "着", "过", "呢", "吧", "啊", "呀"] for w in words),
            "sentence_type": self._detect_sentence_type(sentence),
            "semantic_density": self._calculate_semantic_density(words),
            "break_strength": self._calculate_break_strength(sentence)
        }
        
        return features
    
    def _detect_sentence_type(self, sentence: str) -> SentenceType:
        """检测句子类型"""
        sentence = sentence.strip()
        
        if sentence.endswith(("?", "？")):
            return SentenceType.INTERROGATIVE
        elif sentence.endswith(("!", "！")):
            return SentenceType.EXCLAMATORY
        elif any(sentence.startswith(word) for word in ["请", "让", "要", "不要", "别"]):
            return SentenceType.IMPERATIVE
        else:
            return SentenceType.DECLARATIVE
    
    def _calculate_semantic_density(self, words: List[str]) -> float:
        """计算语义密度 - 优化中文语义词识别"""
        if not words:
            return 0.5  # 默认中等密度
        
        # 中文实词识别（优化逻辑）
        semantic_words = []
        stop_words = {"的", "了", "着", "过", "在", "是", "有", "和", "与", "及", "或", "但", "而", "也", "都", "就", "又", "还", "更", "很", "非常", "十分"}
        
        for word in words:
            if len(word) > 1 and not word.isspace() and word not in stop_words:
                # 检查是否包含实际内容字符
                if any(c.isalnum() or ord(c) > 127 for c in word):  # 包含字母数字或中文字符
                    semantic_words.append(word)
        
        density = len(semantic_words) / len(words) if words else 0.5
        return max(0.2, min(1.0, density))  # 限制在合理范围内
    
    def _calculate_break_strength(self, sentence: str) -> float:
        """计算语义断句强度"""
        # 基于标点符号和语义标记的断句强度
        strong_breaks = sentence.count("。") + sentence.count("！") + sentence.count("？")
        medium_breaks = sentence.count("；") + sentence.count("：")
        weak_breaks = sentence.count("，") + sentence.count("、")
        
        total_chars = len(sentence)
        if total_chars == 0:
            return 0.0
        
        # 标准化断句强度
        strength = (strong_breaks * 1.0 + medium_breaks * 0.6 + weak_breaks * 0.3) / total_chars
        return min(strength * 10, 1.0)  # 归一化到 [0, 1]
    
    def _should_merge_sentences(self, buffer: str, sentence: str, features: Dict[str, Any]) -> bool:
        """判断是否应该合并句子 - 修正版：极度保守"""
        combined_length = len(buffer) + len(sentence)
        
        # 长度限制
        if combined_length > self.MAX_SENTENCE_CHARS:
            return False
        
        # 强断句标记
        if features["break_strength"] > 0.8:
            return False
        
        # ===== 核心修改：只合并真正的碎片 =====
        
        # 1. 单个语气词
        PARTICLES = {"啊", "哦", "呢", "吧", "呀", "啦", "哇", "嘛", "哈"}
        if sentence in PARTICLES:
            return True
        
        # 2. 极短片段（3个字符以下）且不是独立语义单元
        if len(sentence) <= 3:
            # 检查是否是独立的词（如"是的"、"好的"、"谢谢"）
            INDEPENDENT_SHORT = {"是的", "好的", "谢谢", "不是", "没有", "可以", "知道"}
            if sentence not in INDEPENDENT_SHORT:
                return True
        
        # 3. 删除原来的语义密度判断
        # 不再使用 semantic_density < 0.3 作为合并条件
        
        # 默认：不合并！相信 spaCy 的判断
        return False
    
    def _split_long_sentence_semantically(self, sentence: str) -> List[str]:
        """基于语义的长句子拆分"""
        if len(sentence) <= self.MAX_SENTENCE_CHARS:
            return [sentence]
        
        # 使用jieba找到自然的分割点
        words = list(jieba.cut(sentence, cut_all=False))
        
        splits = []
        current_split = ""
        
        for word in words:
            if len(current_split + word) > self.MAX_SENTENCE_CHARS and current_split:
                splits.append(current_split.strip())
                current_split = word
            else:
                current_split += word
        
        if current_split.strip():
            splits.append(current_split.strip())
        
        return splits
    
    def _build_semantic_sentences(self, sentences: List[str]) -> List[SemanticSentence]:
        """构建语义句子对象"""
        semantic_sentences = []
        
        for i, sentence in enumerate(sentences):
            if not sentence.strip():
                continue
            
            # 语义分析
            features = self._analyze_sentence_semantics(sentence)
            
            # 计算置信度
            confidence = self._calculate_semantic_confidence(sentence, features)
            
            # 估算播放时长
            estimated_duration = self._estimate_playback_duration(sentence)
            
            semantic_sentence = SemanticSentence(
                text=sentence.strip(),
                sentence_type=features["sentence_type"],
                confidence=confidence,
                word_count=features["word_count"],
                char_count=features["char_count"],
                estimated_duration_ms=estimated_duration,
                semantic_break_strength=features["break_strength"]
            )
            
            semantic_sentences.append(semantic_sentence)
        
        return semantic_sentences
    
    def _calculate_semantic_confidence(self, sentence: str, features: Dict[str, Any]) -> float:
        """计算语义分析置信度 - 优化计算逻辑"""
        confidence = 0.8  # 基础置信度
        
        # 长度因子（更宽松）
        if self.MIN_SENTENCE_CHARS <= features["char_count"] <= self.MAX_SENTENCE_CHARS:
            confidence += 0.1  # 长度合适时增加置信度
        elif features["char_count"] < self.MIN_SENTENCE_CHARS:
            confidence *= 0.9  # 过短时轻微降低
        elif features["char_count"] > self.MAX_SENTENCE_CHARS:
            confidence *= 0.85  # 过长时轻微降低
        
        # 语义密度因子（优化权重）
        semantic_score = min(features["semantic_density"] * 1.2, 0.3)  # 限制最大贡献
        confidence += semantic_score
        
        # 断句强度因子
        break_score = features["break_strength"] * 0.1
        confidence += break_score
        
        # 句子类型奖励
        if features["sentence_type"] != SentenceType.DECLARATIVE:
            confidence += 0.05  # 问句、感叹句等有明确语义标记
        
        return max(0.3, min(1.0, confidence))  # 确保置信度在合理范围内
    
    def _estimate_playback_duration(self, sentence: str) -> int:
        """估算句子播放时长(毫秒) - 基于中文语音特征"""
        char_count = len(sentence)
        
        # 中文TTS播放速度估算: 约4-5字符/秒
        base_duration = char_count * 220  # 220ms/字符
        
        # 标点符号停顿时间
        pause_time = 0
        pause_time += sentence.count("。") * 500  # 句号停顿500ms
        pause_time += sentence.count("！") * 400  # 感叹号停顿400ms
        pause_time += sentence.count("？") * 450  # 问号停顿450ms
        pause_time += sentence.count("，") * 200  # 逗号停顿200ms
        pause_time += sentence.count("；") * 300  # 分号停顿300ms
        
        return base_duration + pause_time
    
    def _validate_semantic_sentences(self, sentences: List[SemanticSentence]) -> None:
        """验证语义句子质量 - Fail-Fast质量控制"""
        if not sentences:
            raise ValueError("No valid semantic sentences generated")
        
        # 检查置信度（更宽松的验证）
        low_confidence_count = sum(1 for s in sentences if s.confidence < self.MIN_SEMANTIC_CONFIDENCE)
        if low_confidence_count > len(sentences) * 0.6:  # 超过60%低置信度才报错
            logger.warning(f"⚠️ High proportion of low-confidence sentences: {low_confidence_count}/{len(sentences)}")
            # 不再抛出异常，仅记录警告
            # raise RuntimeError(f"Too many low-confidence sentences: {low_confidence_count}/{len(sentences)}")
        
        # 检查句子长度分布
        avg_length = sum(s.char_count for s in sentences) / len(sentences)
        if avg_length < self.MIN_SENTENCE_CHARS or avg_length > self.MAX_SENTENCE_CHARS:
            logger.warning(f"⚠️ Average sentence length {avg_length:.1f} is outside optimal range")
        
        # 验证文本完整性
        total_chars = sum(s.char_count for s in sentences)
        if total_chars == 0:
            raise ValueError("All semantic sentences are empty")
        
        # 计算实际统计信息
        avg_confidence = sum(s.confidence for s in sentences) / len(sentences)
        logger.info(f"📊 Semantic analysis stats: {len(sentences)} sentences, avg_confidence={avg_confidence:.2f}, "
                   f"low_confidence_ratio={low_confidence_count/len(sentences):.1%}")
        
        logger.debug(f"✅ Semantic sentence validation completed")
    
    def _generate_cache_key(self, content: str) -> str:
        """生成缓存键"""
        import hashlib
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
    
    def get_semantic_statistics(self, sentences: List[SemanticSentence]) -> Dict[str, Any]:
        """获取语义分析统计信息"""
        if not sentences:
            return {}
        
        return {
            "total_sentences": len(sentences),
            "avg_confidence": sum(s.confidence for s in sentences) / len(sentences),
            "avg_char_count": sum(s.char_count for s in sentences) / len(sentences),
            "avg_word_count": sum(s.word_count for s in sentences) / len(sentences),
            "total_estimated_duration_ms": sum(s.estimated_duration_ms for s in sentences),
            "sentence_types": {
                stype.value: sum(1 for s in sentences if s.sentence_type == stype)
                for stype in SentenceType
            },
            "confidence_distribution": {
                "high (>0.8)": sum(1 for s in sentences if s.confidence > 0.8),
                "medium (0.6-0.8)": sum(1 for s in sentences if 0.6 <= s.confidence <= 0.8),
                "low (<0.6)": sum(1 for s in sentences if s.confidence < 0.6)
            }
        }
    
    def clear_cache(self) -> None:
        """清理缓存"""
        self._sentence_cache.clear()
        logger.debug("🧹 Semantic sentence cache cleared")


# 工厂函数
def create_semantic_sentence_splitter() -> SemanticSentenceSplitter:
    """创建语义句子拆分器实例
    
    Returns:
        SemanticSentenceSplitter: 拆分器实例
        
    Raises:
        RuntimeError: 初始化失败时抛出 
    """
    try:
        return SemanticSentenceSplitter()
    except Exception as e:
        raise RuntimeError(f"Failed to create semantic sentence splitter: {e}") from e


# 简单的测试和使用示例
async def test_semantic_splitting():
    """测试语义句子拆分功能"""
    test_content = """
    大家好呀！欢迎来到我们的直播间！今天为大家带来一款非常棒的产品。
    这个产品有很多优秀的特点。首先它的质量非常好，经过严格测试。
    其次价格也很实惠，现在还有特价。最后我们提供完善的售后服务。
    您有什么问题吗？感谢大家的观看和支持！
    """
    
    try:
        splitter = create_semantic_sentence_splitter()
        semantic_sentences = splitter.split_sentences_semantic(test_content)
        
        print("🧠 Semantic Sentence Analysis Results:")
        print("=" * 50)
        
        for i, sentence in enumerate(semantic_sentences, 1):
            print(f"{i}. [{sentence.sentence_type.value}] {sentence.text}")
            print(f"   📊 Chars: {sentence.char_count}, Words: {sentence.word_count}")
            print(f"   🎯 Confidence: {sentence.confidence:.2f}, Duration: {sentence.estimated_duration_ms}ms")
            print(f"   🔗 Break Strength: {sentence.semantic_break_strength:.2f}")
            print()
        
        # 统计信息
        stats = splitter.get_semantic_statistics(semantic_sentences)
        print("📈 Semantic Analysis Statistics:")
        print(f"   Total sentences: {stats['total_sentences']}")
        print(f"   Average confidence: {stats['avg_confidence']:.2f}")
        print(f"   Average length: {stats['avg_char_count']:.1f} chars")
        print(f"   Total duration: {stats['total_estimated_duration_ms']/1000:.1f}s")
        print(f"   Sentence types: {stats['sentence_types']}")
        
        print("✅ Semantic splitting test completed successfully!")
        
    except Exception as e:
        print(f"❌ Semantic splitting test failed: {e}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(test_semantic_splitting())