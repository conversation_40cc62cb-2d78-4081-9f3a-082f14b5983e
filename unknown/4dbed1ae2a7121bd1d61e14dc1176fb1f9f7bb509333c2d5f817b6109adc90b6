"""CosyVoice v2 Cloud TTS Engine - Fixed Version

Real-time streaming TTS engine using Alibaba Cloud Model Factory's CosyVoice v2.
Supports 龙安燃 (longanran) and 龙安宣 (longanxuan) voices with ultra-low latency.
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List, AsyncGenerator
from datetime import datetime
from loguru import logger
import threading

from ..models.streaming_audio import (
    AudioStream, StreamingChunk, VoiceProfile, StreamingStatus
)
from ..models.audio import AudioPriority
from ..core.exceptions import ServiceError
from .tts_engines.base import BaseTTSEngine


class VoiceManager:
    """Voice profile manager for CosyVoice v2"""
    
    def __init__(self):
        self.voices = {
            "longanran": VoiceProfile(
                voice_id="longanran",
                name="龙安燃", 
                type="female_energetic",
                description="活力女声，适合产品推广",
                language="zh-CN",
                sample_rate=24000,
                latency_ms=150,
                quality_score=0.95,
                suitable_for=["product_intro", "call_to_action", "price_announcement"],
                energy_level="high"
            ),
            "longanxuan": VoiceProfile(
                voice_id="longanxuan",
                name="龙安宣",
                type="female_calm",
                description="温和女声，适合品牌介绍",
                language="zh-CN",
                sample_rate=24000,
                latency_ms=150,
                quality_score=0.93,
                suitable_for=["opening", "closing", "interaction"],
                energy_level="medium"
            )
        }
        
        self.default_voice = "longanran"
        self.fallback_voice = "longanxuan"
    
    def get_voice_profile(self, voice_id: str) -> Optional[VoiceProfile]:
        """Get voice profile by ID"""
        return self.voices.get(voice_id)
    
    def get_default_voice(self) -> VoiceProfile:
        """Get default voice profile"""
        return self.voices[self.default_voice]
    
    def get_fallback_voice(self) -> VoiceProfile:
        """Get fallback voice profile"""
        return self.voices[self.fallback_voice]
    
    def select_voice_for_context(self, context_type: str) -> VoiceProfile:
        """Select best voice for given context"""
        for voice in self.voices.values():
            if context_type in voice.suitable_for:
                return voice
        return self.get_default_voice()
    
    def is_voice_available(self, voice_id: str) -> bool:
        """Check if voice is available"""
        return voice_id in self.voices


class AudioStreamBuffer:
    """Audio stream buffer management"""
    
    def __init__(self, buffer_size_seconds: float = 2.0):
        self.buffer_size_seconds = buffer_size_seconds
        self.chunks: List[bytes] = []
        self.total_duration_ms = 0
        self.max_buffer_size = int(buffer_size_seconds * 24000 * 2)  # 16-bit PCM
    
    def add_chunk(self, audio_data: bytes, duration_ms: int = None) -> bool:
        """Add audio chunk to buffer"""
        if duration_ms is None:
            duration_ms = len(audio_data) / (2 * 24000) * 1000  # Estimate for 16-bit PCM
        
        if self.total_duration_ms + duration_ms > self.buffer_size_seconds * 1000:
            return False  # Buffer full
        
        self.chunks.append(audio_data)
        self.total_duration_ms += duration_ms
        return True
    
    def get_next_chunk(self) -> Optional[bytes]:
        """Get next chunk from buffer"""
        if self.chunks:
            chunk = self.chunks.pop(0)
            # Estimate duration for removed chunk
            chunk_duration_ms = len(chunk) / (2 * 24000) * 1000
            self.total_duration_ms = max(0, self.total_duration_ms - chunk_duration_ms)
            return chunk
        return None
    
    def get_buffer_status(self) -> Dict[str, Any]:
        """Get buffer status information"""
        return {
            "chunks_count": len(self.chunks),
            "total_duration_ms": self.total_duration_ms,
            "buffer_utilization": self.total_duration_ms / (self.buffer_size_seconds * 1000),
            "is_full": self.total_duration_ms >= self.buffer_size_seconds * 1000
        }


class CosyVoiceV2TTSEngine(BaseTTSEngine):
    """CosyVoice v2 cloud TTS engine with streaming support"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Configuration - 使用环境变量获取API Key
        import os
        self.api_key = config.get("api_key") or os.environ.get("COSYVOICE_V2_API_KEY") or os.environ.get("DASHSCOPE_API_KEY", "")
        self.default_voice = config.get("default_voice", "longanran")
        self.fallback_voice = config.get("fallback_voice", "longanxuan")
        self.chunk_duration_ms = config.get("chunk_duration_ms", 200)
        self.buffer_size_seconds = config.get("buffer_size_seconds", 2.0)
        
        # Components
        self.voice_manager = VoiceManager()
        # 不再需要 StreamingTTSClient，直接使用 DashScope SDK
        self.audio_buffer = AudioStreamBuffer(self.buffer_size_seconds)
        
        # State
        self.is_initialized = False
        
        self.logger = logger.bind(component="cosyvoice_v2_tts")
    
    async def initialize(self) -> None:
        """Initialize the TTS engine"""
        try:
            self.logger.info("Initializing CosyVoice v2 TTS engine")
            
            if not self.api_key:
                raise ServiceError(
                    "CosyVoice v2 API key is required",
                    "cosyvoice_v2_tts",
                    "CONFIG_ERROR"
                )
            
            # Validate API key and check voice availability
            if not await self._validate_api_configuration():
                self.logger.warning("API validation failed, but continuing with initialization")
            
            self.is_initialized = True
            self.logger.info("CosyVoice v2 TTS engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize CosyVoice v2 TTS engine: {str(e)}")
            raise ServiceError(
                f"TTS engine initialization failed: {str(e)}",
                "cosyvoice_v2_tts",
                "INIT_ERROR"
            )
    
    async def _validate_api_configuration(self) -> bool:
        """Validate API key and voice availability"""
        try:
            import dashscope
            from dashscope.audio.tts_v2 import SpeechSynthesizer, AudioFormat
            
            # Set API key
            dashscope.api_key = self.api_key
            
            # Test API key with default voice first
            self.logger.debug("Testing API key validity...")
            
            # Try creating a synthesizer with a basic voice
            test_voices = [self.default_voice, self.fallback_voice]
            working_voice = None
            
            for voice_id in test_voices:
                try:
                    synthesizer = SpeechSynthesizer(
                        model="cosyvoice-v2",
                        voice=voice_id,
                        format=AudioFormat.PCM_24000HZ_MONO_16BIT
                    )
                    
                    # If we can create the synthesizer, this voice works
                    working_voice = voice_id
                    self.logger.info(f"✅ API Key 验证成功，可用音色: {voice_id}")
                    break
                    
                except Exception as voice_error:
                    self.logger.debug(f"音色 {voice_id} 不可用: {voice_error}")
                    continue
            
            if working_voice:
                # Update voice configuration if needed
                if working_voice != self.default_voice:
                    self.logger.warning(f"默认音色 {self.default_voice} 不可用，已切换到 {working_voice}")
                    self.default_voice = working_voice
                
                return True
            else:
                self.logger.error("❌ 所有测试音色都不可用")
                return False
                
        except ImportError:
            self.logger.warning("DashScope SDK not available for validation")
            return True  # Continue without validation
        except Exception as e:
            self.logger.error(f"API configuration validation failed: {e}")
            return False

    async def synthesize_streaming(
        self,
        text: str,
        voice: str = None,
        **kwargs
    ) -> AsyncGenerator[StreamingChunk, None]:
        """Synthesize text to audio with streaming output using DashScope SDK

        Args:
            text: Text to synthesize
            voice: Voice identifier (optional, uses engine default)
            **kwargs: Additional synthesis parameters

        Yields:
            StreamingChunk objects as they become available
        """
        if not self.is_initialized:
            await self.initialize()

        # Voice selection with enhanced fallback logic
        voice_id = voice or self.default_voice
        voice_profile = self.voice_manager.get_voice_profile(voice_id)
        if not voice_profile:
            self.logger.warning(f"Voice {voice_id} not found, using default")
            voice_profile = self.voice_manager.get_default_voice()
            voice_id = voice_profile.voice_id

        stream_id = f"cosyvoice_v2_stream_{uuid.uuid4().hex[:8]}"
        sequence_number = 0
        total_chunks = 0

        # Voice fallback list
        fallback_voices = [voice_id]
        if voice_id != self.default_voice:
            fallback_voices.append(self.default_voice)
        if self.fallback_voice not in fallback_voices:
            fallback_voices.append(self.fallback_voice)

        synthesis_error = None
        for attempt, current_voice_id in enumerate(fallback_voices):
            try:
                # Update voice profile for current attempt
                current_voice_profile = self.voice_manager.get_voice_profile(current_voice_id)
                if not current_voice_profile:
                    # Create temporary profile for fallback voice
                    current_voice_profile = VoiceProfile(
                        voice_id=current_voice_id,
                        name=f"Fallback-{current_voice_id}",
                        type="fallback",
                        description=f"Fallback voice {current_voice_id}",
                        language="zh-CN",
                        sample_rate=24000,
                        latency_ms=150,
                        quality_score=0.8,
                        suitable_for=["fallback"],
                        energy_level="medium"
                    )

                if attempt > 0:
                    self.logger.warning(f"Retrying with fallback voice {attempt}: {current_voice_profile.name}")
                else:
                    self.logger.info(f"Starting streaming synthesis with voice: {current_voice_profile.name}")

                start_time = time.time()

                # 使用 DashScope SDK 进行流式合成
                import dashscope
                from dashscope.audio.tts_v2 import SpeechSynthesizer, AudioFormat, ResultCallback
                import asyncio
                import threading

                # 设置 API Key
                dashscope.api_key = self.api_key

                # 创建回调类来收集音频数据
                class StreamingCallback(ResultCallback):
                    def __init__(self):
                        self.audio_chunks = []
                        self.completed = False
                        self.error_message = None
                        self.lock = threading.Lock()

                    def on_open(self):
                        pass

                    def on_data(self, data: bytes):
                        with self.lock:
                            self.audio_chunks.append(data)

                    def on_complete(self):
                        with self.lock:
                            self.completed = True

                    def on_error(self, message: str):
                        with self.lock:
                            self.error_message = message
                            self.completed = True

                    def on_close(self):
                        pass

                    def on_event(self, message):
                        pass

                # 创建回调实例
                callback = StreamingCallback()

                # 创建合成器
                synthesizer = SpeechSynthesizer(
                    model="cosyvoice-v2",
                    voice=current_voice_id,
                    format=AudioFormat.PCM_24000HZ_MONO_16BIT,
                    callback=callback
                )

                # 在线程中执行流式合成
                def run_synthesis():
                    try:
                        # 分段发送文本
                        text_segments = self._split_text_for_streaming(text)
                        for segment in text_segments:
                            synthesizer.streaming_call(segment)
                        synthesizer.streaming_complete()
                    except Exception as e:
                        callback.error_message = str(e)
                        callback.completed = True

                # 启动合成线程
                synthesis_thread = threading.Thread(target=run_synthesis)
                synthesis_thread.start()

                # 流式返回音频数据
                last_chunk_index = 0
                while True:
                    await asyncio.sleep(0.01)  # 小延迟避免过度轮询

                    with callback.lock:
                        # 检查是否有错误
                        if callback.error_message:
                            synthesis_error = callback.error_message
                            break

                        # 处理新的音频块
                        while last_chunk_index < len(callback.audio_chunks):
                            chunk_data = callback.audio_chunks[last_chunk_index]
                            last_chunk_index += 1

                            # Validate chunk data
                            if not chunk_data or len(chunk_data) == 0:
                                self.logger.warning(f"Empty chunk received at sequence {sequence_number}")
                                continue

                            # 修正：直接使用原始PCM数据，不进行不必要的转换
                            try:
                                # 计算音频时长（16位PCM，24kHz采样率）
                                chunk_duration_ms = len(chunk_data) / (2 * 24000) * 1000  # 2 bytes per sample

                            except Exception as e:
                                self.logger.error(f"Failed to process audio chunk {sequence_number}: {e}")
                                continue

                            # Create audio stream with raw PCM data
                            audio_stream = AudioStream(
                                stream_id=f"{stream_id}_chunk_{sequence_number}",
                                audio_data=chunk_data,  # 使用原始PCM数据
                                sample_rate=24000,
                                channels=1,
                                bit_depth=16,
                                duration_ms=int(chunk_duration_ms),
                                timestamp=datetime.utcnow(),
                                sequence_number=sequence_number,
                                is_final=False,
                                text_content=text if sequence_number == 0 else None,
                                voice_profile=current_voice_profile,
                                synthesis_latency_ms=int((time.time() - start_time) * 1000) if sequence_number == 0 else None
                            )

                            # Create streaming chunk with enhanced metadata
                            chunk = StreamingChunk(
                                chunk_id=f"{stream_id}_chunk_{sequence_number}",
                                stream_id=stream_id,
                                audio_stream=audio_stream,
                                priority=kwargs.get('priority', AudioPriority.NORMAL),
                                status=StreamingStatus.STREAMING,
                                context_type=kwargs.get('context_type', 'narration'),
                                segment_title=kwargs.get('segment_title'),
                                segment_type=kwargs.get('segment_type'),
                                play_after=datetime.utcnow(),
                                expires_at=datetime.utcnow().replace(second=datetime.utcnow().second + 30)
                            )

                            sequence_number += 1
                            total_chunks += 1
                            yield chunk

                        # 检查是否完成
                        if callback.completed:
                            break

                # 等待合成线程完成
                synthesis_thread.join(timeout=5.0)

                # 检查是否有错误
                if callback.error_message:
                    raise ServiceError(
                        f"Synthesis error: {callback.error_message}",
                        "cosyvoice_v2_tts",
                        "SYNTHESIS_ERROR"
                    )

                # Create a final marker chunk if we had any audio
                if total_chunks > 0:
                    final_chunk = StreamingChunk(
                        chunk_id=f"{stream_id}_final",
                        stream_id=stream_id,
                        audio_stream=AudioStream(
                            stream_id=f"{stream_id}_final",
                            audio_data=b'',  # Empty final chunk
                            sample_rate=24000,
                            channels=1,
                            bit_depth=16,
                            duration_ms=0,
                            timestamp=datetime.utcnow(),
                            sequence_number=sequence_number,
                            is_final=True,
                            text_content=None,
                            voice_profile=current_voice_profile
                        ),
                        priority=kwargs.get('priority', AudioPriority.NORMAL),
                        status=StreamingStatus.COMPLETED,
                        context_type=kwargs.get('context_type', 'narration')
                    )
                    yield final_chunk

                synthesis_duration = time.time() - start_time
                self.logger.info(
                    f"Streaming synthesis completed: {total_chunks} chunks in {synthesis_duration:.2f}s with voice {current_voice_profile.name}"
                )

                # Success! Break out of fallback loop
                return

            except ServiceError as e:
                synthesis_error = e
                error_str = str(e)

                # Check if this is a voice-related error that might benefit from fallback
                if "418" in error_str or "SYNTHESIZER_INIT_ERROR" in error_str or "STREAMING_CALL_ERROR" in error_str:
                    self.logger.warning(f"Voice {current_voice_id} failed with error 418, trying next fallback...")
                    continue  # Try next voice
                else:
                    # Other errors should not be retried
                    raise

            except Exception as e:
                synthesis_error = e
                self.logger.warning(f"Voice {current_voice_id} failed: {str(e)}")
                continue  # Try next voice

        # If we get here, all voices failed
        if synthesis_error:
            self.logger.error(f"All voice fallbacks failed. Last error: {synthesis_error}")
            if isinstance(synthesis_error, ServiceError):
                raise synthesis_error
            else:
                raise ServiceError(
                    f"All voice fallbacks failed: {synthesis_error}",
                    "cosyvoice_v2_tts",
                    "ALL_VOICES_FAILED"
                )
        else:
            raise ServiceError(
                "All voice fallbacks failed with unknown error",
                "cosyvoice_v2_tts",
                "ALL_VOICES_FAILED"
            )

    def _split_text_for_streaming(self, text: str) -> List[str]:
        """Split text into segments for streaming synthesis

        Args:
            text: Text to split

        Returns:
            List of text segments
        """
        # 简单的分段策略：按句号、感叹号、问号分割
        import re
        segments = re.split(r'[。！？.!?]', text)
        # 过滤空段落并添加标点符号
        result = []
        for i, segment in enumerate(segments):
            segment = segment.strip()
            if segment:
                # 为非最后一个段落添加标点符号
                if i < len(segments) - 1:
                    segment += '。'
                result.append(segment)

        # 如果没有分段，返回原文本
        if not result:
            result = [text]

        return result

    @property
    def supports_streaming(self) -> bool:
        """CosyVoice v2 supports streaming"""
        return True

    @property
    def engine_name(self) -> str:
        """Engine name"""
        return "cosyvoice_v2"

    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices"""
        return [
            {
                "voice_id": voice.voice_id,
                "name": voice.name,
                "type": voice.type,
                "language": voice.language,
                "description": voice.description
            }
            for voice in self.voice_manager.voices.values()
        ]

    def select_voice_for_segment(self, segment_type: str) -> str:
        """Select appropriate voice for segment type

        Args:
            segment_type: Type of content segment

        Returns:
            Voice ID to use
        """
        voice_profile = self.voice_manager.select_voice_for_context(segment_type)
        return voice_profile.voice_id

    def get_voice_profile(self, voice_id: str) -> Optional[VoiceProfile]:
        """Get voice profile by ID"""
        return self.voice_manager.get_voice_profile(voice_id)

    async def cleanup(self) -> None:
        """Clean up resources"""
        self.is_initialized = False
        self.logger.info("CosyVoice v2 TTS engine cleaned up")
