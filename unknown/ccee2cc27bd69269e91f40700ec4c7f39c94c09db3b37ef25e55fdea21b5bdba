"""Script to Content Converter

Converts structured script segments to playable content for LiveStreamController.
Bridges the gap between ScriptPreviewer and the current streaming architecture.
"""

from typing import Dict, List, Optional, Any, AsyncGenerator, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import asyncio
from loguru import logger

from ..services.script_previewer import ScriptSegment, ScriptTimeline, ScriptSegmentType
from ..core.exceptions import ServiceError
from ..models.playlist_models import SentenceData


@dataclass
class PlayableContent:
    """Content item ready for streaming"""
    content_id: str
    content_text: str
    estimated_duration_seconds: int
    content_type: str
    metadata: Dict[str, Any]
    priority: str


class StreamMode(str, Enum):
    """Stream content modes"""
    SEQUENTIAL = "sequential"  # Play segments in order
    ADAPTIVE = "adaptive"     # Allow dynamic reordering
    INTERACTIVE = "interactive"  # Support interruptions and branches


class ScriptToContentConverter:
    """
    Converts script segments to streamable content for LiveStreamController
    """
    
    def __init__(self):
        self.conversion_stats = {
            "conversions_performed": 0,
            "segments_converted": 0,
            "total_content_duration": 0.0,
            "failed_conversions": 0
        }
        
        # Default conversion settings
        self.settings = {
            "add_interaction_prompts": True,
            "segment_buffer_seconds": 2,  # Buffer between segments
            "max_segment_length": 300,    # Maximum segment length in seconds
            "content_quality_check": True
        }
    
    def convert_timeline_to_content_list(
        self, 
        timeline: ScriptTimeline,
        mode: StreamMode = StreamMode.SEQUENTIAL
    ) -> List[SentenceData]:
        """
        Convert script timeline to structured content list for streaming
        
        Args:
            timeline: Complete script timeline
            mode: Streaming mode
            
        Returns:
            List of SentenceData objects ready for streaming
        """
        try:
            logger.info(f"Converting timeline with {len(timeline.segments)} segments to content list")
            
            content_list = []
            
            if mode == StreamMode.SEQUENTIAL:
                # Simple sequential conversion
                for i, segment in enumerate(timeline.segments):
                    if self._should_include_segment(segment):
                        content_text = self._format_segment_content(segment)
                        if content_text:
                            # Create structured SentenceData instead of simple string
                            sentence_data = SentenceData(
                                text=content_text,
                                type=self._map_segment_type(segment.segment_type),
                                index=i,
                                metadata={
                                    "segment_id": segment.segment_id,
                                    "role": getattr(segment, 'role', None),
                                    "timing_cue": getattr(segment, 'timing_cue', None),
                                    "emotional_tone": getattr(segment, 'emotional_tone', None),
                                    "triggers": getattr(segment, 'triggers', []),
                                    "variables": getattr(segment, 'variables', {})
                                },
                                estimated_duration_ms=int(segment.estimated_duration_seconds * 1000),
                                priority=segment.priority,
                                source="script_timeline"
                            )
                            content_list.append(sentence_data)
            
            elif mode == StreamMode.ADAPTIVE:
                # Adaptive ordering based on priority and timing
                sorted_segments = self._sort_segments_by_priority(timeline.segments)
                for i, segment in enumerate(sorted_segments):
                    if self._should_include_segment(segment):
                        content_text = self._format_segment_content(segment)
                        if content_text:
                            sentence_data = SentenceData(
                                text=content_text,
                                type=self._map_segment_type(segment.segment_type),
                                index=i,
                                metadata={
                                    "segment_id": segment.segment_id,
                                    "role": getattr(segment, 'role', None),
                                    "timing_cue": getattr(segment, 'timing_cue', None),
                                    "emotional_tone": getattr(segment, 'emotional_tone', None),
                                    "triggers": getattr(segment, 'triggers', []),
                                    "variables": getattr(segment, 'variables', {}),
                                    "adaptive_mode": True
                                },
                                estimated_duration_ms=int(segment.estimated_duration_seconds * 1000),
                                priority=segment.priority,
                                source="script_timeline_adaptive"
                            )
                            content_list.append(sentence_data)
            
            elif mode == StreamMode.INTERACTIVE:
                # Interactive mode with explicit interaction markers
                content_list = self._create_interactive_content_list_structured(timeline)
            
            # Update stats
            self.conversion_stats["conversions_performed"] += 1
            self.conversion_stats["segments_converted"] += len(content_list)
            self.conversion_stats["total_content_duration"] += sum(
                s.estimated_duration_seconds for s in timeline.segments
            )
            
            logger.info(f"✅ Converted timeline to {len(content_list)} structured content items")
            return content_list
            
        except Exception as e:
            logger.error(f"❌ Timeline conversion failed: {str(e)}")
            self.conversion_stats["failed_conversions"] += 1
            raise ServiceError(f"Failed to convert timeline: {str(e)}")
    
    async def convert_timeline_to_async_generator(
        self, 
        timeline: ScriptTimeline,
        mode: StreamMode = StreamMode.SEQUENTIAL
    ) -> AsyncGenerator[str, None]:
        """
        Convert script timeline to async content generator for streaming
        
        Args:
            timeline: Complete script timeline  
            mode: Streaming mode
            
        Yields:
            Content strings ready for streaming
        """
        try:
            logger.info(f"Converting timeline to async generator with {len(timeline.segments)} segments")
            
            if mode == StreamMode.SEQUENTIAL:
                for segment in timeline.segments:
                    if self._should_include_segment(segment):
                        content_text = self._format_segment_content(segment)
                        if content_text:
                            yield content_text
                            # Add natural pause between segments
                            await asyncio.sleep(self.settings["segment_buffer_seconds"])
            
            elif mode == StreamMode.ADAPTIVE:
                sorted_segments = self._sort_segments_by_priority(timeline.segments)
                for segment in sorted_segments:
                    if self._should_include_segment(segment):
                        content_text = self._format_segment_content(segment)
                        if content_text:
                            yield content_text
                            await asyncio.sleep(self.settings["segment_buffer_seconds"])
            
            elif mode == StreamMode.INTERACTIVE:
                async for content in self._create_interactive_async_generator(timeline):
                    yield content
            
            logger.info("✅ Async generator conversion completed")
            
        except Exception as e:
            logger.error(f"❌ Async generator conversion failed: {str(e)}")
            raise ServiceError(f"Failed to create content generator: {str(e)}")
    
    def convert_segments_to_playable_content(
        self, 
        segments: List[ScriptSegment]
    ) -> List[PlayableContent]:
        """
        Convert script segments to structured PlayableContent objects
        
        Args:
            segments: List of script segments
            
        Returns:
            List of PlayableContent objects
        """
        try:
            logger.info(f"Converting {len(segments)} segments to PlayableContent")
            
            playable_content = []
            
            for i, segment in enumerate(segments):
                if self._should_include_segment(segment):
                    content_text = self._format_segment_content(segment)
                    
                    if content_text:
                        playable_item = PlayableContent(
                            content_id=f"segment_{segment.segment_id}_{i}",
                            content_text=content_text,
                            estimated_duration_seconds=segment.estimated_duration_seconds,
                            content_type=segment.segment_type.value,
                            metadata={
                                "original_segment_id": segment.segment_id,
                                "title": segment.title,
                                "priority": segment.priority.value,
                                "triggers": segment.triggers,
                                "variables": segment.variables,
                                "persona_notes": segment.persona_notes,
                                "converted_at": datetime.utcnow().isoformat()
                            },
                            priority=segment.priority.value
                        )
                        
                        playable_content.append(playable_item)
            
            logger.info(f"✅ Converted to {len(playable_content)} playable content items")
            return playable_content
            
        except Exception as e:
            logger.error(f"❌ PlayableContent conversion failed: {str(e)}")
            raise ServiceError(f"Failed to convert to playable content: {str(e)}")
    
    def create_stream_config(
        self, 
        timeline: ScriptTimeline,
        mode: StreamMode = StreamMode.SEQUENTIAL
    ) -> Dict[str, Any]:
        """
        Create streaming configuration from script timeline
        
        Args:
            timeline: Complete script timeline
            mode: Streaming mode
            
        Returns:
            Configuration dict for LiveStreamController
        """
        try:
            content_list = self.convert_timeline_to_content_list(timeline, mode)
            
            stream_config = {
                "mode": "SCRIPT_BASED",  # New mode for control.py
                "content": content_list,
                "timeline_metadata": {
                    "total_duration_minutes": timeline.total_duration_minutes,
                    "segments_count": len(timeline.segments),
                    "interaction_points": timeline.interaction_points,
                    "break_points": timeline.break_points,
                    "adaptation_rules": timeline.adaptation_rules
                },
                "streaming_config": {
                    "buffer_size": self.settings.get("segment_buffer_seconds", 2),
                    "quality_check": self.settings.get("content_quality_check", True),
                    "max_segment_length": self.settings.get("max_segment_length", 300)
                },
                "conversion_metadata": {
                    "converted_at": datetime.utcnow().isoformat(),
                    "converter_version": "1.0",
                    "mode": mode.value,
                    "original_segments": len(timeline.segments),
                    "converted_items": len(content_list)
                }
            }
            
            logger.info("✅ Created stream configuration")
            return stream_config
            
        except Exception as e:
            logger.error(f"❌ Stream config creation failed: {str(e)}")
            raise ServiceError(f"Failed to create stream config: {str(e)}")
    
    def _should_include_segment(self, segment: ScriptSegment) -> bool:
        """Determine if segment should be included in conversion"""
        # Skip emergency filler segments by default
        if segment.segment_type == ScriptSegmentType.EMERGENCY_FILLER:
            return False
        
        # Skip if content is too short
        if len(segment.content.strip()) < 10:
            return False
        
        # Skip if duration is too long
        if segment.estimated_duration_seconds > self.settings["max_segment_length"]:
            logger.warning(f"Segment {segment.segment_id} duration too long: {segment.estimated_duration_seconds}s")
            return False
        
        return True
    
    def _format_segment_content(self, segment: ScriptSegment) -> Optional[str]:
        """Format segment content for streaming"""
        try:
            content = segment.content.strip()
            
            if not content:
                return None
            
            # Quality check
            if self.settings.get("content_quality_check", True):
                if not self._validate_content_quality(content):
                    logger.warning(f"Content quality check failed for segment {segment.segment_id}")
                    return None
            
            return content
            
        except Exception as e:
            logger.warning(f"Content formatting failed for segment {segment.segment_id}: {str(e)}")
            return segment.content.strip()  # Fallback to raw content
    
    def _validate_content_quality(self, content: str) -> bool:
        """Basic content quality validation"""
        # Check minimum length
        if len(content.strip()) < 10:
            return False
        
        # Check for placeholder text
        placeholders = ["{", "}", "TODO", "PLACEHOLDER", "待填充", "需要替换"]
        if any(placeholder in content for placeholder in placeholders):
            return False
        
        # Check for reasonable sentence structure
        sentences = content.count('.') + content.count('!') + content.count('?') + content.count('。') + content.count('！') + content.count('？')
        if sentences == 0 and len(content) > 50:
            return False
        
        return True
    
    def _sort_segments_by_priority(self, segments: List[ScriptSegment]) -> List[ScriptSegment]:
        """Sort segments by priority and logical order"""
        # Define type priority
        type_priority = {
            ScriptSegmentType.OPENING: 1,
            ScriptSegmentType.PRODUCT_INTRO: 2,
            ScriptSegmentType.SELLING_POINT: 3,
            ScriptSegmentType.PRICE_ANNOUNCEMENT: 4,
            ScriptSegmentType.CALL_TO_ACTION: 5,
            ScriptSegmentType.INTERACTION: 6,
            ScriptSegmentType.CLOSING: 7,
            ScriptSegmentType.TRANSITION: 8,
            ScriptSegmentType.EMERGENCY_FILLER: 9
        }
        
        # Priority value mapping  
        priority_values = {"HIGH": 1, "MEDIUM": 2, "LOW": 3}
        
        return sorted(segments, key=lambda s: (
            type_priority.get(s.segment_type, 99),
            priority_values.get(s.priority.value, 99)
        ))
    
    def _map_segment_type(self, segment_type: ScriptSegmentType) -> str:
        """Map ScriptSegmentType enum to string type for SentenceData"""
        type_mapping = {
            ScriptSegmentType.OPENING: "opening",
            ScriptSegmentType.PRODUCT_INTRO: "product_intro",
            ScriptSegmentType.SELLING_POINT: "selling_point",
            ScriptSegmentType.PRICE_ANNOUNCEMENT: "price",
            ScriptSegmentType.CALL_TO_ACTION: "cta",
            ScriptSegmentType.INTERACTION: "interaction",
            ScriptSegmentType.CLOSING: "closing",
            ScriptSegmentType.TRANSITION: "transition",
            ScriptSegmentType.EMERGENCY_FILLER: "filler"
        }
        return type_mapping.get(segment_type, "normal")
    
    def _create_interactive_content_list_structured(self, timeline: ScriptTimeline) -> List[SentenceData]:
        """Create structured content list with interaction markers for SentenceData"""
        content_list = []
        
        for i, segment in enumerate(timeline.segments):
            if self._should_include_segment(segment):
                content_text = self._format_segment_content(segment)
                if content_text:
                    # Create structured SentenceData
                    sentence_data = SentenceData(
                        text=content_text,
                        type=self._map_segment_type(segment.segment_type),
                        index=i,
                        metadata={
                            "segment_id": segment.segment_id,
                            "role": getattr(segment, 'role', None),
                            "timing_cue": getattr(segment, 'timing_cue', None),
                            "emotional_tone": getattr(segment, 'emotional_tone', None),
                            "triggers": getattr(segment, 'triggers', []),
                            "variables": getattr(segment, 'variables', {}),
                            "interactive_mode": True
                        },
                        estimated_duration_ms=int(segment.estimated_duration_seconds * 1000),
                        priority=segment.priority,
                        source="script_timeline_interactive"
                    )
                    content_list.append(sentence_data)
                    
                    # Add interaction prompts at defined points
                    if self.settings.get("add_interaction_prompts", True):
                        if i in timeline.interaction_points:
                            interaction_prompt = SentenceData(
                                text="现在是互动时间！大家有什么问题吗？请在评论区告诉我们！",
                                type="interaction",
                                index=len(content_list),
                                metadata={"is_interaction_prompt": True},
                                estimated_duration_ms=3000,
                                priority="high",
                                source="interaction_prompt"
                            )
                            content_list.append(interaction_prompt)
        
        return content_list
    
    def _create_interactive_content_list(self, timeline: ScriptTimeline) -> List[str]:
        """Create content list with interaction markers"""
        content_list = []
        
        for i, segment in enumerate(timeline.segments):
            if self._should_include_segment(segment):
                content_text = self._format_segment_content(segment)
                if content_text:
                    content_list.append(content_text)
                    
                    # Add interaction prompts at defined points
                    if self.settings.get("add_interaction_prompts", True):
                        if i in timeline.interaction_points:
                            interaction_prompt = "现在是互动时间！大家有什么问题吗？请在评论区告诉我们！"
                            content_list.append(interaction_prompt)
        
        return content_list
    
    async def _create_interactive_async_generator(
        self, 
        timeline: ScriptTimeline
    ) -> AsyncGenerator[str, None]:
        """Create interactive async generator with real-time adaptations"""
        for i, segment in enumerate(timeline.segments):
            if self._should_include_segment(segment):
                content_text = self._format_segment_content(segment)
                if content_text:
                    yield content_text
                    
                    # Check for interaction points
                    if i in timeline.interaction_points:
                        interaction_prompt = "现在是互动时间！大家有什么问题吗？请在评论区告诉我们！"
                        await asyncio.sleep(1.0)  # Brief pause
                        yield interaction_prompt
                        await asyncio.sleep(5.0)  # Wait for interaction
                    
                    await asyncio.sleep(self.settings["segment_buffer_seconds"])
    
    def update_settings(self, new_settings: Dict[str, Any]) -> None:
        """Update conversion settings"""
        self.settings.update(new_settings)
        logger.info(f"Updated conversion settings: {new_settings}")
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """Get conversion statistics"""
        return {
            **self.conversion_stats,
            "current_settings": self.settings,
            "success_rate": (
                (self.conversion_stats["conversions_performed"] - self.conversion_stats["failed_conversions"]) 
                / max(self.conversion_stats["conversions_performed"], 1)
            ) * 100
        }
    
    def clear_stats(self) -> None:
        """Clear conversion statistics"""
        self.conversion_stats = {
            "conversions_performed": 0,
            "segments_converted": 0,
            "total_content_duration": 0.0,
            "failed_conversions": 0
        }
        logger.info("Conversion statistics cleared")


# Utility functions for integration
def convert_prepared_stream_to_content(prepared_stream_data: Dict[str, Any]) -> List[SentenceData]:
    """
    Convert prepared stream data from script preview to structured content list
    
    Args:
        prepared_stream_data: Data from /prepare-stream endpoint
        
    Returns:
        List of SentenceData objects for streaming
    """
    try:
        segments = prepared_stream_data.get("segments", [])
        content_list = []
        
        for i, segment in enumerate(segments):
            content = segment.get("content", "").strip()
            if content and len(content) > 10:  # Basic validation
                # Create structured SentenceData object
                sentence_data = SentenceData(
                    text=content,
                    type=segment.get("segment_type", "normal"),
                    index=i,
                    metadata={
                        "segment_id": segment.get("segment_id"),
                        "title": segment.get("title"),
                        "priority": segment.get("priority"),
                        "from_prepared_stream": True
                    },
                    estimated_duration_ms=int(segment.get("estimated_duration_seconds", 10) * 1000),
                    priority=segment.get("priority", "normal"),
                    source="prepared_stream"
                )
                content_list.append(sentence_data)
        
        logger.info(f"Converted prepared stream to {len(content_list)} structured content items")
        return content_list
        
    except Exception as e:
        logger.error(f"Failed to convert prepared stream: {str(e)}")
        return []


def get_default_converter() -> ScriptToContentConverter:
    """Get a default configured converter instance"""
    converter = ScriptToContentConverter()
    
    # Set optimal default settings
    converter.update_settings({
        "add_interaction_prompts": True,
        "segment_buffer_seconds": 1,
        "max_segment_length": 240,  # 4 minutes max per segment
        "content_quality_check": True
    })
    
    return converter