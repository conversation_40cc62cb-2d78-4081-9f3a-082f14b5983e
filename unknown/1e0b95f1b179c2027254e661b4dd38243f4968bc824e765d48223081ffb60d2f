"""流式内容提供器

处理客户端的内容请求，提供下一句音频内容。

Author: Claude Code
Date: 2025-08-08
"""

import asyncio
import base64
import time
import uuid
import yaml
from typing import Dict, Any, Optional, TYPE_CHECKING, <PERSON>ple
from datetime import datetime
from dataclasses import dataclass
from loguru import logger

from ..core.streaming_config import StreamingConfig
from ..models.playlist_models import PlaylistItem
from .circuit_breaker import TTSCircuitBreaker, CircuitBreakerOpenError
from .synthesis_event_bus import get_synthesis_event_bus
from ..exceptions import ContentGenerationError, ContentNotReadyError, InvalidPlaylistStateError

if TYPE_CHECKING:
    from .playlist_manager import PlaylistManager
    from .client_state_tracker import ClientStateTracker


class ContentRequestError(Exception):
    """内容请求异常"""
    pass


@dataclass
class TTSConfig:
    """TTS配置类"""
    timeout_min: float = 10.0
    timeout_max: float = 60.0
    timeout_multiplier: float = 2.0
    retry_initial_delay: int = 1000
    retry_max_delay: int = 8000
    retry_max_attempts: int = 4
    retry_backoff_multiplier: float = 2.0
    future_retention: float = 30.0
    
    @classmethod
    def from_yaml(cls, path: str = "config/tts_config.yaml"):
        """从YAML文件加载配置"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                tts_data = data.get('tts', {})
                return cls(
                    timeout_min=tts_data.get('timeout', {}).get('min_seconds', 10.0),
                    timeout_max=tts_data.get('timeout', {}).get('max_seconds', 60.0),
                    timeout_multiplier=tts_data.get('timeout', {}).get('multiplier', 2.0),
                    retry_initial_delay=tts_data.get('retry', {}).get('initial_delay_ms', 1000),
                    retry_max_delay=tts_data.get('retry', {}).get('max_delay_ms', 8000),
                    retry_max_attempts=tts_data.get('retry', {}).get('max_attempts', 4),
                    retry_backoff_multiplier=tts_data.get('retry', {}).get('backoff_multiplier', 2.0),
                    future_retention=tts_data.get('future_lifecycle', {}).get('retention_seconds', 30.0)
                )
        except FileNotFoundError:
            logger.warning(f"TTS配置文件 {path} 不存在，使用默认配置")
            return cls()


class StreamingContentProvider:
    """
    流式内容提供器
    
    核心职责：
    1. 处理客户端的内容请求
    2. 集成TTS合成逻辑
    3. 提供性能统计和监控
    4. 支持错误处理和降级策略
    """
    
    def __init__(self, playlist_manager: 'PlaylistManager',
                 client_tracker: 'ClientStateTracker',
                 tts_engine: Any,
                 config: StreamingConfig,
                 proactive_synthesizer: Optional[Any] = None,
                 state_broadcaster: Optional[Any] = None):
        self.playlist_manager = playlist_manager
        self.client_tracker = client_tracker
        self.tts_engine = tts_engine
        self.config = config
        self.proactive_synthesizer = proactive_synthesizer  # 可选的预合成器引用
        self.state_broadcaster = state_broadcaster  # 可选的状态广播器
        
        # 加载TTS配置
        self.tts_config = TTSConfig.from_yaml()
        
        # 初始化熔断器
        self.circuit_breaker = TTSCircuitBreaker()
        
        # 请求统计
        self._request_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "synthesis_errors": 0,
            "playlist_errors": 0,
            "client_errors": 0,
            "average_response_time_ms": 0.0,
            "total_bytes_served": 0
        }
        
        # 性能监控
        self._start_time = datetime.utcnow()
        self._last_reset_time = datetime.utcnow()
        
        # 🎯 新架构：基于队列的合成管理
        # 不再需要Future管理，所有并发控制由EventBus处理
        self.event_bus = get_synthesis_event_bus()  # 获取事件总线单例
        self._guardian_timeout_task: Optional[asyncio.Task] = None  # 保留守护者超时任务
        
        # 监控指标
        self._synthesis_metrics = {
            "synthesis_tasks_created": 0,    # 新Future创建次数
            "synthesis_tasks_waited": 0,     # Future等待次数(避免竞态)
            "proactive_synthesis_hits": 0,   # 预合成命中次数
            "qa_presynthesis_cache_hits": 0,   # QA预合成缓存命中
            "qa_presynthesis_cache_misses": 0,  # QA预合成缓存未命中
            # 添加缺失的指标键
            "qa_request_count": 0,           # QA请求计数
            "synthesis_tasks_completed": 0,  # 合成任务完成数
            "qa_synthesis_success": 0,       # QA合成成功数
            "synthesis_timeouts": 0,         # 合成超时数
            "synthesis_errors": 0            # 合成错误数
        }
        
        logger.info("流式内容提供器初始化完成")
        
        # 启动守护者超时监控任务
        self._start_guardian_monitor()
        
    async def handle_next_content_request(self, client_id: str,
                                        request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理客户端的下一句内容请求
        
        Args:
            client_id: 客户端标识符
            request_data: 请求数据
            
        Returns:
            响应数据
        """
        request_start_time = time.time()
        self._request_stats["total_requests"] += 1
        
        try:
            logger.info(f"📨 开始处理内容请求: client={client_id}, data={request_data}")
            
            # 1. 验证请求参数
            if not self._validate_request(request_data):
                raise ContentRequestError("请求参数无效")
                
            # 2. 更新客户端状态 (临时完全跳过以避免死锁)
            # TODO: 修复client_tracker死锁后恢复此代码
            logger.debug(f"临时跳过客户端状态更新: {client_id}")
                
            # 3. 计算请求的内容索引
            next_index = self._calculate_next_index(request_data)
            
            # 验证索引是否在有效范围内
            if next_index < 0:
                raise ContentRequestError(f"无效的索引值: {next_index}")
            
            logger.info(f"📤 处理内容请求: client={client_id}, requested_index={next_index}")
            
            # 更新 PlaylistManager 的播放进度（确保状态同步）
            await self.playlist_manager.update_playback_index(next_index)
            logger.debug(f"✅ Updated PlaylistManager playback index to {next_index}")
            
            # 4. 从播放列表获取内容
            logger.debug(f"🎵 获取播放列表项: index={next_index}")
            try:
                # 添加超时检测
                import asyncio
                playlist_item = await asyncio.wait_for(
                    self.playlist_manager.get_item_at(next_index),
                    timeout=5.0
                )
                logger.debug(f"🎵 播放列表返回: {playlist_item}")
            except asyncio.TimeoutError:
                logger.error(f"❌ 获取播放列表项超时！可能存在死锁")
                raise
            except Exception as e:
                logger.error(f"❌ 获取播放列表项失败: {e}", exc_info=True)
                raise
            
            if not playlist_item:
                # 播放结束
                logger.info(f"📛 播放结束: index={next_index}")
                response = self._create_end_of_content_response(next_index)
                self._update_response_stats(request_start_time, True)
                return response
            
            logger.debug(f"✅ 获取到播放项: {playlist_item.item_id}")
                
            # 5. 获取或合成音频
            try:
                audio_data, duration_ms = await self._get_or_synthesize_audio(
                    playlist_item, client_id
                )
            except ContentNotReadyError as e:
                # 内容尚未准备好，返回特殊的重试响应
                error_id = uuid.uuid4().hex[:12]
                logger.info(f"[Error ID: {error_id}] 内容生成中: {playlist_item.item_id}, retry_after={e.retry_after_ms}ms")
                
                retry_response = {
                    "type": "content_not_ready",
                    "error_code": e.error_code,
                    "message": str(e),
                    "error_id": error_id,
                    "index": next_index,
                    "request_id": request_data.get("request_id"),
                    "retry_after_ms": e.retry_after_ms,
                    "item_id": playlist_item.item_id,
                    "server_time": time.time()
                }
                
                self._update_response_stats(request_start_time, False)
                return retry_response
                
            except ContentGenerationError as e:
                # 合成失败 - 专门处理
                error_id = uuid.uuid4().hex[:12]
                logger.error(f"[Error ID: {error_id}] 音频合成失败: {playlist_item.item_id}", 
                            exc_info=True)  # 包含完整堆栈
                self._request_stats["synthesis_errors"] += 1
                
                error_response = self._create_error_response(
                    "synthesis_failed",
                    None,  # 让 _create_error_response 使用标准消息
                    next_index,
                    request_data.get("request_id"),
                    error_id
                )
                self._update_response_stats(request_start_time, False)
                return error_response
                
            except Exception as e:
                # 未预期的错误
                error_id = uuid.uuid4().hex[:12]
                logger.error(f"[Error ID: {error_id}] 未预期的错误处理内容请求 - {type(e).__name__}: {e}", 
                            exc_info=True)  # 完整堆栈跟踪
                
                # 增加新的统计类别
                if "unexpected_errors" not in self._request_stats:
                    self._request_stats["unexpected_errors"] = 0
                self._request_stats["unexpected_errors"] += 1
                
                error_response = self._create_error_response(
                    "internal_error",
                    None,  # 使用标准消息
                    next_index,
                    request_data.get("request_id"),
                    error_id
                )
                self._update_response_stats(request_start_time, False)
                return error_response
                
            # 6. 构建成功响应
            response = await self._create_content_response(
                playlist_item, audio_data, duration_ms, next_index,
                request_data.get("request_id")  # 传递客户端的request_id
            )
            
            # 7. 更新统计
            self._request_stats["total_bytes_served"] += len(audio_data)
            self._request_stats["successful_requests"] += 1
            self._update_response_stats(request_start_time, True)
            
            logger.debug(f"内容请求处理成功: client={client_id}, index={next_index}, "
                        f"size={len(audio_data)} bytes, duration={duration_ms:.1f}ms")
            
            # # 🎯 调试信息：插入客户端播放时打印当前正在播放的句子全文
            # logger.info(f"[QA调试-插入客户端播放] 当前发送给客户端的句子全文: '{playlist_item.content}'")
            # logger.info(f"[QA调试-插入客户端播放] 发送索引: {next_index}, 句子类型: {playlist_item.type.value}")
            # logger.info(f"[QA调试-插入客户端播放] 音频数据大小: {len(audio_data)} bytes, 时长: {duration_ms:.1f}ms")
            
            return response
            
        except ContentRequestError as e:
            logger.warning(f"内容请求错误: {e}")
            self._request_stats["client_errors"] += 1
            response = self._create_error_response("request_error", str(e))
            self._update_response_stats(request_start_time, False)
            return response
            
        except Exception as e:
            logger.error(f"处理内容请求时发生未预期错误: {e}", exc_info=True)
            self._request_stats["failed_requests"] += 1
            response = self._create_error_response("internal_error", "服务器内部错误")
            self._update_response_stats(request_start_time, False)
            return response
            
    def _validate_request(self, request_data: Dict[str, Any]) -> bool:
        """验证请求参数"""
        required_fields = ["current_index"]
        
        for field in required_fields:
            if field not in request_data:
                logger.warning(f"请求缺少必要字段: {field}")
                return False
                
        # 验证索引范围
        current_index = request_data.get("current_index")
        if not isinstance(current_index, int) or current_index < 0:
            logger.warning(f"无效的current_index: {current_index}")
            return False
            
        return True
        
    def _calculate_next_index(self, request_data: Dict[str, Any]) -> int:
        """计算请求的内容索引
        
        语义说明：
        - current_index: 客户端请求的内容索引（不是已播放的索引）
        - buffered_until: 客户端已缓冲到的索引，需要返回下一个
        """
        current_index = request_data.get("current_index", 0)
        
        # 如果客户端指定了已缓冲到的位置，返回下一个未缓冲的内容
        buffered_until = request_data.get("buffered_until")
        if buffered_until is not None:
            return buffered_until + 1
        
        # 否则返回客户端请求的索引（修正：不再+1）
        return current_index
            
    async def _get_or_synthesize_audio(self, playlist_item: PlaylistItem, 
                                     client_id: str) -> Tuple[bytes, float]:
        """获取或合成音频（新架构：极简版）
        
        所有复杂逻辑都已封装在EventBus中，包括：
        - 请求去重
        - 超时处理
        - 回复队列管理
        """
        # 直接调用事件总线的新接口
        # EventBus会处理所有细节：去重、超时、队列管理
        try:
            # 跟踪QA预合成（移入try块内，避免KeyError）
            if playlist_item.type.is_qa_related:
                self._synthesis_metrics["qa_request_count"] += 1
            
            logger.info(f"📤 请求合成音频: {playlist_item.item_id}")
            
            result = await self.event_bus.request_synthesis(
                item=playlist_item,
                priority=0,  # 客户端请求最高优先级
                client_id=client_id
            )
            
            # 成功获取音频
            audio_data, duration_ms = result
            self._synthesis_metrics["synthesis_tasks_completed"] += 1
            
            if playlist_item.type.is_qa_related:
                self._synthesis_metrics["qa_synthesis_success"] += 1
                
            return audio_data, duration_ms
            
        except ContentNotReadyError:
            # 超时，但后台仍在处理
            self._synthesis_metrics["synthesis_timeouts"] += 1
            raise
            
        except Exception as e:
            # 其他错误
            self._synthesis_metrics["synthesis_errors"] += 1
            logger.error(f"❌ 合成请求失败: {playlist_item.item_id}, error: {e}")
            raise ContentGenerationError(f"Synthesis failed: {e}")
    
    # _background_synthesis_with_lifecycle方法已删除
    # 新架构中，所有合成任务由ProactiveSynthesizer通过队列处理
    
    async def _notify_synthesis_complete(self, item_id: str, success: bool):
        """通过WebSocket推送合成完成通知"""
        if self.state_broadcaster:
            await self.state_broadcaster.broadcast_synthesis_complete(item_id, success)
        else:
            logger.debug(f"📢 合成完成通知(无广播器): item_id={item_id}, success={success}")
    
    async def _execute_synthesis(self, playlist_item: PlaylistItem) -> Tuple[bytes, float]:
        """执行实际的TTS合成（通过熔断器保护）"""
        try:
            # 通过熔断器调用内部合成方法
            return await self.circuit_breaker.call(
                self._do_synthesis, playlist_item
            )
        except CircuitBreakerOpenError as e:
            # 熔断器开启，快速失败
            logger.error(f"⚡ TTS合成被熔断器拒绝: {playlist_item.item_id}")
            raise ContentNotReadyError(
                message="TTS service is temporarily unavailable",
                error_code="SERVICE_UNAVAILABLE",
                retry_after_ms=5000  # 建议5秒后重试
            )
    
    async def _do_synthesis(self, playlist_item: PlaylistItem) -> Tuple[bytes, float]:
        """实际执行TTS合成的内部方法"""
        logger.debug(f"🎤 开始执行合成: {playlist_item.item_id}")
        
        audio_chunks = []
        synthesis_start = time.time()
        
        try:
            # 计算动态超时时间
            estimated_ms = playlist_item.estimate_duration_ms()
            dynamic_timeout = max(
                self.tts_config.timeout_min,
                min(
                    estimated_ms / 1000 * self.tts_config.timeout_multiplier,
                    self.tts_config.timeout_max
                )
            )
            
            logger.info(f"🎯 TTS合成开始: {playlist_item.item_id}, "
                       f"预估时长: {estimated_ms}ms, 超时设置: {dynamic_timeout:.1f}s")
            
            async def do_synthesis():
                async for chunk in self.tts_engine.synthesize_streaming(
                    playlist_item.content
                ):
                    if chunk.audio_stream and chunk.audio_stream.audio_data:
                        audio_chunks.append(chunk.audio_stream.audio_data)
            
            await asyncio.wait_for(do_synthesis(), timeout=dynamic_timeout)
            
            if not audio_chunks:
                raise ValueError("TTS引擎返回空音频数据")
            
            audio_data = b''.join(audio_chunks)
            duration_ms = len(audio_data) / 48  # 24kHz, 16bit估算
            
            synthesis_time = (time.time() - synthesis_start) * 1000
            
            # 监控指标：区分冷启动和常规合成
            is_cold_start = not hasattr(self, '_first_synthesis_done')
            if is_cold_start:
                self._first_synthesis_done = True
                logger.info(f"🔥 TTS首次合成（冷启动）完成: {playlist_item.item_id}, "
                          f"耗时: {synthesis_time:.1f}ms, "
                          f"大小: {len(audio_data)} bytes")
            else:
                logger.info(f"✅ TTS合成完成: {playlist_item.item_id}, "
                          f"耗时: {synthesis_time:.1f}ms, "
                          f"大小: {len(audio_data)} bytes")
            
            # 性能警告
            if synthesis_time > 5000:  # 超过5秒
                logger.warning(f"⚠️ TTS合成耗时过长: {synthesis_time:.1f}ms > 5000ms")
            
            return (audio_data, duration_ms)
            
        except asyncio.TimeoutError:
            synthesis_time = (time.time() - synthesis_start) * 1000
            logger.error(f"⏱️ TTS合成超时: {playlist_item.item_id}, 耗时: {synthesis_time:.1f}ms, "
                        f"超时阈值: {dynamic_timeout:.1f}s")
            raise  # 让上层处理
            
        except Exception as e:
            synthesis_time = (time.time() - synthesis_start) * 1000
            logger.error(f"TTS合成失败: {playlist_item.item_id}, "
                       f"错误: {e}, 耗时: {synthesis_time:.1f}ms")
            raise
            
    async def _create_content_response(self, playlist_item: PlaylistItem,
                                     audio_data: bytes, duration_ms: float,
                                     index: int, request_id: Optional[str] = None) -> Dict[str, Any]:
        """创建内容响应（符合API契约v2.0）"""
        logger.debug(f"🔧 开始创建内容响应: item_id={playlist_item.item_id}, "
                    f"audio_size={len(audio_data)} bytes, duration={duration_ms:.1f}ms, index={index}")
        
        # 获取当前播放列表版本（带防御性处理）
        playlist_version = -1  # 默认值，表示元数据不可用
        warnings = []
        
        try:
            playlist_info = await self.playlist_manager.get_playlist_info()
            playlist_version = playlist_info.version
            logger.debug(f"✅ 成功获取播放列表信息: version={playlist_version}")
        except InvalidPlaylistStateError as e:
            # 精确处理播放列表状态异常
            logger.error(
                f"🚨 播放列表状态异常: {e}", 
                extra={"state_info": e.state_info, "item_id": playlist_item.item_id}
            )
            playlist_version = -1
            warnings.append("Playlist state is invalid")
            # 继续处理，不中断音频发送（弹性设计原则）
        except Exception as e:
            # 通用异常处理（包括原始的 AssertionError 等）
            logger.error(f"⚠️ 获取播放列表信息失败，使用默认值: {e}", exc_info=True)
            playlist_version = -1
            warnings.append("Failed to retrieve playlist metadata")
            # 继续处理，不中断音频发送（弹性设计原则）
        
        # 编码音频数据
        logger.debug(f"🎵 开始编码音频数据: {len(audio_data)} bytes")
        encoded_audio = base64.b64encode(audio_data).decode('utf-8')
        logger.debug(f"✅ 音频编码完成: {len(encoded_audio)} base64 characters")
        
        # 构建符合API契约v2.0的响应
        response = {
            "type": "content",
            "index": index,
            "request_id": request_id or f"server_{int(time.time() * 1000)}",  # 回显客户端的request_id
            "format": "wav",  # API契约要求的字段
            "sample_rate": 24000,  # API契约要求的字段
            "channels": 1,  # API契约要求的字段
            "duration_ms": duration_ms,
            "data": encoded_audio,  # API契约要求使用"data"而非"audio_data"
            "metadata": {  # 将元数据按契约格式组织
                "text": playlist_item.content,
                "type": playlist_item.type.value,
                "is_qa": playlist_item.type.value == "qa_answer",
                "item_id": playlist_item.item_id,
                "playlist_version": playlist_version  # 使用防御性获取的版本号
            }
        }
        
        # 添加warnings字段（如果有警告）
        if warnings:
            response["warnings"] = warnings
            logger.debug(f"⚠️ 响应包含警告: {warnings}")
        
        # 添加可选的扩展信息（不违反契约）
        response["extensions"] = {
            "server_time": time.time(),
            "estimated_size_bytes": len(audio_data),
            "content_type_display": playlist_item.type.display_name
        }
        
        # 添加性能指标（作为扩展信息）
        response["extensions"]["performance"] = {
            "synthesis_time_estimate_ms": playlist_item.estimate_duration_ms() * 0.1  # 粗略估算
        }
        
        # 添加调试信息（在开发模式下，带防御性检查）
        if (hasattr(self, 'config') and self.config and 
            self.config.get("streaming.logging.enable_trace_logging", False)):
            try:
                response["extensions"]["debug"] = {
                    "content_hash": getattr(playlist_item, 'content_hash', 'unknown'),
                    "cache_key": playlist_item.get_cache_key() if hasattr(playlist_item, 'get_cache_key') else 'unknown',
                    "original_index": getattr(playlist_item, 'original_index', None)
                }
            except Exception as e:
                logger.warning(f"⚠️ 无法添加调试信息: {e}")
                response["extensions"]["debug"] = {"error": "Debug info unavailable"}
        
        logger.debug(f"🎉 内容响应创建完成: item_id={playlist_item.item_id}, "
                    f"response_size={len(str(response))} chars, warnings={len(warnings)}")
        return response
        
    def _create_end_of_content_response(self, final_index: int) -> Dict[str, Any]:
        """创建内容结束响应"""
        return {
            "type": "stream_end",
            "final_index": final_index - 1,
            "message": "播放内容已全部完成",
            "reason": "End of playlist reached",
            "server_time": time.time()
        }
        
    def _create_error_response(self, error_type: str, message: Optional[str] = None, 
                             index: Optional[int] = None, request_id: Optional[str] = None,
                             error_id: Optional[str] = None) -> Dict[str, Any]:
        """创建错误响应（符合API契约v2.0）"""
        
        # 生成唯一错误ID
        if error_id is None:
            error_id = uuid.uuid4().hex[:12]
        
        # 标准化用户友好的错误消息
        user_messages = {
            "synthesis_failed": "Content generation temporarily unavailable",
            "internal_error": "An unexpected error occurred",
            "content_not_ready": "Content is being generated",
            "invalid_request": "Invalid request parameters"
        }
        
        # 如果没有提供消息，使用标准消息
        if message is None:
            message = user_messages.get(error_type, "Service error occurred")
        
        response = {
            "type": "error",
            "error_code": error_type.upper(),  # API契约使用error_code字段
            "message": message,
            "error_id": error_id,  # 唯一错误ID
            "request_id": request_id,  # 添加request_id以便客户端匹配
            "recoverable": error_type not in ["fatal", "system_error"],  # 指示是否可恢复
            "server_time": time.time(),
            "retry_recommended": error_type in ["synthesis_failed", "content_not_ready", "internal_error"]
        }
        
        if index is not None:
            response["index"] = index
            
        return response
        
    def _update_response_stats(self, request_start_time: float, success: bool) -> None:
        """更新响应统计"""
        response_time = (time.time() - request_start_time) * 1000
        
        # 更新平均响应时间
        if self._request_stats["total_requests"] == 1:
            self._request_stats["average_response_time_ms"] = response_time
        else:
            # 使用加权平均
            alpha = 0.1
            self._request_stats["average_response_time_ms"] = (
                (1 - alpha) * self._request_stats["average_response_time_ms"] +
                alpha * response_time
            )
            
        if not success:
            self._request_stats["failed_requests"] += 1
            
    def _calculate_qa_cache_hit_rate(self) -> float:
        """计算QA预合成缓存命中率"""
        hits = self._synthesis_metrics.get("qa_presynthesis_cache_hits", 0)
        misses = self._synthesis_metrics.get("qa_presynthesis_cache_misses", 0)
        total = hits + misses
        
        if total == 0:
            return 0.0
        
        return round(hits / total, 4)
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        # 计算运行时间
        uptime_minutes = (datetime.utcnow() - self._start_time).total_seconds() / 60
        
        # 计算成功率
        total_requests = self._request_stats["total_requests"]
        success_rate = (
            self._request_stats["successful_requests"] / total_requests
            if total_requests > 0 else 0.0
        )
        
        # 计算请求速率
        requests_per_minute = total_requests / uptime_minutes if uptime_minutes > 0 else 0.0
        
        # 尝试获取引擎统计（如果支持）
        engine_stats = {}
        if hasattr(self.tts_engine, 'get_stats'):
            try:
                engine_stats = self.tts_engine.get_stats()
            except:
                pass
        
        return {
            "uptime_minutes": round(uptime_minutes, 1),
            "request_stats": {
                **self._request_stats,
                "success_rate": round(success_rate, 4),
                "requests_per_minute": round(requests_per_minute, 2),
                "total_mb_served": round(self._request_stats["total_bytes_served"] / 1024 / 1024, 2)
            },
            "synthesis_metrics": {
                **self._synthesis_metrics,
                "race_conditions_avoided": self._synthesis_metrics.get("synthesis_tasks_waited", 0),
                "queue_based_architecture": True,  # 新架构标识
                "qa_cache_hit_rate": self._calculate_qa_cache_hit_rate()
            },
            "engine_stats": engine_stats,
            "performance": {
                "average_response_time_ms": round(self._request_stats["average_response_time_ms"], 2)
            }
        }
        
    async def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        stats = await self.get_stats()
        
        # 健康检查指标
        is_healthy = True
        issues = []
        
        # 检查成功率
        success_rate = stats["request_stats"]["success_rate"]
        if success_rate < 0.95:
            is_healthy = False
            issues.append(f"请求成功率过低: {success_rate:.2%}")
            
        # 检查响应时间
        avg_response_time = stats["request_stats"]["average_response_time_ms"]
        if avg_response_time > 5000:  # 5秒
            is_healthy = False
            issues.append(f"平均响应时间过高: {avg_response_time:.1f}ms")
            
        # 不再检查缓存命中率（由引擎内部处理）
            
        # 检查错误率
        synthesis_errors = self._request_stats["synthesis_errors"]
        if synthesis_errors > 10 or (total_requests := stats["request_stats"]["total_requests"]) and synthesis_errors / total_requests > 0.1:
            is_healthy = False
            issues.append(f"TTS合成错误过多: {synthesis_errors}")
            
        return {
            "is_healthy": is_healthy,
            "issues": issues,
            "last_check": datetime.utcnow().isoformat(),
            "stats_summary": {
                "total_requests": stats["request_stats"]["total_requests"],
                "success_rate": success_rate,
                "average_response_time_ms": avg_response_time
            }
        }
        
    async def reset_stats(self) -> None:
        """重置统计信息"""
        self._request_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "synthesis_errors": 0,
            "playlist_errors": 0,
            "client_errors": 0,
            "average_response_time_ms": 0.0,
            "total_bytes_served": 0
        }
        
        self._last_reset_time = datetime.utcnow()
        logger.info("统计信息已重置")
        
    async def preload_content(self, start_index: int, count: int) -> Dict[str, Any]:
        """
        预加载内容到缓存
        
        Args:
            start_index: 起始索引
            count: 预加载数量
            
        Returns:
            预加载结果
        """
        logger.info(f"开始预加载内容: start_index={start_index}, count={count}")
        
        preloaded = 0
        already_cached = 0
        failed = 0
        
        for i in range(start_index, start_index + count):
            playlist_item = await self.playlist_manager.get_item_at(i)
            
            if not playlist_item:
                break
                
            # 不再检查缓存（由引擎内部处理）
                
            try:
                # 触发预加载（不等待结果）
                asyncio.create_task(self._preload_single_item(playlist_item))
                preloaded += 1
                
            except Exception as e:
                logger.error(f"预加载失败: {playlist_item.item_id}, 错误: {e}")
                failed += 1
                
        result = {
            "start_index": start_index,
            "requested_count": count,
            "preloaded": preloaded,
            "already_cached": already_cached,
            "failed": failed,
            "total_processed": preloaded + already_cached + failed
        }
        
        logger.info(f"预加载完成: {result}")
        return result
        
    async def _preload_single_item(self, playlist_item: PlaylistItem) -> None:
        """预加载单个项目"""
        try:
            cache_key = playlist_item.get_cache_key()
            
            # 直接触发预合成
            audio_chunks = []
            async for chunk in self.tts_engine.synthesize_streaming(playlist_item.content):
                if chunk.audio_stream and chunk.audio_stream.audio_data:
                    audio_chunks.append(chunk.audio_stream.audio_data)
                    
            audio_data = b''.join(audio_chunks)
            logger.debug(f"预加载完成: {playlist_item.item_id}")
            
        except Exception as e:
            logger.error(f"预加载失败: {playlist_item.item_id}, 错误: {e}")
            raise
            
    def _start_guardian_monitor(self) -> None:
        """启动守护者超时监控任务
        
        该任务定期检查所有活跃的 Future，如果发现超过 5 分钟的僵尸任务，
        将强制清理它们，防止内存泄漏。
        """
        async def guardian_loop():
            GUARDIAN_TIMEOUT = 300.0  # 5 分钟守护者超时
            CHECK_INTERVAL = 30.0  # 每 30 秒检查一次
            
            while True:
                try:
                    await asyncio.sleep(CHECK_INTERVAL)
                    
                    # 新架构：不再需要Future清理
                    # EventBus的孤儿清理机制会自动处理超时的请求
                    # 这里只记录监控信息
                    logger.debug("🔍 守护者监控中... (新架构无需清理Future)")
                        
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"守护者监控任务错误: {e}")
        
        # 创建并启动守护者任务
        self._guardian_timeout_task = asyncio.create_task(guardian_loop())
        logger.info("🛡️ 守护者超时监控任务已启动")
    
    async def cleanup(self) -> None:
        """清理资源"""
        # 停止守护者监控任务
        if self._guardian_timeout_task and not self._guardian_timeout_task.done():
            self._guardian_timeout_task.cancel()
            try:
                await self._guardian_timeout_task
            except asyncio.CancelledError:
                pass
            logger.info("守护者监控任务已停止")
        
        # 新架构：不再需要清理Future
        # 所有请求都通过EventBus管理，会在EventBus.stop()时自动清理
        
        logger.info("流式内容提供器清理完成（新架构）")


# 工厂函数

def create_streaming_content_provider(playlist_manager: 'PlaylistManager',
                                    client_tracker: 'ClientStateTracker',
                                    tts_engine: Any,
                                    config: StreamingConfig,
                                    proactive_synthesizer: Optional[Any] = None) -> StreamingContentProvider:
    """创建流式内容提供器实例"""
    return StreamingContentProvider(playlist_manager, client_tracker, tts_engine, config, proactive_synthesizer)