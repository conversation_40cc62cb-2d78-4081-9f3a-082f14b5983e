"""Audio processing and queue management models

Supports concurrent TTS synthesis, priority-based audio queuing,
and crossfade transitions for seamless playback experience.
"""

from typing import Optional, Any, Dict, List
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
import asyncio

from .constants import AudioPriority, DEFAULT_SAMPLE_RATE, DEFAULT_CROSSFADE_MS


class AudioFormat(Enum):
    """Supported audio formats"""
    WAV = "wav"
    MP3 = "mp3" 
    OGG = "ogg"
    FLAC = "flac"


class AudioStatus(Enum):
    """Audio processing status"""
    PENDING = "pending"       # Waiting for TTS synthesis
    SYNTHESIZING = "synthesizing"  # Currently being synthesized
    READY = "ready"          # Ready for playback
    PLAYING = "playing"      # Currently playing
    COMPLETED = "completed"  # Playback finished
    FAILED = "failed"        # Synthesis or playback failed


class AudioChunk(BaseModel):
    """Individual audio chunk with metadata"""
    
    chunk_id: str = Field(description="Unique identifier for this audio chunk")
    text_content: str = Field(description="Original text that was synthesized")
    audio_data: Optional[bytes] = Field(
        default=None,
        description="Raw audio data (None if not yet synthesized)"
    )
    file_path: Optional[str] = Field(
        default=None,
        description="Path to audio file (alternative to audio_data)"
    )
    
    # Audio properties
    duration_ms: Optional[int] = Field(
        default=None,
        ge=0,
        description="Duration in milliseconds"
    )
    sample_rate: int = Field(
        default=DEFAULT_SAMPLE_RATE,
        description="Audio sample rate in Hz"
    )
    format: AudioFormat = Field(
        default=AudioFormat.WAV,
        description="Audio format"
    )
    
    # Processing metadata
    status: AudioStatus = Field(
        default=AudioStatus.PENDING,
        description="Current processing status"
    )
    synthesis_start_time: Optional[datetime] = Field(
        default=None,
        description="When TTS synthesis started"
    )
    synthesis_end_time: Optional[datetime] = Field(
        default=None,
        description="When TTS synthesis completed"
    )
    
    # TTS parameters
    voice_name: str = Field(
        default="zh-CN-XiaoxiaoNeural",
        description="TTS voice used for synthesis"
    )
    speaking_rate: float = Field(
        default=1.0,
        ge=0.5,
        le=2.0,
        description="Speaking rate multiplier"
    )
    
    # Script segment metadata (optional)
    segment_id: Optional[str] = Field(
        default=None,
        description="ID of the script segment this audio was generated from"
    )
    segment_title: Optional[str] = Field(
        default=None,
        description="Title of the script segment"
    )
    segment_type: Optional[str] = Field(
        default=None,
        description="Type of the script segment"
    )
    priority: AudioPriority = Field(
        default=AudioPriority.NORMAL,
        description="Audio playback priority"
    )
    generated_at: Optional[datetime] = Field(
        default=None,
        description="When this audio chunk was generated"
    )
    segment_index: Optional[int] = Field(
        default=None,
        description="Index of this segment in the timeline"
    )
    
    # Additional fields...
    
    # Crossfade support
    fade_in_ms: int = Field(
        default=0,
        ge=0,
        le=1000,
        description="Fade-in duration in milliseconds"
    )
    fade_out_ms: int = Field(
        default=0,
        ge=0,
        le=1000,
        description="Fade-out duration in milliseconds"
    )
    
    def mark_synthesis_started(self) -> None:
        """Mark chunk as starting synthesis"""
        self.status = AudioStatus.SYNTHESIZING
        self.synthesis_start_time = datetime.utcnow()
    
    def mark_synthesis_completed(self, audio_data: bytes, duration_ms: int) -> None:
        """Mark chunk synthesis as completed
        
        Args:
            audio_data: Synthesized audio data
            duration_ms: Audio duration in milliseconds
        """
        self.audio_data = audio_data
        self.duration_ms = duration_ms
        self.status = AudioStatus.READY
        self.synthesis_end_time = datetime.utcnow()
    
    def mark_synthesis_failed(self) -> None:
        """Mark chunk synthesis as failed"""
        self.status = AudioStatus.FAILED
        self.synthesis_end_time = datetime.utcnow()
    
    def get_synthesis_duration_ms(self) -> Optional[int]:
        """Get TTS synthesis duration in milliseconds
        
        Returns:
            Synthesis duration or None if not completed
        """
        if self.synthesis_start_time and self.synthesis_end_time:
            delta = self.synthesis_end_time - self.synthesis_start_time
            return int(delta.total_seconds() * 1000)
        return None


class AudioQueueItem(BaseModel):
    """Audio queue item with priority and metadata"""
    
    item_id: str = Field(description="Unique identifier for queue item")
    audio_chunk: AudioChunk = Field(description="Audio chunk to play")
    priority: AudioPriority = Field(description="Playback priority")
    
    # Queue metadata
    queued_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="When item was added to queue"
    )
    play_after: Optional[datetime] = Field(
        default=None,
        description="Earliest time this item can be played"
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        description="When this item expires and should be removed"
    )
    
    # Playback settings
    interrupt_current: bool = Field(
        default=False,
        description="Whether this item can interrupt current playback"
    )
    crossfade_duration_ms: int = Field(
        default=DEFAULT_CROSSFADE_MS,
        ge=0,
        le=500,
        description="Crossfade duration for smooth transitions"
    )
    
    # Context information
    context_type: str = Field(
        default="narration",
        description="Type of content (narration, qa, transition, welcome, etc.)"
    )
    related_question_id: Optional[str] = Field(
        default=None,
        description="Related question ID if this is a Q&A response"
    )
    
    def is_expired(self) -> bool:
        """Check if this queue item has expired
        
        Returns:
            True if expired, False otherwise
        """
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def can_play_now(self) -> bool:
        """Check if this item can be played now
        
        Returns:
            True if ready to play, False if should wait
        """
        if self.play_after is None:
            return True
        return datetime.utcnow() >= self.play_after
    
    def get_queue_wait_time_ms(self) -> int:
        """Get how long this item has been waiting in queue
        
        Returns:
            Wait time in milliseconds
        """
        delta = datetime.utcnow() - self.queued_at
        return int(delta.total_seconds() * 1000)


class AudioQueue(BaseModel):
    """Priority-based audio queue with automatic cleanup"""
    
    items: List[AudioQueueItem] = Field(
        default_factory=list,
        description="Queue items sorted by priority"
    )
    max_queue_size: int = Field(
        default=150,  # 增加到150以支持流式音频的大量小块
        ge=1,
        le=500,  # 提高上限以支持更长的流式内容
        description="Maximum number of items in queue"
    )
    auto_cleanup_enabled: bool = Field(
        default=True,
        description="Automatically remove expired items"
    )
    
    def add_item(self, item: AudioQueueItem) -> bool:
        """Add item to queue with priority sorting
        
        Args:
            item: AudioQueueItem to add
            
        Returns:
            True if added successfully, False if queue full
        """
        if len(self.items) >= self.max_queue_size:
            # Try to make space by removing expired items
            if self.auto_cleanup_enabled:
                self._cleanup_expired_items()
            
            # If still full, reject low priority items
            if len(self.items) >= self.max_queue_size:
                return False
        
        self.items.append(item)
        self._sort_by_priority()
        return True
    
    def get_next_item(self) -> Optional[AudioQueueItem]:
        """Get next item ready for playback
        
        Returns:
            Next AudioQueueItem or None if queue empty or no items ready
        """
        if self.auto_cleanup_enabled:
            self._cleanup_expired_items()
        
        for item in self.items:
            if item.can_play_now() and not item.is_expired():
                return item
        
        return None
    
    def remove_item(self, item_id: str) -> bool:
        """Remove item from queue by ID
        
        Args:
            item_id: ID of item to remove
            
        Returns:
            True if removed, False if not found
        """
        for i, item in enumerate(self.items):
            if item.item_id == item_id:
                del self.items[i]
                return True
        return False
    
    def clear_context_items(self, context_type: str) -> int:
        """Remove all items of a specific context type
        
        Args:
            context_type: Context type to remove
            
        Returns:
            Number of items removed
        """
        original_count = len(self.items)
        self.items = [
            item for item in self.items 
            if item.context_type != context_type
        ]
        return original_count - len(self.items)
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """Get queue statistics
        
        Returns:
            Dictionary with queue metrics
        """
        if not self.items:
            return {
                "total_items": 0,
                "by_priority": {},
                "by_context": {},
                "average_wait_time_ms": 0,
            }
        
        by_priority = {}
        by_context = {}
        total_wait_time = 0
        
        for item in self.items:
            # Count by priority
            priority_name = item.priority.name
            by_priority[priority_name] = by_priority.get(priority_name, 0) + 1
            
            # Count by context
            context = item.context_type
            by_context[context] = by_context.get(context, 0) + 1
            
            # Sum wait times
            total_wait_time += item.get_queue_wait_time_ms()
        
        return {
            "total_items": len(self.items),
            "by_priority": by_priority,
            "by_context": by_context,
            "average_wait_time_ms": total_wait_time // len(self.items),
        }
    
    def _sort_by_priority(self) -> None:
        """Sort queue items by priority (lower number = higher priority)"""
        self.items.sort(key=lambda item: item.priority.value)
    
    def _cleanup_expired_items(self) -> None:
        """Remove expired items from queue"""
        self.items = [item for item in self.items if not item.is_expired()]