"""
成就仪表盘 API
提供系统累计成就数据和核心资产统计

Author: Claude Code
Date: 2025-08-12
"""

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import yaml
import os
from pathlib import Path
from loguru import logger

# 创建路由
router = APIRouter(
    prefix="/api/achievements",
    tags=["achievements"],
    responses={404: {"description": "Not found"}},
)


def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    config_path = Path(__file__).parent.parent.parent.parent / "config.yml"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config.get('achievements_dashboard', {})
    except Exception as e:
        logger.error(f"Failed to load config: {e}")
        return {}


async def get_real_data() -> Dict[str, Any]:
    """
    获取真实数据
    TODO: 实现真实数据获取逻辑，从数据库聚合统计
    """
    try:
        # 导入必要的服务
        from ..services.product_service import ProductService
        from ..services.form_storage import FormStorage
        from ..services.script_repository import ScriptRepository
        
        # 获取商品数量
        product_service = ProductService()
        products = await product_service.list_products()
        total_products = len(products)
        
        # 获取配置数量
        form_storage = FormStorage()
        forms = await form_storage.list_forms()
        total_configs = len(forms)
        
        # 获取脚本数量
        script_repo = ScriptRepository()
        scripts = await script_repo.list_scripts()
        total_scripts = len(scripts)
        
        # TODO: 从数据库获取直播历史数据
        # 目前返回默认值，等待数据库表创建后实现
        return {
            "total_sessions": 0,
            "total_duration_hours": 0.0,
            "total_questions_answered": 0,
            "total_products": total_products,
            "total_configs": total_configs,
            "total_scripts": total_scripts,
            "avg_session_duration_hours": 0.0,
            "avg_questions_per_session": 0,
            "system_uptime_days": 1,
            "data_source": "real"
        }
    except Exception as e:
        logger.error(f"Error fetching real data: {e}")
        # 如果获取真实数据失败，返回空数据
        return {
            "total_sessions": 0,
            "total_duration_hours": 0.0,
            "total_questions_answered": 0,
            "total_products": 0,
            "total_configs": 0,
            "total_scripts": 0,
            "avg_session_duration_hours": 0.0,
            "avg_questions_per_session": 0,
            "system_uptime_days": 0,
            "data_source": "error"
        }


def get_mock_data(config: Dict[str, Any]) -> Dict[str, Any]:
    """获取模拟数据"""
    mock_data = config.get('mock_data', {})
    return {
        "total_sessions": mock_data.get('total_sessions', 256),
        "total_duration_hours": mock_data.get('total_duration_hours', 1024.5),
        "total_questions_answered": mock_data.get('total_questions_answered', 3842),
        "total_products": mock_data.get('total_products', 128),
        "total_configs": mock_data.get('total_configs', 42),
        "total_scripts": mock_data.get('total_scripts', 85),
        "avg_session_duration_hours": mock_data.get('avg_session_duration_hours', 4.0),
        "avg_questions_per_session": mock_data.get('avg_questions_per_session', 15),
        "system_uptime_days": mock_data.get('system_uptime_days', 30),
        "data_source": "mock"
    }


@router.get("/")
async def get_achievements() -> Dict[str, Any]:
    """
    获取成就仪表盘数据
    
    Returns:
        包含系统成就和资产统计的JSON数据
    """
    try:
        # 加载配置
        config = load_config()
        
        # 检查是否启用成就仪表盘
        if not config.get('enabled', True):
            return JSONResponse(
                status_code=503,
                content={"error": "Achievements dashboard is disabled"}
            )
        
        # 根据配置决定使用mock数据还是真实数据
        use_mock = config.get('use_mock_data', True)
        
        if use_mock:
            data = get_mock_data(config)
            logger.info("📊 Using mock data for achievements dashboard")
        else:
            data = await get_real_data()
            logger.info("📊 Using real data for achievements dashboard")
        
        # 添加元数据
        data.update({
            "timestamp": datetime.now().isoformat(),
            "refresh_interval": config.get('refresh_interval_seconds', 60),
            "animation_duration": config.get('animation_duration_ms', 2000)
        })
        
        # 计算额外的展示指标
        if data["total_sessions"] > 0:
            data["avg_session_duration_hours"] = round(
                data["total_duration_hours"] / data["total_sessions"], 2
            )
            data["avg_questions_per_session"] = round(
                data["total_questions_answered"] / data["total_sessions"], 1
            )
        
        # 格式化大数字，便于展示
        data["formatted"] = {
            "sessions": format_number(data["total_sessions"]),
            "duration": format_duration(data["total_duration_hours"]),
            "questions": format_number(data["total_questions_answered"]),
            "products": format_number(data["total_products"]),
            "configs": format_number(data["total_configs"]),
            "scripts": format_number(data["total_scripts"])
        }
        
        return data
        
    except Exception as e:
        logger.error(f"Error in get_achievements: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/summary")
async def get_achievements_summary() -> Dict[str, Any]:
    """
    获取成就摘要（简化版本）
    
    Returns:
        核心成就指标的简化版本
    """
    try:
        full_data = await get_achievements()
        
        # 返回简化的摘要数据
        return {
            "core_metrics": {
                "sessions": full_data["total_sessions"],
                "hours": full_data["total_duration_hours"],
                "questions": full_data["total_questions_answered"]
            },
            "assets": {
                "products": full_data["total_products"],
                "configs": full_data["total_configs"],
                "scripts": full_data["total_scripts"]
            },
            "data_source": full_data["data_source"],
            "timestamp": full_data["timestamp"]
        }
        
    except Exception as e:
        logger.error(f"Error in get_achievements_summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def format_number(num: int) -> str:
    """
    格式化数字显示
    1234 -> 1,234
    1234567 -> 1.2M
    """
    if num >= 1000000:
        return f"{num/1000000:.1f}M"
    elif num >= 1000:
        return f"{num/1000:.1f}K"
    else:
        return str(num)


def format_duration(hours: float) -> str:
    """
    格式化时长显示
    将小时转换为更友好的显示格式
    """
    if hours >= 24:
        days = int(hours / 24)
        remaining_hours = int(hours % 24)
        if remaining_hours > 0:
            return f"{days}天{remaining_hours}小时"
        else:
            return f"{days}天"
    else:
        return f"{hours:.1f}小时"


# 用于测试的端点
@router.get("/test")
async def test_achievements():
    """测试端点，返回固定的测试数据"""
    return {
        "status": "ok",
        "message": "Achievements API is working",
        "test_data": {
            "sessions": 100,
            "duration_hours": 400,
            "questions": 1500
        }
    }