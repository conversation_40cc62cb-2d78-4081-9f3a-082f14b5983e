"""直播流状态枚举定义

状态驱动架构的核心组件，定义了所有可能的直播流状态，
用于驱动LiveStreamController的主循环决策逻辑。

Author: Claude Code  
Date: 2025-08-07
"""

from enum import Enum


class LiveStreamState(Enum):
    """直播流状态枚举 - 状态驱动架构的核心
    
    每个状态代表直播流的一个特定阶段，状态机主循环会根据当前状态
    决定执行相应的处理逻辑。
    """
    
    # 基础状态
    IDLE = "idle"                              # 空闲状态，未开始播放
    STREAMING = "streaming"                    # 正常播放主内容
    PAUSED = "paused"                         # 暂停状态
    STOPPED = "stopped"                       # 已停止
    
    # QA流程状态  
    TRANSITION_TO_QA = "transition_to_qa"     # 即将进入QA环节，播放过渡语
    ANSWERING_QA = "answering_qa"             # 正在回答问题
    TRANSITION_FROM_QA = "transition_from_qa" # 从QA回到主内容，播放过渡语
    
    # 系统状态
    ERROR_RECOVERY = "error_recovery"          # 错误恢复状态，确保系统健壮性
    
    def __str__(self) -> str:
        """状态的字符串表示"""
        return self.value
    
    def is_qa_related(self) -> bool:
        """判断是否为QA相关状态"""
        return self in [
            LiveStreamState.TRANSITION_TO_QA,
            LiveStreamState.ANSWERING_QA,
            LiveStreamState.TRANSITION_FROM_QA
        ]
    
    def is_active_playback(self) -> bool:
        """判断是否为活跃播放状态（需要音频输出）"""
        return self in [
            LiveStreamState.STREAMING,
            LiveStreamState.TRANSITION_TO_QA,
            LiveStreamState.ANSWERING_QA,
            LiveStreamState.TRANSITION_FROM_QA
        ]


class StateTransitionError(Exception):
    """状态转换异常"""
    
    def __init__(self, from_state: LiveStreamState, to_state: LiveStreamState, reason: str = ""):
        self.from_state = from_state
        self.to_state = to_state
        self.reason = reason
        super().__init__(f"Invalid transition from {from_state} to {to_state}: {reason}")


def validate_state_transition(from_state: LiveStreamState, to_state: LiveStreamState) -> bool:
    """验证状态转换是否合法
    
    Args:
        from_state: 源状态
        to_state: 目标状态
        
    Returns:
        bool: 是否为合法转换
    """
    # 允许的状态转换规则
    valid_transitions = {
        LiveStreamState.IDLE: [
            LiveStreamState.STREAMING,
            LiveStreamState.TRANSITION_TO_QA,  # 允许空闲状态直接处理QA
            LiveStreamState.STOPPED,
            LiveStreamState.ERROR_RECOVERY
        ],
        LiveStreamState.STREAMING: [
            LiveStreamState.TRANSITION_TO_QA,
            LiveStreamState.PAUSED,
            LiveStreamState.STOPPED,
            LiveStreamState.IDLE,  # 🔥 CRITICAL FIX: 允许正常播放完成后进入空闲状态
            LiveStreamState.ERROR_RECOVERY
        ],
        LiveStreamState.TRANSITION_TO_QA: [
            LiveStreamState.ANSWERING_QA,
            LiveStreamState.ERROR_RECOVERY,
            LiveStreamState.STOPPED
        ],
        LiveStreamState.ANSWERING_QA: [
            LiveStreamState.TRANSITION_FROM_QA,
            LiveStreamState.ERROR_RECOVERY,
            LiveStreamState.STOPPED
        ],
        LiveStreamState.TRANSITION_FROM_QA: [
            LiveStreamState.STREAMING,
            LiveStreamState.ERROR_RECOVERY,
            LiveStreamState.STOPPED
        ],
        LiveStreamState.PAUSED: [
            LiveStreamState.STREAMING,
            LiveStreamState.TRANSITION_TO_QA,  # 允许暂停状态处理QA（根据配置）
            LiveStreamState.STOPPED,
            LiveStreamState.ERROR_RECOVERY
        ],
        LiveStreamState.ERROR_RECOVERY: [
            LiveStreamState.STREAMING,
            LiveStreamState.STOPPED,
            LiveStreamState.IDLE
        ],
        LiveStreamState.STOPPED: [
            LiveStreamState.IDLE
        ]
    }
    
    allowed_states = valid_transitions.get(from_state, [])
    return to_state in allowed_states