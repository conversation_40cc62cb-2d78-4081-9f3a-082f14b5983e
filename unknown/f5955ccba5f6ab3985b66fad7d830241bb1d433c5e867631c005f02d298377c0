"""
幽灵播放监控指标

监控和记录幽灵播放相关的指标，帮助运营人员及时发现问题。
"""

import time
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger


class GhostPlaybackMetrics:
    """幽灵播放监控指标收集器
    
    收集的指标包括：
    - ghost_playback_duration: 幽灵播放持续时间
    - wasted_tts_requests: 浪费的TTS请求数
    - client_dropout_rate: 客户端掉线率
    - no_client_audio_chunks: 无客户端时广播的音频块数
    """
    
    def __init__(self):
        # 幽灵播放相关
        self._ghost_playback_start_time: Optional[float] = None
        self._total_ghost_playback_duration = 0.0
        self._ghost_playback_count = 0
        self._current_ghost_session_wasted_chunks = 0
        
        # TTS浪费统计
        self._total_wasted_tts_requests = 0
        self._total_wasted_audio_bytes = 0
        
        # 客户端连接统计
        self._total_client_connections = 0
        self._total_client_dropouts = 0
        self._last_client_connected_time: Optional[float] = None
        
        # 启动时间
        self._start_time = time.time()
        
    def start_ghost_playback(self):
        """开始记录幽灵播放"""
        if self._ghost_playback_start_time is None:
            self._ghost_playback_start_time = time.time()
            self._ghost_playback_count += 1
            self._current_ghost_session_wasted_chunks = 0
            logger.warning("👻 Ghost playback started - no clients connected")
    
    def end_ghost_playback(self):
        """结束幽灵播放记录"""
        if self._ghost_playback_start_time is not None:
            duration = time.time() - self._ghost_playback_start_time
            self._total_ghost_playback_duration += duration
            self._ghost_playback_start_time = None
            
            logger.info(f"👻 Ghost playback ended - duration: {duration:.1f}s, "
                       f"wasted chunks: {self._current_ghost_session_wasted_chunks}")
            
            # 重置当前会话计数
            self._current_ghost_session_wasted_chunks = 0
    
    def record_wasted_audio_chunk(self, chunk_size_bytes: int):
        """记录浪费的音频块"""
        self._total_wasted_tts_requests += 1
        self._total_wasted_audio_bytes += chunk_size_bytes
        
        if self._ghost_playback_start_time is not None:
            self._current_ghost_session_wasted_chunks += 1
    
    def record_client_connection(self):
        """记录客户端连接"""
        self._total_client_connections += 1
        self._last_client_connected_time = time.time()
        
        # 如果正在幽灵播放，结束它
        if self._ghost_playback_start_time is not None:
            self.end_ghost_playback()
    
    def record_client_dropout(self):
        """记录客户端掉线"""
        self._total_client_dropouts += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取所有监控指标"""
        uptime = time.time() - self._start_time
        
        # 计算当前幽灵播放时长（如果正在进行）
        current_ghost_duration = 0.0
        if self._ghost_playback_start_time is not None:
            current_ghost_duration = time.time() - self._ghost_playback_start_time
        
        # 计算客户端掉线率
        dropout_rate = 0.0
        if self._total_client_connections > 0:
            dropout_rate = self._total_client_dropouts / self._total_client_connections
        
        return {
            "ghost_playback": {
                "is_active": self._ghost_playback_start_time is not None,
                "current_duration_seconds": current_ghost_duration,
                "total_duration_seconds": self._total_ghost_playback_duration + current_ghost_duration,
                "total_count": self._ghost_playback_count,
                "average_duration_seconds": (
                    (self._total_ghost_playback_duration / self._ghost_playback_count)
                    if self._ghost_playback_count > 0 else 0
                ),
                "percentage_of_uptime": (
                    ((self._total_ghost_playback_duration + current_ghost_duration) / uptime * 100)
                    if uptime > 0 else 0
                )
            },
            "wasted_resources": {
                "total_wasted_tts_requests": self._total_wasted_tts_requests,
                "total_wasted_audio_mb": self._total_wasted_audio_bytes / 1024 / 1024,
                "current_session_wasted_chunks": self._current_ghost_session_wasted_chunks,
                "average_waste_per_ghost_session": (
                    self._total_wasted_tts_requests / self._ghost_playback_count
                    if self._ghost_playback_count > 0 else 0
                )
            },
            "client_connection": {
                "total_connections": self._total_client_connections,
                "total_dropouts": self._total_client_dropouts,
                "dropout_rate": dropout_rate * 100,  # 转换为百分比
                "last_connected": (
                    datetime.fromtimestamp(self._last_client_connected_time).isoformat()
                    if self._last_client_connected_time else None
                )
            },
            "system": {
                "uptime_seconds": uptime,
                "metrics_start_time": datetime.fromtimestamp(self._start_time).isoformat()
            }
        }
    
    def get_alert_summary(self) -> Optional[str]:
        """获取告警摘要
        
        如果有需要关注的问题，返回告警信息。
        """
        metrics = self.get_metrics()
        alerts = []
        
        # 检查是否正在幽灵播放
        if metrics["ghost_playback"]["is_active"]:
            duration = metrics["ghost_playback"]["current_duration_seconds"]
            if duration > 60:  # 超过1分钟
                alerts.append(f"⚠️ Ghost playback active for {duration:.0f}s")
        
        # 检查幽灵播放占比
        ghost_percentage = metrics["ghost_playback"]["percentage_of_uptime"]
        if ghost_percentage > 10:  # 超过10%的时间在幽灵播放
            alerts.append(f"⚠️ Ghost playback {ghost_percentage:.1f}% of uptime")
        
        # 检查客户端掉线率
        dropout_rate = metrics["client_connection"]["dropout_rate"]
        if dropout_rate > 20:  # 掉线率超过20%
            alerts.append(f"⚠️ High client dropout rate: {dropout_rate:.1f}%")
        
        # 检查浪费的资源
        wasted_mb = metrics["wasted_resources"]["total_wasted_audio_mb"]
        if wasted_mb > 100:  # 浪费超过100MB
            alerts.append(f"⚠️ Wasted {wasted_mb:.1f}MB on ghost playback")
        
        if alerts:
            return " | ".join(alerts)
        return None


# 全局实例
_ghost_metrics = GhostPlaybackMetrics()


def get_ghost_playback_metrics() -> GhostPlaybackMetrics:
    """获取全局幽灵播放监控指标实例"""
    return _ghost_metrics