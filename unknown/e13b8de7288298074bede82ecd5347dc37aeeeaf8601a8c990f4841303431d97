"""Factory classes for creating TTS engines and LLM adapters

Provides configurable factory classes that can instantiate different
TTS engines and LLM adapters based on configuration.
"""

from typing import Dict, Any, Optional
from loguru import logger

from .tts_engines.base import BaseTTSEngine
from .tts_engines.cosyvoice_unified_engine import CosyVoiceUnifiedEngine  # New unified engine
from .tts_engines.tts_cache_proxy_engine import TTSCacheProxyEngine  # TTS Cache Proxy Engine
# Legacy engines moved to archive or don't exist - only importing unified engine

from .llm_adapters.base import BaseLLMAdapter
from .llm_adapters.qwen_adapter import QwenLLMAdapter
from .llm_adapters.openai_adapter import OpenAILLMAdapter
from .llm_adapters.litellm_adapter import LiteLLMAdapter

from ..core.config import cfg
from ..core.exceptions import ServiceError


class TTSEngineFactory:
    """Factory for creating TTS engines"""
    
    # Registry of available TTS engines
    _engines = {
        # New unified engine - the only available engine after refactoring
        'cosyvoice_unified': CosyVoiceUnifiedEngine,  # Primary recommendation
        'unified': CosyVoiceUnifiedEngine,            # Alias
        'default': CosyVoiceUnifiedEngine,            # Default fallback
        'cosyvoice_v2': CosyVoiceUnifiedEngine,       # Legacy alias for compatibility
        'cozyvoice_cloud': CosyVoiceUnifiedEngine,    # Typo-tolerant alias
        'cozyvoice': CosyVoiceUnifiedEngine,          # Typo-tolerant alias
        # TTS Cache Proxy Engine
        'tts_cache_proxy': TTSCacheProxyEngine,       # Cache proxy engine
        'cache_proxy': TTSCacheProxyEngine,           # Alias
        # Legacy engines removed during refactoring - only unified engine available
    }
    
    @classmethod
    def create_engine(
        cls, 
        engine_name: Optional[str] = None, 
        config: Optional[Dict[str, Any]] = None
    ) -> BaseTTSEngine:
        """Create a TTS engine instance
        
        Args:
            engine_name: Name of the TTS engine (defaults to configured engine)
            config: Engine-specific configuration (defaults to config from YAML)
            
        Returns:
            Initialized TTS engine instance
            
        Raises:
            ServiceError: If engine not found or creation fails
        """
        try:
            # Use default engine if none specified
            if engine_name is None:
                engine_name = cfg.tts_engine
            
            # Get configuration if not provided
            if config is None:
                config = cfg.get_tts_config(engine_name)
            
            # Normalize engine name
            engine_name = engine_name.lower().replace('-', '_')
            if engine_name == 'edge_tts':
                engine_name = 'edge-tts'
            
            # Find engine class
            if engine_name not in cls._engines:
                available = list(cls._engines.keys())
                raise ServiceError(
                    f"Unknown TTS engine '{engine_name}'. "
                    f"Available engines: {available}",
                    service_name=engine_name
                )
            
            engine_class = cls._engines[engine_name]
            
            # Create and return engine instance
            engine = engine_class(config)
            logger.info(f"Created TTS engine: {engine_name}")
            
            return engine
            
        except Exception as e:
            logger.error(f"Failed to create TTS engine '{engine_name}': {e}")
            raise ServiceError(f"TTS engine creation failed: {e}", service_name=engine_name)
    
    @classmethod
    def register_engine(
        cls, 
        name: str, 
        engine_class: type
    ) -> None:
        """Register a new TTS engine
        
        Args:
            name: Engine name
            engine_class: Engine class (must inherit from BaseTTSEngine)
        """
        if not issubclass(engine_class, BaseTTSEngine):
            raise ValueError("Engine class must inherit from BaseTTSEngine")
        
        cls._engines[name] = engine_class
        logger.info(f"Registered TTS engine: {name}")
    
    @classmethod
    def get_available_engines(cls) -> list:
        """Get list of available engine names"""
        return list(cls._engines.keys())


class LLMAdapterFactory:
    """Factory for creating LLM adapters"""
    
    # Registry of available LLM adapters
    _adapters = {
        # LiteLLM unified gateway (preferred)
        'litellm': LiteLLMAdapter,
        'gateway': LiteLLMAdapter,
        'unified': LiteLLMAdapter,
        
        # Direct provider adapters (fallback)
        'qwen': QwenLLMAdapter,
        'qwen-plus': QwenLLMAdapter,
        'qwen-turbo': QwenLLMAdapter,
        'openai': OpenAILLMAdapter,
        'openai-gpt4': OpenAILLMAdapter,
        'gpt-4': OpenAILLMAdapter,
        'gpt-4o': OpenAILLMAdapter,
        'gpt-4o-mini': OpenAILLMAdapter,
    }
    
    @classmethod
    def create_adapter(
        cls, 
        provider_name: Optional[str] = None, 
        config: Optional[Dict[str, Any]] = None
    ) -> BaseLLMAdapter:
        """Create an LLM adapter instance
        
        Args:
            provider_name: Name of the LLM provider (defaults to configured provider)
            config: Provider-specific configuration (defaults to config from YAML)
            
        Returns:
            Initialized LLM adapter instance
            
        Raises:
            ServiceError: If adapter not found or creation fails
        """
        try:
            # Use default provider if none specified
            if provider_name is None:
                provider_name = cfg.ai_model_provider
            
            # Get configuration if not provided
            if config is None:
                config = cfg.get_llm_config(provider_name)
            
            # Normalize provider name
            provider_key = provider_name.lower()
            
            # Find adapter class
            if provider_key not in cls._adapters:
                available = list(cls._adapters.keys())
                raise ServiceError(
                    f"Unknown LLM provider '{provider_name}'. "
                    f"Available providers: {available}"
                )
            
            adapter_class = cls._adapters[provider_key]
            
            # Create and return adapter instance
            adapter = adapter_class(config)
            logger.info(f"Created LLM adapter: {provider_name}")
            
            return adapter
            
        except Exception as e:
            logger.error(f"Failed to create LLM adapter '{provider_name}': {e}")
            raise ServiceError(f"LLM adapter creation failed: {e}", service_name=provider_name)
    
    @classmethod
    def register_adapter(
        cls, 
        name: str, 
        adapter_class: type
    ) -> None:
        """Register a new LLM adapter
        
        Args:
            name: Adapter name
            adapter_class: Adapter class (must inherit from BaseLLMAdapter)
        """
        if not issubclass(adapter_class, BaseLLMAdapter):
            raise ValueError("Adapter class must inherit from BaseLLMAdapter")
        
        cls._adapters[name] = adapter_class
        logger.info(f"Registered LLM adapter: {name}")
    
    @classmethod
    def get_available_adapters(cls) -> list:
        """Get list of available adapter names"""
        return list(cls._adapters.keys())


class ServiceFactory:
    """Unified factory for creating services with fallback support"""
    
    @classmethod
    async def create_tts_engine(
        cls, 
        engine_name: Optional[str] = None
    ) -> BaseTTSEngine:
        """Create TTS engine - fail-fast principle
        
        Args:
            engine_name: Engine name (defaults to configured engine)
            
        Returns:
            Initialized TTS engine instance
            
        Raises:
            ServiceError: If engine creation or initialization fails
        """
        # Use specified engine or default from config
        if engine_name is None:
            engine_name = cfg.tts_engine or 'cosyvoice_unified'
        
        try:
            # Create and initialize the engine
            engine = TTSEngineFactory.create_engine(engine_name)
            await engine.initialize()
            logger.info(f"Successfully initialized TTS engine: {engine_name}")
            return engine
            
        except Exception as e:
            # Fail fast - no fallback
            logger.error(f"Failed to initialize TTS engine '{engine_name}': {e}")
            raise ServiceError(
                f"TTS engine '{engine_name}' initialization failed: {e}",
                service_name="TTS"
            ) from e
    
    @classmethod
    async def create_llm_adapter(
        cls, 
        provider_name: Optional[str] = None
    ) -> BaseLLMAdapter:
        """Create LLM adapter - fail-fast principle
        
        Args:
            provider_name: Provider name (defaults to configured provider)
            
        Returns:
            Initialized LLM adapter instance
            
        Raises:
            ServiceError: If adapter creation or initialization fails
        """
        # Use specified provider or default from config
        if provider_name is None:
            provider_name = cfg.ai_model_provider or 'litellm'
        
        try:
            # Create and initialize the adapter
            adapter = LLMAdapterFactory.create_adapter(provider_name)
            await adapter.initialize()
            logger.info(f"Successfully initialized LLM adapter: {provider_name}")
            return adapter
            
        except Exception as e:
            # Fail fast - no fallback
            logger.error(f"Failed to initialize LLM adapter '{provider_name}': {e}")
            raise ServiceError(
                f"LLM adapter '{provider_name}' initialization failed: {e}",
                service_name="LLM"
            ) from e