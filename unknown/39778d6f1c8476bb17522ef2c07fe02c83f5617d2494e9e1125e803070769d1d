"""LiteLLM Unified Adapter implementation

Implements LLM interface using LiteLLM as a unified model gateway
for multi-provider model access with automatic load balancing and fallback.
"""

import asyncio
import time
import hashlib
import json
from typing import Dict, Any, Optional, List, AsyncGenerator, Tuple
from loguru import logger
from pydantic import ValidationError

from .base import BaseLLMAdapter, LLMMessage, LLMResponse
from ...core.exceptions import ServiceError
from ...core.config import cfg


def _create_service_error(message: str, error_code: Optional[str] = None, **kwargs) -> ServiceError:
    """Create ServiceError with litellm service name pre-filled
    
    Args:
        message: Error message
        error_code: Optional error code
        **kwargs: Additional ServiceError parameters
        
    Returns:
        ServiceError instance with service_name="litellm"
    """
    return ServiceError(message, service_name="litellm", error_code=error_code, **kwargs)


class LiteLLMAdapter(BaseLLMAdapter):
    """LiteLLM unified adapter for multi-provider model access"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize LiteLLM adapter
        
        Args:
            config: LiteLLM configuration from config.yml
        """
        super().__init__(config)
        self._client = None
        self._model = config.get('model', cfg.ai_model_provider)
        self._api_base = config.get('api_base', 'http://localhost:4000')
        self._timeout = config.get('timeout_seconds', 30)
        self._retry_attempts = config.get('retry_attempts', 3)
        self._master_key = config.get('master_key', cfg.litellm_master_key)
        
        # Cache-related attributes
        self._cache_enabled = False
        self._cache_type = 'local'
        self._cache_stats = {'hits': 0, 'misses': 0, 'total': 0}
        
        # Request coalescing for cache stampede prevention
        self._inflight_requests: Dict[str, asyncio.Task] = {}
        self._request_lock = asyncio.Lock()
        
        logger.info(f"LiteLLM adapter initialized with model: {self._model}")
    
    async def initialize(self) -> None:
        """Initialize LiteLLM client with advanced caching and robustness"""
        try:
            # Import LiteLLM
            import litellm
            from litellm.caching.caching import Cache
            from openai import AsyncOpenAI
            
            # Check if master key is available
            if not self._master_key:
                raise _create_service_error(
                    "LITELLM_MASTER_KEY not found in environment variables"
                )
            
            # Configure LiteLLM settings
            litellm.set_verbose = False  # Reduce noise in logs
            litellm.drop_params = True   # Drop unsupported parameters
            
            # Get cache configuration from config.yml
            cache_config = cfg.get_yaml_config('llm.caching', {})
            cache_enabled = cache_config.get('enabled', self.config.get('cache_enabled', True))
            
            if cache_enabled:
                cache_type = cache_config.get('type', 'local')
                ttl_seconds = cache_config.get('ttl_seconds', 3600)
                max_cache_size = cache_config.get('max_cache_size', 1000)
                
                try:
                    if cache_type == 'redis':
                        # Try to initialize Redis cache
                        try:
                            import redis
                            
                            # Test Redis connection first
                            redis_host = cfg.redis_host or 'localhost'
                            redis_port = cfg.redis_port or 6379
                            redis_password = cfg.redis_password
                            
                            redis_client = redis.Redis(
                                host=redis_host,
                                port=redis_port,
                                password=redis_password,
                                socket_connect_timeout=2,
                                socket_timeout=2
                            )
                            redis_client.ping()  # Test connection
                            
                            # If connection successful, use Redis cache
                            litellm.cache = Cache(
                                type="redis",
                                host=redis_host,
                                port=redis_port,
                                password=redis_password,
                                ttl=ttl_seconds
                            )
                            self._cache_type = 'redis'
                            self._cache_enabled = True
                            logger.info(f"✅ Redis caching enabled with TTL: {ttl_seconds}s")
                            
                        except (ImportError, Exception) as redis_error:
                            # Redis not available or connection failed - fallback to local
                            logger.warning(f"⚠️ Redis cache initialization failed: {redis_error}, falling back to local cache")
                            litellm.cache = Cache(
                                type="local",
                                ttl=ttl_seconds,
                                max_size=max_cache_size
                            )
                            self._cache_type = 'local'
                            self._cache_enabled = True
                            logger.info(f"✅ Local caching enabled (fallback) with TTL: {ttl_seconds}s, max_size: {max_cache_size}")
                    
                    else:  # local cache explicitly configured or default
                        litellm.cache = Cache(
                            type="local",
                            ttl=ttl_seconds,
                            max_size=max_cache_size
                        )
                        self._cache_type = 'local'
                        self._cache_enabled = True
                        logger.info(f"✅ Local caching enabled with TTL: {ttl_seconds}s, max_size: {max_cache_size}")
                        
                except Exception as cache_error:
                    # Cache initialization failed - continue without cache
                    logger.warning(f"⚠️ Cache initialization failed: {cache_error}, continuing without cache")
                    litellm.cache = None
                    self._cache_enabled = False
            else:
                logger.info("Cache disabled by configuration")
                litellm.cache = None
                self._cache_enabled = False
            
            # Create OpenAI-compatible client pointing to LiteLLM proxy
            self._client = AsyncOpenAI(
                api_key=self._master_key,
                base_url=self._api_base,
                timeout=self._timeout
            )
            
            # Test the connection
            await self._test_connection()
            
            self.is_initialized = True
            logger.info("LiteLLM client initialized successfully")
            
        except ImportError as e:
            raise _create_service_error(
                "LiteLLM not installed. Install with: pip install litellm"
            ) from e
        except Exception as e:
            logger.error(f"Failed to initialize LiteLLM adapter: {e}")
            raise _create_service_error(f"LiteLLM initialization failed: {e}")
    
    async def _test_connection(self) -> None:
        """Test the connection to LiteLLM proxy"""
        try:
            # Make a simple test request
            test_messages = [
                {"role": "user", "content": "Hello"}
            ]
            
            response = await self._client.chat.completions.create(
                model=self._model,
                messages=test_messages,
                max_tokens=5,
                timeout=5
            )
            
            logger.debug("LiteLLM connection test successful")
            
        except Exception as e:
            logger.error(f"LiteLLM connection test failed: {e}")
            raise _create_service_error(f"Failed to connect to LiteLLM proxy: {e}")
    
    async def generate(
        self, 
        messages: List[LLMMessage], 
        **kwargs
    ) -> LLMResponse:
        """Generate response using LiteLLM
        
        Args:
            messages: Conversation messages
            **kwargs: Additional parameters
            
        Returns:
            LLMResponse with generated content
        """
        if not self.is_initialized:
            await self.initialize()
        
        if not self.validate_messages(messages):
            raise _create_service_error("Invalid message format")
        
        start_time = time.time()
        
        try:
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Merge default parameters with kwargs
            params = self.get_default_parameters()
            params.update(kwargs)
            params['stream'] = False  # Non-streaming mode
            
            # Enable caching if requested and supported
            cache_enabled = params.pop('caching', False)
            if cache_enabled:
                params['caching'] = True
            
            # Make request with retry logic
            response = await self._make_request_with_retry(
                openai_messages, params
            )
            
            # Calculate response time
            response_time_ms = (time.time() - start_time) * 1000
            
            # Extract response data
            choice = response.choices[0]
            content = choice.message.content
            
            # Create standardized response
            llm_response = LLMResponse(
                content=content,
                model=response.model,
                usage={
                    'prompt_tokens': response.usage.prompt_tokens if response.usage else 0,
                    'completion_tokens': response.usage.completion_tokens if response.usage else 0,
                    'total_tokens': response.usage.total_tokens if response.usage else 0
                },
                finish_reason=choice.finish_reason,
                response_time_ms=response_time_ms
            )
            
            logger.debug(f"LiteLLM generated {len(content)} chars in {response_time_ms:.1f}ms")
            return llm_response
            
        except Exception as e:
            logger.error(f"LiteLLM generation failed: {e}")
            raise _create_service_error(f"Generation failed: {e}")
    
    async def generate_streaming(
        self, 
        messages: List[LLMMessage], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response using LiteLLM
        
        Args:
            messages: Conversation messages
            **kwargs: Additional parameters
            
        Yields:
            Partial response chunks
        """
        if not self.is_initialized:
            await self.initialize()
        
        if not self.validate_messages(messages):
            raise _create_service_error("Invalid message format")
        
        try:
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Merge parameters with streaming enabled
            params = self.get_default_parameters()
            params.update(kwargs)
            params['stream'] = True
            
            # Make streaming request
            async for chunk_content in self._make_streaming_request(openai_messages, params):
                if chunk_content:  # Skip empty chunks
                    yield chunk_content
                    
        except Exception as e:
            logger.error(f"LiteLLM streaming generation failed: {e}")
            raise _create_service_error(f"Streaming generation failed: {e}")
    
    async def generate_answer(self, question: str) -> str:
        """Generate an answer for Q&A scenarios
        
        Args:
            question: The user's question
            
        Returns:
            The LLM's answer as a string
            
        Raises:
            ServiceError: If generation fails
        """
        if not question or not question.strip():
            raise _create_service_error("Question cannot be empty")
        
        # Convert question to LLMMessage format
        messages = [LLMMessage(role="user", content=question.strip())]
        
        try:
            # Use the standard generate method
            response = await self.generate(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"LiteLLM generate_answer failed: {e}")
            raise _create_service_error(f"Failed to generate answer: {e}")
    
    async def generate_qa_complete(self, question: str, transition_config: Dict[str, Any]) -> 'QACompleteResponse':
        """生成包含过渡语的完整QA回答 - Fail-Fast原则
        
        Args:
            question: 用户问题
            transition_config: 过渡语配置
            
        Returns:
            QACompleteResponse: 包含完整回答的响应对象
            
        Raises:
            ServiceError: 任何处理失败都立即抛出异常
        """
        if not question or not question.strip():
            raise _create_service_error("Question cannot be empty")
        
        # 构建Prompt
        prompt = self._build_qa_prompt(question.strip(), transition_config)
        logger.info(f"🤖 QA generation started for: {question[:50]}...")
        logger.debug(f"QA Prompt: {prompt[:500]}...")
        
        # 重试机制（不是降级）
        max_retries = 3
        last_error = None
        
        for attempt in range(max_retries):
            try:
                # 调用LLM - 要求JSON输出
                messages = [LLMMessage(
                    role="system", 
                    content="You must respond with valid JSON format only."
                ), LLMMessage(
                    role="user", 
                    content=prompt
                )]
                
                # 降低温度提高稳定性
                temperature = 0.7 if attempt == 0 else 0.5
                
                response = await self.generate(
                    messages=messages,
                    temperature=temperature,
                    max_tokens=600,  # 确保有足够的token生成完整回答
                    caching=True  # 为QA启用缓存
                )
                
                # 记录原始响应
                logger.debug(f"QA Response (attempt {attempt + 1}): {response.content[:500]}...")
                
                # 解析JSON - 失败立即抛异常
                import json
                data = json.loads(response.content)
                
                # 验证数据结构 - Pydantic会抛异常
                from ...models.content import QACompleteResponse
                qa_response = QACompleteResponse(**data)
                
                logger.info(f"✅ QA generated successfully on attempt {attempt + 1}, "
                           f"length={len(qa_response.complete_answer)}, "
                           f"confidence={qa_response.confidence}")
                return qa_response
                
            except json.JSONDecodeError as e:
                last_error = f"JSON parsing failed: {e}"
                logger.error(f"❌ Attempt {attempt + 1} failed: {last_error}")
                
            except ValidationError as e:
                last_error = f"Response validation failed: {e}"
                logger.error(f"❌ Attempt {attempt + 1} failed: {last_error}")
                
            except Exception as e:
                last_error = f"Unexpected error: {e}"
                logger.error(f"❌ Attempt {attempt + 1} failed: {last_error}")
                
            if attempt < max_retries - 1:
                await asyncio.sleep(1)  # 短暂延迟后重试
        
        # 所有重试失败 - Fail-Fast
        raise _create_service_error(
            f"QA generation failed after {max_retries} attempts: {last_error}"
        )
    
    def _build_qa_prompt(self, question: str, config: Dict[str, Any]) -> str:
        """构建稳定Prompt - 固定示例
        
        Args:
            question: 用户问题
            config: 过渡语配置
            
        Returns:
            str: 构建好的Prompt
            
        Raises:
            ServiceError: 配置缺失时立即失败
        """
        # 获取过渡语示例
        to_qa_examples = config.get('to_qa_examples', config.get('to_qa', []))[:3]
        from_qa_examples = config.get('from_qa_examples', config.get('from_qa', []))[:3]
        
        if not to_qa_examples or not from_qa_examples:
            raise _create_service_error("Transition examples not configured")
        
        # 构建稳定的Prompt，包含多个固定示例
        return f"""你是一位专业的AI主播。请回答观众问题并生成完整的播报内容。

要求：
1. 用自然的过渡语引入问题（参考示例但不要完全照搬）
2. 提供准确、有价值的回答
3. 用合适的语句结束并过渡回主播内容
4. 整体要口语化、自然流畅
5. 总长度控制在50-500字符

过渡语参考示例：
开场风格：
{chr(10).join(f'- {ex}' for ex in to_qa_examples)}

结束风格：
{chr(10).join(f'- {ex}' for ex in from_qa_examples)}

观众问题：{question}

请返回以下JSON格式（不要有任何其他内容）：
{{
    "complete_answer": "你的完整回答（包含自然的开场过渡、核心回答、结束过渡）",
    "confidence": 0.9
}}

注意：complete_answer必须是一个完整、连贯的段落，直接可以播报。"""
    
    async def _make_request_with_retry(
        self, 
        messages: List[Dict[str, str]], 
        params: Dict[str, Any]
    ):
        """Make request with retry logic and cache coalescing"""
        # Generate cache key for request coalescing
        cache_key = self._generate_cache_key(messages, params)
        
        # Use request coalescing to prevent cache stampede
        return await self._make_request_with_coalescing(
            cache_key,
            self._execute_litellm_request,
            messages,
            params
        )
    
    async def _execute_litellm_request(
        self,
        messages: List[Dict[str, str]],
        params: Dict[str, Any]
    ):
        """Execute the actual LiteLLM request with retry logic"""
        last_exception = None
        
        for attempt in range(self._retry_attempts):
            try:
                # Use LiteLLM completion function directly for better caching support
                import litellm
                
                # Prepare parameters for LiteLLM
                litellm_params = {
                    'model': self._model,
                    'messages': messages,
                    **params
                }
                
                # Track cache statistics before request
                cache_stats_before = await self.get_cache_stats()
                
                # Make async completion call
                response = await litellm.acompletion(**litellm_params)
                
                # Check if this was a cache hit
                cache_stats_after = await self.get_cache_stats()
                if self._cache_enabled:
                    if cache_stats_after['cache_hits'] > cache_stats_before.get('cache_hits', 0):
                        logger.debug("🎯 Cache hit!")
                        self._cache_stats['hits'] += 1
                    else:
                        logger.debug("💫 Cache miss - fetching from API")
                        self._cache_stats['misses'] += 1
                    self._cache_stats['total'] += 1
                
                return response
                
            except Exception as e:
                last_exception = e
                logger.warning(f"LiteLLM request attempt {attempt + 1} failed: {e}")
                
                if attempt < self._retry_attempts - 1:
                    # Wait before retry (exponential backoff)
                    wait_time = 2 ** attempt
                    await asyncio.sleep(wait_time)
                    
        # All retries failed
        raise _create_service_error(f"All {self._retry_attempts} attempts failed: {last_exception}")
    
    def _generate_cache_key(self, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate a unique cache key for the request"""
        # Create a stable string representation of the request
        key_data = {
            'model': self._model,
            'messages': messages,
            'params': {k: v for k, v in params.items() if k not in ['stream', 'caching']}
        }
        key_string = json.dumps(key_data, sort_keys=True)
        
        # Generate MD5 hash as cache key
        return hashlib.md5(key_string.encode()).hexdigest()
    
    async def _make_request_with_coalescing(
        self,
        cache_key: str,
        request_func,
        *args,
        **kwargs
    ):
        """Request coalescing mechanism to prevent cache stampede"""
        async with self._request_lock:
            # Check if an identical request is already in-flight
            if cache_key in self._inflight_requests:
                # Wait for the existing request to complete
                logger.debug(f"🔄 Request coalescing: waiting for in-flight request {cache_key[:8]}...")
                existing_task = self._inflight_requests[cache_key]
                # Don't hold the lock while waiting
                
        # If we found an existing request, wait for it outside the lock
        if cache_key in self._inflight_requests:
            try:
                return await existing_task
            except Exception:
                # If the existing request failed, we'll try again
                pass
        
        # Create a new request
        async with self._request_lock:
            # Double-check in case another request started while we were waiting
            if cache_key in self._inflight_requests:
                return await self._inflight_requests[cache_key]
            
            # Create new request task
            future = asyncio.create_task(request_func(*args, **kwargs))
            self._inflight_requests[cache_key] = future
        
        try:
            result = await future
            return result
        finally:
            # Clean up completed request
            async with self._request_lock:
                self._inflight_requests.pop(cache_key, None)
    
    async def _make_streaming_request(
        self, 
        messages: List[Dict[str, str]], 
        params: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """Make streaming request"""
        try:
            stream = await self._client.chat.completions.create(
                model=self._model,
                messages=messages,
                **params
            )
            
            async for chunk in stream:
                if chunk.choices:
                    delta = chunk.choices[0].delta
                    if delta.content:
                        yield delta.content
                        
        except Exception as e:
            logger.error(f"Streaming request failed: {e}")
            raise
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict[str, str]]:
        """Convert LLMMessage objects to OpenAI format
        
        Args:
            messages: List of LLMMessage objects
            
        Returns:
            List of message dictionaries
        """
        openai_messages = []
        
        for msg in messages:
            openai_msg = {
                'role': msg.role,
                'content': msg.content
            }
            
            if msg.name:
                openai_msg['name'] = msg.name
                
            openai_messages.append(openai_msg)
        
        return openai_messages
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics
        
        Returns:
            Dict with cache statistics including hits, misses, hit rate
        """
        if self._cache_enabled:
            try:
                import litellm
                
                # Try to get stats from LiteLLM cache if available
                if hasattr(litellm.cache, 'cache_hits'):
                    return {
                        'enabled': True,
                        'type': self._cache_type,
                        'cache_hits': getattr(litellm.cache, 'cache_hits', self._cache_stats['hits']),
                        'cache_misses': getattr(litellm.cache, 'cache_misses', self._cache_stats['misses']),
                        'hit_rate': self._calculate_hit_rate(),
                        'total_requests': self._cache_stats['total']
                    }
                else:
                    # Use our internal stats
                    return {
                        'enabled': True,
                        'type': self._cache_type,
                        'cache_hits': self._cache_stats['hits'],
                        'cache_misses': self._cache_stats['misses'],
                        'hit_rate': self._calculate_hit_rate(),
                        'total_requests': self._cache_stats['total']
                    }
            except Exception as e:
                logger.warning(f"Failed to get cache stats: {e}")
        
        return {'enabled': False, 'type': 'none'}
    
    def _calculate_hit_rate(self) -> float:
        """Calculate cache hit rate"""
        total = self._cache_stats['total']
        if total == 0:
            return 0.0
        return self._cache_stats['hits'] / total
    
    async def clear_cache(self, pattern: Optional[str] = None) -> bool:
        """Clear cache entries
        
        Args:
            pattern: Optional pattern to match cache keys (not all backends support this)
            
        Returns:
            True if cache was cleared successfully
        """
        if self._cache_enabled:
            try:
                import litellm
                
                if hasattr(litellm.cache, 'flush_cache'):
                    # Clear all cache
                    litellm.cache.flush_cache()
                    logger.info(f"✅ Cache cleared: all entries")
                    
                    # Reset statistics
                    self._cache_stats = {'hits': 0, 'misses': 0, 'total': 0}
                    return True
                else:
                    logger.warning("Cache backend does not support flush operation")
                    
            except Exception as e:
                logger.error(f"Failed to clear cache: {e}")
        
        return False
    
    async def cleanup(self) -> None:
        """Clean up LiteLLM resources"""
        # Clear in-flight requests
        if self._inflight_requests:
            for task in self._inflight_requests.values():
                if not task.done():
                    task.cancel()
            self._inflight_requests.clear()
        
        if self._client:
            # Close the client if it has a close method
            if hasattr(self._client, 'close'):
                await self._client.close()
            self._client = None
        
        self.is_initialized = False
        logger.info("LiteLLM adapter cleaned up")
    
    @property
    def supports_streaming(self) -> bool:
        """LiteLLM supports streaming"""
        return True
    
    @property
    def supports_caching(self) -> bool:
        """LiteLLM supports caching"""
        return True
    
    @property
    def provider_name(self) -> str:
        """Provider name"""
        return "litellm"
    
    @property
    def model_name(self) -> str:
        """Model name

        Returns the model name as configured, without any provider prefix.
        This allows PydanticAI agents to handle the model name formatting
        appropriately based on their own logic.
        """
        return self._model