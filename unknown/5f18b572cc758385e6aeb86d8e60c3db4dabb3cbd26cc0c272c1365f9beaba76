"""Script Segment Converter

Converts ScriptSegment objects to TTS-compatible format with advanced
text processing, SSML generation, and audio optimization features.
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from loguru import logger

from ..services.script_previewer import ScriptSegment, ScriptSegmentType, PriorityLevel
from ..models.audio import AudioPriority
from ..core.exceptions import ServiceError


class TTSCompatibleSegment:
    """TTS-compatible segment with optimized text and metadata"""
    
    def __init__(
        self,
        segment_id: str,
        original_segment: ScriptSegment,
        tts_text: str,
        ssml_text: Optional[str] = None,
        voice_config: Optional[Dict[str, Any]] = None,
        audio_priority: AudioPriority = AudioPriority.NORMAL,
        processing_hints: Optional[Dict[str, Any]] = None
    ):
        self.segment_id = segment_id
        self.original_segment = original_segment
        self.tts_text = tts_text
        self.ssml_text = ssml_text
        self.voice_config = voice_config or {}
        self.audio_priority = audio_priority
        self.processing_hints = processing_hints or {}
        self.created_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "segment_id": self.segment_id,
            "tts_text": self.tts_text,
            "ssml_text": self.ssml_text,
            "voice_config": self.voice_config,
            "audio_priority": str(self.audio_priority.value) if hasattr(self.audio_priority, 'value') else str(self.audio_priority),
            "processing_hints": self.processing_hints,
            "original_segment_type": self.original_segment.segment_type.value,
            "original_title": self.original_segment.title,
            "created_at": self.created_at.isoformat()
        }


class ScriptSegmentConverter:
    """
    Advanced script segment converter that transforms ScriptSegment objects
    into TTS-compatible format with optimized text processing and SSML generation.
    """
    
    def __init__(self):
        self.logger = logger.bind(component="script_segment_converter")
        
        # Text processing configuration
        self.text_processing_config = {
            "max_segment_length": 1000,
            "sentence_break_ms": 500,
            "clause_break_ms": 300,
            "paragraph_break_ms": 800,
            "emphasis_boost": 1.2,
            "speed_variations": {
                ScriptSegmentType.OPENING: 0.95,
                ScriptSegmentType.PRICE_ANNOUNCEMENT: 0.85,
                ScriptSegmentType.CALL_TO_ACTION: 0.90,
                ScriptSegmentType.PRODUCT_INTRO: 1.0,
                ScriptSegmentType.SELLING_POINT: 1.0,
                ScriptSegmentType.INTERACTION: 1.1,
                ScriptSegmentType.TRANSITION: 1.05,
                ScriptSegmentType.CLOSING: 0.95,
                ScriptSegmentType.EMERGENCY_FILLER: 1.2
            }
        }
        
        # Voice configuration templates
        self.voice_templates = {
            ScriptSegmentType.OPENING: {
                "speaking_rate": 0.95,
                "pitch_variation": "medium",
                "emphasis": "warm"
            },
            ScriptSegmentType.PRICE_ANNOUNCEMENT: {
                "speaking_rate": 0.85,
                "pitch_variation": "high",
                "emphasis": "strong"
            },
            ScriptSegmentType.CALL_TO_ACTION: {
                "speaking_rate": 0.90,
                "pitch_variation": "high",
                "emphasis": "urgent"
            },
            ScriptSegmentType.PRODUCT_INTRO: {
                "speaking_rate": 1.0,
                "pitch_variation": "medium",
                "emphasis": "confident"
            },
            ScriptSegmentType.SELLING_POINT: {
                "speaking_rate": 1.0,
                "pitch_variation": "medium",
                "emphasis": "persuasive"
            },
            ScriptSegmentType.INTERACTION: {
                "speaking_rate": 1.1,
                "pitch_variation": "low",
                "emphasis": "conversational"
            },
            ScriptSegmentType.TRANSITION: {
                "speaking_rate": 1.05,
                "pitch_variation": "low",
                "emphasis": "smooth"
            },
            ScriptSegmentType.CLOSING: {
                "speaking_rate": 0.95,
                "pitch_variation": "medium",
                "emphasis": "friendly"
            },
            ScriptSegmentType.EMERGENCY_FILLER: {
                "speaking_rate": 1.2,
                "pitch_variation": "low",
                "emphasis": "neutral"
            }
        }
    
    def convert_segment(self, segment: ScriptSegment) -> TTSCompatibleSegment:
        """Convert a single script segment to TTS-compatible format
        
        Args:
            segment: Script segment to convert
            
        Returns:
            TTS-compatible segment with optimized text and configuration
        """
        try:
            self.logger.debug(f"Converting segment '{segment.title}' ({segment.segment_type.value})")
            
            # Step 1: Clean and optimize text
            tts_text = self._process_text_for_tts(segment)
            
            # Step 2: Generate SSML if needed
            ssml_text = self._generate_ssml(segment, tts_text)
            
            # Step 3: Create voice configuration
            voice_config = self._create_voice_config(segment)
            
            # Step 4: Determine audio priority
            audio_priority = self._get_audio_priority(segment)
            
            # Step 5: Create processing hints
            processing_hints = self._create_processing_hints(segment)
            
            # Create TTS-compatible segment
            tts_segment = TTSCompatibleSegment(
                segment_id=segment.segment_id,
                original_segment=segment,
                tts_text=tts_text,
                ssml_text=ssml_text,
                voice_config=voice_config,
                audio_priority=audio_priority,
                processing_hints=processing_hints
            )
            
            self.logger.debug(f"Successfully converted segment '{segment.title}'")
            return tts_segment
            
        except Exception as e:
            self.logger.error(f"Failed to convert segment '{segment.title}': {e}")
            raise ServiceError(f"Segment conversion failed: {e}", "script_segment_converter")
    
    def convert_segments_batch(self, segments: List[ScriptSegment]) -> List[TTSCompatibleSegment]:
        """Convert multiple script segments to TTS-compatible format
        
        Args:
            segments: List of script segments to convert
            
        Returns:
            List of TTS-compatible segments
        """
        if not segments:
            self.logger.warning("No segments provided for batch conversion")
            return []
        
        self.logger.info(f"Converting {len(segments)} segments to TTS-compatible format")
        
        tts_segments = []
        
        for i, segment in enumerate(segments):
            try:
                self.logger.debug(f"Processing segment {i+1}/{len(segments)}: {segment.title}")
                
                tts_segment = self.convert_segment(segment)
                tts_segments.append(tts_segment)
                
                self.logger.debug(f"✅ Segment {i+1} converted successfully")
                
            except Exception as e:
                self.logger.error(f"❌ Failed to convert segment {i+1} '{segment.title}': {e}")
                # Continue with other segments instead of failing completely
                continue
        
        self.logger.info(f"Batch conversion completed: {len(tts_segments)}/{len(segments)} segments converted")
        return tts_segments
    
    def _process_text_for_tts(self, segment: ScriptSegment) -> str:
        """Process and optimize text for TTS synthesis
        
        Args:
            segment: Script segment
            
        Returns:
            Optimized text ready for TTS
        """
        text = segment.content.strip()
        
        # Step 1: Handle variable substitution FIRST (before any other processing)
        text = self._substitute_variables(segment, text)
        
        # Step 2: Basic text cleaning
        text = text.replace('\n', ' ').replace('\r', ' ')
        text = ' '.join(text.split())  # Normalize whitespace
        
        # Step 3: Remove problematic characters for TTS
        text = self._clean_special_characters(text)
        
        # Step 4: Apply segment-specific text optimizations
        text = self._apply_segment_optimizations(segment, text)
        
        # Step 5: Ensure length limits
        if len(text) > self.text_processing_config["max_segment_length"]:
            self.logger.warning(f"Segment text too long ({len(text)} chars), truncating")
            text = text[:self.text_processing_config["max_segment_length"] - 3] + "..."
        
        return text
    
    def _clean_special_characters(self, text: str) -> str:
        """Clean special characters that may cause TTS issues"""
        # Replace problematic characters
        replacements = {
            '&': '和',
            '%': '百分之',
            '@': '在',
            '#': '号',
            '$': '美元',
            '€': '欧元',
            '£': '英镑',
            '¥': '元',
            '*': '',
            '_': '',
            '|': '',
            '\\': '',
            '/': '或',
            '<': '小于',
            '>': '大于',
            '[': '（',
            ']': '）',
            '{': '（',
            '}': '）'
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def _apply_segment_optimizations(self, segment: ScriptSegment, text: str) -> str:
        """Apply segment-type specific text optimizations"""
        segment_type = segment.segment_type
        
        if segment_type == ScriptSegmentType.OPENING:
            # Add welcoming tone
            if not text.endswith(('。', '！', '!')):
                text = text + "。"
            
        elif segment_type == ScriptSegmentType.PRICE_ANNOUNCEMENT:
            # Emphasize price information
            text = text.replace('元', '元！')
            text = text.replace('特价', '特价优惠')
            text = text.replace('折扣', '超值折扣')
            
        elif segment_type == ScriptSegmentType.CALL_TO_ACTION:
            # Add urgency and call-to-action emphasis
            if not text.endswith(('！', '!')):
                text = text + "！"
            text = text.replace('立即', '立即行动')
            text = text.replace('现在', '现在就')
            
        elif segment_type == ScriptSegmentType.PRODUCT_INTRO:
            # Ensure clear product introduction
            text = text.replace('产品', '这款产品')
            
        elif segment_type == ScriptSegmentType.INTERACTION:
            # Make it more conversational
            if not text.endswith(('？', '?', '吗', '呢')):
                text = text + "呢？"
        
        return text
    
    def _substitute_variables(self, segment: ScriptSegment, text: str) -> str:
        """Substitute variables in the text with actual values"""
        if not segment.variables:
            self.logger.debug("No variables to substitute")
            return text
        
        self.logger.debug(f"Substituting variables: {segment.variables}")
        original_text = text
        
        for key, value in segment.variables.items():
            placeholder = f"{{{key}}}"
            if placeholder in text:
                text = text.replace(placeholder, str(value))
                self.logger.debug(f"Substituted variable {key}: {placeholder} -> {value}")
            else:
                self.logger.debug(f"Variable placeholder {placeholder} not found in text")
        
        if text != original_text:
            self.logger.debug(f"Text after substitution: {text}")
        
        return text
    
    def _generate_ssml(self, segment: ScriptSegment, text: str) -> Optional[str]:
        """Generate SSML markup for enhanced TTS control
        
        Args:
            segment: Script segment
            text: Processed text
            
        Returns:
            SSML markup string or None if not needed
        """
        segment_type = segment.segment_type
        
        # Get speed variation
        speed_rate = self.text_processing_config["speed_variations"].get(segment_type, 1.0)
        
        # Build SSML
        ssml_parts = ['<speak>']
        
        # Add prosody control
        if speed_rate != 1.0:
            ssml_parts.append(f'<prosody rate="{speed_rate}">')
        
        # Add emphasis for specific segment types
        if segment_type in [ScriptSegmentType.PRICE_ANNOUNCEMENT, ScriptSegmentType.CALL_TO_ACTION]:
            ssml_parts.append('<emphasis level="strong">')
            ssml_parts.append(text)
            ssml_parts.append('</emphasis>')
        else:
            ssml_parts.append(text)
        
        # Close prosody if opened
        if speed_rate != 1.0:
            ssml_parts.append('</prosody>')
        
        # Add breaks for specific segment types
        if segment_type == ScriptSegmentType.OPENING:
            ssml_parts.append('<break time="800ms"/>')
        elif segment_type == ScriptSegmentType.PRICE_ANNOUNCEMENT:
            ssml_parts.append('<break time="600ms"/>')
        
        ssml_parts.append('</speak>')
        
        ssml = ''.join(ssml_parts)
        
        # Return SSML only if it adds value over plain text
        if '<prosody' in ssml or '<emphasis' in ssml or '<break' in ssml:
            return ssml
        
        return None
    
    def _create_voice_config(self, segment: ScriptSegment) -> Dict[str, Any]:
        """Create voice configuration for the segment"""
        base_config = self.voice_templates.get(segment.segment_type, {})
        
        # Add segment-specific adjustments
        config = base_config.copy()
        
        # Adjust based on priority
        if segment.priority == PriorityLevel.HIGH:
            config["emphasis_boost"] = config.get("emphasis_boost", 1.0) * 1.1
        elif segment.priority == PriorityLevel.LOW:
            config["emphasis_boost"] = config.get("emphasis_boost", 1.0) * 0.9
        
        # Add estimated duration hint
        estimated_words = len(segment.content.split())
        config["estimated_words"] = estimated_words
        config["estimated_duration_seconds"] = segment.estimated_duration_seconds
        
        return config
    
    def _get_audio_priority(self, segment: ScriptSegment) -> AudioPriority:
        """Determine audio priority for the segment"""
        segment_type = segment.segment_type
        
        # Map segment types to audio priorities
        priority_map = {
            ScriptSegmentType.OPENING: AudioPriority.HIGH,
            ScriptSegmentType.CALL_TO_ACTION: AudioPriority.HIGH,
            ScriptSegmentType.PRICE_ANNOUNCEMENT: AudioPriority.HIGH,
            ScriptSegmentType.PRODUCT_INTRO: AudioPriority.NORMAL,
            ScriptSegmentType.SELLING_POINT: AudioPriority.NORMAL,
            ScriptSegmentType.INTERACTION: AudioPriority.NORMAL,
            ScriptSegmentType.TRANSITION: AudioPriority.NORMAL,
            ScriptSegmentType.CLOSING: AudioPriority.NORMAL,
            ScriptSegmentType.EMERGENCY_FILLER: AudioPriority.URGENT
        }
        
        base_priority = priority_map.get(segment_type, AudioPriority.NORMAL)
        
        # Adjust based on segment priority
        if segment.priority == PriorityLevel.HIGH and base_priority == AudioPriority.NORMAL:
            return AudioPriority.HIGH
        elif segment.priority == PriorityLevel.LOW and base_priority == AudioPriority.HIGH:
            return AudioPriority.NORMAL
        
        return base_priority
    
    def _create_processing_hints(self, segment: ScriptSegment) -> Dict[str, Any]:
        """Create processing hints for TTS engines"""
        hints = {
            "segment_type": segment.segment_type.value,
            "original_length": len(segment.content),
            "word_count": len(segment.content.split()),
            "has_variables": bool(segment.variables),
            "trigger_count": len(segment.triggers) if segment.triggers else 0
        }
        
        # Add context hints
        if segment.segment_type == ScriptSegmentType.PRICE_ANNOUNCEMENT:
            hints["contains_pricing"] = True
            hints["emphasis_needed"] = True
        elif segment.segment_type == ScriptSegmentType.CALL_TO_ACTION:
            hints["urgency_level"] = "high"
            hints["emphasis_needed"] = True
        elif segment.segment_type == ScriptSegmentType.INTERACTION:
            hints["conversational"] = True
            hints["pause_after"] = True
        
        return hints
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """Get conversion statistics and configuration info"""
        return {
            "text_processing_config": self.text_processing_config,
            "supported_segment_types": [t.value for t in ScriptSegmentType],
            "voice_template_count": len(self.voice_templates),
            "max_segment_length": self.text_processing_config["max_segment_length"]
        }


# Global converter instance
_segment_converter: Optional[ScriptSegmentConverter] = None


def get_script_segment_converter() -> ScriptSegmentConverter:
    """Get global script segment converter instance"""
    global _segment_converter
    if _segment_converter is None:
        _segment_converter = ScriptSegmentConverter()
    return _segment_converter