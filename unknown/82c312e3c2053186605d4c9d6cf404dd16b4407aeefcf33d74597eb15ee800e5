"""播放列表相关的异常定义

Author: Claude Code
Date: 2025-08-11
"""


class InvalidPlaylistStateError(Exception):
    """播放列表状态无效异常
    
    当播放列表处于不一致或无效状态时抛出此异常。
    提供清晰的业务错误契约，便于调用方精确处理。
    """
    
    def __init__(self, message: str, state_info: dict = None):
        """初始化异常
        
        Args:
            message: 错误描述
            state_info: 播放列表状态信息，用于调试
        """
        super().__init__(message)
        self.state_info = state_info or {}


class PlaylistNotReadyError(Exception):
    """播放列表尚未准备就绪异常
    
    当播放列表还在初始化或加载过程中时抛出此异常。
    """
    
    def __init__(self, message: str = "Playlist is not ready"):
        super().__init__(message)


class PlaylistIndexOutOfRangeError(Exception):
    """播放列表索引超出范围异常
    
    当请求的播放列表索引超出有效范围时抛出此异常。
    """
    
    def __init__(self, requested_index: int, max_index: int):
        message = f"Requested index {requested_index} is out of range (max: {max_index})"
        super().__init__(message)
        self.requested_index = requested_index
        self.max_index = max_index