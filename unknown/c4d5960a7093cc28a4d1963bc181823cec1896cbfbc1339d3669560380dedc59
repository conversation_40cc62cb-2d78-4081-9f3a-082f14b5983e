"""智能播放列表系统配置管理器

统一管理所有配置参数，支持动态加载和热更新。
避免硬编码魔数，提供类型安全的配置访问接口。

Author: Claude Code
Date: 2025-08-08
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from loguru import logger
import os


class StreamingConfig:
    """流媒体配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为项目根目录下的config/streaming_config.yml
        """
        if config_path is None:
            # 自动查找配置文件
            current_dir = Path(__file__).parent
            project_root = current_dir.parent.parent.parent
            config_path = project_root / "config" / "streaming_config.yml"
            
        self.config_path = Path(config_path)
        self._config: Dict[str, Any] = {}
        self._load_config()
        
    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                self._config = self._get_default_config()
                return
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
                
            logger.info(f"成功加载配置文件: {self.config_path}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}，使用默认配置")
            self._config = self._get_default_config()
            
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "streaming": {
                "client_buffer": {
                    "healthy_threshold_ms": 2000,
                    "at_risk_threshold_ms": 500,
                    "depleted_threshold_ms": 100,
                    "target_buffer_ms": 3000
                },
                "qa_insertion": {
                    "min_delay_sentences": 1,
                    "max_delay_sentences": 5,
                    "health_based_delay": {
                        "healthy": 3,
                        "at_risk": 2,
                        "depleted": 1
                    },
                    "use_min_strategy": True,
                    "network_compensation_per_100ms": 1
                },
                "tts_cache": {
                    "max_items": 100,
                    "ttl_seconds": 300,
                    "eviction_strategy": "lru"
                },
                "proactive_synthesis": {
                    "enabled": True,
                    "look_ahead_items": 5,
                    "check_interval_seconds": 1,
                    "max_concurrent_synthesis": 3
                },
                "state_recovery": {
                    "enabled": True,
                    "snapshot_interval_seconds": 10,
                    "snapshot_ttl_seconds": 300,
                    "storage_path": "./state_snapshots"
                },
                "websocket": {
                    "heartbeat_interval_seconds": 10,
                    "message_timeout_seconds": 60,
                    "max_reconnect_attempts": 3
                },
                "performance": {
                    "max_clients_per_session": 100,
                    "request_queue_size": 1000,
                    "worker_pool_size": 10
                }
            }
        }
        
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号路径
        
        Args:
            key_path: 配置键路径，如 'streaming.client_buffer.healthy_threshold_ms'
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
                    
            return value
        except Exception:
            return default
            
    def set(self, key_path: str, value: Any) -> None:
        """
        设置配置值（运行时修改，不保存到文件）
        
        Args:
            key_path: 配置键路径
            value: 配置值
        """
        keys = key_path.split('.')
        config = self._config
        
        # 创建嵌套字典结构
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
            
        config[keys[-1]] = value
        logger.debug(f"配置已更新: {key_path} = {value}")
        
    def reload(self) -> bool:
        """
        重新加载配置文件
        
        Returns:
            是否成功重新加载
        """
        try:
            old_config = self._config.copy()
            self._load_config()
            
            if old_config != self._config:
                logger.info("配置已重新加载并发生变化")
                return True
            else:
                logger.debug("配置已重新加载，无变化")
                return False
                
        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
            return False
            
    # ==================== 便捷属性访问 ====================
    
    @property
    def client_buffer_healthy_ms(self) -> int:
        """客户端缓冲健康阈值（毫秒）"""
        return self.get('streaming.client_buffer.healthy_threshold_ms', 2000)
        
    @property
    def client_buffer_at_risk_ms(self) -> int:
        """客户端缓冲风险阈值（毫秒）"""
        return self.get('streaming.client_buffer.at_risk_threshold_ms', 500)
        
    @property
    def client_buffer_depleted_ms(self) -> int:
        """客户端缓冲耗尽阈值（毫秒）"""
        return self.get('streaming.client_buffer.depleted_threshold_ms', 100)
        
    @property
    def qa_insertion_strategy(self) -> str:
        """QA插入策略：'min' 或 'max'"""
        return "min" if self.get('streaming.qa_insertion.use_min_strategy', True) else "max"
        
    @property
    def qa_healthy_delay(self) -> int:
        """健康状态下的QA延迟句数"""
        return self.get('streaming.qa_insertion.health_based_delay.healthy', 3)
        
    @property
    def qa_at_risk_delay(self) -> int:
        """风险状态下的QA延迟句数"""
        return self.get('streaming.qa_insertion.health_based_delay.at_risk', 2)
        
    @property
    def qa_depleted_delay(self) -> int:
        """耗尽状态下的QA延迟句数"""
        return self.get('streaming.qa_insertion.health_based_delay.depleted', 1)
        
    @property
    def tts_cache_max_items(self) -> int:
        """TTS缓存最大项目数"""
        return self.get('streaming.tts_cache.max_items', 100)
        
    @property
    def tts_cache_ttl_seconds(self) -> int:
        """TTS缓存过期时间（秒）"""
        return self.get('streaming.tts_cache.ttl_seconds', 300)
        
    @property
    def proactive_synthesis_enabled(self) -> bool:
        """是否启用预合成"""
        return self.get('streaming.proactive_synthesis.enabled', True)
        
    @property
    def proactive_look_ahead_items(self) -> int:
        """预合成前瞻项目数"""
        return self.get('streaming.proactive_synthesis.look_ahead_items', 5)
        
    @property
    def proactive_check_interval(self) -> int:
        """预合成检查间隔（秒）"""
        return self.get('streaming.proactive_synthesis.check_interval_seconds', 1)
        
    @property
    def proactive_max_concurrent(self) -> int:
        """预合成最大并发数"""
        return self.get('streaming.proactive_synthesis.max_concurrent_synthesis', 3)
        
    @property
    def websocket_heartbeat_interval(self) -> int:
        """WebSocket心跳间隔（秒）"""
        return self.get('streaming.websocket.heartbeat_interval_seconds', 10)
        
    @property
    def websocket_message_timeout(self) -> int:
        """WebSocket消息超时（秒）"""
        return self.get('streaming.websocket.message_timeout_seconds', 60)
        
    @property
    def state_recovery_enabled(self) -> bool:
        """是否启用状态恢复"""
        return self.get('streaming.state_recovery.enabled', True)
        
    @property
    def state_recovery_snapshot_interval(self) -> int:
        """状态快照间隔（秒）"""
        return self.get('streaming.state_recovery.snapshot_interval_seconds', 10)
        
    @property
    def state_recovery_storage_path(self) -> str:
        """状态恢复存储路径"""
        return self.get('streaming.state_recovery.storage_path', './state_snapshots')
        
    def get_health_based_delay(self, health_status: str) -> int:
        """
        根据健康状态获取QA延迟句数
        
        Args:
            health_status: 健康状态 ('healthy', 'at_risk', 'depleted')
            
        Returns:
            延迟句数
        """
        delay_key = f'streaming.qa_insertion.health_based_delay.{health_status}'
        return self.get(delay_key, 2)
        
    def validate_config(self) -> Dict[str, list]:
        """
        验证配置的有效性
        
        Returns:
            验证结果，包含错误和警告列表
        """
        errors = []
        warnings = []
        
        # 检查必要的配置项
        required_paths = [
            'streaming.client_buffer.healthy_threshold_ms',
            'streaming.qa_insertion.use_min_strategy',
            'streaming.tts_cache.max_items'
        ]
        
        for path in required_paths:
            if self.get(path) is None:
                errors.append(f"缺少必要配置项: {path}")
                
        # 检查数值范围
        if self.client_buffer_healthy_ms <= self.client_buffer_at_risk_ms:
            errors.append("健康阈值必须大于风险阈值")
            
        if self.client_buffer_at_risk_ms <= self.client_buffer_depleted_ms:
            errors.append("风险阈值必须大于耗尽阈值")
            
        if self.tts_cache_max_items <= 0:
            errors.append("TTS缓存大小必须大于0")
            
        # 检查路径
        storage_path = Path(self.state_recovery_storage_path)
        if not storage_path.parent.exists():
            warnings.append(f"状态恢复存储路径的父目录不存在: {storage_path.parent}")
            
        return {
            "errors": errors,
            "warnings": warnings
        }
        
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "config_file": str(self.config_path),
            "file_exists": self.config_path.exists(),
            "client_buffer": {
                "healthy_ms": self.client_buffer_healthy_ms,
                "at_risk_ms": self.client_buffer_at_risk_ms,
                "depleted_ms": self.client_buffer_depleted_ms
            },
            "qa_insertion": {
                "strategy": self.qa_insertion_strategy,
                "delays": {
                    "healthy": self.qa_healthy_delay,
                    "at_risk": self.qa_at_risk_delay,
                    "depleted": self.qa_depleted_delay
                }
            },
            "tts_cache": {
                "max_items": self.tts_cache_max_items,
                "ttl_seconds": self.tts_cache_ttl_seconds
            },
            "proactive_synthesis": {
                "enabled": self.proactive_synthesis_enabled,
                "look_ahead": self.proactive_look_ahead_items
            },
            "state_recovery": {
                "enabled": self.state_recovery_enabled,
                "storage_path": self.state_recovery_storage_path
            }
        }


# 全局配置实例
streaming_config = StreamingConfig()


def get_streaming_config() -> StreamingConfig:
    """获取全局配置实例"""
    return streaming_config


def reload_streaming_config() -> bool:
    """重新加载全局配置"""
    return streaming_config.reload()


# 配置验证和初始化检查
def _validate_on_import():
    """模块导入时验证配置"""
    validation_result = streaming_config.validate_config()
    
    if validation_result["errors"]:
        logger.error("配置验证失败:")
        for error in validation_result["errors"]:
            logger.error(f"  - {error}")
            
    if validation_result["warnings"]:
        logger.warning("配置警告:")
        for warning in validation_result["warnings"]:
            logger.warning(f"  - {warning}")
            
    logger.info("配置管理器初始化完成")
    logger.debug(f"配置摘要: {streaming_config.get_config_summary()}")


# 执行初始化检查
_validate_on_import()