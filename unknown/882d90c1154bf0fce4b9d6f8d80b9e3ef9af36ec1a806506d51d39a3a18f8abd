"""QA管理API模块"""

from fastapi import APIRouter, HTTPException, Depends, Request, Query, Path
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
import sqlite3
import json
from datetime import datetime, date
import logging
import asyncio
from pathlib import Path as PathLib

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/qa", tags=["QA Management"])


# Pydantic 模型
class QAEntry(BaseModel):
    question: str = Field(..., min_length=1, max_length=500, description="问题文本")
    answer: str = Field(..., min_length=1, max_length=2000, description="答案文本")
    category: Optional[str] = Field(None, max_length=50, description="分类")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签列表")
    
    @validator('tags')
    def validate_tags(cls, v):
        if v and len(v) > 10:
            raise ValueError('标签数量不能超过10个')
        return v


class QAEntryUpdate(BaseModel):
    question: Optional[str] = Field(None, min_length=1, max_length=500)
    answer: Optional[str] = Field(None, min_length=1, max_length=2000)
    category: Optional[str] = Field(None, max_length=50)
    tags: Optional[List[str]] = None
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)


class QAQuery(BaseModel):
    question: str = Field(..., min_length=1, max_length=500, description="用户问题")
    session_id: Optional[str] = Field(None, description="会话ID")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="上下文信息")


class QAResponse(BaseModel):
    answer: str
    confidence: float
    source: str
    processing_time: float
    metadata: Optional[Dict[str, Any]] = None


class PendingQAAction(BaseModel):
    action: str = Field(..., pattern="^(approve|reject)$")
    reviewer: Optional[str] = Field(None, max_length=100)
    note: Optional[str] = Field(None, max_length=500)


# 依赖注入函数
def get_db_connection():
    """获取数据库连接"""
    try:
        from ..core.config import cfg
        db_path = cfg.knowledge_base_db_path
        conn = sqlite3.connect(db_path, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise HTTPException(status_code=500, detail="数据库连接失败")


def get_knowledge_base_manager():
    """获取知识库管理器"""
    # TODO: 这里需要从依赖注入容器获取实际的实例
    from ..core.dependencies import get_knowledge_base_manager as _get_kb_manager
    try:
        return _get_kb_manager()
    except Exception as e:
        logger.error(f"获取知识库管理器失败: {e}")
        raise HTTPException(status_code=500, detail="知识库服务不可用")


def get_enhanced_qa_manager():
    """获取增强版QA管理器"""
    # TODO: 这里需要从依赖注入容器获取实际的实例
    from ..core.dependencies import get_enhanced_qa_manager as _get_manager
    try:
        return _get_manager()
    except Exception as e:
        logger.error(f"获取QA管理器失败: {e}")
        raise HTTPException(status_code=500, detail="QA服务不可用")


# API 路由
@router.post("/query", response_model=QAResponse)
async def query_qa(
    query: QAQuery,
    qa_manager=Depends(get_enhanced_qa_manager)
):
    """查询问答"""
    try:
        start_time = datetime.utcnow()
        
        # 构建问题数据
        question_data = {
            'text': query.question,
            'session_id': query.session_id,
            'context': query.context,
            'timestamp': start_time.isoformat()
        }
        
        # 处理问题
        result = await qa_manager.handle_qa(question_data)
        
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        if result.get('should_respond', False):
            return QAResponse(
                answer=result['answer'],
                confidence=result.get('confidence', 0.0),
                source=result.get('source', 'unknown'),
                processing_time=processing_time,
                metadata=result.get('metadata', {})
            )
        else:
            raise HTTPException(
                status_code=404, 
                detail={
                    "message": "无法找到相关答案",
                    "reason": result.get('reason', 'unknown'),
                    "confidence": result.get('confidence', 0.0)
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"QA查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@router.post("/entries", status_code=201)
async def create_qa_entry(
    entry: QAEntry,
    kb_manager=Depends(get_knowledge_base_manager)
):
    """创建新的QA条目"""
    try:
        entry_id = await kb_manager.add_entry(
            question=entry.question,
            answer=entry.answer,
            metadata={
                'category': entry.category,
                'tags': entry.tags,
                'created_via': 'api'
            }
        )
        
        return {
            "id": entry_id,
            "message": "QA条目创建成功",
            "question": entry.question[:50] + "..." if len(entry.question) > 50 else entry.question
        }
        
    except Exception as e:
        logger.error(f"创建QA条目失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")


@router.get("/entries")
async def list_qa_entries(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, max_length=200, description="搜索关键词"),
    category: Optional[str] = Query(None, max_length=50, description="分类筛选"),
    sort_by: str = Query("created_at", regex="^(created_at|hit_count|confidence_score)$"),
    sort_order: str = Query("desc", regex="^(asc|desc)$"),
    conn: sqlite3.Connection = Depends(get_db_connection)
):
    """分页查询QA条目"""
    try:
        cursor = conn.cursor()
        
        # 构建查询条件
        where_clauses = ["1=1"]
        params = []
        
        if search:
            where_clauses.append("(question LIKE ? OR answer LIKE ?)")
            search_pattern = f"%{search}%"
            params.extend([search_pattern, search_pattern])
            
        if category:
            where_clauses.append("category = ?")
            params.append(category)
            
        where_clause = " AND ".join(where_clauses)
        
        # 获取总数
        count_query = f"SELECT COUNT(*) FROM qa_entries WHERE {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        # 分页查询
        query = f"""
            SELECT id, question, answer, category, tags, hit_count, 
                   confidence_score, created_at, updated_at
            FROM qa_entries 
            WHERE {where_clause}
            ORDER BY {sort_by} {sort_order.upper()}
            LIMIT ? OFFSET ?
        """
        params.extend([page_size, (page - 1) * page_size])
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # 转换结果
        items = []
        for row in rows:
            items.append({
                'id': row['id'],
                'question': row['question'],
                'answer': row['answer'],
                'category': row['category'],
                'tags': json.loads(row['tags']) if row['tags'] else [],
                'hit_count': row['hit_count'],
                'confidence_score': row['confidence_score'],
                'created_at': row['created_at'],
                'updated_at': row['updated_at']
            })
            
        return {
            'items': items,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size,
            'has_next': page * page_size < total,
            'has_prev': page > 1
        }
        
    except Exception as e:
        logger.error(f"查询QA条目失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    finally:
        conn.close()


@router.get("/entries/{entry_id}")
async def get_qa_entry(
    entry_id: int = Path(..., ge=1),
    kb_manager=Depends(get_knowledge_base_manager)
):
    """获取单个QA条目"""
    try:
        entry = await kb_manager.get_entry_by_id(entry_id)
        if not entry:
            raise HTTPException(status_code=404, detail="QA条目不存在")
            
        return entry
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取QA条目失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.put("/entries/{entry_id}")
async def update_qa_entry(
    entry_id: int = Path(..., ge=1),
    update: QAEntryUpdate = ...,
    conn: sqlite3.Connection = Depends(get_db_connection)
):
    """更新QA条目"""
    try:
        cursor = conn.cursor()
        
        # 检查条目是否存在
        cursor.execute("SELECT id FROM qa_entries WHERE id = ?", (entry_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="QA条目不存在")
        
        # 构建更新语句
        updates = []
        params = []
        
        if update.question is not None:
            updates.append("question = ?")
            params.append(update.question)
            
        if update.answer is not None:
            updates.append("answer = ?")
            params.append(update.answer)
            
        if update.category is not None:
            updates.append("category = ?")
            params.append(update.category)
            
        if update.tags is not None:
            updates.append("tags = ?")
            params.append(json.dumps(update.tags))
            
        if update.confidence_score is not None:
            updates.append("confidence_score = ?")
            params.append(update.confidence_score)
            
        if not updates:
            raise HTTPException(status_code=400, detail="没有要更新的字段")
            
        updates.append("updated_at = CURRENT_TIMESTAMP")
        
        query = f"UPDATE qa_entries SET {', '.join(updates)} WHERE id = ?"
        params.append(entry_id)
        
        cursor.execute(query, params)
        conn.commit()
        
        # 同时更新FTS索引
        if update.question or update.answer:
            cursor.execute("SELECT question, answer FROM qa_entries WHERE id = ?", (entry_id,))
            row = cursor.fetchone()
            if row:
                cursor.execute("""
                    INSERT OR REPLACE INTO qa_entries_fts (rowid, question, answer) 
                    VALUES (?, ?, ?)
                """, (entry_id, row['question'], row['answer']))
                conn.commit()
        
        return {"message": "更新成功", "entry_id": entry_id}
        
    except HTTPException:
        raise
    except Exception as e:
        conn.rollback()
        logger.error(f"更新QA条目失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")
    finally:
        conn.close()


@router.delete("/entries/{entry_id}")
async def delete_qa_entry(
    entry_id: int = Path(..., ge=1),
    kb_manager=Depends(get_knowledge_base_manager)
):
    """删除QA条目"""
    try:
        success = await kb_manager.delete_entry(entry_id)
        if not success:
            raise HTTPException(status_code=404, detail="QA条目不存在")
            
        return {"message": "删除成功", "entry_id": entry_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除QA条目失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/pending-reviews")
async def get_pending_reviews(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: str = Query("pending", regex="^(pending|approved|rejected)$"),
    conn: sqlite3.Connection = Depends(get_db_connection)
):
    """获取待审核的LLM生成答案"""
    try:
        cursor = conn.cursor()
        
        # 查询待审核条目
        cursor.execute("""
            SELECT id, question, generated_answer, context, confidence_score,
                   hit_count, status, created_at, reviewed_at, reviewed_by
            FROM pending_qa_entries 
            WHERE status = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """, (status, page_size, (page - 1) * page_size))
        
        rows = cursor.fetchall()
        
        # 获取总数
        cursor.execute("SELECT COUNT(*) FROM pending_qa_entries WHERE status = ?", (status,))
        total = cursor.fetchone()[0]
        
        items = []
        for row in rows:
            items.append({
                'id': row['id'],
                'question': row['question'],
                'generated_answer': row['generated_answer'],
                'context': json.loads(row['context']) if row['context'] else {},
                'confidence_score': row['confidence_score'],
                'hit_count': row['hit_count'],
                'status': row['status'],
                'created_at': row['created_at'],
                'reviewed_at': row['reviewed_at'],
                'reviewed_by': row['reviewed_by']
            })
            
        return {
            'items': items,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }
        
    except Exception as e:
        logger.error(f"获取待审核条目失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    finally:
        conn.close()


@router.post("/pending-reviews/{review_id}/action")
async def handle_pending_review(
    review_id: int = Path(..., ge=1),
    action: PendingQAAction = ...,
    kb_manager=Depends(get_knowledge_base_manager),
    conn: sqlite3.Connection = Depends(get_db_connection)
):
    """处理待审核条目（批准或拒绝）"""
    try:
        cursor = conn.cursor()
        
        # 获取待审核条目
        cursor.execute("""
            SELECT question, generated_answer, status
            FROM pending_qa_entries 
            WHERE id = ? AND status = 'pending'
        """, (review_id,))
        row = cursor.fetchone()
        
        if not row:
            raise HTTPException(status_code=404, detail="待审核条目不存在或已处理")
            
        if action.action == "approve":
            # 批准并添加到知识库
            entry_id = await kb_manager.add_entry(
                question=row['question'],
                answer=row['generated_answer'],
                metadata={
                    'source': 'llm_generated',
                    'approved': True,
                    'reviewer': action.reviewer
                }
            )
            
            # 更新状态
            cursor.execute("""
                UPDATE pending_qa_entries 
                SET status = 'approved', 
                    reviewed_at = CURRENT_TIMESTAMP,
                    reviewed_by = ?
                WHERE id = ?
            """, (action.reviewer, review_id))
            
            message = f"已批准并添加到知识库 (ID: {entry_id})"
            
        else:  # reject
            # 拒绝
            cursor.execute("""
                UPDATE pending_qa_entries 
                SET status = 'rejected',
                    reviewed_at = CURRENT_TIMESTAMP,
                    reviewed_by = ?
                WHERE id = ?
            """, (action.reviewer, review_id))
            
            message = "已拒绝"
            
        conn.commit()
        
        return {
            "message": message,
            "action": action.action,
            "review_id": review_id,
            "reviewer": action.reviewer,
            "note": action.note
        }
        
    except HTTPException:
        raise
    except Exception as e:
        conn.rollback()
        logger.error(f"处理待审核条目失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")
    finally:
        conn.close()


@router.get("/stats")
async def get_qa_stats(
    kb_manager=Depends(get_knowledge_base_manager),
    qa_manager=Depends(get_enhanced_qa_manager)
):
    """获取QA系统统计信息"""
    try:
        # 获取知识库统计
        kb_stats = await kb_manager.get_stats()
        
        # 获取QA管理器统计
        qa_stats = await qa_manager.get_full_stats()
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'knowledge_base': kb_stats,
            'qa_manager': qa_stats,
            'system_status': 'healthy'
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.get("/categories")
async def get_categories(conn: sqlite3.Connection = Depends(get_db_connection)):
    """获取所有分类"""
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT category, COUNT(*) as count
            FROM qa_entries 
            WHERE category IS NOT NULL
            GROUP BY category
            ORDER BY count DESC
        """)
        
        categories = [
            {"name": row['category'], "count": row['count']}
            for row in cursor.fetchall()
        ]
        
        return {"categories": categories}
        
    except Exception as e:
        logger.error(f"获取分类失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")
    finally:
        conn.close()


@router.get("/search")
async def search_qa(
    q: str = Query(..., min_length=1, max_length=200, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=50, description="返回结果数量"),
    kb_manager=Depends(get_knowledge_base_manager)
):
    """搜索QA条目"""
    try:
        # 使用知识库的混合搜索
        results = await kb_manager.hybrid_search(q, top_k=limit)
        
        search_results = [
            {
                "id": result[0],
                "score": result[1],
                "question": result[2],
                "answer": result[3]
            }
            for result in results
        ]
        
        return {
            "query": q,
            "results": search_results,
            "count": len(search_results)
        }
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.post("/batch/import")
async def batch_import_qa(
    entries: List[QAEntry] = ...,
    kb_manager=Depends(get_knowledge_base_manager)
):
    """批量导入QA条目"""
    if len(entries) > 100:
        raise HTTPException(status_code=400, detail="批量导入不能超过100条")
        
    try:
        entry_dicts = []
        for entry in entries:
            entry_dicts.append({
                'question': entry.question,
                'answer': entry.answer,
                'category': entry.category,
                'tags': entry.tags
            })
            
        await kb_manager.batch_update_index(entry_dicts)
        
        return {
            "message": f"成功导入{len(entries)}条QA",
            "count": len(entries)
        }
        
    except Exception as e:
        logger.error(f"批量导入失败: {e}")
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.get("/management", response_class=HTMLResponse)
async def get_management_page(request: Request):
    """返回QA管理界面HTML页面"""
    html_content = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QA话术管理系统 v3</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif; 
            background: #f5f7fa; 
            color: #2c3e50;
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 20px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 { 
            font-size: 28px; 
            font-weight: 300;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        .btn-success {
            background: #2ecc71;
            color: white;
        }
        .btn-success:hover {
            background: #27ae60;
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            text-align: center;
        }
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
            font-weight: 500;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .table th {
            background: #f8f9fa;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
        }
        .table td {
            padding: 12px;
            border-bottom: 1px solid #e9ecef;
        }
        .table tr:hover {
            background: #f8f9fa;
        }
        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }
        .search-input {
            flex: 1;
            max-width: 400px;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            backdrop-filter: blur(5px);
        }
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            width: 90%;
            max-width: 600px;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }
        .modal-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }
        .close {
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        .close:hover {
            background: #f8f9fa;
            color: #000;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-radius: 50%;
            border-top: 3px solid #667eea;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 6px;
            font-weight: 500;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            gap: 5px;
        }
        .page-btn {
            padding: 10px 15px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        .page-btn:hover {
            background: #f8f9fa;
        }
        .page-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }
        .nav-tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
            position: relative;
        }
        .nav-tab.active {
            color: #667eea;
        }
        .nav-tab.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #667eea;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>🤖 QA话术管理系统 v3</h1>
            <p style="margin-top: 5px; opacity: 0.9;">智能问答管理平台</p>
        </div>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid" id="statsContainer">
            <div class="stat-card">
                <div class="stat-value" id="totalEntries">-</div>
                <div class="stat-label">总条目数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="pendingCount">-</div>
                <div class="stat-label">待审核数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="todayCount">-</div>
                <div class="stat-label">今日新增</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="hitRate">-</div>
                <div class="stat-label">命中率</div>
            </div>
        </div>

        <div class="card">
            <!-- 标签页导航 -->
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="switchTab('qa-list')">QA管理</button>
                <button class="nav-tab" onclick="switchTab('pending-review')">待审核</button>
                <button class="nav-tab" onclick="switchTab('analytics')">数据分析</button>
            </div>

            <!-- QA列表标签页 -->
            <div id="qa-list" class="tab-content active">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <div class="search-bar">
                        <input type="text" id="searchInput" class="form-control search-input" placeholder="搜索问题或答案...">
                        <button class="btn btn-primary" onclick="searchQA()">🔍 搜索</button>
                        <button class="btn" onclick="clearSearch()">清空</button>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="showAddModal()">➕ 添加QA</button>
                        <button class="btn btn-success" onclick="refreshData()">🔄 刷新</button>
                    </div>
                </div>

                <div class="loading" id="loadingIndicator">
                    <div class="spinner"></div>
                    正在加载...
                </div>

                <table class="table" id="qaTable" style="display: none;">
                    <thead>
                        <tr>
                            <th style="width: 60px;">ID</th>
                            <th>问题</th>
                            <th>答案</th>
                            <th style="width: 100px;">分类</th>
                            <th style="width: 80px;">命中次数</th>
                            <th style="width: 80px;">置信度</th>
                            <th style="width: 120px;">创建时间</th>
                            <th style="width: 150px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="qaTableBody">
                    </tbody>
                </table>

                <div class="pagination" id="pagination"></div>
            </div>

            <!-- 待审核标签页 -->
            <div id="pending-review" class="tab-content">
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-success" onclick="loadPendingReviews()">🔄 刷新待审核</button>
                </div>
                
                <table class="table" id="pendingTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>问题</th>
                            <th>生成答案</th>
                            <th>置信度</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="pendingTableBody">
                    </tbody>
                </table>
            </div>

            <!-- 数据分析标签页 -->
            <div id="analytics" class="tab-content">
                <h3 style="margin-bottom: 20px;">📊 系统统计</h3>
                <div id="analyticsContent">
                    <div class="loading">
                        <div class="spinner"></div>
                        正在加载分析数据...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑QA模态框 -->
    <div id="qaModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">添加QA</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="modalAlert"></div>
            <form id="qaForm" onsubmit="saveQA(event)">
                <div class="form-group">
                    <label>问题 <span style="color: red;">*</span></label>
                    <textarea id="questionInput" class="form-control" rows="3" required 
                              placeholder="请输入问题内容..."></textarea>
                </div>
                <div class="form-group">
                    <label>答案 <span style="color: red;">*</span></label>
                    <textarea id="answerInput" class="form-control" rows="5" required 
                              placeholder="请输入答案内容..."></textarea>
                </div>
                <div class="form-group">
                    <label>分类</label>
                    <input type="text" id="categoryInput" class="form-control" 
                           placeholder="例如：产品介绍、价格咨询、售后服务">
                </div>
                <div class="form-group">
                    <label>标签</label>
                    <input type="text" id="tagsInput" class="form-control" 
                           placeholder="用逗号分隔，例如：热门,推荐,常见问题">
                </div>
                <div style="text-align: right; margin-top: 30px;">
                    <button type="button" class="btn" onclick="closeModal()" 
                            style="margin-right: 10px; background: #6c757d; color: white;">取消</button>
                    <button type="submit" class="btn btn-primary" id="saveBtn">
                        💾 保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let currentEditId = null;
        let currentSearch = '';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });

        // 刷新所有数据
        async function refreshData() {
            await Promise.all([
                loadQAList(),
                loadStats()
            ]);
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/qa/stats');
                const stats = await response.json();
                
                document.getElementById('totalEntries').textContent = 
                    stats.knowledge_base?.total_entries || 0;
                document.getElementById('pendingCount').textContent = 
                    stats.knowledge_base?.pending_count || 0;
                document.getElementById('todayCount').textContent = 
                    stats.knowledge_base?.today_count || 0;
                
                // 计算命中率
                const qaStats = stats.qa_manager || {};
                const hitRate = qaStats.success_rate_percent || 0;
                document.getElementById('hitRate').textContent = hitRate.toFixed(1) + '%';
                
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载QA列表
        async function loadQAList(page = 1, search = '') {
            try {
                currentPage = page;
                currentSearch = search;
                
                document.getElementById('loadingIndicator').style.display = 'block';
                document.getElementById('qaTable').style.display = 'none';
                
                const params = new URLSearchParams({
                    page: page,
                    page_size: 20
                });
                
                if (search) {
                    params.append('search', search);
                }
                
                const response = await fetch(`/api/qa/entries?${params}`);
                const data = await response.json();
                
                renderQATable(data.items);
                renderPagination(data.total, data.page, data.page_size);
                
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('qaTable').style.display = 'table';
                
            } catch (error) {
                console.error('加载QA列表失败:', error);
                showAlert('加载失败，请稍后重试', 'error');
            }
        }

        // 渲染QA表格
        function renderQATable(qaList) {
            const tbody = document.getElementById('qaTableBody');
            if (qaList.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                            📝 暂无数据
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = qaList.map(qa => `
                <tr>
                    <td><strong>${qa.id}</strong></td>
                    <td>
                        <div title="${qa.question}">
                            ${qa.question.length > 60 ? qa.question.substring(0, 60) + '...' : qa.question}
                        </div>
                    </td>
                    <td>
                        <div title="${qa.answer}">
                            ${qa.answer.length > 80 ? qa.answer.substring(0, 80) + '...' : qa.answer}
                        </div>
                    </td>
                    <td>
                        <span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                            ${qa.category || '未分类'}
                        </span>
                    </td>
                    <td><strong>${qa.hit_count}</strong></td>
                    <td>
                        <span style="color: ${qa.confidence_score >= 0.8 ? '#2ecc71' : qa.confidence_score >= 0.5 ? '#f39c12' : '#e74c3c'};">
                            ${qa.confidence_score.toFixed(2)}
                        </span>
                    </td>
                    <td style="font-size: 12px; color: #6c757d;">
                        ${new Date(qa.created_at).toLocaleString()}
                    </td>
                    <td>
                        <button class="btn btn-primary" style="font-size: 12px; padding: 5px 10px; margin-right: 5px;" 
                                onclick="editQA(${qa.id}, ${encodeURIComponent(JSON.stringify(qa))})">
                            ✏️ 编辑
                        </button>
                        <button class="btn btn-danger" style="font-size: 12px; padding: 5px 10px;" 
                                onclick="deleteQA(${qa.id})">
                            🗑️ 删除
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染分页
        function renderPagination(total, page, pageSize) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let html = '';
            
            // 上一页
            if (page > 1) {
                html += `<button class="page-btn" onclick="loadQAList(${page - 1}, '${currentSearch}')">‹ 上一页</button>`;
            }
            
            // 页码
            for (let i = Math.max(1, page - 2); i <= Math.min(totalPages, page + 2); i++) {
                html += `<button class="page-btn ${i === page ? 'active' : ''}" onclick="loadQAList(${i}, '${currentSearch}')">${i}</button>`;
            }
            
            // 下一页
            if (page < totalPages) {
                html += `<button class="page-btn" onclick="loadQAList(${page + 1}, '${currentSearch}')">下一页 ›</button>`;
            }
            
            pagination.innerHTML = html;
        }

        // 搜索功能
        function searchQA() {
            const searchText = document.getElementById('searchInput').value.trim();
            loadQAList(1, searchText);
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            loadQAList(1);
        }

        // 模态框相关
        function showAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '添加QA';
            document.getElementById('qaForm').reset();
            document.getElementById('modalAlert').innerHTML = '';
            document.getElementById('qaModal').style.display = 'block';
        }

        function editQA(id, dataStr) {
            try {
                const qa = JSON.parse(decodeURIComponent(dataStr));
                currentEditId = id;
                document.getElementById('modalTitle').textContent = '编辑QA';
                document.getElementById('questionInput').value = qa.question;
                document.getElementById('answerInput').value = qa.answer;
                document.getElementById('categoryInput').value = qa.category || '';
                document.getElementById('tagsInput').value = (qa.tags || []).join(', ');
                document.getElementById('modalAlert').innerHTML = '';
                document.getElementById('qaModal').style.display = 'block';
            } catch (error) {
                console.error('解析QA数据失败:', error);
                showAlert('数据解析失败', 'error');
            }
        }

        function closeModal() {
            document.getElementById('qaModal').style.display = 'none';
        }

        // 保存QA
        async function saveQA(event) {
            event.preventDefault();
            
            const saveBtn = document.getElementById('saveBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<div class="spinner" style="width: 16px; height: 16px;"></div> 保存中...';
            saveBtn.disabled = true;
            
            const data = {
                question: document.getElementById('questionInput').value.trim(),
                answer: document.getElementById('answerInput').value.trim(),
                category: document.getElementById('categoryInput').value.trim() || null,
                tags: document.getElementById('tagsInput').value.split(',').map(t => t.trim()).filter(t => t)
            };
            
            try {
                let response;
                if (currentEditId) {
                    response = await fetch(`/api/qa/entries/${currentEditId}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                } else {
                    response = await fetch('/api/qa/entries', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                }
                
                if (response.ok) {
                    closeModal();
                    showAlert(currentEditId ? '更新成功' : '添加成功', 'success');
                    loadQAList(currentPage, currentSearch);
                    loadStats();
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || '操作失败');
                }
                
            } catch (error) {
                console.error('保存失败:', error);
                document.getElementById('modalAlert').innerHTML = 
                    `<div class="alert alert-error">保存失败: ${error.message}</div>`;
            } finally {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            }
        }

        // 删除QA
        async function deleteQA(id) {
            if (!confirm('确定要删除这条QA吗？此操作不可恢复。')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/qa/entries/${id}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    showAlert('删除成功', 'success');
                    loadQAList(currentPage, currentSearch);
                    loadStats();
                } else {
                    throw new Error('删除失败');
                }
            } catch (error) {
                console.error('删除失败:', error);
                showAlert('删除失败，请稍后重试', 'error');
            }
        }

        // 加载待审核列表
        async function loadPendingReviews() {
            try {
                const response = await fetch('/api/qa/pending-reviews');
                const data = await response.json();
                
                const tbody = document.getElementById('pendingTableBody');
                if (data.items.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 40px; color: #6c757d;">
                                ✅ 暂无待审核内容
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                tbody.innerHTML = data.items.map(item => `
                    <tr>
                        <td>${item.id}</td>
                        <td title="${item.question}">
                            ${item.question.length > 50 ? item.question.substring(0, 50) + '...' : item.question}
                        </td>
                        <td title="${item.generated_answer}">
                            ${item.generated_answer.length > 80 ? item.generated_answer.substring(0, 80) + '...' : item.generated_answer}
                        </td>
                        <td>${item.confidence_score.toFixed(2)}</td>
                        <td style="font-size: 12px; color: #6c757d;">
                            ${new Date(item.created_at).toLocaleString()}
                        </td>
                        <td>
                            <button class="btn btn-success" style="font-size: 12px; padding: 5px 10px; margin-right: 5px;" 
                                    onclick="handleReview(${item.id}, 'approve')">
                                ✅ 批准
                            </button>
                            <button class="btn btn-danger" style="font-size: 12px; padding: 5px 10px;" 
                                    onclick="handleReview(${item.id}, 'reject')">
                                ❌ 拒绝
                            </button>
                        </td>
                    </tr>
                `).join('');
                
            } catch (error) {
                console.error('加载待审核列表失败:', error);
                showAlert('加载失败，请稍后重试', 'error');
            }
        }

        // 处理审核
        async function handleReview(id, action) {
            const actionText = action === 'approve' ? '批准' : '拒绝';
            if (!confirm(`确定要${actionText}这条生成的答案吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/qa/pending-reviews/${id}/action`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: action,
                        reviewer: 'admin'
                    })
                });
                
                if (response.ok) {
                    showAlert(`${actionText}成功`, 'success');
                    loadPendingReviews();
                    loadStats();
                } else {
                    throw new Error(`${actionText}失败`);
                }
            } catch (error) {
                console.error(`${actionText}失败:`, error);
                showAlert(`${actionText}失败，请稍后重试`, 'error');
            }
        }

        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签页按钮的激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示目标标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 加载对应数据
            if (tabName === 'pending-review') {
                loadPendingReviews();
            } else if (tabName === 'analytics') {
                loadAnalytics();
            }
        }

        // 加载分析数据
        async function loadAnalytics() {
            const content = document.getElementById('analyticsContent');
            try {
                const response = await fetch('/api/qa/stats');
                const stats = await response.json();
                
                content.innerHTML = `
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">${stats.knowledge_base?.total_entries || 0}</div>
                            <div class="stat-label">知识库条目</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.knowledge_base?.faiss_entries || 0}</div>
                            <div class="stat-label">向量索引</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${(stats.qa_manager?.success_rate_percent || 0).toFixed(1)}%</div>
                            <div class="stat-label">成功率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${(stats.qa_manager?.average_response_time || 0).toFixed(2)}s</div>
                            <div class="stat-label">平均响应时间</div>
                        </div>
                    </div>
                    
                    <h4 style="margin: 30px 0 15px 0;">🔥 热门问题</h4>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        ${stats.knowledge_base?.hot_questions?.map(q => `
                            <div style="margin-bottom: 15px; padding: 15px; background: white; border-radius: 6px; border-left: 4px solid #667eea;">
                                <div style="font-weight: 600; margin-bottom: 8px;">${q.question}</div>
                                <div style="color: #6c757d; font-size: 14px;">${q.answer}</div>
                                <div style="margin-top: 8px; font-size: 12px; color: #667eea;">
                                    <span style="background: #e3f2fd; padding: 2px 8px; border-radius: 12px;">
                                        命中 ${q.hit_count} 次
                                    </span>
                                </div>
                            </div>
                        `).join('') || '<p style="color: #6c757d; text-align: center;">暂无热门问题数据</p>'}
                    </div>
                `;
                
            } catch (error) {
                console.error('加载分析数据失败:', error);
                content.innerHTML = `
                    <div class="alert alert-error">
                        加载分析数据失败，请稍后重试
                    </div>
                `;
            }
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.minWidth = '300px';
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('qaModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
            if (event.ctrlKey && event.key === 'k') {
                event.preventDefault();
                document.getElementById('searchInput').focus();
            }
        });
    </script>
</body>
</html>
'''
    return HTMLResponse(content=html_content)


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "3.0.0"
    }