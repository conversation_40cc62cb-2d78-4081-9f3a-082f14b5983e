"""音频会话管理核心组件

实现会话级状态隔离，解决全局暂停状态冲突问题。
遵循状态机模式和CLAUDE.md规范。
"""

import asyncio
import time
from enum import Enum
from typing import Optional, Dict, Set, Any
from dataclasses import dataclass, field
from loguru import logger

from ..core.exceptions import ServiceError


class SessionState(Enum):
    """音频会话状态枚举"""
    IDLE = "idle"
    ACTIVE = "active"
    PAUSED_FOR_QA = "paused_for_qa"
    TERMINATING = "terminating"
    TERMINATED = "terminated"


class SessionType(Enum):
    """会话类型枚举"""
    MAIN_STREAM = "main_stream"
    QA_STREAM = "qa_stream"


@dataclass
class AudioSession:
    """音频会话实体
    
    管理单个音频会话的状态、生命周期和关联的TTS引擎。
    实现状态机模式确保状态转换的正确性。
    """
    
    session_id: str
    session_type: SessionType
    state: SessionState = field(default=SessionState.IDLE)
    tts_engine: Optional[Any] = field(default=None)
    paused_by_qa: Optional[str] = field(default=None)
    created_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    config: Dict[str, Any] = field(default_factory=dict)
    
    # 新增：生命周期管理字段 - 修复会话误终止问题
    lifecycle: str = field(default="persistent")  # "persistent" | "temporary"
    segment_count: int = field(default=0)  # 已播放分段数
    is_protected: bool = field(default=False)  # 防止意外终止的保护标志
    
    def __post_init__(self):
        """初始化后处理"""
        self.metrics = {
            "chunks_sent": 0,
            "bytes_sent": 0,
            "errors_count": 0
        }
        
        # 自动设置会话保护：主会话默认启用保护机制
        if self.session_type == SessionType.MAIN_STREAM:
            self.is_protected = True
            self.lifecycle = "persistent"
        elif self.session_type == SessionType.QA_STREAM:
            self.is_protected = False
            self.lifecycle = "temporary"
    
    async def transition_to(self, new_state: SessionState, context: Optional[Dict[str, Any]] = None) -> bool:
        """控制状态转换
        
        Args:
            new_state: 目标状态
            context: 状态转换上下文信息
            
        Returns:
            bool: 是否成功转换
        """
        context = context or {}
        
        # 定义有效的状态转换（增加灵活性以支持恢复场景）
        valid_transitions = {
            SessionState.IDLE: [SessionState.ACTIVE, SessionState.PAUSED_FOR_QA, SessionState.TERMINATING],
            SessionState.ACTIVE: [SessionState.PAUSED_FOR_QA, SessionState.TERMINATING],
            SessionState.PAUSED_FOR_QA: [SessionState.ACTIVE, SessionState.TERMINATING],
            SessionState.TERMINATING: [SessionState.TERMINATED],
            SessionState.TERMINATED: []
        }
        
        if new_state not in valid_transitions.get(self.state, []):
            logger.error(f"❌ Invalid state transition for session {self.session_id}: {self.state.value} → {new_state.value}")
            return False
        
        old_state = self.state
        self.state = new_state
        self.last_activity = time.time()
        
        # 执行状态转换逻辑
        try:
            await self._handle_state_transition(old_state, new_state, context)
            logger.info(f"🔄 Session {self.session_id} state: {old_state.value} → {new_state.value}")
            return True
        except Exception as e:
            # 转换失败，回滚状态
            self.state = old_state
            logger.error(f"❌ State transition failed for session {self.session_id}: {e}")
            return False
    
    async def _handle_state_transition(self, old_state: SessionState, new_state: SessionState, context: Dict[str, Any]):
        """处理状态转换的副作用
        
        Args:
            old_state: 原状态
            new_state: 新状态
            context: 转换上下文
        """
        if new_state == SessionState.PAUSED_FOR_QA:
            # 暂停TTS引擎
            if self.tts_engine and hasattr(self.tts_engine, 'pause'):
                await self.tts_engine.pause()
                logger.debug(f"🛑 TTS engine paused for session {self.session_id}")
            
            # 记录暂停原因
            self.paused_by_qa = context.get('qa_session_id')
            
        elif old_state == SessionState.PAUSED_FOR_QA and new_state == SessionState.ACTIVE:
            # 智能恢复TTS引擎：检查连接状态并决定恢复策略
            if self.tts_engine:
                resume_success = await self._smart_resume_engine(context)
                if not resume_success:
                    logger.warning(f"⚠️ Smart resume failed for session {self.session_id}, engine may need rebuilding")
            
            # 清除暂停信息
            self.paused_by_qa = None
            
        elif new_state == SessionState.TERMINATING:
            # 开始终止流程
            if self.tts_engine and hasattr(self.tts_engine, 'close'):
                try:
                    await self.tts_engine.close()
                    logger.debug(f"🔌 TTS engine closed for session {self.session_id}")
                except Exception as e:
                    logger.warning(f"⚠️ Error closing TTS engine for session {self.session_id}: {e}")
        
        elif new_state == SessionState.TERMINATED:
            # 清理资源
            self.tts_engine = None
            logger.debug(f"🗑️ Session {self.session_id} resources cleaned up")
    
    def is_healthy(self) -> bool:
        """检查会话健康状态
        
        Returns:
            bool: 会话是否健康
        """
        if self.state == SessionState.TERMINATED:
            return False
        
        # 检查TTS引擎连接
        if self.tts_engine and hasattr(self.tts_engine, 'is_connected'):
            if not self.tts_engine.is_connected:
                return False
        
        # 检查会话超时（30分钟无活动）
        if time.time() - self.last_activity > 30 * 60:
            return False
        
        return True
    
    def update_metrics(self, chunks_sent: int = 0, bytes_sent: int = 0, errors_count: int = 0):
        """更新会话指标
        
        Args:
            chunks_sent: 发送的音频块数量
            bytes_sent: 发送的字节数
            errors_count: 错误数量
        """
        self.metrics["chunks_sent"] += chunks_sent
        self.metrics["bytes_sent"] += bytes_sent
        self.metrics["errors_count"] += errors_count
        self.last_activity = time.time()
    
    async def _smart_resume_engine(self, context: Dict[str, Any]) -> bool:
        """智能恢复TTS引擎
        
        根据TTS引擎的实际状态选择最优的恢复策略：
        1. 如果连接仍然活跃，尝试简单恢复
        2. 如果连接已断开，快速重建连接
        3. 如果重建失败，创建新的引擎实例
        
        Args:
            context: 恢复上下文信息
            
        Returns:
            bool: 是否成功恢复
        """
        if not self.tts_engine:
            return False
        
        engine_type = type(self.tts_engine).__name__
        logger.info(f"🛠️ Starting smart resume for {engine_type} in session {self.session_id}")
        
        try:
            # 策略 1: 检查连接状态并尝试简单恢复
            if hasattr(self.tts_engine, 'is_connected') and self.tts_engine.is_connected:
                if hasattr(self.tts_engine, 'resume'):
                    logger.debug(f"🔄 Engine still connected, attempting simple resume")
                    resume_result = await self.tts_engine.resume()
                    if resume_result:
                        logger.info(f"✅ Simple resume successful for session {self.session_id}")
                        return True
                    else:
                        logger.warning(f"⚠️ Simple resume failed, trying connection rebuild")
            
            # 策略 2: 快速重建连接（针对CosyVoice等WebSocket引擎）
            if hasattr(self.tts_engine, 'connect') and hasattr(self.tts_engine, 'is_connected'):
                logger.debug(f"🔄 Attempting fast connection rebuild")
                
                # 清理旧连接状态
                if hasattr(self.tts_engine, 'cleanup'):
                    await self.tts_engine.cleanup()
                
                # 重新连接
                await self.tts_engine.connect()
                
                # 验证连接状态
                if self.tts_engine.is_connected:
                    # 重置暂停状态
                    if hasattr(self.tts_engine, 'is_paused'):
                        self.tts_engine.is_paused = False
                    if hasattr(self.tts_engine, 'pause_event'):
                        self.tts_engine.pause_event.set()
                    
                    logger.info(f"✅ Fast connection rebuild successful for session {self.session_id}")
                    return True
                else:
                    logger.warning(f"⚠️ Fast connection rebuild failed, connection not established")
            
            # 策略 3: 创建新的引擎实例（最后手段）
            logger.warning(f"🔄 Previous strategies failed, attempting engine recreation")
            
            # 保存原引擎配置
            original_config = getattr(self.tts_engine, 'config', None) or self.config.get('tts_config', {})
            
            # 清理旧引擎
            try:
                if hasattr(self.tts_engine, 'close'):
                    await self.tts_engine.close()
            except Exception as cleanup_error:
                logger.debug(f"🗑️ Engine cleanup error (expected): {cleanup_error}")
            
            # 重新创建引擎（需要在子类中实现）
            new_engine = await self._create_new_engine_instance(original_config)
            if new_engine:
                self.tts_engine = new_engine
                logger.info(f"✅ Engine recreation successful for session {self.session_id}")
                return True
            else:
                logger.error(f"❌ Engine recreation failed for session {self.session_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Smart resume failed for session {self.session_id}: {e}")
            return False
    
    async def _create_new_engine_instance(self, config: Dict[str, Any]) -> Optional[Any]:
        """创建新的TTS引擎实例
        
        这是一个默认实现，子类可以重写来提供具体的引擎创建逻辑。
        增强了错误处理和日志记录，确保问题快速诊断。
        
        Args:
            config: 引擎配置
            
        Returns:
            Optional[Any]: 新的引擎实例，失败时返回None
        """
        logger.info(f"🛠️ Creating new TTS engine for session {self.session_id} (type: {self.session_type.value})")
        
        # 默认实现：尝试使用工厂模式创建引擎
        try:
            # 对于CosyVoice引擎，使用直接导入
            if self.session_type.value == "main_stream":
                logger.debug(f"🔧 Importing CosyVoiceUnifiedEngine for main stream session")
                
                from ..services.tts_engines.cosyvoice_unified_engine import CosyVoiceUnifiedEngine
                logger.debug(f"✅ CosyVoiceUnifiedEngine imported successfully")
                
                # 验证配置
                if not config:
                    logger.warning(f"⚠️ Empty config provided, using defaults")
                    config = {}
                
                logger.debug(f"🎛️ Engine config: {config}")
                
                # 创建引擎实例
                engine = CosyVoiceUnifiedEngine(config=config)
                logger.debug(f"✅ Engine instance created: {type(engine).__name__}")
                
                # 初始化引擎
                await engine.initialize()
                logger.info(f"🎉 TTS engine successfully created and initialized for session {self.session_id}")
                
                return engine
            else:
                logger.debug(f"ℹ️ Non-main session type {self.session_type.value}, skipping engine creation")
                return None
                
        except ImportError as e:
            # 特别处理导入错误，这是我们修复的核心问题
            if "attempted relative import beyond top-level package" in str(e):
                logger.error(f"🚨 CRITICAL: Relative import error (should be fixed): {e}")
                logger.error(f"   This indicates the import path fix may not be working correctly")
                logger.error(f"   Expected fix: 'from ..services.tts_engines.cosyvoice_unified_engine import'")
            else:
                logger.error(f"❌ Import error creating engine: {e}")
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to create new engine instance: {e}")
            logger.error(f"   Session ID: {self.session_id}")
            logger.error(f"   Session type: {self.session_type.value}")
            logger.error(f"   Config: {config}")
            
            # 记录堆栈跟踪以便调试
            import traceback
            logger.debug(f"🔍 Engine creation error traceback:\n{traceback.format_exc()}")
            
            return None


class AudioSessionManager:
    """音频会话管理器
    
    实现会话级状态隔离，管理主流和Q&A会话的生命周期。
    遵循Fail-Fast原则，所有错误立即抛出异常。
    """
    
    def __init__(self):
        """初始化会话管理器"""
        self.sessions: Dict[str, AudioSession] = {}
        self.active_main_session: Optional[str] = None
        self.active_qa_sessions: Set[str] = set()
        self._lock = asyncio.Lock()
        
        # Phase 2: 增强的会话恢复功能
        self.recovery_cache: Dict[str, Dict[str, Any]] = {}  # 缓存会话恢复信息
        self.prewarmed_engines: Dict[str, Any] = {}  # 预热的TTS引擎实例
        self.recovery_stats = {
            "successful_recoveries": 0,
            "failed_recoveries": 0,
            "fast_recoveries": 0,  # 快速恢复（<500ms）
            "slow_recoveries": 0   # 缓慢恢复（>2s）
        }
        
        # Phase 4: 性能优化和监控
        self.performance_monitor = {
            "session_operations": {
                "create_count": 0,
                "terminate_count": 0,
                "pause_count": 0,
                "resume_count": 0,
                "force_stop_count": 0
            },
            "timing_stats": {
                "avg_create_time": 0.0,
                "avg_resume_time": 0.0,
                "avg_terminate_time": 0.0,
                "max_create_time": 0.0,
                "max_resume_time": 0.0
            },
            "resource_usage": {
                "peak_sessions": 0,
                "active_engines": 0,
                "memory_warnings": 0
            }
        }
        self.connection_pool = {}  # 连接池管理
        self.last_cleanup_time = time.time()
    
    async def create_session(
        self, 
        session_id: str, 
        session_type: SessionType,
        config: Optional[Dict[str, Any]] = None,
        lifecycle: str = "persistent"  # 新增：生命周期参数
    ) -> AudioSession:
        """创建新的音频会话
        
        Args:
            session_id: 会话ID
            session_type: 会话类型
            config: 会话配置
            lifecycle: 会话生命周期 ("persistent" | "temporary")
            
        Returns:
            AudioSession: 创建的会话实例
            
        Raises:
            ServiceError: 当会话已存在或创建失败时
        """
        start_time = time.time()  # 性能监控
        
        async with self._lock:
            if session_id in self.sessions:
                raise ServiceError(
                    f"Session {session_id} already exists",
                    "audio_session_manager",
                    "SESSION_EXISTS"
                )
            
            config = config or {}
            session = AudioSession(
                session_id=session_id,
                session_type=session_type,
                config=config
            )
            
            # 应用生命周期设置（覆盖默认行为）
            session.lifecycle = lifecycle
            if lifecycle == "temporary":
                session.is_protected = False
            elif lifecycle == "persistent":
                session.is_protected = True
            
            self.sessions[session_id] = session
            
            # 更新活跃会话跟踪，增加保护机制日志
            if session_type == SessionType.MAIN_STREAM:
                if self.active_main_session:
                    logger.warning(f"⚠️ Replacing active main session {self.active_main_session} with {session_id}")
                self.active_main_session = session_id
                logger.info(f"🛡️ Main session {session_id} created with protection: {session.is_protected}")
            elif session_type == SessionType.QA_STREAM:
                self.active_qa_sessions.add(session_id)
                logger.info(f"⚡ QA session {session_id} created as temporary session")
            
            # 性能监控
            create_duration = time.time() - start_time
            self._record_operation_timing("create", create_duration)
            self._update_resource_stats()
            
            logger.info(f"✅ Created {session_type.value} session: {session_id} in {create_duration:.3f}s")
            return session
    
    async def get_session(self, session_id: str) -> Optional[AudioSession]:
        """获取指定的会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            Optional[AudioSession]: 会话实例，不存在时返回None
        """
        return self.sessions.get(session_id)
    
    async def get_main_session(self) -> Optional[AudioSession]:
        """获取当前活跃的主会话
        
        Returns:
            Optional[AudioSession]: 主会话实例，不存在时返回None
        """
        if self.active_main_session:
            return self.sessions.get(self.active_main_session)
        return None
    
    async def get_qa_sessions(self) -> Set[AudioSession]:
        """获取所有活跃的Q&A会话
        
        Returns:
            Set[AudioSession]: Q&A会话集合
        """
        qa_sessions = set()
        for session_id in self.active_qa_sessions:
            session = self.sessions.get(session_id)
            if session:
                qa_sessions.add(session)
        return qa_sessions
    
    async def pause_main_session(self, qa_session_id: str) -> bool:
        """暂停主会话以进行Q&A
        
        Args:
            qa_session_id: 引起暂停的Q&A会话ID
            
        Returns:
            bool: 是否成功暂停
            
        Raises:
            ServiceError: 当暂停失败时
        """
        async with self._lock:
            main_session = await self.get_main_session()
            if not main_session:
                logger.warning("No active main session to pause")
                return False
            
            if main_session.state != SessionState.ACTIVE:
                logger.warning(f"Main session {main_session.session_id} is not active (state: {main_session.state.value})")
                return False
            
            # 执行暂停
            success = await main_session.transition_to(
                SessionState.PAUSED_FOR_QA,
                context={"qa_session_id": qa_session_id}
            )
            
            if not success:
                raise ServiceError(
                    f"Failed to pause main session {main_session.session_id}",
                    "audio_session_manager",
                    "PAUSE_FAILED"
                )
            
            logger.info(f"🛑 Main session {main_session.session_id} paused for Q&A: {qa_session_id}")
            return True
    
    async def force_stop_main_session(self, qa_session_id: str) -> bool:
        """强制立即停止主会话以进行Q&A
        
        与pause_main_session不同，这个方法会立即强制停止TTS引擎，
        停止音频生成和传输，实现真正的立即中断。
        
        Args:
            qa_session_id: 引起强制停止的Q&A会话ID
            
        Returns:
            bool: 是否成功强制停止
            
        Raises:
            ServiceError: 当强制停止失败时
        """
        async with self._lock:
            main_session = await self.get_main_session()
            if not main_session:
                logger.warning("No active main session to force stop")
                return False
            
            if main_session.state != SessionState.ACTIVE:
                logger.warning(f"Main session {main_session.session_id} is not active (state: {main_session.state.value})")
                return False
            
            logger.info(f"🚨 Force stopping main session {main_session.session_id} for Q&A: {qa_session_id}")
            
            # 1. 强制立即停止TTS引擎（Q&A需要即时反馈，不能等待缓存播放完）
            if main_session.tts_engine:
                try:
                    # Q&A场景必须使用强制停止，实现真正的立即中断
                    if hasattr(main_session.tts_engine, 'force_stop'):
                        await main_session.tts_engine.force_stop()
                        logger.debug(f"🚨 TTS engine force stopped for immediate Q&A response")
                    elif hasattr(main_session.tts_engine, 'graceful_stop'):
                        # 如果没有force_stop，使用graceful_stop
                        await main_session.tts_engine.graceful_stop()
                        logger.debug(f"🛑 TTS engine gracefully stopped (fallback)")
                    else:
                        # 最后回退到传统暂停
                        await main_session.tts_engine.pause()
                        logger.debug(f"⏸️ TTS engine paused (fallback)")
                except Exception as e:
                    logger.warning(f"⚠️ Error stopping TTS engine: {e}")
            
            # 2. 执行状态转换到强制暂停
            success = await main_session.transition_to(
                SessionState.PAUSED_FOR_QA,
                context={
                    "qa_session_id": qa_session_id,
                    "force_stopped": True,  # 标记为强制停止
                    "stop_timestamp": time.time()
                }
            )
            
            if not success:
                raise ServiceError(
                    f"Failed to force stop main session {main_session.session_id}",
                    "audio_session_manager",
                    "FORCE_STOP_FAILED"
                )
            
            logger.info(f"✅ Main session {main_session.session_id} force stopped for Q&A: {qa_session_id}")
            return True
    
    async def interrupt_for_qa(self, checkpoint: 'PlaybackCheckpoint') -> bool:
        """为Q&A中断会话 (简化版 - 修复关键问题3)
        
        只负责TTS引擎的停止，不再尝试保存复杂状态。
        检查点由调用者（LiveStreamManager）管理。
        
        Args:
            checkpoint: 播放检查点（由调用者管理）
            
        Returns:
            bool: 是否成功中断
        """
        try:
            if self.tts_engine:
                # 直接强制停止 - 接受TTS引擎的破坏性中断
                await self.tts_engine.force_stop()
                logger.info("🚨 TTS engine interrupted for Q&A")
                
                # 简单的状态转换
                await self.transition_to(SessionState.PAUSED_FOR_QA)
                return True
            else:
                logger.warning("No TTS engine to interrupt")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error interrupting session for Q&A: {e}")
            return False
    
    async def resume_from_checkpoint(self) -> bool:
        """从检查点恢复会话 (简化版 - 修复关键问题3)
        
        只负责重建TTS引擎，不再尝试恢复引擎状态。
        实际的内容恢复由LiveStreamManager处理。
        
        Returns:
            bool: 是否成功恢复
        """
        try:
            # 创建新的TTS引擎实例（接受无状态重建）
            self.tts_engine = self._create_new_engine_instance()
            
            # 转换到活跃状态
            await self.transition_to(SessionState.ACTIVE)
            
            logger.info("✅ Session resumed with new TTS engine - content recovery delegated to LiveStreamManager")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error resuming session from checkpoint: {e}")
            return False

    async def resume_main_session(self, qa_session_id: str) -> bool:
        """恢复主会话（Q&A完成后）
        
        Args:
            qa_session_id: 完成的Q&A会话ID
            
        Returns:
            bool: 是否成功恢复
            
        Raises:
            ServiceError: 当恢复失败时
        """
        return await self.enhanced_resume_main_session(qa_session_id)
    
    async def terminate_session(self, session_id: str, force: bool = False) -> bool:
        """终止指定的会话
        
        Args:
            session_id: 要终止的会话ID
            force: 是否强制终止（忽略保护机制）
            
        Returns:
            bool: 是否成功终止
        """
        async with self._lock:
            session = self.sessions.get(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for termination")
                return False
            
            # 保护机制：检查受保护会话
            if session.is_protected and not force:
                logger.warning(f"🛡️ Protected session {session_id} ({session.session_type.value}) "
                             f"cannot be terminated without force=True")
                logger.warning(f"   Session lifecycle: {session.lifecycle}, segments: {session.segment_count}")
                logger.warning(f"   This is likely preventing 'No active main session to resume' errors")
                return False
            
            # 记录终止原因
            if force:
                logger.info(f"🔥 Force terminating session {session_id} ({session.session_type.value})")
            else:
                logger.info(f"🗑️ Terminating session {session_id} ({session.session_type.value})")
            
            # 状态转换：首先转换到TERMINATING
            if session.state != SessionState.TERMINATED:
                await session.transition_to(SessionState.TERMINATING)
                await session.transition_to(SessionState.TERMINATED)
            
            # 清理会话跟踪
            if session.session_type == SessionType.MAIN_STREAM and self.active_main_session == session_id:
                self.active_main_session = None
            elif session.session_type == SessionType.QA_STREAM and session_id in self.active_qa_sessions:
                self.active_qa_sessions.remove(session_id)
            
            # 从会话字典中移除
            del self.sessions[session_id]
            
            logger.info(f"🗑️ Session {session_id} terminated and cleaned up")
            return True
    
    async def terminate_main_session(self, session_id: str = None) -> bool:
        """安全终止主会话（仅在直播完全结束时使用）
        
        Args:
            session_id: 主会话ID，如果为None则终止当前活跃主会话
            
        Returns:
            bool: 是否成功终止
        """
        if session_id is None:
            session_id = self.active_main_session
            
        if not session_id:
            logger.warning("No main session to terminate")
            return False
            
        main_session = await self.get_session(session_id)
        if not main_session or main_session.session_type != SessionType.MAIN_STREAM:
            logger.warning(f"Session {session_id} is not a main session")
            return False
            
        logger.info(f"🔚 Safely terminating main session {session_id} (live stream ended)")
        # 强制终止主会话（忽略保护机制）
        return await self.terminate_session(session_id, force=True)
    
    async def cleanup_inactive_sessions(self) -> int:
        """清理不活跃的会话
        
        Returns:
            int: 清理的会话数量
        """
        cleanup_count = 0
        inactive_sessions = []
        
        async with self._lock:
            for session_id, session in self.sessions.items():
                if not session.is_healthy():
                    inactive_sessions.append(session_id)
        
        # 清理不健康的会话
        for session_id in inactive_sessions:
            await self.terminate_session(session_id)
            cleanup_count += 1
        
        if cleanup_count > 0:
            logger.info(f"🧹 Cleaned up {cleanup_count} inactive sessions")
        
        return cleanup_count
    
    def get_status(self) -> Dict[str, Any]:
        """获取会话管理器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            "total_sessions": len(self.sessions),
            "active_main_session": self.active_main_session,
            "active_qa_sessions": list(self.active_qa_sessions),
            "session_details": {
                session_id: {
                    "type": session.session_type.value,
                    "state": session.state.value,
                    "paused_by_qa": session.paused_by_qa,
                    "created_at": session.created_at,
                    "last_activity": session.last_activity,
                    "metrics": session.metrics
                }
                for session_id, session in self.sessions.items()
            }
        }
    
    async def enhanced_resume_main_session(self, qa_session_id: str) -> bool:
        """增强的主会话恢复机制
        
        实现智能化的会话恢复，包括：
        1. 预热连接复用
        2. 恢复状态缓存
        3. 快速重建机制
        4. 性能监控和优化
        
        Args:
            qa_session_id: 完成的Q&A会话黑
            
        Returns:
            bool: 是否成功恢复
        """
        start_time = time.time()
        logger.info(f"🔄 TIMING: Main session resume started at {start_time:.3f} for QA {qa_session_id}")
        
        async with self._lock:
            main_session = await self.get_main_session()
            if not main_session:
                logger.warning("No active main session to resume - attempting recovery...")
                # 容错机制：尝试恢复丢失的主会话
                recovery_success = await self._attempt_main_session_recovery(qa_session_id)
                if recovery_success:
                    main_session = await self.get_main_session()
                    logger.info(f"✅ Successfully recovered main session: {main_session.session_id}")
                else:
                    logger.error("❌ Failed to recover main session - this resolves the 'No active main session to resume' issue")
                    return False
            
            if main_session.state != SessionState.PAUSED_FOR_QA:
                logger.warning(f"Main session {main_session.session_id} is not paused (state: {main_session.state.value})")
                return False
            
            # 检查是否由指定的Q&A会话暂停
            if main_session.paused_by_qa != qa_session_id:
                logger.warning(f"Main session paused by different Q&A session: {main_session.paused_by_qa} != {qa_session_id}")
                return False
            
            logger.info(f"🚀 Starting enhanced resume for main session {main_session.session_id}")
            
            try:
                # 策略 1: 尝试使用预热的引擎
                recovery_success = await self._try_prewarmed_recovery(main_session, qa_session_id)
                if recovery_success:
                    recovery_time = time.time() - start_time
                    self.recovery_stats["successful_recoveries"] += 1
                    if recovery_time < 0.5:
                        self.recovery_stats["fast_recoveries"] += 1
                    logger.info(f"✨ Fast prewarmed recovery completed in {recovery_time:.3f}s")
                    return True
                
                # 策略 2: 执行标准的智能恢复
                success = await main_session.transition_to(
                    SessionState.ACTIVE,
                    context={
                        "resumed_by_qa": qa_session_id,
                        "recovery_start_time": start_time,
                        "use_enhanced_recovery": True
                    }
                )
                
                if success:
                    recovery_time = time.time() - start_time
                    self.recovery_stats["successful_recoveries"] += 1
                    
                    if recovery_time < 0.5:
                        self.recovery_stats["fast_recoveries"] += 1
                    elif recovery_time > 2.0:
                        self.recovery_stats["slow_recoveries"] += 1
                    
                    logger.info(f"✅ Enhanced resume completed in {recovery_time:.3f}s for main session {main_session.session_id}")
                    logger.info(f"🔄 TIMING: Main session resume completed at {time.time():.3f} (duration: {recovery_time:.3f}s)")
                    
                    # 策略 3: 异步预热下一个引擎实例（为下次Q&A做准备）
                    asyncio.create_task(self._prewarm_engine_for_next_qa(main_session))
                    
                    return True
                else:
                    self.recovery_stats["failed_recoveries"] += 1
                    raise ServiceError(
                        f"Failed to resume main session {main_session.session_id}",
                        "audio_session_manager",
                        "ENHANCED_RESUME_FAILED"
                    )
                    
            except Exception as e:
                recovery_time = time.time() - start_time
                self.recovery_stats["failed_recoveries"] += 1
                logger.error(f"❌ Enhanced resume failed after {recovery_time:.3f}s: {e}")
                raise
    
    async def _try_prewarmed_recovery(self, main_session: AudioSession, qa_session_id: str) -> bool:
        """尝试使用预热的引擎进行快速恢复
        
        Args:
            main_session: 主会话实例
            qa_session_id: Q&A会话黑
            
        Returns:
            bool: 是否成功使用预热恢复
        """
        prewarmed_key = f"main_session_{main_session.session_type.value}"
        
        if prewarmed_key in self.prewarmed_engines:
            logger.debug(f"🔥 Found prewarmed engine for {prewarmed_key}")
            
            try:
                prewarmed_engine = self.prewarmed_engines.pop(prewarmed_key)
                
                # 检查预热引擎的健康状态
                if (hasattr(prewarmed_engine, 'is_connected') and 
                    prewarmed_engine.is_connected):
                    
                    # 更换引擎并恢复状态
                    old_engine = main_session.tts_engine
                    main_session.tts_engine = prewarmed_engine
                    
                    # 清理旧引擎（异步）
                    if old_engine:
                        asyncio.create_task(self._cleanup_old_engine(old_engine))
                    
                    logger.info(f"✨ Prewarmed engine successfully swapped for session {main_session.session_id}")
                    return True
                else:
                    logger.warning(f"⚠️ Prewarmed engine not connected, discarding")
                    asyncio.create_task(self._cleanup_old_engine(prewarmed_engine))
                    
            except Exception as e:
                logger.warning(f"⚠️ Prewarmed recovery failed: {e}")
        
        return False
    
    async def _prewarm_engine_for_next_qa(self, main_session: AudioSession):
        """为下一次Q&A预热引擎实例
        
        Args:
            main_session: 主会话实例
        """
        try:
            logger.debug(f"🔥 Starting engine prewarming for next Q&A")
            
            # 获取当前引擎配置
            config = main_session.config.get('tts_config', {})
            
            # 创建预热引擎
            new_engine = await main_session._create_new_engine_instance(config)
            
            if new_engine:
                prewarmed_key = f"main_session_{main_session.session_type.value}"
                
                # 清理旧的预热引擎（如果存在）
                if prewarmed_key in self.prewarmed_engines:
                    old_prewarmed = self.prewarmed_engines.pop(prewarmed_key)
                    asyncio.create_task(self._cleanup_old_engine(old_prewarmed))
                
                # 储存新的预热引擎
                self.prewarmed_engines[prewarmed_key] = new_engine
                logger.info(f"✨ Engine prewarming completed for next Q&A")
            else:
                logger.warning(f"⚠️ Engine prewarming failed")
                
        except Exception as e:
            logger.error(f"❌ Engine prewarming error: {e}")
    
    async def _cleanup_old_engine(self, engine: Any):
        """异步清理旧引擎
        
        Args:
            engine: 要清理的引擎实例
        """
        try:
            if hasattr(engine, 'close'):
                await engine.close()
            elif hasattr(engine, 'cleanup'):
                await engine.cleanup()
            logger.debug(f"🗑️ Old engine cleaned up successfully")
        except Exception as e:
            logger.debug(f"🗑️ Engine cleanup error (expected): {e}")
    
    def _record_operation_timing(self, operation: str, duration: float):
        """记录操作耗时统计
        
        Args:
            operation: 操作类型 (create, resume, terminate等)
            duration: 操作耗时（秒）
        """
        # 记录操作计数（先更新计数）
        count_key = f"{operation}_count"
        self.performance_monitor["session_operations"][count_key] = self.performance_monitor["session_operations"].get(count_key, 0) + 1
        
        # 更新性能监控统计
        if operation in ["create", "resume", "terminate"]:
            stats = self.performance_monitor["timing_stats"]
            
            # 更新平均耗时
            avg_key = f"avg_{operation}_time"
            max_key = f"max_{operation}_time"
            
            current_count = self.performance_monitor["session_operations"][count_key]
            
            if avg_key in stats and current_count > 1:
                # 计算新的平均值
                current_avg = stats[avg_key]
                new_avg = (current_avg * (current_count - 1) + duration) / current_count
                stats[avg_key] = new_avg
            else:
                stats[avg_key] = duration
            
            # 更新最大耗时
            if max_key in stats:
                stats[max_key] = max(stats[max_key], duration)
            else:
                stats[max_key] = duration
        
        logger.debug(f"📊 {operation} operation took {duration:.3f}s")
    
    def _update_resource_stats(self):
        """更新资源使用统计"""
        current_sessions = len(self.sessions)
        active_engines = sum(1 for session in self.sessions.values() if session.tts_engine is not None)
        
        # 更新峰值会话数
        if current_sessions > self.performance_monitor["resource_usage"]["peak_sessions"]:
            self.performance_monitor["resource_usage"]["peak_sessions"] = current_sessions
        
        # 更新活跃引擎数
        self.performance_monitor["resource_usage"]["active_engines"] = active_engines
        
        # 检查内存警告条件
        if current_sessions > 10:  # 超过10个会话时发出警告
            self.performance_monitor["resource_usage"]["memory_warnings"] += 1
            logger.warning(f"⚠️ High session count detected: {current_sessions} sessions")
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """获取恢复统计信息
        
        Returns:
            Dict[str, Any]: 恢复统计数据
        """
        total_attempts = self.recovery_stats["successful_recoveries"] + self.recovery_stats["failed_recoveries"]
        
        return {
            **self.recovery_stats,
            "total_recovery_attempts": total_attempts,
            "success_rate": (self.recovery_stats["successful_recoveries"] / max(1, total_attempts)),
            "fast_recovery_rate": (self.recovery_stats["fast_recoveries"] / max(1, self.recovery_stats["successful_recoveries"])),
            "prewarmed_engines_available": len(self.prewarmed_engines),
            "cached_recovery_states": len(self.recovery_cache),
            "performance_monitor": self.performance_monitor
        }
    
    async def _attempt_main_session_recovery(self, qa_session_id: str) -> bool:
        """尝试恢复丢失的主会话
        
        当主会话意外丢失时，尝试重建或从缓存恢复。
        这是解决"No active main session to resume"错误的关键方法。
        
        Args:
            qa_session_id: 引起恢复的Q&A会话ID
            
        Returns:
            bool: 是否成功恢复
        """
        try:
            logger.info(f"🔄 Attempting main session recovery triggered by QA: {qa_session_id}")
            
            # 策略1：检查所有会话中是否有主会话但追踪状态丢失
            for session_id, session in self.sessions.items():
                if (session.session_type == SessionType.MAIN_STREAM and 
                    session.state == SessionState.PAUSED_FOR_QA and
                    session.paused_by_qa == qa_session_id):
                    
                    logger.info(f"🔍 Found orphaned main session {session_id}, restoring tracking")
                    self.active_main_session = session_id
                    return True
            
            # 策略2：检查是否有未正确标记的主会话
            main_sessions = [s for s in self.sessions.values() 
                           if s.session_type == SessionType.MAIN_STREAM]
            
            if main_sessions:
                # 选择最近活跃的主会话
                latest_main = max(main_sessions, key=lambda s: s.last_activity)
                logger.info(f"🔄 Restoring latest main session: {latest_main.session_id}")
                self.active_main_session = latest_main.session_id
                
                # 修正会话状态
                if latest_main.state != SessionState.PAUSED_FOR_QA:
                    await latest_main.transition_to(
                        SessionState.PAUSED_FOR_QA,
                        context={"qa_session_id": qa_session_id, "recovered": True}
                    )
                
                return True
            
            # 策略3：如果完全没有主会话，记录问题但不创建新会话
            # （因为会话应该由外部系统管理）
            logger.error(f"❌ No main sessions found for recovery - "
                        f"this indicates a serious session management issue")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Main session recovery failed: {e}")
            return False
    
    async def health_check_sessions(self) -> Dict[str, Any]:
        """全面的会话健康检查和自动修复
        
        定期执行以发现和修复会话管理问题，预防"No active main session"等错误。
        
        Returns:
            Dict[str, Any]: 健康检查报告
        """
        health_report = {
            "timestamp": time.time(),
            "main_session_healthy": False,
            "protected_sessions_count": 0,
            "orphaned_sessions_count": 0,
            "issues_found": [],
            "auto_fixes_applied": [],
            "recommendations": []
        }
        
        try:
            logger.info("🏥 Starting comprehensive session health check...")
            
            # 检查1：主会话健康状态
            main_session = await self.get_main_session()
            if main_session:
                health_report["main_session_healthy"] = main_session.is_healthy()
                if main_session.is_protected:
                    health_report["protected_sessions_count"] += 1
                    
                # 检查主会话状态一致性
                if self.active_main_session != main_session.session_id:
                    issue = f"Main session tracking inconsistent: {self.active_main_session} vs {main_session.session_id}"
                    health_report["issues_found"].append(issue)
                    
                    # 自动修复
                    self.active_main_session = main_session.session_id
                    health_report["auto_fixes_applied"].append("Fixed main session tracking")
            else:
                # 检查是否有孤立的主会话
                main_sessions = [s for s in self.sessions.values() 
                               if s.session_type == SessionType.MAIN_STREAM]
                
                if main_sessions:
                    health_report["orphaned_sessions_count"] = len(main_sessions)
                    issue = f"Found {len(main_sessions)} orphaned main sessions"
                    health_report["issues_found"].append(issue)
                    
                    # 自动修复：恢复最近的主会话
                    latest_main = max(main_sessions, key=lambda s: s.last_activity)
                    self.active_main_session = latest_main.session_id
                    health_report["auto_fixes_applied"].append(
                        f"Restored main session tracking to {latest_main.session_id}"
                    )
                    health_report["main_session_healthy"] = True
            
            # 检查2：保护机制状态
            for session in self.sessions.values():
                if session.is_protected:
                    health_report["protected_sessions_count"] += 1
                    
                # 检查主会话是否正确设置了保护
                if (session.session_type == SessionType.MAIN_STREAM and 
                    not session.is_protected):
                    issue = f"Main session {session.session_id} missing protection"
                    health_report["issues_found"].append(issue)
                    
                    # 自动修复
                    session.is_protected = True
                    health_report["auto_fixes_applied"].append(
                        f"Added protection to main session {session.session_id}"
                    )
            
            # 检查3：会话状态不一致
            qa_sessions_count = len(self.active_qa_sessions)
            actual_qa_count = len([s for s in self.sessions.values() 
                                 if s.session_type == SessionType.QA_STREAM])
            
            if qa_sessions_count != actual_qa_count:
                issue = f"QA session tracking inconsistent: tracked={qa_sessions_count}, actual={actual_qa_count}"
                health_report["issues_found"].append(issue)
                
                # 自动修复QA会话追踪
                self.active_qa_sessions.clear()
                for session_id, session in self.sessions.items():
                    if session.session_type == SessionType.QA_STREAM:
                        self.active_qa_sessions.add(session_id)
                
                health_report["auto_fixes_applied"].append("Fixed QA session tracking")
            
            # 生成建议
            if not health_report["main_session_healthy"]:
                health_report["recommendations"].append(
                    "Consider restarting the main streaming session"
                )
            
            if health_report["orphaned_sessions_count"] > 0:
                health_report["recommendations"].append(
                    "Review session lifecycle management to prevent orphaning"
                )
            
            # 记录健康检查结果
            issues_count = len(health_report["issues_found"])
            fixes_count = len(health_report["auto_fixes_applied"])
            
            if issues_count == 0:
                logger.info("✅ Session health check: All systems healthy")
            else:
                logger.info(f"🔧 Session health check: Found {issues_count} issues, applied {fixes_count} auto-fixes")
                
            return health_report
            
        except Exception as e:
            logger.error(f"❌ Session health check failed: {e}")
            health_report["issues_found"].append(f"Health check error: {str(e)}")
            return health_report


# 全局会话管理器实例
_session_manager: Optional[AudioSessionManager] = None


def get_session_manager() -> AudioSessionManager:
    """获取全局会话管理器实例
    
    Returns:
        AudioSessionManager: 会话管理器实例
    """
    global _session_manager
    if _session_manager is None:
        _session_manager = AudioSessionManager()
    return _session_manager