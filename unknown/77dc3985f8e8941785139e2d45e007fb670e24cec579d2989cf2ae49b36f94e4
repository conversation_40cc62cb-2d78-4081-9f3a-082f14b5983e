"""增强版QA管理器 - 集成QA系统v3"""

import asyncio
import time
import json
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import logging
from prometheus_client import Counter, Histogram, Gauge

from .preprocessor import QuestionPreprocessor
from .knowledge_base import KnowledgeBaseManager
from .router import QARouter, RouteDecision
from .ranker import QARanker
from .manager import QAManager  # 原始QA管理器

logger = logging.getLogger(__name__)

# 监控指标
qa_requests_total = Counter('qa_requests_total', 'Total QA requests', ['source'])
qa_processing_time = Histogram('qa_processing_time_seconds', 'QA processing time')
qa_active_sessions = Gauge('qa_active_sessions', 'Active QA sessions')


class EnhancedQAManager(QAManager):
    """增强版QA管理器，集成知识库和智能路由"""
    
    def __init__(self,
                 knowledge_base: KnowledgeBaseManager,
                 router: QARouter,
                 ranker: QARan<PERSON>,
                 preprocessor: QuestionPreprocessor,
                 **kwargs):
        
        # 调用父类初始化
        super().__init__(**kwargs)
        
        # 新增组件
        self.knowledge_base = knowledge_base
        self.router = router
        self.ranker = ranker
        self.preprocessor = preprocessor
        
        # 问题队列和缓冲区
        self.question_buffer = []
        self.buffer_lock = asyncio.Lock()
        self.last_process_time = time.time()
        
        # 配置参数
        self.buffer_size = 5
        self.buffer_timeout = 10  # 秒
        
        # 扩展统计信息
        self.enhanced_stats = {
            'knowledge_base_hits': 0,
            'llm_generations': 0,
            'ignored_questions': 0,
            'cache_hits': 0,
            'buffer_processed_batches': 0,
            'average_buffer_size': 0.0,
            'router_stats': {},
            'ranker_stats': {}
        }
        
        logger.info("✅ EnhancedQAManager 初始化完成")
        
    async def handle_qa(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理QA请求的增强版主入口"""
        start_time = time.time()
        qa_requests_total.labels(source='enhanced').inc()
        qa_active_sessions.inc()
        
        try:
            with qa_processing_time.time():
                result = await self._process_single_question(question_data)
                
            # 更新缓存
            if result.get('should_respond') and result.get('answer'):
                await self.preprocessor.update_cache_answer(
                    question_data.get('text', ''),
                    result['answer']
                )
                
            return result
            
        except Exception as e:
            logger.error(f"Enhanced QA处理失败: {e}")
            raise
        finally:
            qa_active_sessions.dec()
            
    async def _process_single_question(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个问题"""
        start_time = time.time()
        question_text = question_data.get('text', '')
        context = question_data.get('context', {})
        
        if not question_text:
            return {
                'should_respond': False,
                'reason': 'empty_question'
            }
            
        try:
            # 1. 文本预处理和验证
            if not self.preprocessor.is_question_valid(question_text):
                self.enhanced_stats['ignored_questions'] += 1
                return {
                    'should_respond': False,
                    'reason': 'invalid_question'
                }
                
            normalized_question = await self.preprocessor.normalize_question(question_text)
            
            # 2. 检查重复缓存
            cached_answer = await self.preprocessor.check_duplicate(normalized_question)
            if cached_answer:
                self.enhanced_stats['cache_hits'] += 1
                logger.info("命中重复问题缓存")
                return {
                    'should_respond': True,
                    'answer': cached_answer,
                    'source': 'cache',
                    'confidence': 1.0,
                    'processing_time': time.time() - start_time
                }
                
            # 3. 知识库检索
            search_results = await self.knowledge_base.hybrid_search(
                normalized_question, 
                top_k=5
            )
            
            # 4. 路由决策
            route_decision, route_metadata = await self.router.route_question(
                normalized_question,
                search_results,
                context
            )
            
            # 5. 根据路由决策处理
            if route_decision == RouteDecision.DIRECT_ANSWER:
                return await self._handle_direct_answer(search_results, route_metadata)
                
            elif route_decision == RouteDecision.LLM_GENERATE:
                return await self._handle_llm_generation(
                    normalized_question, search_results, context, route_metadata
                )
                
            else:  # RouteDecision.IGNORE
                self.enhanced_stats['ignored_questions'] += 1
                return {
                    'should_respond': False,
                    'reason': route_metadata.get('reason', 'low_relevance'),
                    'confidence': route_metadata.get('confidence', 0.0),
                    'metadata': route_metadata
                }
                
        except Exception as e:
            logger.error(f"处理单个问题失败: {e}")
            raise
            
    async def _handle_direct_answer(self, search_results: List[Tuple], 
                                   metadata: Dict) -> Dict[str, Any]:
        """处理直接回答"""
        if not search_results:
            return {
                'should_respond': False,
                'reason': 'no_search_results'
            }
            
        top_result = search_results[0]
        answer = top_result[3]  # 答案在第4个位置
        
        # 更新命中次数
        await self.knowledge_base.update_hit_count(top_result[0])
        self.enhanced_stats['knowledge_base_hits'] += 1
        
        return {
            'should_respond': True,
            'answer': answer,
            'source': 'knowledge_base',
            'confidence': top_result[1],
            'kb_entry_id': top_result[0],
            'metadata': metadata
        }
        
    async def _handle_llm_generation(self, question: str, search_results: List[Tuple],
                                   context: Dict, metadata: Dict) -> Dict[str, Any]:
        """处理LLM生成回答"""
        try:
            # 准备知识库上下文
            kb_context = self._prepare_kb_context(search_results[:3])
            
            # 构建增强的问题数据
            enhanced_question_data = {
                'text': question,
                'context': context,
                'kb_context': kb_context,
                'search_results': search_results
            }
            
            # 调用父类的LLM处理逻辑
            llm_result = await super().handle_qa(enhanced_question_data)
            
            # 如果成功生成答案，保存到待审核表
            if llm_result.get('answer'):
                await self._save_pending_qa(question, llm_result['answer'], context)
                
            self.enhanced_stats['llm_generations'] += 1
            
            return {
                'should_respond': True,
                'answer': llm_result.get('answer', ''),
                'source': 'llm_generated',
                'confidence': llm_result.get('confidence', 0.8),
                'response_time': llm_result.get('response_time', 0.0),
                'metadata': {**metadata, 'llm_metadata': llm_result}
            }
            
        except Exception as e:
            logger.error(f"LLM生成回答失败: {e}")
            # Fail-fast: 直接抛出异常
            raise
            
    def _prepare_kb_context(self, search_results: List[Tuple]) -> str:
        """准备知识库上下文"""
        if not search_results:
            return ""
            
        context_parts = []
        for idx, (_, score, question, answer) in enumerate(search_results):
            context_parts.append(
                f"参考{idx+1} (相似度:{score:.2f}):\n"
                f"Q: {question}\n"
                f"A: {answer}\n"
            )
            
        return "\n".join(context_parts)
        
    async def _save_pending_qa(self, question: str, answer: str, context: Dict):
        """保存LLM生成的答案到待审核表"""
        try:
            cursor = self.knowledge_base.db_connection.cursor()
            cursor.execute("""
                INSERT INTO pending_qa_entries 
                (question, generated_answer, context, confidence_score)
                VALUES (?, ?, ?, ?)
            """, (
                question,
                answer,
                json.dumps(context),
                0.8  # 默认置信度
            ))
            self.knowledge_base.db_connection.commit()
        except Exception as e:
            logger.error(f"保存待审核QA失败: {e}")
            
    async def handle_batch_questions(self, questions: List[Dict[str, Any]], 
                                   context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """批量处理问题（新增功能）"""
        if not questions:
            return []
            
        start_time = time.time()
        
        try:
            # 使用排序器对问题进行优先级排序
            ranked_questions = self.ranker.rank_questions(questions, context)
            
            # 只处理前N个高优先级问题
            max_process = min(3, len(ranked_questions))
            results = []
            
            # 并发处理高优先级问题
            tasks = []
            for i in range(max_process):
                question = ranked_questions[i]
                task = self._process_single_question(question)
                tasks.append(task)
                
            # 等待所有任务完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"批处理问题{i}失败: {result}")
                    results.append({
                        'should_respond': False,
                        'reason': 'processing_error',
                        'error': str(result)
                    })
                else:
                    results.append(result)
                    
            # 更新统计
            self.enhanced_stats['buffer_processed_batches'] += 1
            self.enhanced_stats['average_buffer_size'] = (
                (self.enhanced_stats['average_buffer_size'] * 
                 (self.enhanced_stats['buffer_processed_batches'] - 1) + 
                 len(questions)) / 
                self.enhanced_stats['buffer_processed_batches']
            )
            
            processing_time = time.time() - start_time
            logger.info(f"批处理{len(questions)}个问题完成，耗时{processing_time:.2f}秒")
            
            return results
            
        except Exception as e:
            logger.error(f"批量处理问题失败: {e}")
            raise
            
    async def add_to_buffer(self, question_data: Dict[str, Any]) -> bool:
        """添加问题到缓冲区"""
        async with self.buffer_lock:
            self.question_buffer.append({
                **question_data,
                'buffer_timestamp': time.time()
            })
            
            # 检查是否需要处理缓冲区
            should_process = (
                len(self.question_buffer) >= self.buffer_size or
                (self.question_buffer and 
                 time.time() - self.last_process_time > self.buffer_timeout)
            )
            
            if should_process:
                # 异步处理缓冲区
                asyncio.create_task(self._process_buffer())
                
            return should_process
            
    async def _process_buffer(self):
        """处理问题缓冲区"""
        async with self.buffer_lock:
            if not self.question_buffer:
                return
                
            # 获取当前缓冲区的所有问题
            questions_to_process = self.question_buffer.copy()
            self.question_buffer.clear()
            self.last_process_time = time.time()
            
        try:
            # 获取直播上下文
            context = await self._get_live_context()
            
            # 批量处理问题
            results = await self.handle_batch_questions(questions_to_process, context)
            
            # 发送响应（这里需要根据实际架构实现）
            await self._send_batch_responses(questions_to_process, results)
            
        except Exception as e:
            logger.error(f"处理缓冲区失败: {e}")
            
    async def _get_live_context(self) -> Dict:
        """获取当前直播上下文"""
        # TODO: 从状态管理器获取实际的直播上下文
        return {
            'current_topic': '产品介绍',
            'recent_products': [],
            'audience_engagement': {'active_users': 100},
            'product_keywords': ['质量', '价格', '优惠']
        }
        
    async def _send_batch_responses(self, questions: List[Dict], results: List[Dict]):
        """发送批量响应"""
        # 这里应该根据实际的架构发送响应
        # 可能是通过WebSocket、消息队列等
        for question, result in zip(questions, results):
            if result.get('should_respond'):
                logger.info(f"发送回复: {result['answer'][:50]}...")
                # TODO: 实际发送逻辑
                
    def get_enhanced_stats(self) -> Dict[str, Any]:
        """获取增强版统计信息"""
        base_stats = super().get_stats()
        
        # 合并增强统计
        enhanced_stats = {
            **base_stats,
            **self.enhanced_stats,
            'router_stats': self.router.get_stats() if self.router else {},
            'ranker_stats': self.ranker.get_stats() if self.ranker else {},
            'knowledge_base_stats': None,
            'buffer_stats': {
                'current_buffer_size': len(self.question_buffer),
                'last_process_time': self.last_process_time
            }
        }
        
        # 异步获取知识库统计
        # enhanced_stats['knowledge_base_stats'] = await self.knowledge_base.get_stats()
        
        return enhanced_stats
        
    async def get_full_stats(self) -> Dict[str, Any]:
        """获取完整统计信息（包括异步数据）"""
        enhanced_stats = self.get_enhanced_stats()
        
        # 获取知识库统计
        if self.knowledge_base:
            kb_stats = await self.knowledge_base.get_stats()
            enhanced_stats['knowledge_base_stats'] = kb_stats
            
        return enhanced_stats
        
    async def health_check(self) -> Dict[str, Any]:
        """增强版健康检查"""
        health_info = await super().health_check()
        
        # 检查新增组件
        try:
            # 检查知识库
            if self.knowledge_base and self.knowledge_base.db_connection:
                health_info["components"]["knowledge_base"] = "healthy"
            else:
                health_info["components"]["knowledge_base"] = "unhealthy"
                health_info["status"] = "degraded"
                
            # 检查路由器
            router_stats = self.router.get_stats() if self.router else {}
            if router_stats.get('llm_failure_rate', 0) < 0.5:
                health_info["components"]["router"] = "healthy"
            else:
                health_info["components"]["router"] = "degraded"
                
            # 检查预处理器
            if self.preprocessor:
                health_info["components"]["preprocessor"] = "healthy"
            else:
                health_info["components"]["preprocessor"] = "unhealthy"
                
        except Exception as e:
            health_info["status"] = "unhealthy"
            health_info["enhanced_error"] = str(e)
            
        return health_info
        
    async def cleanup(self):
        """清理资源"""
        try:
            await super().cleanup()
            
            # 清理新增组件
            if self.knowledge_base:
                # 知识库的清理在其析构函数中处理
                pass
                
            # 处理剩余的缓冲区问题
            if self.question_buffer:
                logger.info(f"处理剩余的{len(self.question_buffer)}个缓冲区问题")
                await self._process_buffer()
                
        except Exception as e:
            logger.warning(f"EnhancedQAManager cleanup error: {e}")
            
        logger.info("🧹 EnhancedQAManager cleanup completed")