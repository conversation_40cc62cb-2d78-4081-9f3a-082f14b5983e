"""Setup script for Elasticsearch indices

Creates and initializes all required indices for the AI Live Streamer system
with proper mappings, settings, and sample data.
"""

import asyncio
from typing import List, Dict, Any
from loguru import logger

from ..services.search import ElasticsearchService
from ..utils.index_manager import IndexManager
from ..core.exceptions import ServiceError


async def setup_elasticsearch_indices(
    es_service: ElasticsearchService,
    create_sample_data: bool = True
) -> Dict[str, Any]:
    """Set up all Elasticsearch indices for the system
    
    Args:
        es_service: Initialized Elasticsearch service
        create_sample_data: Whether to create sample documents
        
    Returns:
        Setup results summary
        
    Raises:
        ServiceError: If setup fails
    """
    try:
        logger.info("Starting Elasticsearch indices setup")
        
        # Initialize index manager
        index_manager = IndexManager(es_service)
        
        # Setup results
        results = {
            "knowledge_base": {"status": "pending"},
            "style_corpus": {"status": "pending"},
            "sample_data": {"status": "pending"}
        }
        
        # Create knowledge base index
        try:
            kb_index = await index_manager.create_versioned_index("knowledge_base", 1)
            await index_manager.switch_alias_to_version("knowledge_base", 1)
            results["knowledge_base"] = {
                "status": "success",
                "index_name": kb_index,
                "alias": index_manager._get_alias_name("knowledge_base")
            }
            logger.info(f"Knowledge base index created: {kb_index}")
        except Exception as e:
            results["knowledge_base"] = {"status": "failed", "error": str(e)}
            logger.error(f"Failed to create knowledge base index: {str(e)}")
        
        # Create style corpus index
        try:
            style_index = await index_manager.create_versioned_index("style_corpus", 1)
            await index_manager.switch_alias_to_version("style_corpus", 1)
            results["style_corpus"] = {
                "status": "success", 
                "index_name": style_index,
                "alias": index_manager._get_alias_name("style_corpus")
            }
            logger.info(f"Style corpus index created: {style_index}")
        except Exception as e:
            results["style_corpus"] = {"status": "failed", "error": str(e)}
            logger.error(f"Failed to create style corpus index: {str(e)}")
        
        # Create sample data if requested
        if create_sample_data:
            try:
                sample_count = await _create_sample_data(es_service, index_manager)
                results["sample_data"] = {
                    "status": "success",
                    "documents_created": sample_count
                }
                logger.info(f"Created {sample_count} sample documents")
            except Exception as e:
                results["sample_data"] = {"status": "failed", "error": str(e)}
                logger.error(f"Failed to create sample data: {str(e)}")
        else:
            results["sample_data"] = {"status": "skipped"}
        
        # Check overall success
        failed_components = [k for k, v in results.items() if v["status"] == "failed"]
        if failed_components:
            logger.warning(f"Setup completed with failures in: {failed_components}")
        else:
            logger.info("Elasticsearch indices setup completed successfully")
        
        return results
        
    except Exception as e:
        logger.error(f"Elasticsearch setup failed: {str(e)}")
        raise ServiceError(
            f"Failed to setup Elasticsearch indices: {str(e)}",
            "elasticsearch_setup",
            "SETUP_ERROR"
        )


async def _create_sample_data(
    es_service: ElasticsearchService,
    index_manager: IndexManager
) -> int:
    """Create sample documents for testing
    
    Args:
        es_service: Elasticsearch service
        index_manager: Index manager instance
        
    Returns:
        Number of documents created
    """
    # Sample knowledge base documents
    kb_alias = index_manager._get_alias_name("knowledge_base")
    kb_documents = [
        {
            "_id": "kb_001",
            "doc_id": "kb_001",
            "type": "spec",
            "sku": "SKU001",
            "brand": "TestBrand",
            "title": "产品规格说明",
            "chunk_text": "这是一个高质量的产品，具有优异的性能和可靠性。主要规格包括...",
            "embedding": es_service.generate_embedding("这是一个高质量的产品，具有优异的性能和可靠性"),
            "metadata": {"category": "electronics", "price_range": "premium"},
            "created_at": "2025-01-01T00:00:00Z",
            "updated_at": "2025-01-01T00:00:00Z",
            "version": 1
        },
        {
            "_id": "kb_002", 
            "doc_id": "kb_002",
            "type": "faq",
            "sku": "SKU001",
            "brand": "TestBrand",
            "title": "常见问题解答",
            "chunk_text": "Q: 这个产品的质保期是多长？A: 我们提供2年质保服务，涵盖所有制造缺陷...",
            "embedding": es_service.generate_embedding("产品质保期2年质保服务制造缺陷"),
            "metadata": {"category": "support", "priority": "high"},
            "created_at": "2025-01-01T00:00:00Z",
            "updated_at": "2025-01-01T00:00:00Z",
            "version": 1
        },
        {
            "_id": "kb_003",
            "doc_id": "kb_003", 
            "type": "story",
            "sku": "SKU002",
            "brand": "TestBrand",
            "title": "用户成功案例",
            "chunk_text": "李先生使用我们的产品后，工作效率提升了30%，他说这是他买过最值得的投资...",
            "embedding": es_service.generate_embedding("用户成功案例工作效率提升30%值得投资"),
            "metadata": {"category": "testimonial", "rating": 5},
            "created_at": "2025-01-01T00:00:00Z",
            "updated_at": "2025-01-01T00:00:00Z",
            "version": 1
        }
    ]
    
    # Sample style corpus documents
    style_alias = index_manager._get_alias_name("style_corpus")
    style_documents = [
        {
            "_id": "style_001",
            "section_id": "style_001",
            "section_type": "opening",
            "content": "大家好！欢迎来到我们的直播间，今天给大家带来一些非常棒的产品推荐...",
            "embedding": es_service.generate_embedding("大家好欢迎直播间产品推荐"),
            "tone_tags": ["友好", "热情", "专业"],
            "style_features": {"sentence_length": "medium", "formality": "casual"},
            "suitable_contexts": ["product_launch", "daily_stream"],
            "time_contexts": ["morning", "afternoon"],
            "usage_count": 5,
            "effectiveness_score": 0.85,
            "source_document": "sample_scripts.md",
            "extracted_at": "2025-01-01T00:00:00Z"
        },
        {
            "_id": "style_002",
            "section_id": "style_002", 
            "section_type": "selling_point",
            "content": "这款产品最大的亮点就是它的创新设计和卓越性能，让您的使用体验完全不同...",
            "embedding": es_service.generate_embedding("产品亮点创新设计卓越性能使用体验"),
            "tone_tags": ["说服力", "专业", "自信"],
            "style_features": {"sentence_length": "long", "formality": "professional"},
            "suitable_contexts": ["product_demo", "feature_highlight"],
            "time_contexts": ["afternoon", "evening"],
            "usage_count": 12,
            "effectiveness_score": 0.92,
            "source_document": "selling_scripts.md",
            "extracted_at": "2025-01-01T00:00:00Z"
        },
        {
            "_id": "style_003",
            "section_id": "style_003",
            "section_type": "cta", 
            "content": "现在下单还有限时优惠，数量有限，先到先得！点击下方链接立即购买吧！",
            "embedding": es_service.generate_embedding("限时优惠数量有限先到先得立即购买"),
            "tone_tags": ["紧迫感", "激励", "行动导向"],
            "style_features": {"sentence_length": "short", "formality": "urgent"},
            "suitable_contexts": ["promotion", "flash_sale"],
            "time_contexts": ["afternoon", "evening"],
            "usage_count": 8,
            "effectiveness_score": 0.78,
            "source_document": "cta_scripts.md",
            "extracted_at": "2025-01-01T00:00:00Z"
        }
    ]
    
    # Bulk index documents
    kb_count = await es_service.bulk_index_documents(kb_alias, kb_documents)
    style_count = await es_service.bulk_index_documents(style_alias, style_documents)
    
    total_count = kb_count + style_count
    logger.info(f"Created {kb_count} knowledge base docs and {style_count} style corpus docs")
    
    return total_count


async def cleanup_elasticsearch_indices(es_service: ElasticsearchService) -> Dict[str, Any]:
    """Clean up all indices (for testing/development)
    
    Args:
        es_service: Elasticsearch service
        
    Returns:
        Cleanup results
    """
    try:
        logger.info("Starting Elasticsearch indices cleanup")
        
        # Get all indices matching our patterns
        patterns = ["ai_live_streamer_*"]
        results = {"deleted_indices": [], "errors": []}
        
        for pattern in patterns:
            try:
                response = await es_service.client.cat.indices(
                    index=pattern,
                    format="json",
                    h="index"
                )
                
                for index_info in response:
                    index_name = index_info["index"]
                    try:
                        await es_service.client.indices.delete(index=index_name)
                        results["deleted_indices"].append(index_name)
                        logger.info(f"Deleted index: {index_name}")
                    except Exception as e:
                        error_msg = f"Failed to delete {index_name}: {str(e)}"
                        results["errors"].append(error_msg)
                        logger.error(error_msg)
                        
            except Exception as e:
                if "index_not_found" not in str(e):
                    error_msg = f"Failed to list indices for pattern {pattern}: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)
        
        logger.info(f"Cleanup completed. Deleted {len(results['deleted_indices'])} indices")
        return results
        
    except Exception as e:
        logger.error(f"Cleanup failed: {str(e)}")
        raise ServiceError(
            f"Failed to cleanup Elasticsearch indices: {str(e)}",
            "elasticsearch_cleanup",
            "CLEANUP_ERROR"
        )


# CLI entry point for setup
async def main() -> None:
    """Main entry point for index setup script"""
    import sys
    
    # Initialize Elasticsearch service
    es_service = ElasticsearchService()
    
    try:
        await es_service.initialize()
        
        # Check command line arguments
        if len(sys.argv) > 1 and sys.argv[1] == "cleanup":
            results = await cleanup_elasticsearch_indices(es_service)
            print(f"Cleanup results: {results}")
        else:
            results = await setup_elasticsearch_indices(es_service, create_sample_data=True)
            print(f"Setup results: {results}")
            
    except Exception as e:
        logger.error(f"Script failed: {str(e)}")
        sys.exit(1)
        
    finally:
        await es_service.close()


if __name__ == "__main__":
    asyncio.run(main())