"""Script processing pipeline for Style Corpus creation

Handles parsing of script documents, classification into style categories,
and creation of the Style Corpus for few-shot prompt examples.
"""

import re
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
import hashlib
from loguru import logger

from .base import ETLBase, ETLTask, ETLTaskResult, PipelineStatus, create_etl_flow
from ...services.search import ElasticsearchService
from ...utils.index_manager import IndexManager
from ...models.content import ScriptSectionType
from ...core.exceptions import ServiceError, ValidationError
from ...core.config import cfg


class StyleClassifier:
    """Classifies script content into style categories"""
    
    def __init__(self) -> None:
        """Initialize style classifier with pattern matching rules"""
        # Keywords and patterns for each script section type
        self.classification_patterns = {
            ScriptSectionType.OPENING: {
                "keywords": ["欢迎", "大家好", "开始", "直播间", "hello", "welcome", "开场"],
                "patterns": [
                    r"大家好[！!]?",
                    r"欢迎.*直播间",
                    r"今天.*给大家",
                    r"开始.*介绍"
                ],
                "position_hints": ["start", "beginning"]
            },
            ScriptSectionType.SELLING_POINT: {
                "keywords": ["特点", "优势", "亮点", "功能", "性能", "优质", "卓越", "独特"],
                "patterns": [
                    r"最大的.*是",
                    r".*的特点",
                    r".*优势.*",
                    r"这款.*具有",
                    r"独特.*设计"
                ],
                "position_hints": ["middle"]
            },
            ScriptSectionType.OBJECTION_HANDLING: {
                "keywords": ["但是", "不过", "可能", "担心", "疑虑", "问题", "解答", "其实"],
                "patterns": [
                    r"可能.*担心",
                    r"有人.*说",
                    r"其实.*是",
                    r"不用担心",
                    r"这个问题"
                ],
                "position_hints": ["middle", "late"]
            },
            ScriptSectionType.CLOSING: {
                "keywords": ["总结", "最后", "结束", "谢谢", "感谢", "再见", "下次"],
                "patterns": [
                    r"最后.*说",
                    r"总结.*一下",
                    r"谢谢.*观看",
                    r"感谢.*支持",
                    r"今天.*就到这里"
                ],
                "position_hints": ["end"]
            },
            ScriptSectionType.CTA: {
                "keywords": ["购买", "下单", "点击", "链接", "立即", "现在", "马上", "优惠", "限时"],
                "patterns": [
                    r"点击.*链接",
                    r"立即.*购买",
                    r"现在.*下单",
                    r"限时.*优惠",
                    r"马上.*行动"
                ],
                "position_hints": ["middle", "late", "end"]
            },
            ScriptSectionType.INTERACTION: {
                "keywords": ["评论", "留言", "互动", "问题", "回答", "大家", "观众"],
                "patterns": [
                    r"有.*问题",
                    r"评论区.*",
                    r"大家.*觉得",
                    r"可以.*留言",
                    r"我们.*互动"
                ],
                "position_hints": ["any"]
            }
        }
    
    async def classify_script_section(
        self, 
        text: str, 
        position_hint: Optional[str] = None
    ) -> Tuple[ScriptSectionType, float]:
        """Classify script section into type
        
        Args:
            text: Script text to classify
            position_hint: Position in document (start/middle/end)
            
        Returns:
            Tuple of (section_type, confidence_score)
        """
        text_lower = text.lower()
        scores = {}
        
        for section_type, patterns in self.classification_patterns.items():
            score = 0.0
            
            # Keyword matching
            keyword_matches = sum(1 for keyword in patterns["keywords"] if keyword in text_lower)
            score += keyword_matches * 0.3
            
            # Pattern matching
            pattern_matches = 0
            for pattern in patterns["patterns"]:
                if re.search(pattern, text_lower):
                    pattern_matches += 1
            score += pattern_matches * 0.5
            
            # Position bonus
            if position_hint and position_hint in patterns["position_hints"]:
                score += 0.2
            elif "any" in patterns["position_hints"]:
                score += 0.1
            
            scores[section_type] = score
        
        # Find best match
        if not scores or max(scores.values()) == 0:
            return ScriptSectionType.SELLING_POINT, 0.0  # Default fallback
        
        best_type = max(scores, key=scores.get)
        confidence = min(scores[best_type], 1.0)
        
        return best_type, confidence
    
    async def extract_style_features(self, text: str) -> Dict[str, Any]:
        """Extract style features from text
        
        Args:
            text: Script text
            
        Returns:
            Dictionary of style features
        """
        features = {}
        
        # Basic text metrics
        sentences = re.split(r'[.!?。！？]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        features["sentence_count"] = len(sentences)
        features["average_sentence_length"] = sum(len(s) for s in sentences) / len(sentences) if sentences else 0
        features["total_length"] = len(text)
        features["word_count"] = len(text.split())
        
        # Sentiment and tone indicators
        exclamation_count = text.count('!') + text.count('！')
        question_count = text.count('?') + text.count('？')
        
        features["exclamation_ratio"] = exclamation_count / len(sentences) if sentences else 0
        features["question_ratio"] = question_count / len(sentences) if sentences else 0
        
        # Formality indicators
        formal_words = ["您", "请", "谢谢", "感谢", "敬请", "尊敬"]
        casual_words = ["哈哈", "嘿", "哇", "超", "特别", "非常棒"]
        
        formal_count = sum(1 for word in formal_words if word in text)
        casual_count = sum(1 for word in casual_words if word in text)
        
        features["formality_score"] = (formal_count - casual_count) / len(text.split()) if text.split() else 0
        
        # Urgency indicators
        urgent_words = ["立即", "马上", "现在", "快", "限时", "紧急"]
        urgent_count = sum(1 for word in urgent_words if word in text)
        features["urgency_score"] = urgent_count / len(text.split()) if text.split() else 0
        
        return features


class ScriptParser:
    """Parses script documents and extracts sections"""
    
    def __init__(self) -> None:
        """Initialize script parser"""
        self.style_classifier = StyleClassifier()
    
    async def parse_script_document(self, text: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse script document into sections
        
        Args:
            text: Script text content
            metadata: Document metadata
            
        Returns:
            List of script sections with classifications
        """
        sections = []
        
        # Split text into logical sections
        # Try multiple splitting strategies
        
        # Strategy 1: Split by double newlines (paragraphs)
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        if len(paragraphs) < 2:
            # Strategy 2: Split by single newlines
            paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
        
        if len(paragraphs) < 2:
            # Strategy 3: Split by sentences
            paragraphs = re.split(r'[.!?。！？]\s*', text)
            paragraphs = [p.strip() for p in paragraphs if len(p.strip()) > 20]
        
        # Process each section
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph.strip()) < 10:  # Skip very short sections
                continue
            
            # Determine position hint
            position_hint = None
            if i == 0:
                position_hint = "start"
            elif i == len(paragraphs) - 1:
                position_hint = "end"
            else:
                position_hint = "middle"
            
            # Classify section
            section_type, confidence = await self.style_classifier.classify_script_section(
                paragraph, position_hint
            )
            
            # Extract style features
            style_features = await self.style_classifier.extract_style_features(paragraph)
            
            # Create section data
            section_id = f"{metadata.get('content_hash', 'unknown')}_{i}"
            section_data = {
                "section_id": section_id,
                "section_type": section_type.value,
                "content": paragraph,
                "section_index": i,
                "classification_confidence": confidence,
                "position_hint": position_hint,
                "style_features": style_features,
                "source_metadata": metadata,
                "extracted_at": datetime.utcnow()
            }
            
            sections.append(section_data)
        
        logger.info(f"Parsed {len(sections)} sections from script document")
        return sections
    
    async def infer_tone_tags(self, text: str, style_features: Dict[str, Any]) -> List[str]:
        """Infer tone tags from text and features
        
        Args:
            text: Script text
            style_features: Extracted style features
            
        Returns:
            List of tone tags
        """
        tags = []
        
        # Formality
        if style_features.get("formality_score", 0) > 0.1:
            tags.append("正式")
        elif style_features.get("formality_score", 0) < -0.1:
            tags.append("随意")
        else:
            tags.append("中性")
        
        # Energy level
        if style_features.get("exclamation_ratio", 0) > 0.3:
            tags.append("热情")
        if style_features.get("urgency_score", 0) > 0.1:
            tags.append("紧迫")
        
        # Interaction style
        if style_features.get("question_ratio", 0) > 0.2:
            tags.append("互动")
        
        # Length-based tags
        if style_features.get("average_sentence_length", 0) > 30:
            tags.append("详细")
        elif style_features.get("average_sentence_length", 0) < 15:
            tags.append("简洁")
        
        # Content-based tags
        text_lower = text.lower()
        if any(word in text_lower for word in ["专业", "技术", "规格", "参数"]):
            tags.append("专业")
        if any(word in text_lower for word in ["友好", "亲切", "温暖", "关怀"]):
            tags.append("友好")
        if any(word in text_lower for word in ["说服", "推荐", "建议", "值得"]):
            tags.append("说服")
        
        return tags[:5]  # Limit to 5 tags


class ScriptProcessor(ETLBase):
    """Script processing ETL pipeline for Style Corpus creation"""
    
    def __init__(self) -> None:
        """Initialize script processor"""
        super().__init__("script_processor")
        self.script_parser = ScriptParser()
        self.es_service: Optional[ElasticsearchService] = None
        self.index_manager: Optional[IndexManager] = None
    
    async def initialize_services(self) -> None:
        """Initialize Elasticsearch services"""
        if not self.es_service:
            self.es_service = ElasticsearchService()
            await self.es_service.initialize()
            
        if not self.index_manager:
            self.index_manager = IndexManager(self.es_service)
    
    async def extract(self, source_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract script documents from source directory
        
        Args:
            source_config: Configuration with script directory and patterns
            
        Returns:
            List of extracted script documents
        """
        script_directory = Path(source_config.get("script_directory", "data/scripts"))
        file_patterns = source_config.get("file_patterns", ["*.md", "*.txt"])
        
        if not script_directory.exists():
            raise ServiceError(
                f"Script directory does not exist: {script_directory}",
                "script_processor",
                "DIRECTORY_NOT_FOUND"
            )
        
        extracted_scripts = []
        
        # Scan for script files
        for pattern in file_patterns:
            for file_path in script_directory.glob(pattern):
                if file_path.is_file():
                    try:
                        script_data = await self._extract_single_script(file_path)
                        if script_data:
                            extracted_scripts.append(script_data)
                    except Exception as e:
                        self.logger.error(f"Failed to extract script {file_path}: {str(e)}")
                        continue
        
        self.logger.info(f"Extracted {len(extracted_scripts)} script documents")
        return extracted_scripts
    
    async def _extract_single_script(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Extract single script file
        
        Args:
            file_path: Path to script file
            
        Returns:
            Script data or None if extraction fails
        """
        try:
            # Read script content
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            if not content or len(content.strip()) < 50:
                self.logger.warning(f"Insufficient content in script {file_path}")
                return None
            
            # Generate metadata
            file_stats = file_path.stat()
            metadata = {
                "file_path": str(file_path),
                "file_name": file_path.name,
                "file_size": file_stats.st_size,
                "file_modified": datetime.fromtimestamp(file_stats.st_mtime),
                "content_hash": hashlib.md5(content.encode()).hexdigest(),
                "original_length": len(content)
            }
            
            return {
                "content": content,
                "metadata": metadata,
                "extracted_at": datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to extract script {file_path}: {str(e)}")
            return None
    
    async def transform(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform scripts into classified sections with embeddings
        
        Args:
            data: List of extracted script documents
            
        Returns:
            List of processed script sections for Style Corpus
        """
        await self.initialize_services()
        
        processed_sections = []
        
        for script_data in data:
            try:
                # Parse script into sections
                sections = await self.script_parser.parse_script_document(
                    script_data["content"],
                    script_data["metadata"]
                )
                
                # Process each section
                for section in sections:
                    try:
                        # Generate embedding
                        embedding = self.es_service.generate_embedding(section["content"])
                        
                        # Infer tone tags
                        tone_tags = await self.script_parser.infer_tone_tags(
                            section["content"],
                            section["style_features"]
                        )
                        
                        # Determine suitable contexts
                        suitable_contexts = self._determine_suitable_contexts(
                            section["section_type"],
                            section["style_features"]
                        )
                        
                        # Determine time contexts
                        time_contexts = self._determine_time_contexts(
                            section["content"],
                            section["style_features"]
                        )
                        
                        # Create final section document
                        section_doc = {
                            "_id": section["section_id"],
                            "section_id": section["section_id"],
                            "section_type": section["section_type"],
                            "content": section["content"],
                            "embedding": embedding,
                            "tone_tags": tone_tags,
                            "style_features": section["style_features"],
                            "suitable_contexts": suitable_contexts,
                            "time_contexts": time_contexts,
                            "usage_count": 0,
                            "effectiveness_score": None,
                            "source_document": section["source_metadata"]["file_name"],
                            "extracted_at": section["extracted_at"].isoformat(),
                            "metadata": {
                                "section_index": section["section_index"],
                                "classification_confidence": section["classification_confidence"],
                                "position_hint": section["position_hint"]
                            }
                        }
                        
                        processed_sections.append(section_doc)
                        
                    except Exception as e:
                        self.logger.error(f"Failed to process section {section['section_id']}: {str(e)}")
                        continue
                
            except Exception as e:
                self.logger.error(f"Failed to transform script: {str(e)}")
                continue
        
        self.logger.info(f"Transformed {len(data)} scripts into {len(processed_sections)} sections")
        return processed_sections
    
    def _determine_suitable_contexts(self, section_type: str, style_features: Dict[str, Any]) -> List[str]:
        """Determine suitable contexts for script section
        
        Args:
            section_type: Type of script section
            style_features: Style features
            
        Returns:
            List of suitable context names
        """
        contexts = []
        
        # Base contexts by section type
        section_contexts = {
            "opening": ["product_launch", "daily_stream", "special_event"],
            "selling_point": ["product_demo", "feature_highlight", "comparison"],
            "objection_handling": ["q_and_a", "customer_support", "product_demo"],
            "closing": ["daily_stream", "product_launch", "summary"],
            "cta": ["promotion", "flash_sale", "product_launch"],
            "interaction": ["q_and_a", "community_building", "live_chat"]
        }
        
        contexts.extend(section_contexts.get(section_type, ["general"]))
        
        # Add contexts based on style features
        if style_features.get("urgency_score", 0) > 0.1:
            contexts.append("flash_sale")
        
        if style_features.get("formality_score", 0) > 0.1:
            contexts.append("professional_presentation")
        elif style_features.get("formality_score", 0) < -0.1:
            contexts.append("casual_chat")
        
        return list(set(contexts))  # Remove duplicates
    
    def _determine_time_contexts(self, content: str, style_features: Dict[str, Any]) -> List[str]:
        """Determine suitable time contexts
        
        Args:
            content: Section content
            style_features: Style features
            
        Returns:
            List of suitable time context names
        """
        time_contexts = []
        content_lower = content.lower()
        
        # Check for time-specific keywords
        if any(word in content_lower for word in ["早上好", "上午", "早安"]):
            time_contexts.append("morning")
        if any(word in content_lower for word in ["下午好", "午后"]):
            time_contexts.append("afternoon")
        if any(word in content_lower for word in ["晚上好", "晚安", "夜晚"]):
            time_contexts.append("evening")
        
        # Default contexts based on energy level
        energy_level = style_features.get("exclamation_ratio", 0)
        if energy_level > 0.3:
            time_contexts.extend(["morning", "afternoon"])
        elif energy_level < 0.1:
            time_contexts.extend(["evening", "night"])
        
        # If no specific time context, suitable for any time
        if not time_contexts:
            time_contexts = ["morning", "afternoon", "evening"]
        
        return time_contexts
    
    async def load(self, data: List[Dict[str, Any]], target_config: Dict[str, Any]) -> int:
        """Load processed sections into Style Corpus index
        
        Args:
            data: List of processed script sections
            target_config: Target configuration
            
        Returns:
            Number of successfully loaded sections
        """
        await self.initialize_services()
        
        index_version = target_config.get("index_version", None)
        create_new_version = target_config.get("create_new_version", True)
        
        # Create new index version if requested
        if create_new_version:
            if index_version is None:
                current_version = await self.index_manager.get_current_version("style_corpus")
                index_version = (current_version or 0) + 1
            
            index_name = await self.index_manager.create_versioned_index("style_corpus", index_version)
            self.logger.info(f"Created new style corpus index: {index_name}")
        else:
            # Use existing active index
            index_name = self.index_manager._get_alias_name("style_corpus")
        
        # Bulk load sections
        if data:
            loaded_count = await self.es_service.bulk_index_documents(index_name, data)
            
            # Switch alias to new index if we created a new version
            if create_new_version and loaded_count > 0:
                await self.index_manager.switch_alias_to_version("style_corpus", index_version)
                self.logger.info(f"Switched style corpus alias to version {index_version}")
            
            return loaded_count
        
        return 0
    
    async def _validate_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Validate individual script section item
        
        Args:
            item: Section data to validate
            
        Returns:
            Validated item
            
        Raises:
            ValidationError: If validation fails
        """
        required_fields = ["section_id", "section_type", "content", "embedding"]
        
        for field in required_fields:
            if field not in item:
                raise ValidationError(f"Missing required field: {field}")
        
        # Validate section type
        valid_types = [t.value for t in ScriptSectionType]
        if item["section_type"] not in valid_types:
            raise ValidationError(f"Invalid section type: {item['section_type']}")
        
        # Validate content length
        if len(item["content"].strip()) < 10:
            raise ValidationError("Section content too short")
        
        # Validate embedding
        if not isinstance(item["embedding"], list) or len(item["embedding"]) == 0:
            raise ValidationError("Invalid embedding format")
        
        return item


# Prefect ETL tasks and flows

@ETLTask("extract_script_documents", retries=2, timeout_seconds=900)
async def extract_script_documents(source_config: Dict[str, Any]) -> ETLTaskResult:
    """Extract script documents from source directory"""
    processor = ScriptProcessor()
    started_at = datetime.utcnow()
    result = processor.create_result("extract_script_documents", started_at)
    
    try:
        scripts = await processor.extract(source_config)
        
        result.processed_count = len(scripts)
        result.input_data_size = sum(len(script.get("content", "")) for script in scripts)
        result.metadata["source_directory"] = source_config.get("script_directory")
        result.metadata["scripts"] = scripts
        
        return result
        
    except Exception as e:
        result.status = PipelineStatus.FAILED
        result.error_count = 1
        result.errors.append({
            "error": str(e),
            "component": "script_extraction"
        })
        raise


@ETLTask("transform_script_sections", retries=1, timeout_seconds=2400)
async def transform_script_sections(extract_result: ETLTaskResult) -> ETLTaskResult:
    """Transform scripts into classified sections"""
    processor = ScriptProcessor()
    started_at = datetime.utcnow()
    result = processor.create_result("transform_script_sections", started_at)
    
    try:
        scripts = extract_result.metadata.get("scripts", [])
        sections = await processor.transform(scripts)
        
        result.processed_count = len(sections)
        result.input_data_size = extract_result.output_data_size or extract_result.input_data_size
        result.output_data_size = sum(len(section.get("content", "")) for section in sections)
        result.metadata["sections"] = sections
        
        return result
        
    except Exception as e:
        result.status = PipelineStatus.FAILED
        result.error_count = 1
        result.errors.append({
            "error": str(e),
            "component": "script_transformation"
        })
        raise


@ETLTask("load_style_corpus", retries=2, timeout_seconds=1200)
async def load_style_corpus(transform_result: ETLTaskResult, target_config: Dict[str, Any]) -> ETLTaskResult:
    """Load processed sections into Style Corpus index"""
    processor = ScriptProcessor()
    started_at = datetime.utcnow()
    result = processor.create_result("load_style_corpus", started_at)
    
    try:
        sections = transform_result.metadata.get("sections", [])
        loaded_count = await processor.load(sections, target_config)
        
        result.processed_count = loaded_count
        result.input_data_size = transform_result.output_data_size
        result.metadata["loaded_sections"] = loaded_count
        result.metadata["target_index"] = target_config.get("index_version")
        
        return result
        
    except Exception as e:
        result.status = PipelineStatus.FAILED
        result.error_count = 1
        result.errors.append({
            "error": str(e),
            "component": "corpus_loading"
        })
        raise


@create_etl_flow(
    name="style_corpus_etl_pipeline",
    description="Complete ETL pipeline for Style Corpus creation from scripts",
    timeout_minutes=90
)
async def style_corpus_etl_pipeline(
    source_config: Dict[str, Any],
    target_config: Dict[str, Any]
) -> Dict[str, ETLTaskResult]:
    """Complete Style Corpus ETL pipeline
    
    Args:
        source_config: Source configuration for script extraction
        target_config: Target configuration for Style Corpus loading
        
    Returns:
        Dictionary of task results
    """
    # Execute ETL pipeline
    extract_result = await extract_script_documents(source_config)
    transform_result = await transform_script_sections(extract_result)
    load_result = await load_style_corpus(transform_result, target_config)
    
    return {
        "extract": extract_result,
        "transform": transform_result,
        "load": load_result
    }