/**
 * Global Design System CSS
 * 全局设计系统样式表
 * 
 * 为AI直播系统提供统一、现代、专业的视觉设计语言
 * Author: Claude Code
 * Version: 1.0.0
 */

/* ============================
   1. CSS Variables - 设计令牌
   ============================ */
:root {
    /* Primary Colors - 主色调 */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-gradient-hover: linear-gradient(135deg, #5a67d8 0%, #6b4299 100%);
    --primary-color: #667eea;
    --primary-dark: #764ba2;
    --primary-light: #818cf8;
    --primary-bg: #f8f9ff;
    
    /* Semantic Colors - 语义色 */
    --success-color: #48bb78;
    --success-bg: #d4f4dd;
    --warning-color: #f6ad55;
    --warning-bg: #fff5e6;
    --error-color: #fc8181;
    --error-bg: #fee;
    --info-color: #63b3ed;
    --info-bg: #e6f4ff;
    
    /* Neutral Colors - 中性色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Background Colors - 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9ff;
    --bg-tertiary: #f5f7fa;
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* Text Colors - 文字色 */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    --text-inverse: #ffffff;
    
    /* Border Colors - 边框色 */
    --border-color: #e8ecf1;
    --border-light: #f0f2f5;
    --border-dark: #d4d9e0;
    --border-focus: rgba(102, 126, 234, 0.3);
    --border-error: rgba(248, 129, 129, 0.3);
    --border-success: rgba(72, 187, 120, 0.3);
    
    /* Shadows - 阴影系统 */
    --shadow-xs: 0 0 0 1px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* 特殊阴影 */
    --shadow-card: 0 2px 10px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-card-hover: 0 8px 30px rgba(102, 126, 234, 0.15), 0 4px 12px rgba(102, 126, 234, 0.1);
    --shadow-focus: 0 0 0 3px rgba(102, 126, 234, 0.1);
    --shadow-error: 0 0 0 3px rgba(248, 129, 129, 0.1);
    --shadow-success: 0 0 0 3px rgba(72, 187, 120, 0.1);
    
    /* Typography - 字体系统升级 */
    --font-sans: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Consolas', monospace;
    --font-heading: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
    
    /* 字重系统 */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* 行高系统 - 基于黄金比例1.618 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.618;
    --line-height-loose: 1.8;
    
    /* 字号系统 - 基于模块化尺度 */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */
    
    /* 字母间距系统 */
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    
    /* Spacing - 8点网格间距系统 */
    --spacing-0: 0;
    --spacing-1: 0.25rem;   /* 4px */
    --spacing-2: 0.5rem;    /* 8px */
    --spacing-3: 0.75rem;   /* 12px */
    --spacing-4: 1rem;      /* 16px */
    --spacing-5: 1.25rem;   /* 20px */
    --spacing-6: 1.5rem;    /* 24px */
    --spacing-8: 2rem;      /* 32px */
    --spacing-10: 2.5rem;   /* 40px */
    --spacing-12: 3rem;     /* 48px */
    --spacing-16: 4rem;     /* 64px */
    --spacing-20: 5rem;     /* 80px */
    
    /* 兼容性别名 */
    --spacing-xs: var(--spacing-1);
    --spacing-sm: var(--spacing-2);
    --spacing-md: var(--spacing-4);
    --spacing-lg: var(--spacing-6);
    --spacing-xl: var(--spacing-8);
    --spacing-2xl: var(--spacing-12);
    
    /* 黄金比例容器宽度 */
    --container-narrow: 38.197rem;   /* 611px - φ based */
    --container-medium: 61.803rem;   /* 989px - φ based */
    --container-wide: 80rem;         /* 1280px - max width */
    
    /* Border Radius - 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions - 过渡系统 */
    --transition-fast: 150ms ease;
    --transition-base: 250ms ease;
    --transition-slow: 350ms ease;
    --transition-slower: 500ms ease;
    
    /* 高级缓动曲线 */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
    --ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
    --ease-out-back: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* Z-index - 层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ============================
   2. Reset & Base Styles
   ============================ */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
    letter-spacing: var(--letter-spacing-normal);
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
    text-rendering: optimizeLegibility;
    -webkit-font-feature-settings: 'kern' 1;
    font-feature-settings: 'kern' 1;
    -webkit-text-size-adjust: 100%;
}

/* ============================
   3. Typography - 排版
   ============================ */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--text-primary);
    margin-top: 0;
}

/* 基于黄金比例的标题层级 */
h1 { 
    font-size: var(--text-5xl); 
    font-weight: var(--font-weight-bold);
    line-height: 1.1;
    letter-spacing: -0.02em;
    margin-bottom: var(--spacing-8); 
}

h2 { 
    font-size: var(--text-4xl); 
    font-weight: var(--font-weight-bold);
    line-height: 1.15;
    letter-spacing: -0.015em;
    margin-bottom: var(--spacing-6); 
}

h3 { 
    font-size: var(--text-3xl); 
    font-weight: var(--font-weight-semibold);
    line-height: 1.2;
    letter-spacing: -0.01em;
    margin-bottom: var(--spacing-5); 
}

h4 { 
    font-size: var(--text-2xl); 
    font-weight: var(--font-weight-semibold);
    line-height: 1.25;
    margin-bottom: var(--spacing-4); 
}

h5 { 
    font-size: var(--text-xl); 
    font-weight: var(--font-weight-medium);
    line-height: 1.3;
    margin-bottom: var(--spacing-3); 
}

h6 { 
    font-size: var(--text-lg); 
    font-weight: var(--font-weight-medium);
    line-height: 1.35;
    margin-bottom: var(--spacing-3); 
}

p {
    font-size: var(--text-base);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-4);
    color: var(--text-secondary);
    max-width: 65ch; /* 优化可读性的行长度 */
}

/* 大段落文本 */
.text-large {
    font-size: var(--text-lg);
    line-height: var(--line-height-relaxed);
}

/* 小段落文本 */
.text-small {
    font-size: var(--text-sm);
    line-height: var(--line-height-normal);
}

/* 引用文本 */
blockquote {
    font-size: var(--text-lg);
    font-style: italic;
    line-height: var(--line-height-relaxed);
    margin: var(--spacing-6) 0;
    padding-left: var(--spacing-6);
    border-left: 4px solid var(--primary-color);
    color: var(--text-secondary);
}

/* ============================
   4. Container & Layout
   ============================ */
/* 容器系统 - 基于黄金比例 */
.container {
    width: 100%;
    max-width: var(--container-wide);
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.container-narrow {
    width: 100%;
    max-width: var(--container-narrow);
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.container-medium {
    width: 100%;
    max-width: var(--container-medium);
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-6);
}

/* 网格系统 - 8点网格和黄金比例 */
.grid {
    display: grid;
    gap: var(--spacing-6);
}

/* 基础网格列 */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

/* 黄金比例布局 */
.grid-golden {
    grid-template-columns: 1fr 1.618fr; /* 1:φ */
}

.grid-golden-reverse {
    grid-template-columns: 1.618fr 1fr; /* φ:1 */
}

/* 网格间距变体 */
.gap-0 { gap: var(--spacing-0); }
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-5 { gap: var(--spacing-5); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* 列间距和行间距 */
.gap-x-2 { column-gap: var(--spacing-2); }
.gap-x-4 { column-gap: var(--spacing-4); }
.gap-x-6 { column-gap: var(--spacing-6); }
.gap-y-2 { row-gap: var(--spacing-2); }
.gap-y-4 { row-gap: var(--spacing-4); }
.gap-y-6 { row-gap: var(--spacing-6); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
/* 兼容性别名 */
.gap-sm { gap: var(--spacing-2); }
.gap-md { gap: var(--spacing-4); }
.gap-lg { gap: var(--spacing-6); }

/* ============================
   5. Components - 组件
   ============================ */

/* 卡片组件 - 优化间距和比例 */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

/* 卡片尺寸变体 */
.card-compact {
    padding: var(--spacing-4);
}

.card-comfortable {
    padding: var(--spacing-8);
}

.card-spacious {
    padding: var(--spacing-10);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-base);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-card-hover);
    border-color: var(--border-dark);
    transition: all 300ms var(--ease-out-cubic);
}

.card:hover::before {
    transform: scaleX(1);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-5);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--border-light);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    color: var(--text-secondary);
}

.card-footer {
    margin-top: var(--spacing-5);
    padding-top: var(--spacing-4);
    border-top: 1px solid var(--border-light);
}

/* 信息卡片组件 - 基于产品卡片的优秀设计 */
.info-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-color);
    transition: all var(--transition-base);
    position: relative;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
}

/* 信息卡片内容结构 */
.info-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.info-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1.4;
}

.info-card-subtitle {
    font-size: 0.75rem;
    background: var(--bg-secondary);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-family: monospace;
}

.info-card-status {
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.info-card-status.success {
    background: var(--success-bg);
    color: var(--success-color);
}

.info-card-status.warning {
    background: var(--warning-bg);
    color: var(--warning-color);
}

.info-card-status.error {
    background: var(--error-bg);
    color: var(--error-color);
}

.info-card-status.info {
    background: var(--info-bg);
    color: var(--info-color);
}

.info-card-meta {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.info-card-description {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.info-card-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.info-card-stats.three-col {
    grid-template-columns: 1fr 1fr 1fr;
}

.info-card-stats.four-col {
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

.info-card-stat-item {
    text-align: center;
}

.info-card-stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.info-card-stat-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.info-card-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* 进度条样式 (用于配置管理页面) */
.info-card-progress {
    margin-bottom: var(--spacing-md);
}

.info-card-progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.info-card-progress-bar {
    height: 6px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.info-card-progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    transition: width var(--transition-slow);
    border-radius: var(--radius-full);
}

/* 响应式网格支持 */
.info-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

@media (max-width: 768px) {
    .info-cards-grid {
        grid-template-columns: 1fr;
    }
    
    .info-card-stats {
        grid-template-columns: 1fr;
    }
    
    .info-card-stats.three-col,
    .info-card-stats.four-col {
        grid-template-columns: 1fr 1fr;
    }
}

/* 按钮组件 - 优化尺寸和比例 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-5); /* 12px 20px - 黄金比例 */
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-wide);
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    min-height: 2.5rem; /* 40px - 最小点击目标 */
}

.btn:focus {
    outline: none;
    box-shadow: var(--shadow-focus);
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: var(--primary-gradient-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 6px 20px rgba(102, 126, 234, 0.4);
    transition: all 200ms var(--ease-out-cubic);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--gray-100);
    border-color: var(--border-dark);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-inverse);
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-success);
    transition: all 200ms var(--ease-out-cubic);
}

.btn-danger {
    background: var(--error-color);
    color: var(--text-inverse);
}

.btn-danger:hover {
    background: #f56565;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-error);
    transition: all 200ms var(--ease-out-cubic);
}

/* 按钮尺寸系统 - 基于8点网格 */
.btn-xs {
    padding: var(--spacing-1) var(--spacing-3); /* 4px 12px */
    font-size: var(--text-xs);
    min-height: 1.75rem; /* 28px */
    gap: var(--spacing-1);
}

.btn-sm {
    padding: var(--spacing-2) var(--spacing-4); /* 8px 16px */
    font-size: var(--text-sm);
    min-height: 2rem; /* 32px */
    gap: var(--spacing-1);
}

.btn-md {
    padding: var(--spacing-3) var(--spacing-5); /* 12px 20px */
    font-size: var(--text-sm);
    min-height: 2.5rem; /* 40px - 默认尺寸 */
    gap: var(--spacing-2);
}

.btn-lg {
    padding: var(--spacing-4) var(--spacing-6); /* 16px 24px */
    font-size: var(--text-base);
    min-height: 3rem; /* 48px */
    gap: var(--spacing-2);
}

.btn-xl {
    padding: var(--spacing-5) var(--spacing-8); /* 20px 32px */
    font-size: var(--text-lg);
    min-height: 3.5rem; /* 56px */
    gap: var(--spacing-3);
}

/* 表单组件 - 优化间距 */
.form-group {
    margin-bottom: var(--spacing-5);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-wide);
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4); /* 12px 16px */
    font-family: var(--font-sans);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    min-height: 2.5rem; /* 40px - 与按钮保持一致 */
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-focus);
    transform: translateY(-1px);
    transition: all 200ms var(--ease-out-cubic);
}

.form-control:disabled {
    background: var(--gray-100);
    cursor: not-allowed;
}

/* Select */
.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23718096' d='M6 9L1 4h10z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.875rem center;
    background-size: 12px;
    padding-right: 2.5rem;
}

/* Checkbox & Radio */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    width: 1.125rem;
    height: 1.125rem;
    margin-right: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.form-check-input:checked {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
}

/* 徽章组件 - 优化尺寸 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-3); /* 4px 12px */
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    letter-spacing: var(--letter-spacing-wider);
    border-radius: var(--radius-full);
    text-transform: uppercase;
    white-space: nowrap;
}

.badge-primary {
    background: var(--primary-bg);
    color: var(--primary-color);
}

.badge-success {
    background: var(--success-bg);
    color: var(--success-color);
}

.badge-warning {
    background: var(--warning-bg);
    color: var(--warning-color);
}

.badge-danger {
    background: var(--error-bg);
    color: var(--error-color);
}

/* 警告组件 - 优化间距 */
.alert {
    padding: var(--spacing-4);
    border-radius: var(--radius-md);
    border-left: 4px solid;
    margin-bottom: var(--spacing-6);
    font-size: var(--text-sm);
    line-height: var(--line-height-relaxed);
}

.alert-info {
    background: var(--info-bg);
    border-color: var(--info-color);
    color: #2c5282;
}

.alert-success {
    background: var(--success-bg);
    border-color: var(--success-color);
    color: #22543d;
}

.alert-warning {
    background: var(--warning-bg);
    border-color: var(--warning-color);
    color: #744210;
}

.alert-danger {
    background: var(--error-bg);
    border-color: var(--error-color);
    color: #742a2a;
}

/* 进度条 - 优化尺寸和比例 */
.progress {
    height: var(--spacing-2); /* 8px */
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-4);
    position: relative;
}

/* 进度条尺寸变体 */
.progress-sm {
    height: 4px;
}

.progress-lg {
    height: var(--spacing-3); /* 12px */
}

.progress-xl {
    height: var(--spacing-4); /* 16px */
}

.progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    transition: width var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 100%
    );
    animation: shimmer 2s infinite;
}

/* Modal Component */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    z-index: var(--z-modal-backdrop);
    animation: fadeIn var(--transition-fast);
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-2xl);
    z-index: var(--z-modal);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: auto;
    animation: slideUp 300ms var(--ease-out-back);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* Header Component */
.header {
    background: var(--primary-gradient);
    color: var(--text-inverse);
    padding: var(--spacing-lg) 0;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.25);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 50%;
    height: 200%;
    background: rgba(255, 255, 255, 0.05);
    transform: rotate(45deg);
}

.header-content {
    position: relative;
    z-index: 1;
}

/* Navigation */
.nav {
    display: flex;
    list-style: none;
    gap: var(--spacing-xs);
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

.nav-link.active {
    background: var(--primary-bg);
    color: var(--primary-color);
    font-weight: 500;
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: 2px solid var(--border-light);
    margin-bottom: var(--spacing-lg);
}

.nav-tabs .nav-link {
    border-bottom: 3px solid transparent;
    margin-bottom: -2px;
    border-radius: 0;
}

.nav-tabs .nav-link.active {
    border-bottom-color: var(--primary-color);
    background: transparent;
}

/* ============================
   6. Utilities - 实用类
   ============================ */

/* 文本工具类 */

/* 对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-inverse { color: var(--text-inverse); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--error-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }

/* 字重 */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 字号 */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }

/* 行高 */
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* 字母间距 */
.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.tracking-wider { letter-spacing: var(--letter-spacing-wider); }

/* 文本变换 */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* 间距工具类 - 基于8点网格 */

/* Margin */
.m-0 { margin: var(--spacing-0); }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }
.m-10 { margin: var(--spacing-10); }
.m-12 { margin: var(--spacing-12); }

/* Margin Top */
.mt-0 { margin-top: var(--spacing-0); }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-5 { margin-top: var(--spacing-5); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }
.mt-10 { margin-top: var(--spacing-10); }
.mt-12 { margin-top: var(--spacing-12); }

/* Margin Bottom */
.mb-0 { margin-bottom: var(--spacing-0); }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }
.mb-10 { margin-bottom: var(--spacing-10); }
.mb-12 { margin-bottom: var(--spacing-12); }

/* Margin Left & Right */
.mx-auto { margin-left: auto; margin-right: auto; }
.mx-1 { margin-left: var(--spacing-1); margin-right: var(--spacing-1); }
.mx-2 { margin-left: var(--spacing-2); margin-right: var(--spacing-2); }
.mx-4 { margin-left: var(--spacing-4); margin-right: var(--spacing-4); }
.mx-6 { margin-left: var(--spacing-6); margin-right: var(--spacing-6); }

.my-1 { margin-top: var(--spacing-1); margin-bottom: var(--spacing-1); }
.my-2 { margin-top: var(--spacing-2); margin-bottom: var(--spacing-2); }
.my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }
.my-6 { margin-top: var(--spacing-6); margin-bottom: var(--spacing-6); }
.my-8 { margin-top: var(--spacing-8); margin-bottom: var(--spacing-8); }

/* Padding */
.p-0 { padding: var(--spacing-0); }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }
.p-10 { padding: var(--spacing-10); }
.p-12 { padding: var(--spacing-12); }

/* Padding X & Y */
.px-1 { padding-left: var(--spacing-1); padding-right: var(--spacing-1); }
.px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
.px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
.px-8 { padding-left: var(--spacing-8); padding-right: var(--spacing-8); }

.py-1 { padding-top: var(--spacing-1); padding-bottom: var(--spacing-1); }
.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
.py-6 { padding-top: var(--spacing-6); padding-bottom: var(--spacing-6); }
.py-8 { padding-top: var(--spacing-8); padding-bottom: var(--spacing-8); }

/* Display Utilities */
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

/* Position Utilities */
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

/* Border Utilities */
.border { border: 1px solid var(--border-color); }
.border-top { border-top: 1px solid var(--border-color); }
.border-bottom { border-bottom: 1px solid var(--border-color); }
.border-left { border-left: 1px solid var(--border-color); }
.border-right { border-right: 1px solid var(--border-color); }
.border-0 { border: none; }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-none { box-shadow: none; }

/* ============================
   7. Animations - 动画
   ============================ */

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translate(-50%, -40%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 新增的精致动画 */
@keyframes breathe {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-6px);
    }
}

@keyframes wiggle {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(-3deg);
    }
    75% {
        transform: rotate(3deg);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* 精致的渐入动画 */
@keyframes slideInDown {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 动画类 */
.animate-fadeIn { animation: fadeIn var(--transition-base); }
.animate-fadeInUp { animation: fadeInUp var(--transition-base); }
.animate-slideIn { animation: slideIn var(--transition-base); }
.animate-slideInDown { animation: slideInDown var(--transition-base); }
.animate-slideInLeft { animation: slideInLeft var(--transition-base); }
.animate-slideInRight { animation: slideInRight var(--transition-base); }
.animate-scaleIn { animation: scaleIn var(--transition-base); }
.animate-spin { animation: spin 1s linear infinite; }
.animate-pulse { animation: pulse 2s ease-in-out infinite; }
.animate-breathe { animation: breathe 3s ease-in-out infinite; }
.animate-float { animation: float 3s ease-in-out infinite; }
.animate-wiggle { animation: wiggle 0.5s ease-in-out; }

/* 动画延迟 */
.animate-delay-75 { animation-delay: 75ms; }
.animate-delay-100 { animation-delay: 100ms; }
.animate-delay-150 { animation-delay: 150ms; }
.animate-delay-200 { animation-delay: 200ms; }
.animate-delay-300 { animation-delay: 300ms; }
.animate-delay-500 { animation-delay: 500ms; }
.animate-delay-700 { animation-delay: 700ms; }
.animate-delay-1000 { animation-delay: 1000ms; }

/* ============================
   8. Responsive Design - 响应式
   ============================ */

/* 响应式设计 - Mobile First 断点系统 */

/* 小屏幕手机: 320px - 639px */
@media (max-width: 639px) {
    :root {
        --text-base: 0.875rem; /* 14px - 小屏幕减小字体 */
    }
    
    .container {
        padding: 0 var(--spacing-4);
    }
    
    h1 { font-size: var(--text-3xl); }
    h2 { font-size: var(--text-2xl); }
    h3 { font-size: var(--text-xl); }
    
    .card {
        padding: var(--spacing-4);
    }
}

/* 大屏幕手机: 640px+ */
@media (min-width: 640px) {
    .container {
        max-width: 40rem; /* 640px */
    }
    
    .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .sm\:flex-row { flex-direction: row; }
    .sm\:text-left { text-align: left; }
    .sm\:text-center { text-align: center; }
    .sm\:d-block { display: block; }
    .sm\:d-none { display: none; }
}

/* 平板: 768px+ */
@media (min-width: 768px) {
    .container {
        max-width: 48rem; /* 768px */
    }
    
    .container-narrow {
        max-width: 32rem; /* 512px - 平板上的窄容器 */
    }
    
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .md\:flex-row { flex-direction: row; }
    .md\:text-lg { font-size: var(--text-lg); }
    .md\:p-6 { padding: var(--spacing-6); }
    .md\:d-block { display: block; }
    .md\:d-none { display: none; }
}

/* 桌面: 1024px+ */
@media (min-width: 1024px) {
    .container {
        max-width: 64rem; /* 1024px */
    }
    
    .container-medium {
        max-width: var(--container-medium);
    }
    
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    .lg\:grid-golden { grid-template-columns: var(--grid-golden); }
    .lg\:flex-row { flex-direction: row; }
    .lg\:text-xl { font-size: var(--text-xl); }
    .lg\:p-8 { padding: var(--spacing-8); }
    .lg\:d-block { display: block; }
    .lg\:d-none { display: none; }
}

/* 大桌面: 1280px+ */
@media (min-width: 1280px) {
    .container {
        max-width: var(--container-wide);
    }
    
    .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
    .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
    .xl\:text-2xl { font-size: var(--text-2xl); }
    .xl\:p-10 { padding: var(--spacing-10); }
}

/* 超宽屏: 1536px+ */
@media (min-width: 1536px) {
    .container {
        max-width: 96rem; /* 1536px */
    }
    
    .\32xl\:text-3xl { font-size: var(--text-3xl); }
}

/* ============================
   9. Accessibility - 可访问性
   ============================ */

/* Focus Styles */
*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Skip to Content */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 0;
    background: var(--primary-color);
    color: var(--text-inverse);
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    z-index: var(--z-tooltip);
}

.skip-to-content:focus {
    top: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --shadow-sm: 0 0 0 1px currentColor;
        --shadow-md: 0 0 0 2px currentColor;
        --shadow-lg: 0 0 0 3px currentColor;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode variables can be defined here */
}

/* ============================
   10. Print Styles - 打印样式
   ============================ */

@media print {
    body {
        background: white;
        color: black;
    }
    
    .no-print,
    .header,
    .nav,
    .btn,
    .modal {
        display: none !important;
    }
    
    .container {
        max-width: 100%;
    }
    
    a {
        text-decoration: underline;
    }
}