<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品管理 - AI直播运营控制台</title>
    
    <!-- Global Design System CSS -->
    <link rel="stylesheet" href="./static/css/global-design-system.css?v=1.0.0">
    
    <!-- UI Components Library -->
    <script src="./static/js/ui-components.js?v=1.0.0"></script>
    
    <style>
        /* Page Specific Styles */
        .page-header {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-lg) 0;
            margin-bottom: var(--spacing-xl);
        }
        
        .page-header .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg) var(--spacing-xl);
        }
        
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        .toolbar-left {
            display: flex;
            gap: var(--spacing-md);
            align-items: center;
        }
        
        .toolbar-right {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }
        
        .search-box {
            position: relative;
            min-width: 300px;
        }
        
        .search-input {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            padding-left: 40px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1rem;
        }
        
        .filter-select {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 0.875rem;
            min-width: 120px;
        }
        
        .products-grid {
            margin-bottom: var(--spacing-xl);
        }
        
        .empty-state {
            text-align: center;
            padding: var(--spacing-xl) var(--spacing-lg);
            grid-column: 1 / -1;
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: var(--spacing-lg);
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .empty-description {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
        }
        
        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-overlay);
            z-index: var(--z-modal-backdrop);
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn var(--transition-base);
        }
        
        .modal-content {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
            animation: slideUp var(--transition-base);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }
        
        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
        }
        
        .modal-close:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }
        
        .form-group {
            margin-bottom: var(--spacing-lg);
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            font-size: 0.875rem;
        }
        
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color var(--transition-fast);
        }
        
        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .modal-actions {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-color);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-xl);
        }
        
        .pagination button {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            background: var(--bg-primary);
            color: var(--text-primary);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .pagination button:hover:not(:disabled) {
            background: var(--primary-color);
            color: var(--text-inverse);
            border-color: var(--primary-color);
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .pagination .current {
            background: var(--primary-color);
            color: var(--text-inverse);
            border-color: var(--primary-color);
        }
        
        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar-left,
            .toolbar-right {
                width: 100%;
                justify-content: space-between;
            }
            
            .search-box {
                min-width: auto;
                flex: 1;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div>
                <div class="breadcrumb">
                    <a href="./">首页</a>
                    <span>></span>
                    <span>商品管理</span>
                </div>
                <h1 class="page-title">🛍️ 商品管理</h1>
            </div>
            <div>
                <a href="./" class="btn btn-secondary">返回控制台</a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-container">
        <!-- Toolbar -->
        <div class="toolbar">
            <div class="toolbar-left">
                <button class="btn btn-primary" onclick="openCreateProductModal()">
                    + 新建商品
                </button>
                <div class="search-box">
                    <span class="search-icon">🔍</span>
                    <input type="text" class="search-input" placeholder="搜索商品名称、SKU或描述..." 
                           id="searchInput" onkeyup="handleSearch()">
                </div>
                <select class="filter-select" id="categoryFilter" onchange="handleFilter()">
                    <option value="">所有分类</option>
                    <option value="electronics">电子产品</option>
                    <option value="clothing">服装</option>
                    <option value="home">家居用品</option>
                    <option value="beauty">美妆护肤</option>
                    <option value="food">食品饮料</option>
                    <option value="other">其他</option>
                </select>
            </div>
            <div class="toolbar-right">
                <button class="btn btn-secondary" onclick="exportProducts()">
                    📤 导出
                </button>
                <button class="btn btn-secondary" onclick="importProducts()">
                    📥 导入
                </button>
                <button class="btn btn-secondary" onclick="loadProducts()">
                    🔄 刷新
                </button>
            </div>
        </div>

        <!-- Products Grid -->
        <div id="productsGrid" class="info-cards-grid products-grid">
            <!-- Loading State -->
            <div class="empty-state">
                <div class="empty-icon">⏳</div>
                <div class="empty-title">加载中...</div>
                <div class="empty-description">正在获取商品数据</div>
            </div>
        </div>

        <!-- Pagination -->
        <div id="pagination" class="pagination" style="display: none;">
            <!-- Pagination will be generated dynamically -->
        </div>
    </div>

    <!-- Create/Edit Product Modal -->
    <div id="productModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">新建商品</h2>
                <button class="modal-close" onclick="closeProductModal()">×</button>
            </div>
            
            <form id="productForm" onsubmit="handleSubmitProduct(event)">
                <div class="form-group">
                    <label class="form-label" for="productSku">商品SKU *</label>
                    <input type="text" id="productSku" class="form-input" required 
                           placeholder="例: PROD-001" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="productName">商品名称 *</label>
                    <input type="text" id="productName" class="form-input" required 
                           placeholder="输入商品名称" maxlength="200">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="productCategory">商品分类 *</label>
                        <select id="productCategory" class="form-select" required>
                            <option value="">请选择分类</option>
                            <option value="electronics">电子产品</option>
                            <option value="clothing">服装</option>
                            <option value="home">家居用品</option>
                            <option value="beauty">美妆护肤</option>
                            <option value="food">食品饮料</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="productPrice">商品价格</label>
                        <input type="number" id="productPrice" class="form-input" 
                               placeholder="0.00" min="0" step="0.01">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="productStock">库存数量</label>
                    <input type="number" id="productStock" class="form-input" 
                           placeholder="0" min="0" step="1">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="productDescription">商品描述</label>
                    <textarea id="productDescription" class="form-textarea" 
                              placeholder="输入商品详细描述..." maxlength="1000"></textarea>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeProductModal()">
                        取消
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        创建商品
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Global state
        let currentProducts = [];
        let filteredProducts = [];
        let currentPage = 1;
        let itemsPerPage = 12;
        let currentEditingId = null;
        let searchTimeout = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            handleURLParams();
        });

        // Handle URL parameters for direct navigation
        async function handleURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const editId = urlParams.get('edit');
            const qaId = urlParams.get('qa');
            
            // 如果有URL参数，等待商品加载完成后再处理
            if (editId || qaId) {
                // 等待一下让商品数据加载完成
                setTimeout(() => {
                    if (editId) {
                        const productId = parseInt(editId);
                        const product = currentProducts.find(p => p.id === productId);
                        if (product) {
                            openEditProductModal(productId);
                        } else {
                            UI.Toast.error(`商品 ID ${productId} 不存在`);
                            // 清除URL参数，避免刷新时重复提示
                            window.history.replaceState({}, '', window.location.pathname);
                        }
                    } else if (qaId) {
                        const productId = parseInt(qaId);
                        const product = currentProducts.find(p => p.id === productId);
                        if (product) {
                            openQAManagement(productId);
                        } else {
                            UI.Toast.error(`商品 ID ${productId} 不存在`);
                            // 清除URL参数
                            window.history.replaceState({}, '', window.location.pathname);
                        }
                    }
                }, 1500); // 给商品加载留足时间
            }
        }

        // Load products from API
        async function loadProducts() {
            const grid = document.getElementById('productsGrid');
            
            try {
                showLoadingState();
                const response = await fetch('/api/v1/simple-products/');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const products = await response.json();
                currentProducts = products || [];
                filteredProducts = [...currentProducts];
                
                renderProducts();
                
            } catch (error) {
                console.error('Failed to load products:', error);
                showErrorState(error.message);
            }
        }

        // Show loading state
        function showLoadingState() {
            const grid = document.getElementById('productsGrid');
            grid.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">⏳</div>
                    <div class="empty-title">加载中...</div>
                    <div class="empty-description">正在获取商品数据</div>
                </div>
            `;
        }

        // Show error state
        function showErrorState(error) {
            const grid = document.getElementById('productsGrid');
            grid.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">⚠️</div>
                    <div class="empty-title">加载失败</div>
                    <div class="empty-description">错误: ${error}</div>
                    <button class="btn btn-primary" onclick="loadProducts()" style="margin-top: var(--spacing-md);">
                        重试
                    </button>
                </div>
            `;
        }

        // Show empty state
        function showEmptyState() {
            const grid = document.getElementById('productsGrid');
            grid.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🛍️</div>
                    <div class="empty-title">暂无商品</div>
                    <div class="empty-description">点击"新建商品"开始创建您的第一个商品</div>
                    <button class="btn btn-primary" onclick="openCreateProductModal()" style="margin-top: var(--spacing-md);">
                        + 新建商品
                    </button>
                </div>
            `;
        }

        // Render products
        function renderProducts() {
            const grid = document.getElementById('productsGrid');
            
            if (filteredProducts.length === 0) {
                showEmptyState();
                document.getElementById('pagination').style.display = 'none';
                return;
            }
            
            // Calculate pagination
            const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const currentPageProducts = filteredProducts.slice(startIndex, endIndex);
            
            // Render products
            let html = '';
            currentPageProducts.forEach(product => {
                const status = product.stock > 0 ? 'success' : 'warning';
                const statusText = product.stock > 0 ? '有库存' : '缺货';
                
                html += `
                    <div class="info-card">
                        <div class="info-card-header">
                            <div>
                                <h3 class="info-card-title">${escapeHtml(product.name)}</h3>
                                <div class="info-card-subtitle">${escapeHtml(product.sku)}</div>
                            </div>
                            <div class="info-card-status ${status}">${statusText}</div>
                        </div>
                        
                        <div class="info-card-meta">
                            分类: ${getCategoryText(product.category)}<br>
                            创建时间: ${new Date(product.created_at).toLocaleString()}
                        </div>
                        
                        <div class="info-card-description">
                            ${escapeHtml(product.description || '暂无描述')}
                        </div>
                        
                        <div class="info-card-stats">
                            <div class="info-card-stat-item">
                                <span class="info-card-stat-value">¥${product.price || '0'}</span>
                                <span class="info-card-stat-label">价格</span>
                            </div>
                            <div class="info-card-stat-item">
                                <span class="info-card-stat-value">${product.stock || '0'}</span>
                                <span class="info-card-stat-label">库存</span>
                            </div>
                        </div>
                        
                        <div class="info-card-actions">
                            <button class="btn btn-primary btn-sm" onclick="viewProductDetail(${product.id})">
                                查看详情
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="openEditProductModal(${product.id})">
                                编辑
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="openQAManagement(${product.id})">
                                QA管理
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="duplicateProduct(${product.id})">
                                复制
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})">
                                删除
                            </button>
                        </div>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
            
            // Render pagination
            renderPagination(totalPages);
        }

        // Render pagination
        function renderPagination(totalPages) {
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }
            
            pagination.style.display = 'flex';
            
            let html = '';
            
            // Previous button
            html += `
                <button onclick="goToPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                    ← 上一页
                </button>
            `;
            
            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    html += `<button class="current">${i}</button>`;
                } else if (i === 1 || i === totalPages || Math.abs(i - currentPage) <= 2) {
                    html += `<button onclick="goToPage(${i})">${i}</button>`;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    html += `<span>...</span>`;
                }
            }
            
            // Next button
            html += `
                <button onclick="goToPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                    下一页 →
                </button>
            `;
            
            pagination.innerHTML = html;
        }

        // Go to page
        function goToPage(page) {
            const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
            if (page < 1 || page > totalPages) return;
            
            currentPage = page;
            renderProducts();
        }

        // Handle search
        function handleSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const query = document.getElementById('searchInput').value.toLowerCase().trim();
                filterProducts();
            }, 300);
        }

        // Handle filter
        function handleFilter() {
            currentPage = 1;
            filterProducts();
        }

        // Filter products
        function filterProducts() {
            const query = document.getElementById('searchInput').value.toLowerCase().trim();
            const category = document.getElementById('categoryFilter').value;
            
            filteredProducts = currentProducts.filter(product => {
                const matchesSearch = !query || 
                    product.name.toLowerCase().includes(query) ||
                    product.sku.toLowerCase().includes(query) ||
                    (product.description && product.description.toLowerCase().includes(query));
                
                const matchesCategory = !category || product.category === category;
                
                return matchesSearch && matchesCategory;
            });
            
            currentPage = 1;
            renderProducts();
        }

        // Open create product modal
        function openCreateProductModal() {
            currentEditingId = null;
            document.getElementById('modalTitle').textContent = '新建商品';
            document.getElementById('submitBtn').textContent = '创建商品';
            document.getElementById('productForm').reset();
            document.getElementById('productSku').readOnly = false;
            document.getElementById('productModal').style.display = 'flex';
        }

        // Open edit product modal
        async function openEditProductModal(productId) {
            currentEditingId = productId;
            document.getElementById('modalTitle').textContent = '编辑商品';
            document.getElementById('submitBtn').textContent = '保存更改';
            
            try {
                const response = await fetch(`/api/v1/simple-products/${productId}`);
                if (!response.ok) throw new Error('获取商品信息失败');
                
                const product = await response.json();
                
                document.getElementById('productSku').value = product.sku;
                document.getElementById('productSku').readOnly = true;
                document.getElementById('productName').value = product.name;
                document.getElementById('productCategory').value = product.category;
                document.getElementById('productPrice').value = product.price || '';
                document.getElementById('productStock').value = product.stock || '';
                document.getElementById('productDescription').value = product.description || '';
                
                document.getElementById('productModal').style.display = 'flex';
                
            } catch (error) {
                UI.Toast.error('获取商品信息失败: ' + error.message);
            }
        }

        // Close product modal
        function closeProductModal() {
            document.getElementById('productModal').style.display = 'none';
            document.getElementById('productForm').reset();
            currentEditingId = null;
        }

        // Handle submit product
        async function handleSubmitProduct(event) {
            event.preventDefault();
            
            const formData = {
                sku: document.getElementById('productSku').value.trim(),
                name: document.getElementById('productName').value.trim(),
                category: document.getElementById('productCategory').value,
                price: parseFloat(document.getElementById('productPrice').value) || 0,
                stock: parseInt(document.getElementById('productStock').value) || 0,
                description: document.getElementById('productDescription').value.trim()
            };
            
            try {
                UI.Loading.show(currentEditingId ? '保存中...' : '创建中...');
                
                let response;
                if (currentEditingId) {
                    // Update existing product
                    response = await fetch(`/api/v1/simple-products/${currentEditingId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                } else {
                    // Create new product
                    response = await fetch('/api/v1/simple-products/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                }
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || '操作失败');
                }
                
                UI.Loading.hide();
                UI.Toast.success(currentEditingId ? '商品更新成功' : '商品创建成功');
                closeProductModal();
                loadProducts();
                
            } catch (error) {
                UI.Loading.hide();
                UI.Toast.error('操作失败: ' + error.message);
            }
        }

        // Delete product
        async function deleteProduct(productId) {
            const product = currentProducts.find(p => p.id === productId);
            if (!product) return;
            
            UI.Modal.confirm(
                `确定要删除商品 "${product.name}" 吗？此操作不可撤销。`,
                async () => {
                    try {
                        UI.Loading.show('删除中...');
                        
                        const response = await fetch(`/api/v1/simple-products/${productId}`, {
                            method: 'DELETE'
                        });
                        
                        if (!response.ok) {
                            throw new Error('删除失败');
                        }
                        
                        UI.Loading.hide();
                        UI.Toast.success('商品已删除');
                        loadProducts();
                        
                    } catch (error) {
                        UI.Loading.hide();
                        UI.Toast.error('删除失败: ' + error.message);
                    }
                }
            );
        }

        // Duplicate product
        async function duplicateProduct(productId) {
            const product = currentProducts.find(p => p.id === productId);
            if (!product) return;
            
            const newSku = product.sku + '-COPY-' + Date.now();
            const formData = {
                sku: newSku,
                name: product.name + ' (副本)',
                category: product.category,
                price: product.price || 0,
                stock: product.stock || 0,
                description: product.description || ''
            };
            
            try {
                UI.Loading.show('复制中...');
                
                const response = await fetch('/api/v1/simple-products/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || '复制失败');
                }
                
                UI.Loading.hide();
                UI.Toast.success('商品复制成功');
                loadProducts();
                
            } catch (error) {
                UI.Loading.hide();
                UI.Toast.error('复制失败: ' + error.message);
            }
        }

        // View product detail
        function viewProductDetail(productId) {
            window.location.href = `./product_detail.html?id=${productId}`;
        }

        // Open QA management
        function openQAManagement(productId) {
            // 直接跳转到详情页的QA管理区域
            window.location.href = `./product_detail.html?id=${productId}#qa-section`;
        }

        // Export products
        function exportProducts() {
            if (currentProducts.length === 0) {
                UI.Toast.warning('暂无商品数据可导出');
                return;
            }
            
            const csv = convertToCSV(currentProducts);
            downloadCSV(csv, `products_${new Date().toISOString().split('T')[0]}.csv`);
            UI.Toast.success('商品数据已导出');
        }

        // Import products
        function importProducts() {
            UI.Toast.info('商品导入功能开发中');
        }

        // Utility functions
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function getCategoryText(category) {
            const categories = {
                'electronics': '电子产品',
                'clothing': '服装',
                'home': '家居用品',
                'beauty': '美妆护肤',
                'food': '食品饮料',
                'other': '其他'
            };
            return categories[category] || category;
        }

        function convertToCSV(data) {
            const headers = ['SKU', '商品名称', '分类', '价格', '库存', '描述', '创建时间'];
            const rows = data.map(product => [
                product.sku,
                product.name,
                getCategoryText(product.category),
                product.price || 0,
                product.stock || 0,
                product.description || '',
                new Date(product.created_at).toLocaleString()
            ]);
            
            const csvContent = [headers, ...rows]
                .map(row => row.map(field => `"${field}"`).join(','))
                .join('\n');
                
            return csvContent;
        }

        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Close modal when clicking outside
        document.getElementById('productModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProductModal();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeProductModal();
            }
        });
    </script>
</body>
</html>