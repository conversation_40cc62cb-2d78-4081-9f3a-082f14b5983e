"""智能路由决策器模块 - 清理版"""

from enum import Enum
from typing import List, Tuple, Dict, Optional, Any
from dataclasses import dataclass
import logging
import asyncio
import time

logger = logging.getLogger(__name__)


class RouteDecision(Enum):
    """路由决策类型"""
    DIRECT_ANSWER = "direct_answer"
    LLM_GENERATE = "llm_generate"
    IGNORE = "ignore"


@dataclass
class RoutingContext:
    """路由决策上下文"""
    current_topic: str = ""
    product_keywords: List[str] = None
    recent_questions: List[str] = None
    user_engagement_level: float = 0.0
    
    def __post_init__(self):
        if self.product_keywords is None:
            self.product_keywords = []
        if self.recent_questions is None:
            self.recent_questions = []


class QARouterError(Exception):
    """QA路由器异常"""
    pass


class QARouter:
    """基于置信度的智能路由决策器"""
    
    def __init__(self, llm_adapter, 
                 high_threshold: float = 0.72,
                 low_threshold: float = 0.55,
                 llm_timeout: float = 5.0):
        self.llm_adapter = llm_adapter
        self.τ_high = high_threshold
        self.τ_low = low_threshold
        self.llm_timeout = llm_timeout
        
        # 统计信息
        self._stats = {
            'total_questions': 0,
            'decision_counts': {
                'direct_answer': 0,
                'llm_generate': 0,
                'ignore': 0
            },
            'average_confidence': 0.0,
            'processing_times': []
        }
        
    async def route_question(self, question: str, context: RoutingContext) -> RouteDecision:
        """路由问题到相应处理路径"""
        start_time = time.time()
        
        try:
            self._stats['total_questions'] += 1
            
            # 计算相关性置信度
            confidence = self._calculate_relevance_confidence(question, context)
            
            # 更新平均置信度
            old_avg = self._stats['average_confidence']
            n = self._stats['total_questions']
            self._stats['average_confidence'] = (old_avg * (n-1) + confidence) / n
            
            # 路由决策
            if confidence >= self.τ_high:
                decision = RouteDecision.DIRECT_ANSWER
                self._stats['decision_counts']['direct_answer'] += 1
            elif confidence < self.τ_low:
                decision = RouteDecision.IGNORE
                self._stats['decision_counts']['ignore'] += 1
            else:
                # 中等置信度，使用LLM判断
                is_relevant = await self._llm_relevance_judgment(question, context)
                if is_relevant:
                    decision = RouteDecision.LLM_GENERATE
                    self._stats['decision_counts']['llm_generate'] += 1
                else:
                    decision = RouteDecision.IGNORE
                    self._stats['decision_counts']['ignore'] += 1
                    
            # 记录处理时间
            processing_time = time.time() - start_time
            self._stats['processing_times'].append(processing_time)
            
            return decision
            
        except Exception as e:
            logger.error(f"路由问题失败: {e}")
            raise QARouterError(f"路由失败: {e}")
    
    def _calculate_relevance_confidence(self, question: str, context: RoutingContext) -> float:
        """计算问题的相关性置信度"""
        if not question:
            return 0.0
            
        score = 0.0
        question_lower = question.lower()
        
        # 1. 关键词匹配 (权重: 0.4)
        keyword_matches = 0
        for keyword in context.product_keywords:
            if keyword.lower() in question_lower:
                keyword_matches += 1
        
        if context.product_keywords:
            keyword_score = min(keyword_matches / len(context.product_keywords), 1.0)
            score += keyword_score * 0.4
        
        # 2. 话题相关性 (权重: 0.3)
        if context.current_topic and context.current_topic.lower() in question_lower:
            score += 0.3
        
        # 3. 与最近问题的相似性 (权重: 0.2)
        if context.recent_questions:
            max_similarity = 0.0
            for recent_q in context.recent_questions:
                similarity = self._calculate_text_similarity(question, recent_q)
                max_similarity = max(max_similarity, similarity)
            score += max_similarity * 0.2
        
        # 4. 用户参与度调整 (权重: 0.1)
        score += context.user_engagement_level * 0.1
        
        return min(score, 1.0)
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似性（简单实现）"""
        if not text1 or not text2:
            return 0.0
            
        # 简单的词汇重叠相似性
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
            
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    async def _llm_relevance_judgment(self, question: str, context: RoutingContext) -> bool:
        """使用LLM进行相关性判断"""
        if not self.llm_adapter:
            return False
            
        try:
            prompt = f"""
请判断以下问题是否与当前直播内容相关：

当前话题: {context.current_topic}
产品关键词: {', '.join(context.product_keywords)}
问题: {question}

请回答"相关"或"不相关"，并简要说明理由。
"""
            
            response = await asyncio.wait_for(
                self.llm_adapter.generate_response(prompt),
                timeout=self.llm_timeout
            )
            
            # 解析LLM响应
            response_lower = response.lower()
            return '相关' in response_lower and '不相关' not in response_lower
            
        except Exception as e:
            logger.warning(f"LLM相关性判断失败: {e}")
            raise QARouterError(f"LLM判断失败: {e}")
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        stats = self._stats.copy()
        if self._stats['processing_times']:
            stats['avg_processing_time'] = sum(self._stats['processing_times']) / len(self._stats['processing_times'])
        else:
            stats['avg_processing_time'] = 0.0
        return stats