"""播放列表相关数据模型

定义智能播放列表系统的核心数据结构，包括播放列表项、内容类型等。

Author: Claude Code  
Date: 2025-08-08
"""

import hashlib
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List

from .playable_interface import IPlayableItem


@dataclass
class SentenceData:
    """统一的句子数据模型
    
    用于在不同组件间传递句子数据，确保类型安全和数据完整性。
    这是播放列表系统的标准输入格式。
    """
    text: str                               # 句子文本内容
    type: str = "normal"                    # 句子类型 (normal, emphasis, question, etc.)
    index: Optional[int] = None             # 原始索引（如果有）
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据
    estimated_duration_ms: Optional[int] = None  # 预估播放时长（毫秒）
    priority: str = "normal"                # 优先级 (low, normal, high)
    source: str = "script"                  # 数据来源 (script, dynamic, qa, etc.)
    
    def __post_init__(self):
        """初始化后处理"""
        # 验证必填字段
        if not self.text or not self.text.strip():
            raise ValueError("SentenceData text cannot be empty")
        
        # 自动估算时长（如果未提供）
        if self.estimated_duration_ms is None:
            # 简单估算：每个字符约100ms（可根据实际TTS调整）
            self.estimated_duration_ms = len(self.text) * 100
        
        # 确保metadata是字典
        if self.metadata is None:
            self.metadata = {}
    
    @classmethod
    def from_string(cls, text: str, index: Optional[int] = None) -> 'SentenceData':
        """从简单字符串创建SentenceData对象
        
        便捷工厂方法，用于快速创建句子数据。
        """
        return cls(
            text=text,
            type="normal",
            index=index,
            source="string"
        )
    
    @classmethod
    def from_script_segment(cls, segment: Any) -> 'SentenceData':
        """从ScriptSegment创建SentenceData对象
        
        用于转换script_previewer的输出。
        """
        return cls(
            text=segment.content if hasattr(segment, 'content') else str(segment),
            type=getattr(segment, 'segment_type', 'normal'),
            metadata={
                "segment_id": getattr(segment, 'segment_id', None),
                "role": getattr(segment, 'role', None),
                "timing": getattr(segment, 'timing_cue', None)
            },
            estimated_duration_ms=getattr(segment, 'estimated_duration_seconds', 0) * 1000,
            priority=getattr(segment, 'priority', 'normal'),
            source="script_segment"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "text": self.text,
            "type": self.type,
            "index": self.index,
            "metadata": self.metadata,
            "estimated_duration_ms": self.estimated_duration_ms,
            "priority": self.priority,
            "source": self.source
        }


class ItemType(Enum):
    """播放列表项类型枚举"""
    SCRIPT = "script"                      # 原始脚本内容
    QA_ANSWER = "qa_answer"                # QA回答内容（包含完整过渡语）
    
    def __str__(self) -> str:
        return self.value
        
    @property
    def is_qa_related(self) -> bool:
        """判断是否为QA相关类型"""
        return self == ItemType.QA_ANSWER
        
    @property
    def display_name(self) -> str:
        """显示名称"""
        names = {
            ItemType.SCRIPT: "脚本内容",
            ItemType.QA_ANSWER: "QA回答"
        }
        return names.get(self, self.value)


@dataclass
class PlaylistItem:
    """播放列表项数据模型
    
    表示播放列表中的一个项目，可以是原始脚本内容或QA相关内容。
    实现 IPlayableItem 接口的协议，支持决策者-执行者架构。
    
    注意：由于 dataclass 和 ABC 的兼容性问题，我们不直接继承 IPlayableItem，
    但确保实现了所有必要的接口方法。
    """
    item_id: str                           # 唯一标识符
    type: ItemType                         # 内容类型
    content: str                           # 文本内容
    original_index: Optional[int] = None   # 原始脚本中的索引（仅SCRIPT类型）
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    created_at: datetime = field(default_factory=datetime.utcnow)  # 创建时间
    content_hash: str = field(init=False)  # 内容哈希值，用于缓存键
    
    def __post_init__(self):
        """初始化后处理"""
        # 自动计算内容哈希
        self.content_hash = hashlib.md5(self.content.encode('utf-8')).hexdigest()
        
        # 验证数据
        self._validate()
        
    def _validate(self):
        """验证数据完整性"""
        if not self.content or not self.content.strip():
            raise ValueError("内容不能为空")
            
        if self.type == ItemType.SCRIPT and self.original_index is None:
            raise ValueError("SCRIPT类型必须指定original_index")
            
        if self.type.is_qa_related and "qa_id" not in self.metadata:
            raise ValueError("QA相关类型必须在metadata中指定qa_id")
            
    @property
    def item_type(self) -> str:
        """实现 IPlayableItem 接口：返回项目类型"""
        return self.type.value
    
    def get_cache_key(self) -> str:
        """获取TTS缓存键
        
        基于内容的确定性生成，确保相同内容总是产生相同的缓存键，
        不依赖于item_id，避免因重复创建PlaylistItem导致的缓存失效。
        """
        # 组合所有影响音频输出的关键属性
        key_components = [
            self.content,           # 文本内容
            self.type.value,        # 内容类型（影响语调等）
            # 未来可添加: voice_config, speed, pitch等
        ]
        key_string = "|".join(key_components)
        # 使用SHA256生成稳定的哈希值
        return hashlib.sha256(key_string.encode('utf-8')).hexdigest()[:16]
        
    def get_display_info(self) -> Dict[str, Any]:
        """获取显示信息"""
        return {
            "id": self.item_id,
            "type": self.type.value,
            "type_display": self.type.display_name,
            "content_preview": self.content[:50] + "..." if len(self.content) > 50 else self.content,
            "content_length": len(self.content),
            "created_at": self.created_at.isoformat(),
            "is_qa": self.type.is_qa_related,
            "metadata": self.metadata.copy()
        }
        
    def estimate_duration_ms(self) -> int:
        """估算播放时长（毫秒）
        
        基于文本长度的粗略估算，实际时长需要TTS引擎提供
        """
        # 粗略估算：中文平均每字50ms，英文每单词100ms
        # 这是一个非常粗糙的估算，主要用于预计算
        chinese_chars = sum(1 for c in self.content if '\u4e00' <= c <= '\u9fff')
        other_chars = len(self.content) - chinese_chars
        words = len(self.content.split())
        
        # 中文按字计算，英文按单词计算
        estimated_ms = chinese_chars * 50 + max(other_chars // 4, words) * 100
        
        # 最小时长500ms，最大时长30秒
        return max(500, min(estimated_ms, 30000))
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "item_id": self.item_id,
            "type": self.type.value,
            "content": self.content,
            "original_index": self.original_index,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "content_hash": self.content_hash,
            "estimated_duration_ms": self.estimate_duration_ms()
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PlaylistItem':
        """从字典创建实例"""
        return cls(
            item_id=data["item_id"],
            type=ItemType(data["type"]),
            content=data["content"],
            original_index=data.get("original_index"),
            metadata=data.get("metadata", {}),
            created_at=datetime.fromisoformat(data["created_at"])
        )
        
    @classmethod
    def create_script_item(cls, content: str, original_index: int, 
                          sentence_type: str = "normal") -> 'PlaylistItem':
        """创建脚本项目"""
        return cls(
            item_id=f"script_{original_index}_{uuid.uuid4().hex[:8]}",
            type=ItemType.SCRIPT,
            content=content,
            original_index=original_index,
            metadata={
                "sentence_type": sentence_type,
                "source": "original_script"
            }
        )
        
    @classmethod
    def create_qa_answer(cls, content: str, qa_id: str, question: str = "") -> 'PlaylistItem':
        """创建QA回答项目"""
        return cls(
            item_id=f"qa_{qa_id}_answer_{uuid.uuid4().hex[:8]}",
            type=ItemType.QA_ANSWER,
            content=content,
            original_index=None,
            metadata={
                "qa_id": qa_id,
                "question": question,
                "sequence": "answer",
                "source": "qa_system"
            }
        )


@dataclass
class PlaylistModification:
    """播放列表修改记录"""
    timestamp: datetime
    version: int
    action: str                            # 修改动作：initialize, qa_insertion, manual_edit
    details: Dict[str, Any]                # 修改详情
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "version": self.version,
            "action": self.action,
            "details": self.details
        }


@dataclass 
class PlaylistStats:
    """播放列表统计信息"""
    version: int                           # 当前版本
    total_items: int                       # 总项目数
    script_items: int                      # 脚本项目数
    qa_sequences: int                      # QA序列数
    total_estimated_duration_ms: int       # 总估算时长
    checksum: str                          # 内容校验和
    last_modified: datetime                # 最后修改时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "version": self.version,
            "total_items": self.total_items,
            "script_items": self.script_items,
            "qa_sequences": self.qa_sequences,
            "total_estimated_duration_ms": self.total_estimated_duration_ms,
            "total_estimated_duration_minutes": round(self.total_estimated_duration_ms / 60000, 1),
            "checksum": self.checksum,
            "last_modified": self.last_modified.isoformat()
        }


class QAInsertionStrategy(Enum):
    """QA插入策略"""
    MIN_POSITION = "min"                   # 使用最小位置（保守策略）
    MAX_POSITION = "max"                   # 使用最大位置（激进策略）
    AVERAGE_POSITION = "average"           # 使用平均位置（均衡策略）
    IMMEDIATE = "immediate"                # 立即插入（下一个位置）
    
    @property
    def description(self) -> str:
        """策略描述"""
        descriptions = {
            QAInsertionStrategy.MIN_POSITION: "选择所有客户端中最早的安全插入点",
            QAInsertionStrategy.MAX_POSITION: "选择所有客户端中最晚的安全插入点", 
            QAInsertionStrategy.AVERAGE_POSITION: "使用平均插入点",
            QAInsertionStrategy.IMMEDIATE: "在当前播放位置后立即插入"
        }
        return descriptions.get(self, self.value)


@dataclass
class QAInsertionResult:
    """QA插入结果
    
    明确的API契约，用于playlist_manager返回QA插入操作的结果。
    包含用于预合成的items和插入位置等信息。
    """
    items_inserted: List[PlaylistItem]        # 已插入播放列表的items
    items_for_pre_synthesis: List[PlaylistItem]  # 用于预合成的items（同一批实例）
    insertion_index: int                      # 插入位置
    success: bool                             # 是否成功
    qa_id: str                               # QA标识符
    error_message: Optional[str] = None      # 错误信息（如果失败）
    playlist_version: Optional[int] = None   # 插入后的播放列表版本


@dataclass
class QAInsertionRequest:
    """QA插入请求"""
    qa_id: str                             # QA标识符
    question: str                          # 用户问题
    answer: str                            # QA回答（包含完整的过渡语）
    strategy: QAInsertionStrategy = QAInsertionStrategy.MIN_POSITION  # 插入策略
    priority: int = 0                      # 优先级（数字越大优先级越高）
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    def build_items(self) -> List[PlaylistItem]:
        """构建播放列表项目序列
        
        answer已经包含了完整的过渡语，直接作为一个完整项目
        """
        # 只返回一个包含完整回答的项目
        return [PlaylistItem.create_qa_answer(
            self.answer, self.qa_id, self.question
        )]
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "qa_id": self.qa_id,
            "question": self.question,
            "answer": self.answer,
            "strategy": self.strategy.value,
            "priority": self.priority,
            "created_at": self.created_at.isoformat()
        }


# 辅助函数

def calculate_playlist_checksum(items: List[PlaylistItem]) -> str:
    """计算播放列表校验和"""
    content = "".join(item.content_hash for item in items)
    return hashlib.sha256(content.encode()).hexdigest()[:16]


def estimate_total_duration(items: List[PlaylistItem]) -> int:
    """估算总播放时长（毫秒）"""
    return sum(item.estimate_duration_ms() for item in items)


def group_qa_sequences(items: List[PlaylistItem]) -> Dict[str, List[PlaylistItem]]:
    """将QA项目按qa_id分组"""
    qa_groups = {}
    
    for item in items:
        if item.type.is_qa_related:
            qa_id = item.metadata.get("qa_id")
            if qa_id:
                if qa_id not in qa_groups:
                    qa_groups[qa_id] = []
                qa_groups[qa_id].append(item)
                
    return qa_groups


def find_qa_sequence_bounds(items: List[PlaylistItem], qa_id: str) -> tuple[int, int]:
    """
    查找QA序列在播放列表中的边界位置
    
    Returns:
        (start_index, end_index) 或 (-1, -1) 如果未找到
    """
    start_idx = -1
    end_idx = -1
    
    for i, item in enumerate(items):
        if (item.type.is_qa_related and 
            item.metadata.get("qa_id") == qa_id):
            if start_idx == -1:
                start_idx = i
            end_idx = i
            
    return start_idx, end_idx