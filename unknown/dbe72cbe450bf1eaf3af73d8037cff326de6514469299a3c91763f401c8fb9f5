"""问题预处理器模块"""

import re
import time
import json
import jieba
import asyncio
from typing import Optional
from redis import Redis
from simhash import Simhash
import opencc
import logging

logger = logging.getLogger(__name__)


class ContentSafetyError(Exception):
    """内容安全检测异常"""
    pass


class QuestionPreprocessor:
    """问题预处理器，负责文本规范化和去重"""
    
    def __init__(self, redis_client: Optional[Redis] = None, simhash_threshold: int = 3):
        self.redis_client = redis_client
        self.simhash_threshold = simhash_threshold
        self.converter = opencc.OpenCC('t2s') if opencc else None  # 繁体转简体
        self._initialized = False
        self._redis_enabled = False  # Track if Redis is enabled by config
        
        # 敏感词列表（实际使用时应从配置文件加载）
        self.sensitive_words = {
            '政治', '敏感', '违法', '暴力', '色情', '赌博', 
            '诈骗', '传销', '毒品', '恐怖', '分裂', '邪教'
        }
        
    async def initialize(self, redis_config: Optional[dict] = None):
        """异步初始化
        
        Args:
            redis_config: Redis配置字典，包含enabled, host, port, db, password等
        """
        if not self._initialized:
            # 检查是否启用Redis
            if redis_config and redis_config.get('enabled', False):
                try:
                    # 初始化Redis连接
                    if self.redis_client is None:
                        from redis import Redis
                        self.redis_client = Redis(
                            host=redis_config.get('host', 'localhost'),
                            port=redis_config.get('port', 6379),
                            db=redis_config.get('db', 0),
                            password=redis_config.get('password'),
                            decode_responses=True
                        )
                        # 测试连接
                        await asyncio.to_thread(self.redis_client.ping)
                        self._redis_enabled = True
                        logger.info(f"Redis连接成功: {redis_config.get('host')}:{redis_config.get('port')}")
                    
                except Exception as e:
                    logger.error(f"Redis连接失败: {e}")
                    # Fail-Fast: 如果配置启用了Redis但连接失败，立即报错
                    raise RuntimeError(f"Redis配置启用但连接失败: {e}")
            else:
                # Redis未启用，这是合法的
                self._redis_enabled = False
                self.redis_client = None
                logger.info("问题预处理器初始化完成（Redis未启用）")
                
            self._initialized = True
            logger.info("问题预处理器初始化完成")
                
    async def cleanup(self):
        """清理资源"""
        if self.redis_client:
            try:
                self.redis_client.close()
            except Exception as e:
                logger.warning(f"Redis连接关闭失败: {e}")
        self._initialized = False
        logger.info("问题预处理器已清理")
        
    async def update_cache_answer(self, text: str, answer: str, ttl: int = 300) -> None:
        """更新缓存中问题的答案"""
        if not self._redis_enabled or not self.redis_client or not text or not answer:
            return
            
        try:
            # 计算SimHash指纹
            simhash_value = await asyncio.to_thread(self._calculate_simhash, text)
            cache_key = f"simhash:{simhash_value}"
            
            # 更新缓存，包含答案
            await asyncio.to_thread(
                self.redis_client.setex,
                cache_key,
                ttl,
                json.dumps({
                    'question': text,
                    'answer': answer,
                    'timestamp': time.time()
                })
            )
            logger.debug(f"已更新问题答案缓存: {text[:30]}...")
            
        except Exception as e:
            logger.warning(f"更新答案缓存失败: {e}")
        
    async def normalize_question(self, text: str) -> str:
        """文本规范化处理"""
        if not text:
            return ""
            
        # 1. 去除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 2. 繁简转换
        if self.converter:
            try:
                text = self.converter.convert(text)
            except Exception as e:
                logger.warning(f"繁简转换失败: {e}")
        
        # 3. 全角转半角（数字和字母）
        text = self._full_to_half(text)
        
        # 转换大写字母为小写
        text = text.lower()
        
        # 4. 标准化标点符号（保留基本标点）
        # 处理多个连续的句号 - 必须先处理较长的模式
        text = re.sub(r'[。.]{2,}', '...', text)
        
        # 多个连续的感叹号/问号合并为单个  
        text = re.sub(r'[！!]{2,}', '！', text)
        text = re.sub(r'[？?]{2,}', '？', text)
        
        # 转换标点符号为中文标点（在句号处理之后）
        text = text.replace('!', '！').replace('?', '？')
        # 不转换单独的点，因为已经在上面处理过了
        
        # 移除不常见特殊字符(保留中文、英文、数字和基本标点)
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s，。？！、；：""''（）【】….]', '', text)
        
        # 5. 再次清理多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
        
    async def check_duplicate(self, text: str, ttl: int = 300) -> Optional[str]:
        """基于SimHash的去重检查
        
        Returns:
            如果找到重复问题，返回缓存的答案；否则返回None
        """
        if not text:
            return None
            
        if not self._redis_enabled or not self.redis_client:
            # Redis未启用，返回None表示无缓存
            return None
            
        try:
            # 计算SimHash指纹
            simhash_value = await asyncio.to_thread(self._calculate_simhash, text)
            
            # 检查Redis缓存中的相似指纹
            cache_key_pattern = "simhash:*"
            cached_keys = await asyncio.to_thread(self.redis_client.keys, cache_key_pattern)
            
            for cached_key in cached_keys:
                try:
                    key_str = cached_key if isinstance(cached_key, str) else cached_key.decode()
                    cached_hash = int(key_str.split(':')[1])
                    hamming_distance = bin(simhash_value ^ cached_hash).count('1')
                    
                    if hamming_distance <= self.simhash_threshold:
                        # 发现重复，尝试获取缓存的答案
                        cached_data = await asyncio.to_thread(self.redis_client.get, key_str)
                        if cached_data:
                            try:
                                data = json.loads(cached_data)
                                return data.get('answer')  # 返回缓存的答案
                            except json.JSONDecodeError:
                                pass
                        return None  # 找到重复但没有答案
                except (ValueError, IndexError) as e:
                    logger.warning(f"解析缓存键失败: {e}")
                    continue
            
            # 缓存当前指纹（暂时不存答案）
            cache_key = f"simhash:{simhash_value}"
            await asyncio.to_thread(
                self.redis_client.setex, 
                cache_key, 
                ttl, 
                json.dumps({
                    'question': text,
                    'timestamp': time.time()
                })
            )
            
            return None
            
        except Exception as e:
            logger.error(f"重复检查失败: {e}")
            raise
        
    def _calculate_simhash(self, text: str) -> int:
        """计算文本的SimHash指纹"""
        if not text:
            return 0
            
        # 使用jieba分词
        words = list(jieba.cut(text))
        if not words:
            return 0
            
        return Simhash(words).value
        
    def _full_to_half(self, text: str) -> str:
        """全角转半角"""
        result = []
        for char in text:
            code = ord(char)
            if code == 0x3000:  # 全角空格
                code = 0x20
            elif 0xFF01 <= code <= 0xFF5E:  # 其他全角字符
                code -= 0xFEE0
            result.append(chr(code))
        
        # 处理特殊的全角字符
        result_text = ''.join(result)
        # 全角数字转半角
        result_text = result_text.replace('１', '1').replace('２', '2').replace('３', '3')
        result_text = result_text.replace('４', '4').replace('５', '5').replace('６', '6')
        result_text = result_text.replace('７', '7').replace('８', '8').replace('９', '9').replace('０', '0')
        
        # 特殊符号转换
        result_text = result_text.replace('①', '1').replace('②', '2').replace('③', '3')
        result_text = result_text.replace('④', '4').replace('⑤', '5').replace('⑥', '6')
        
        return result_text
        
    async def content_safety_check(self, text: str) -> None:
        """内容安全审查"""
        if not text:
            return
            
        # 简单的关键词过滤
        text_lower = text.lower()
        for word in self.sensitive_words:
            if word in text_lower:
                logger.warning(f"检测到敏感词: {word}")
                raise ContentSafetyError(f"内容包含敏感词: {word}")
                
        # 垃圾信息检测
        spam_patterns = [
            r'免费.*点击',
            r'立即.*折扣',
            r'限时.*优惠',
            r'微信.*\d+',
            r'加我.*qq'
        ]
        
        for pattern in spam_patterns:
            if re.search(pattern, text_lower):
                raise ContentSafetyError(f"检测到垃圾信息模式: {pattern}")
                
        # TODO: 可以集成外部内容安全API
        
    def extract_keywords(self, text: str) -> list[str]:
        """提取关键词"""
        if not text:
            return []
            
        # 使用jieba提取关键词
        words = jieba.cut(text)
        
        # 过滤停用词和单字符
        stopwords = {'的', '是', '在', '有', '和', '就', '都', '而', '及', '与', '或', '但', '不', '了', '吗', '呢', '吧', '啊'}
        keywords = [word.strip() for word in words 
                   if len(word.strip()) > 1 and word.strip() not in stopwords]
        
        return list(set(keywords))  # 去重
        
    def is_question_valid(self, text: str) -> bool:
        """验证问题是否有效"""
        if not text:
            return False
            
        # 长度检查
        if len(text) < 2 or len(text) > 500:
            return False
            
        # 内容安全检查 (简化版，不做异步调用)
        try:
            # 简单的同步安全检查
            text_lower = text.lower()
            for word in self.sensitive_words:
                if word in text_lower:
                    return False
        except Exception:
            return False
            
        # 检查是否包含有意义的内容
        keywords = self.extract_keywords(text)
        if len(keywords) == 0:
            return False
            
        return True
        
    def get_question_type(self, text: str) -> str:
        """识别问题类型"""
        if not text:
            return "unknown"
            
        # 价格相关
        price_keywords = {'价格', '多少钱', '贵', '便宜', '费用', '成本'}
        if any(keyword in text for keyword in price_keywords):
            return "price_inquiry"
            
        # 购买相关
        purchase_keywords = {'怎么买', '在哪买', '购买', '下单', '订购', '预订'}
        if any(keyword in text for keyword in purchase_keywords):
            return "purchase_inquiry"
            
        # 产品特性
        feature_keywords = {'特点', '功能', '作用', '效果', '优势', '好处'}
        if any(keyword in text for keyword in feature_keywords):
            return "feature_inquiry"
            
        # 使用方法
        usage_keywords = {'怎么用', '如何使用', '用法', '操作', '步骤'}
        if any(keyword in text for keyword in usage_keywords):
            return "usage_inquiry"
            
        # 配送物流
        shipping_keywords = {'配送', '物流', '快递', '发货', '送货', '到货'}
        if any(keyword in text for keyword in shipping_keywords):
            return "shipping_inquiry"
            
        return "general_inquiry"