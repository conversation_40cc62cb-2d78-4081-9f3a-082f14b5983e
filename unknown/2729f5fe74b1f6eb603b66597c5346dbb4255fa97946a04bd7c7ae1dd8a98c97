"""认证API端点

提供CosyVoice临时Token获取接口，确保前端安全访问。
"""

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from typing import Optional
from loguru import logger

from ..core.exceptions import ServiceError

auth_router = APIRouter(prefix="/api/auth", tags=["authentication"])


class TokenResponse(BaseModel):
    """Token响应模型"""
    token: str
    expires_in: int
    token_type: str = "bearer"


class TokenStatusResponse(BaseModel):
    """Token状态响应模型"""
    has_token: bool
    is_valid: bool
    expires_in: float
    expires_at: float = None


@auth_router.post("/token", response_model=TokenResponse)
async def get_temporary_token():
    """获取临时Token（会话Token）
    
    为前端客户端提供60秒有效期的会话Token，作为官方临时Token的替代方案。
    保持原有API接口兼容性。
    
    Returns:
        TokenResponse: 包含会话Token和过期时间的响应
        
    Raises:
        HTTPException: Token获取失败时抛出
    """
    try:
        from ..services.auth.session_token_manager import get_session_token_manager
        
        session_manager = get_session_token_manager()
        session_token = await session_manager.create_session_token()
        
        logger.info("Session token provided to client (via legacy /token endpoint)")
        
        return TokenResponse(
            token=session_token,
            expires_in=60  # 60秒有效期
        )
        
    except ServiceError as e:
        logger.error(f"Service error getting session token: {e}")
        
        if e.error_code == "CONFIG_ERROR":
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="CosyVoice not configured properly"
            )
        elif e.error_code == "INVALID_API_KEY_FORMAT":
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Invalid CosyVoice API key"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate temporary token"
            )
            
    except Exception as e:
        logger.error(f"Unexpected error getting session token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@auth_router.get("/token/status", response_model=TokenStatusResponse)
async def get_token_status():
    """获取当前Token状态
    
    返回当前Token的有效性和过期时间信息。
    
    Returns:
        TokenStatusResponse: Token状态信息
    """
    try:
        from ..services.auth.token_manager import get_token_manager
        
        token_manager = get_token_manager()
        token_info = token_manager.get_token_info()
        
        return TokenStatusResponse(
            has_token=token_info["has_token"],
            is_valid=token_info["is_valid"],
            expires_in=token_info["expires_in"],
            expires_at=token_info["expires_at"]
        )
        
    except Exception as e:
        logger.error(f"Error getting token status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get token status"
        )


@auth_router.post("/token/refresh")
async def refresh_token():
    """强制刷新Token
    
    手动触发Token刷新，用于测试或故障恢复。
    
    Returns:
        TokenResponse: 新的Token信息
    """
    try:
        from ..services.auth.token_manager import get_token_manager
        
        token_manager = get_token_manager()
        
        # 强制刷新Token
        await token_manager.invalidate_token()
        token = await token_manager.get_valid_token()
        
        token_info = token_manager.get_token_info()
        
        logger.info("Token manually refreshed")
        
        return TokenResponse(
            token=token,
            expires_in=int(token_info.get("expires_in", 50))
        )
        
    except Exception as e:
        logger.error(f"Error refreshing token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh token"
        )


class ConnectivityTestResponse(BaseModel):
    """连通性测试响应模型"""
    api_key_valid: bool
    connectivity_ok: bool
    token_generation_ok: bool
    latency_ms: Optional[int]
    error: Optional[str]


class SessionTokenResponse(BaseModel):
    """会话Token响应模型"""
    session_token: str
    expires_in: int
    token_type: str = "session"


class SessionTokenRequest(BaseModel):
    """会话Token请求模型"""
    client_id: Optional[str] = None


@auth_router.post("/test-connectivity", response_model=ConnectivityTestResponse)
async def test_api_connectivity():
    """测试CosyVoice API连通性
    
    验证API Key、网络连接和Token生成是否正常工作。
    
    Returns:
        ConnectivityTestResponse: 连通性测试结果
    """
    try:
        from ..services.auth.token_manager import get_token_manager
        
        token_manager = get_token_manager()
        test_result = await token_manager.test_api_connectivity()
        
        logger.info(f"API connectivity test completed: {test_result}")
        
        return ConnectivityTestResponse(**test_result)
        
    except ServiceError as e:
        logger.error(f"Connectivity test service error: {e}")
        
        # 根据错误类型返回相应结果
        if e.error_code == "CONFIG_ERROR":
            return ConnectivityTestResponse(
                api_key_valid=False,
                connectivity_ok=False,
                token_generation_ok=False,
                latency_ms=None,
                error=str(e)
            )
        elif e.error_code == "INVALID_API_KEY_FORMAT":
            return ConnectivityTestResponse(
                api_key_valid=False,
                connectivity_ok=True,
                token_generation_ok=False,
                latency_ms=None,
                error=str(e)
            )
        else:
            return ConnectivityTestResponse(
                api_key_valid=True,
                connectivity_ok=False,
                token_generation_ok=False,
                latency_ms=None,
                error=str(e)
            )
            
    except Exception as e:
        logger.error(f"Unexpected error in connectivity test: {e}")
        return ConnectivityTestResponse(
            api_key_valid=False,
            connectivity_ok=False,
            token_generation_ok=False,
            latency_ms=None,
            error=f"Unexpected error: {str(e)}"
        )


@auth_router.post("/session", response_model=SessionTokenResponse)
async def create_session_token(request: SessionTokenRequest = SessionTokenRequest()):
    """创建会话Token
    
    为前端客户端提供60秒有效期的会话Token，作为官方临时Token的替代方案。
    会话Token用于WebSocket连接认证，避免在前端暴露API Key。
    
    Args:
        request: 会话Token请求，包含可选的客户端ID
        
    Returns:
        SessionTokenResponse: 包含会话Token和过期时间的响应
        
    Raises:
        HTTPException: 会话Token创建失败时抛出
    """
    try:
        from ..services.auth.session_token_manager import get_session_token_manager
        
        session_manager = get_session_token_manager()
        session_token = await session_manager.create_session_token(request.client_id)
        
        logger.info(f"Session token created for client: {request.client_id}")
        
        return SessionTokenResponse(
            session_token=session_token,
            expires_in=60  # 60秒有效期
        )
        
    except ServiceError as e:
        logger.error(f"Service error creating session token: {e}")
        
        if e.error_code == "CONFIG_ERROR":
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="CosyVoice not configured properly"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create session token"
            )
            
    except Exception as e:
        logger.error(f"Unexpected error creating session token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@auth_router.post("/session/validate")
async def validate_session_token(session_token: str):
    """验证会话Token
    
    验证提供的会话Token是否有效且未过期。
    主要用于WebSocket连接时的Token验证。
    
    Args:
        session_token: 要验证的会话Token
        
    Returns:
        验证结果和会话信息
        
    Raises:
        HTTPException: Token验证失败时抛出
    """
    try:
        from ..services.auth.session_token_manager import get_session_token_manager
        
        session_manager = get_session_token_manager()
        session_data = await session_manager.validate_session_token(session_token)
        
        if session_data:
            logger.debug(f"Session token validated successfully")
            return {
                "valid": True,
                "client_id": session_data.get("client_id"),
                "expires_at": session_data.get("expires_at")
            }
        else:
            logger.warning("Session token validation failed")
            return {
                "valid": False,
                "error": "Invalid or expired session token"
            }
        
    except Exception as e:
        logger.error(f"Error validating session token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate session token"
        )