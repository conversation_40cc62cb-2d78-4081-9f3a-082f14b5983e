"""Validation error parser for converting Pydantic errors to user-friendly messages"""

from typing import Dict, List, Any, Optional
from pydantic import ValidationError as PydanticValidationError
import json
from loguru import logger


class ValidationErrorParser:
    """Parser for converting Pydantic validation errors to structured user-friendly messages"""
    
    # Field name mappings to Chinese
    FIELD_NAME_MAPPING = {
        # Basic Information (Section 1)
        'stream_title': '直播标题',
        'stream_type': '直播类型',
        'planned_duration_minutes': '计划时长',
        'target_audience': '目标观众',
        'stream_description': '直播描述',
        
        # Product Information (Section 2)
        'primary_sku': '主要SKU',
        'product_name': '产品名称',
        'brand': '品牌',
        'category': '产品类别',
        'current_price': '当前价格',
        'original_price': '原价',
        'key_specifications': '关键规格',
        'product_images': '产品图片',
        'product_video_url': '产品视频',
        'related_skus': '相关SKU',
        
        # Selling Points Structure (Section 3)
        'primary_value_proposition': '主要价值主张',
        'selling_points': '卖点',
        'competitive_advantages': '竞争优势',
        'call_to_actions': '行动号召',
        'objection_handling': '异议处理',
        'point_id': '卖点ID',
        'title': '标题',
        'description': '描述',
        'supporting_facts': '支撑事实',
        'priority': '优先级',
        
        # Persona Configuration (Section 4)
        'selected_persona_id': '选择的人设ID',
        'persona_name': '人设名称',
        'voice_style': '语音风格',
        'speaking_rate': '语速',
        'energy_level': '能量等级',
        
        # Advanced Settings (Section 5)
        'words_per_minute_override': '语速覆盖设置',
        'pause_duration_seconds': '暂停时长',
        'background_music_enabled': '背景音乐',
        'interaction_frequency': '互动频率',
        
        # Review and Validation (Section 6)
        'content_validation_rules': '内容验证规则',
        'compliance_validation_rules': '合规验证规则',
        'technical_validation_rules': '技术验证规则',
        'content_review_completed': '内容审核完成',
        'compliance_review_completed': '合规审核完成',
        'technical_review_completed': '技术审核完成'
    }
    
    # Error type mappings to Chinese
    ERROR_TYPE_MAPPING = {
        'value_error': '数值错误',
        'type_error': '类型错误',
        'missing': '缺少必填项',
        'extra_forbidden': '不允许的额外字段',
        'const': '常量值错误',
        'enum': '枚举值错误',
        'greater_than': '数值过小',
        'greater_than_equal': '数值过小',
        'less_than': '数值过大',
        'less_than_equal': '数值过大',
        'multiple_of': '数值倍数错误',
        'min_items': '项目数量不足',
        'max_items': '项目数量过多',
        'min_length': '长度不足',
        'max_length': '长度过长',
        'regex': '格式不正确',
        'json_invalid': 'JSON格式错误',
        'url': 'URL格式错误',
        'email': '邮箱格式错误'
    }
    
    @classmethod
    def parse_pydantic_error(cls, error: PydanticValidationError) -> Dict[str, Any]:
        """Parse Pydantic ValidationError into structured field-level errors
        
        Args:
            error: Pydantic ValidationError instance
            
        Returns:
            Structured error information with field-level details
        """
        field_errors = []
        general_errors = []
        
        try:
            for error_detail in error.errors():
                parsed_error = cls._parse_single_error(error_detail)
                
                if parsed_error['field_path']:
                    field_errors.append(parsed_error)
                else:
                    general_errors.append(parsed_error)
                    
        except Exception as e:
            logger.error(f"Failed to parse Pydantic error: {e}")
            # Fallback to simple string representation
            general_errors.append({
                'field_path': '',
                'field_name': '未知字段',
                'error_type': 'unknown',
                'error_message': str(error),
                'user_message': f'数据验证失败: {str(error)}'
            })
        
        return {
            'field_errors': field_errors,
            'general_errors': general_errors,
            'total_errors': len(field_errors) + len(general_errors),
            'has_field_errors': len(field_errors) > 0
        }
    
    @classmethod
    def _parse_single_error(cls, error_detail: Dict[str, Any]) -> Dict[str, Any]:
        """Parse a single error detail from Pydantic
        
        Args:
            error_detail: Single error detail from Pydantic error.errors()
            
        Returns:
            Parsed error information
        """
        # Extract field path
        field_path = '.'.join(str(loc) for loc in error_detail.get('loc', []))
        field_name = cls._get_field_display_name(field_path)
        
        # Extract error information
        error_type = error_detail.get('type', 'unknown')
        error_msg = error_detail.get('msg', 'Unknown error')
        error_input = error_detail.get('input')
        
        # Generate user-friendly message
        user_message = cls._generate_user_message(field_name, error_type, error_msg, error_input)
        
        return {
            'field_path': field_path,
            'field_name': field_name,
            'error_type': error_type,
            'error_message': error_msg,
            'invalid_input': error_input,
            'user_message': user_message
        }
    
    @classmethod
    def _get_field_display_name(cls, field_path: str) -> str:
        """Get user-friendly field display name
        
        Args:
            field_path: Dot-separated field path
            
        Returns:
            User-friendly field name in Chinese
        """
        if not field_path:
            return '未知字段'
            
        # Handle nested fields (e.g., "selling_points.0.title")
        parts = field_path.split('.')
        main_field = parts[0]
        
        # Get the main field name
        display_name = cls.FIELD_NAME_MAPPING.get(main_field, main_field)
        
        # Handle array indices and nested fields
        if len(parts) > 1:
            if parts[1].isdigit():
                # Array index
                index = int(parts[1]) + 1  # Convert to 1-based index
                display_name += f' (第{index}项)'
                
                # Check for nested field after array index
                if len(parts) > 2:
                    nested_field = parts[2]
                    nested_name = cls.FIELD_NAME_MAPPING.get(nested_field, nested_field)
                    display_name += f' - {nested_name}'
            else:
                # Direct nested field
                nested_field = parts[1]
                nested_name = cls.FIELD_NAME_MAPPING.get(nested_field, nested_field)
                display_name += f' - {nested_name}'
        
        return display_name
    
    @classmethod
    def _generate_user_message(cls, field_name: str, error_type: str, error_msg: str, error_input: Any) -> str:
        """Generate user-friendly error message
        
        Args:
            field_name: User-friendly field name
            error_type: Pydantic error type
            error_msg: Original error message
            error_input: Invalid input value
            
        Returns:
            User-friendly error message in Chinese
        """
        # Handle specific error types with custom messages
        if error_type == 'missing':
            return f'{field_name} 是必填项，不能为空'
        
        elif error_type == 'value_error':
            if 'Price must be positive' in error_msg:
                return f'{field_name} 必须大于0'
            elif 'Selling point IDs must be unique' in error_msg:
                return f'{field_name} 的ID不能重复'
            elif 'must be finalized' in error_msg:
                return f'{field_name} 不能包含占位符文本'
            else:
                return f'{field_name} 的值无效: {error_msg}'
        
        elif error_type == 'type_error':
            return f'{field_name} 的数据类型不正确'
        
        elif error_type.startswith('greater_than'):
            return f'{field_name} 的值必须更大'
        
        elif error_type.startswith('less_than'):
            return f'{field_name} 的值必须更小'
        
        elif error_type == 'min_items':
            # Extract minimum count from error message
            if 'at least 1 items' in error_msg:
                return f'{field_name} 至少需要1项'
            elif 'at least 2 items' in error_msg:
                return f'{field_name} 至少需要2项'
            else:
                return f'{field_name} 项目数量不足'
        
        elif error_type == 'max_items':
            return f'{field_name} 项目数量过多'
        
        elif error_type == 'min_length':
            # Extract minimum length from error message
            if 'at least 5 characters' in error_msg or 'ensure this value has at least 5 characters' in error_msg:
                return f'{field_name} 至少需要5个字符'
            elif 'at least 3 characters' in error_msg or 'ensure this value has at least 3 characters' in error_msg:
                return f'{field_name} 至少需要3个字符'
            else:
                return f'{field_name} 长度不足'
        
        elif error_type == 'max_length':
            return f'{field_name} 长度过长'
        
        elif error_type == 'enum':
            return f'{field_name} 的值不在允许的选项中'
        
        else:
            # Fallback to generic message
            error_type_display = cls.ERROR_TYPE_MAPPING.get(error_type, error_type)
            return f'{field_name} {error_type_display}: {error_msg}'
    
    @classmethod
    def format_errors_for_display(cls, parsed_errors: Dict[str, Any]) -> str:
        """Format parsed errors for user display
        
        Args:
            parsed_errors: Parsed error information from parse_pydantic_error
            
        Returns:
            Formatted error message for display
        """
        messages = []
        
        # Add field-specific errors
        for field_error in parsed_errors['field_errors']:
            messages.append(f"• {field_error['user_message']}")
        
        # Add general errors
        for general_error in parsed_errors['general_errors']:
            messages.append(f"• {general_error['user_message']}")
        
        if not messages:
            return "数据验证失败，请检查输入内容"
        
        return '\n'.join(messages)
