[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-live-streamer"
version = "0.1.0"
description = "AI驱动的直播解说系统"
authors = [{name = "AI Live Streamer Team", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langgraph>=0.2.0",
    "langgraph-checkpoint>=1.0.0",
    "pydantic-ai>=0.0.10",
    "elasticsearch>=8.0.0",
    "litellm>=1.0.0",
    "prefect>=3.0.0",
    "pydantic>=2.0.0",
    "fastapi>=0.100.0",
    "uvicorn>=0.23.0",
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "scikit-learn>=1.3.0",
    "sentence-transformers>=2.2.0",
    "openai>=1.0.0",
    "anthropic>=0.20.0",
    "httpx>=0.25.0",
    "aiofiles>=23.0.0",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "tenacity>=8.2.0",
    "pytz>=2023.3",
    "pydub>=0.25.0",
    "soundfile>=0.12.0",
    "edge-tts>=6.1.0",
    "PyPDF2>=3.0.0",
    "python-docx>=0.8.11",
    "markdown>=3.4.0",
    "beautifulsoup4>=4.12.0",
    "PyYAML>=6.0.0",
    # "cosyvoice>=0.0.8",  # Temporarily disabled for testing (onnxruntime-gpu issue)
    "dashscope>=1.20.0",
    # Streaming audio dependencies
    "sounddevice>=0.4.6",
    "websockets>=10.4",
    "aiohttp>=3.8.0",
    "librosa>=0.9.2",
    # QA System v3 dependencies
    "faiss-cpu>=1.7.4",
    "simhash>=2.1.2",
    "jieba>=0.42.1",
    "transformers>=4.36.0",
    "opencc-python-reimplemented>=0.1.7",
    "prometheus-client>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]
redis = [
    "redis>=5.0.0",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.ruff]
target-version = "py311"
line-length = 88
select = ["E", "F", "I", "N", "W", "UP"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
strict = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=src --cov-report=html --cov-report=term-missing"
asyncio_mode = "auto"