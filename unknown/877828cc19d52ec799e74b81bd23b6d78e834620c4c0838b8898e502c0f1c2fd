"""Session storage service for form editing sessions

Manages form editing sessions with expiration handling and cleanup.
"""

from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from loguru import logger

from .database_manager import DatabaseManager
from ...models.forms import FormSession
from ...core.exceptions import ServiceError


class SessionStorage:
    """High-level interface for form session persistence"""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None) -> None:
        """Initialize session storage service
        
        Args:
            db_manager: Database manager instance, creates new one if None
        """
        self.db_manager = db_manager or DatabaseManager()
        
        # Session state synchronization callbacks
        self._sync_callbacks: List[Callable[[str, str], None]] = []
        
        logger.info("Session storage service initialized")
    
    def create_session(self, session: FormSession) -> str:
        """Create new form editing session
        
        Args:
            session: FormSession instance to store
            
        Returns:
            Session ID of created session
            
        Raises:
            ServiceError: If session creation fails
        """
        try:
            session_sql = """
                INSERT INTO form_sessions (
                    session_id, form_id, user_id, started_at, 
                    last_activity_at, expires_at, current_section, unsaved_changes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            session_params = (
                session.session_id,
                session.form_id,
                session.user_id,
                session.started_at.isoformat(),
                session.last_activity_at.isoformat(),
                session.expires_at.isoformat(),
                session.current_section,
                session.unsaved_changes
            )
            
            affected_rows = self.db_manager.execute_update(session_sql, session_params)
            if affected_rows != 1:
                raise ServiceError(f"Failed to create session, affected rows: {affected_rows}", "session_storage")
            
            logger.info(f"Created session {session.session_id} for form {session.form_id}")
            return session.session_id
            
        except Exception as e:
            logger.error(f"Failed to create session {session.session_id}: {e}")
            raise ServiceError(f"Failed to create session: {e}", "session_storage")
    
    def get_session(self, session_id: str) -> Optional[FormSession]:
        """Retrieve form session by ID
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            FormSession instance or None if not found or expired
            
        Raises:
            ServiceError: If database operation fails
        """
        try:
            session_sql = "SELECT * FROM form_sessions WHERE session_id = ?"
            session_rows = self.db_manager.execute_query(session_sql, (session_id,))
            
            if not session_rows:
                return None
            
            session_row = session_rows[0]
            session = self._reconstruct_session(dict(session_row))
            
            # Check if session is expired
            if session.is_expired():
                logger.info(f"Session {session_id} expired, removing from database")
                self.delete_session(session_id)
                return None
            
            logger.debug(f"Retrieved session {session_id} successfully")
            return session
            
        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            raise ServiceError(f"Failed to get session: {e}", "session_storage")
    
    def update_session(self, session: FormSession) -> None:
        """Update existing form session
        
        Args:
            session: Updated FormSession instance
            
        Raises:
            ServiceError: If update operation fails
        """
        try:
            session_sql = """
                UPDATE form_sessions SET
                    last_activity_at = ?, expires_at = ?, current_section = ?,
                    unsaved_changes = ?
                WHERE session_id = ?
            """
            
            session_params = (
                session.last_activity_at.isoformat(),
                session.expires_at.isoformat(),
                session.current_section,
                session.unsaved_changes,
                session.session_id
            )
            
            affected_rows = self.db_manager.execute_update(session_sql, session_params)
            if affected_rows != 1:
                raise ServiceError(f"Session not found or update failed: {session.session_id}", "session_storage")
            
            logger.debug(f"Updated session {session.session_id} successfully")
            
        except Exception as e:
            logger.error(f"Failed to update session {session.session_id}: {e}")
            raise ServiceError(f"Failed to update session: {e}", "session_storage")
    
    def delete_session(self, session_id: str) -> bool:
        """Delete form session
        
        Args:
            session_id: Session ID to delete
            
        Returns:
            True if session was deleted, False if session not found
            
        Raises:
            ServiceError: If delete operation fails
        """
        try:
            session_sql = "DELETE FROM form_sessions WHERE session_id = ?"
            affected_rows = self.db_manager.execute_update(session_sql, (session_id,))
            
            if affected_rows == 0:
                return False
            
            logger.info(f"Deleted session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            raise ServiceError(f"Failed to delete session: {e}", "session_storage")
    
    def list_sessions_for_form(self, form_id: str) -> List[FormSession]:
        """List all active sessions for a specific form
        
        Args:
            form_id: Form ID to get sessions for
            
        Returns:
            List of active FormSession instances
            
        Raises:
            ServiceError: If query fails
        """
        try:
            sessions_sql = """
                SELECT * FROM form_sessions 
                WHERE form_id = ? AND expires_at > ?
                ORDER BY last_activity_at DESC
            """
            
            current_time = datetime.utcnow().isoformat()
            rows = self.db_manager.execute_query(sessions_sql, (form_id, current_time))
            
            sessions = []
            for row in rows:
                session = self._reconstruct_session(dict(row))
                sessions.append(session)
            
            logger.debug(f"Listed {len(sessions)} active sessions for form {form_id}")
            return sessions
            
        except Exception as e:
            logger.error(f"Failed to list sessions for form {form_id}: {e}")
            raise ServiceError(f"Failed to list sessions: {e}", "session_storage")
    
    def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions from database
        
        Returns:
            Number of expired sessions removed
            
        Raises:
            ServiceError: If cleanup operation fails
        """
        try:
            cleanup_sql = "DELETE FROM form_sessions WHERE expires_at <= ?"
            current_time = datetime.utcnow().isoformat()
            
            affected_rows = self.db_manager.execute_update(cleanup_sql, (current_time,))
            
            if affected_rows > 0:
                logger.info(f"Cleaned up {affected_rows} expired sessions")
            
            return affected_rows
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired sessions: {e}")
            raise ServiceError(f"Failed to cleanup expired sessions: {e}", "session_storage")
    
    def extend_session(self, session_id: str, extension_minutes: int = 30) -> Optional[FormSession]:
        """Extend session expiration time
        
        Args:
            session_id: Session ID to extend
            extension_minutes: Number of minutes to extend by
            
        Returns:
            Updated FormSession instance or None if session not found
            
        Raises:
            ServiceError: If extension operation fails
        """
        try:
            session = self.get_session(session_id)
            if not session:
                return None
            
            # Extend session
            session.extend_session(extension_minutes)
            
            # Update in database
            self.update_session(session)
            
            logger.info(f"Extended session {session_id} by {extension_minutes} minutes")
            return session
            
        except Exception as e:
            logger.error(f"Failed to extend session {session_id}: {e}")
            raise ServiceError(f"Failed to extend session: {e}", "session_storage")
    
    def mark_session_activity(self, session_id: str) -> Optional[FormSession]:
        """Mark activity on session to prevent timeout
        
        Args:
            session_id: Session ID to mark activity for
            
        Returns:
            Updated FormSession instance or None if session not found
            
        Raises:
            ServiceError: If update operation fails
        """
        try:
            session = self.get_session(session_id)
            if not session:
                return None
            
            # Mark activity
            session.mark_activity()
            
            # Update in database
            self.update_session(session)
            
            logger.debug(f"Marked activity for session {session_id}")
            return session
            
        except Exception as e:
            logger.error(f"Failed to mark activity for session {session_id}: {e}")
            raise ServiceError(f"Failed to mark session activity: {e}", "session_storage")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get session storage statistics
        
        Returns:
            Dictionary with session statistics
        """
        try:
            current_time = datetime.utcnow().isoformat()
            
            stats_sql = """
                SELECT 
                    COUNT(*) as total_sessions,
                    COUNT(CASE WHEN expires_at > ? THEN 1 END) as active_sessions,
                    COUNT(CASE WHEN expires_at <= ? THEN 1 END) as expired_sessions,
                    COUNT(CASE WHEN unsaved_changes = 1 THEN 1 END) as sessions_with_unsaved_changes
                FROM form_sessions
            """
            
            rows = self.db_manager.execute_query(stats_sql, (current_time, current_time))
            stats = dict(rows[0]) if rows else {}
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get session stats: {e}")
            raise ServiceError(f"Failed to get session stats: {e}", "session_storage")
    
    def _reconstruct_session(self, session_data: Dict[str, Any]) -> FormSession:
        """Reconstruct FormSession object from database data"""
        # Parse datetime fields
        session_data['started_at'] = datetime.fromisoformat(session_data['started_at'])
        session_data['last_activity_at'] = datetime.fromisoformat(session_data['last_activity_at'])
        session_data['expires_at'] = datetime.fromisoformat(session_data['expires_at'])
        
        # Convert boolean fields
        session_data['unsaved_changes'] = bool(session_data['unsaved_changes'])
        session_data['auto_save_enabled'] = True  # Default value
        
        return FormSession(**session_data)
    
    # Session State Synchronization Methods
    
    def register_sync_callback(self, callback: Callable[[str, str], None]) -> None:
        """Register callback for form-to-stream session synchronization
        
        Args:
            callback: Function to call when form changes (form_id, action)
                     Actions: 'form_updated', 'form_completed', 'form_section_changed'
        """
        self._sync_callbacks.append(callback)
        logger.debug(f"Registered sync callback, total callbacks: {len(self._sync_callbacks)}")
    
    def trigger_form_sync(self, form_id: str, action: str = "form_updated") -> None:
        """Trigger synchronization callbacks for form changes
        
        Args:
            form_id: ID of the form that changed
            action: Type of change that occurred
        """
        try:
            for callback in self._sync_callbacks:
                try:
                    callback(form_id, action)
                except Exception as e:
                    logger.error(f"Sync callback failed for form {form_id}: {e}")
            
            logger.debug(f"Triggered {len(self._sync_callbacks)} sync callbacks for form {form_id}")
            
        except Exception as e:
            logger.error(f"Failed to trigger form sync for {form_id}: {e}")
    
    def mark_form_completed(self, form_id: str, completed_by_session: str) -> None:
        """Mark form as completed and trigger completion sync
        
        Args:
            form_id: ID of the completed form
            completed_by_session: Session ID that completed the form
        """
        try:
            # Update session metadata to mark form completion
            session = self.get_session(completed_by_session)
            if session:
                # Note: This would require extending FormSession model to include metadata
                # For now, we trigger the sync callback
                logger.info(f"Form {form_id} completed by session {completed_by_session}")
                self.trigger_form_sync(form_id, "form_completed")
            
        except Exception as e:
            logger.error(f"Failed to mark form {form_id} as completed: {e}")
    
    def update_session_with_sync(self, session: FormSession, trigger_sync: bool = True) -> None:
        """Update session and optionally trigger synchronization
        
        Args:
            session: Updated FormSession instance
            trigger_sync: Whether to trigger sync callbacks
        """
        try:
            # Perform regular update
            self.update_session(session)
            
            # Trigger sync if requested
            if trigger_sync:
                self.trigger_form_sync(session.form_id, "form_section_changed")
            
        except Exception as e:
            logger.error(f"Failed to update session with sync {session.session_id}: {e}")
            raise ServiceError(f"Failed to update session with sync: {e}", "session_storage")
    
    def get_active_session_for_form(self, form_id: str) -> Optional[FormSession]:
        """Get the most recent active session for a form
        
        Args:
            form_id: Form ID to get active session for
            
        Returns:
            Most recent active FormSession or None
        """
        try:
            active_sessions = self.list_sessions_for_form(form_id)
            if active_sessions:
                # Return most recent (sessions are ordered by last_activity_at DESC)
                return active_sessions[0]
            return None
            
        except Exception as e:
            logger.error(f"Failed to get active session for form {form_id}: {e}")
            return None
    
    def sync_session_state_to_stream(self, form_id: str) -> Dict[str, Any]:
        """Get form session state for streaming session synchronization
        
        Args:
            form_id: Form ID to get sync data for
            
        Returns:
            Dictionary with session sync data
        """
        try:
            active_session = self.get_active_session_for_form(form_id)
            if not active_session:
                return {}
            
            return {
                "form_session_id": active_session.session_id,
                "form_id": form_id,
                "last_activity": active_session.last_activity_at.isoformat(),
                "current_section": active_session.current_section,
                "has_unsaved_changes": active_session.unsaved_changes,
                "session_active": not active_session.is_expired(),
                "user_id": active_session.user_id
            }
            
        except Exception as e:
            logger.error(f"Failed to get sync state for form {form_id}: {e}")
            return {}