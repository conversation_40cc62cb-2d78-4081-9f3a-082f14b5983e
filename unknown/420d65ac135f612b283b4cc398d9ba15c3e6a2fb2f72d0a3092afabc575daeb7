"""
WebSocket消息模型定义 (API Contract v2.0)

本模块定义了所有WebSocket消息的Pydantic模型，严格遵循API契约v2.0规范。
这些模型用于验证和序列化WebSocket通信中的消息，确保前后端通信的一致性。
"""

from typing import Dict, Any, Optional, Literal
from pydantic import BaseModel, Field, validator
import time


# ============== 客户端 → 服务端消息 ==============

class ContentRequest(BaseModel):
    """内容请求消息 (API Contract v2.0)"""
    type: Literal["content_request"] = "content_request"
    index: int = Field(..., ge=0, description="请求的内容索引（0-based）")
    request_id: str = Field(..., description="客户端生成的唯一请求ID")
    
    @validator('request_id')
    def validate_request_id(cls, v):
        if not v or len(v) < 5:
            raise ValueError("request_id必须至少包含5个字符")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "type": "content_request",
                "index": 0,
                "request_id": "req_1754123456_abc"
            }
        }


class QAInsertRequest(BaseModel):
    """QA插入请求消息 (API Contract v2.0)"""
    type: Literal["qa_insert"] = "qa_insert"
    question_id: str = Field(..., description="问题ID")
    strategy: Literal["immediate", "next_safe_point", "queue"] = Field(
        ..., description="插入策略"
    )
    request_id: str = Field(..., description="客户端生成的唯一请求ID")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "qa_insert",
                "question_id": "q_1754123456_xyz",
                "strategy": "immediate",
                "request_id": "req_1754123456_def"
            }
        }


class StateSyncRequest(BaseModel):
    """状态同步请求消息 (API Contract v2.0)"""
    type: Literal["state_sync"] = "state_sync"
    request_id: str = Field(..., description="客户端生成的唯一请求ID")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "state_sync",
                "request_id": "req_1754123456_ghi"
            }
        }




# ============== 服务端 → 客户端消息 ==============



class AudioContentMetadata(BaseModel):
    """音频内容元数据"""
    text: str = Field(..., description="音频对应的文本内容")
    type: str = Field(..., description="内容类型")
    is_qa: bool = Field(default=False, description="是否为QA内容")
    # 扩展字段（可选）
    item_id: Optional[str] = Field(None, description="内容项ID")
    playlist_version: Optional[int] = Field(None, description="播放列表版本")


class ContentResponse(BaseModel):
    """音频内容响应消息 (API Contract v2.0)"""
    type: Literal["content"] = "content"
    index: int = Field(..., ge=0, description="内容索引")
    request_id: str = Field(..., description="回显客户端的request_id")
    format: Literal["wav"] = "wav"
    sample_rate: Literal[24000] = 24000
    channels: Literal[1] = 1
    duration_ms: float = Field(..., gt=0, description="音频时长（毫秒）")
    data: str = Field(..., description="Base64编码的WAV音频数据")
    metadata: AudioContentMetadata = Field(..., description="内容元数据")
    # 可选扩展字段
    extensions: Optional[Dict[str, Any]] = Field(None, description="扩展信息")
    
    @validator('data')
    def validate_audio_data(cls, v):
        if not v or len(v) < 100:  # Base64编码的WAV至少应该有一定长度
            raise ValueError("音频数据无效或过短")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "type": "content",
                "index": 0,
                "request_id": "req_1754123456_abc",
                "format": "wav",
                "sample_rate": 24000,
                "channels": 1,
                "duration_ms": 3500,
                "data": "base64_encoded_audio_data...",
                "metadata": {
                    "text": "大家好，欢迎来到直播间！",
                    "type": "greeting",
                    "is_qa": False
                }
            }
        }


class StateUpdate(BaseModel):
    """状态更新消息 (API Contract v2.4)
    
    This message is bound by API_CONTRACT.md v2.4, Section 2
    See: design_docs/API_CONTRACT.md#state-update
    """
    type: Literal["state_update"] = "state_update"
    current_index: int = Field(..., ge=0, description="当前播放索引")
    total_segments: int = Field(..., ge=0, description="总段落数")
    playback_status: Literal["playing", "paused", "stopped", "buffering"] = Field(
        ..., description="播放状态"
    )
    buffer_health: Literal["good", "low", "critical"] = Field(
        ..., description="缓冲区健康状态"
    )
    server_time: float = Field(default_factory=time.time, description="服务器时间戳")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "state_update",
                "current_index": 5,
                "total_segments": 20,
                "playback_status": "playing",
                "buffer_health": "good",
                "server_time": 1754123456789
            }
        }


class ErrorMessage(BaseModel):
    """错误消息 (API Contract v2.0)"""
    type: Literal["error"] = "error"
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    request_id: Optional[str] = Field(None, description="相关的请求ID")
    recoverable: bool = Field(default=True, description="是否可恢复")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "error",
                "error_code": "CONTENT_NOT_FOUND",
                "message": "Content for index 99 not found",
                "request_id": "req_1754123456_abc",
                "recoverable": True
            }
        }


class QAReadyNotification(BaseModel):
    """QA就绪通知消息 (API Contract v2.0)"""
    type: Literal["qa_ready"] = "qa_ready"
    question_id: str = Field(..., description="问题ID")
    insert_after_index: int = Field(..., ge=0, description="插入位置索引")
    question_text: str = Field(..., description="问题文本")
    estimated_duration_ms: float = Field(..., gt=0, description="预计时长（毫秒）")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "qa_ready",
                "question_id": "q_1754123456_xyz",
                "insert_after_index": 5,
                "question_text": "这个产品有什么优惠吗？",
                "estimated_duration_ms": 8000
            }
        }


class ConnectionEstablished(BaseModel):
    """连接建立消息 (API Contract v2.0)"""
    type: Literal["connection_established"] = "connection_established"
    client_id: str = Field(..., description="客户端ID")
    session_id: str = Field(..., description="会话ID")
    server_time: float = Field(default_factory=lambda: time.time() * 1000, 
                              description="服务器时间戳（毫秒）")
    protocol_version: Literal["2.0"] = "2.0"
    
    class Config:
        schema_extra = {
            "example": {
                "type": "connection_established",
                "client_id": "client_123",
                "session_id": "session_1754123456_abc12345",
                "server_time": 1754123456789,
                "protocol_version": "2.0"
            }
        }


# ============== 消息验证工具函数 ==============

def validate_client_message(message_data: Dict[str, Any]) -> BaseModel:
    """
    验证客户端消息是否符合API契约
    
    Args:
        message_data: 原始消息数据
        
    Returns:
        验证后的Pydantic模型实例
        
    Raises:
        ValueError: 消息格式不符合契约
    """
    message_type = message_data.get("type")
    
    if message_type == "content_request":
        return ContentRequest(**message_data)
    elif message_type == "qa_insert":
        return QAInsertRequest(**message_data)
    elif message_type == "state_sync":
        return StateSyncRequest(**message_data)
    else:
        raise ValueError(f"Unknown client message type: {message_type}")


def validate_server_message(message_data: Dict[str, Any]) -> BaseModel:
    """
    验证服务端消息是否符合API契约
    
    Args:
        message_data: 原始消息数据
        
    Returns:
        验证后的Pydantic模型实例
        
    Raises:
        ValueError: 消息格式不符合契约
    """
    message_type = message_data.get("type")
    
    if message_type == "content":
        return ContentResponse(**message_data)
    elif message_type == "state_update":
        return StateUpdate(**message_data)
    elif message_type == "error":
        return ErrorMessage(**message_data)
    elif message_type == "qa_ready":
        return QAReadyNotification(**message_data)
    elif message_type == "connection_established":
        return ConnectionEstablished(**message_data)
    else:
        raise ValueError(f"Unknown server message type: {message_type}")