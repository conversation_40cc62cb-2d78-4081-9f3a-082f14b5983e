"""内存锁管理器 - 单进程场景

警告: 仅在单进程模式下有效，不支持跨进程锁

Author: <PERSON> Code
Date: 2025-01-08
"""

import asyncio
import time
from typing import Dict, Optional
from loguru import logger

from .lock_manager import AbstractLockManager


class MemoryLockManager(AbstractLockManager):
    """内存锁管理器 - 仅单进程有效"""
    
    def __init__(self):
        """初始化内存锁管理器"""
        self._locks: Dict[str, Dict[str, any]] = {}
        self._lock = asyncio.Lock()  # 保护内部数据结构的锁
        
        logger.warning(
            "⚠️ MemoryLockManager仅在单进程模式下有效，"
            "不支持跨进程锁。生产环境请使用FileLockManager"
        )
    
    async def acquire_lock(
        self,
        lock_key: str,
        holder_id: str,
        timeout_seconds: int = 60
    ) -> bool:
        """获取内存锁"""
        async with self._lock:
            current_time = time.time()
            
            if lock_key in self._locks:
                lock_info = self._locks[lock_key]
                # 检查锁是否已过期
                if current_time - lock_info['acquired_at'] < lock_info['timeout']:
                    # 锁仍然有效
                    return False
                else:
                    # 锁已过期，可以获取
                    logger.debug(f"🕒 内存锁已过期: {lock_key}")
            
            # 获取锁
            self._locks[lock_key] = {
                'holder_id': holder_id,
                'acquired_at': current_time,
                'timeout': timeout_seconds
            }
            
            logger.info(f"🔒 内存锁获取成功: {lock_key} (holder: {holder_id})")
            return True
    
    async def release_lock(
        self,
        lock_key: str,
        holder_id: str
    ) -> bool:
        """释放内存锁"""
        async with self._lock:
            if lock_key not in self._locks:
                return False
            
            lock_info = self._locks[lock_key]
            if lock_info['holder_id'] != holder_id:
                logger.warning(
                    f"⚠️ 尝试释放非持有者的锁: {lock_key} "
                    f"(当前持有者: {lock_info['holder_id']}, 请求者: {holder_id})"
                )
                return False
            
            # 释放锁
            del self._locks[lock_key]
            logger.info(f"🔓 内存锁释放成功: {lock_key}")
            return True
    
    def cleanup(self):
        """清理所有锁"""
        self._locks.clear()
        logger.info("🧹 MemoryLockManager cleanup completed")