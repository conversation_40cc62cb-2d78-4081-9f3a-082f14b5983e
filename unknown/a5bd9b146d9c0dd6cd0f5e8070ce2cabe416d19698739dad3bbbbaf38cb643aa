/**
 * Format Utilities
 * 格式化工具函数
 */

const Format = {
    /**
     * 日期格式化
     */
    date(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    /**
     * 相对时间
     */
    timeAgo(date) {
        const now = new Date();
        const past = new Date(date);
        const diff = now - past;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (seconds < 60) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        return this.date(date, 'MM-DD');
    },

    /**
     * 数字格式化
     */
    number(num, decimals = 0) {
        if (typeof num !== 'number') return '-';
        return decimals > 0 ? 
            num.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',') :
            num.toLocaleString();
    },

    /**
     * 文件大小格式化
     */
    fileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 时长格式化（秒转时分秒）
     */
    duration(seconds) {
        if (!seconds || seconds < 0) return '0秒';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟${secs}秒`;
        } else {
            return `${secs}秒`;
        }
    },

    /**
     * 货币格式化
     */
    currency(amount, currency = '¥') {
        if (typeof amount !== 'number') return '-';
        return currency + amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    /**
     * 状态标签映射
     */
    statusLabel(status, type = 'default') {
        const statusMaps = {
            default: {
                'completed': '已完成',
                'draft': '草稿',
                'pending': '待处理',
                'active': '活跃',
                'inactive': '非活跃'
            },
            stream: {
                'stopped': '已停止',
                'running': '直播中',
                'paused': '已暂停',
                'narrating': '解说中',
                'answering': '回答问题中',
                'planning': '准备中'
            },
            form: {
                'completed': '已完成',
                'draft': '草稿',
                'in_progress': '进行中'
            }
        };
        
        return statusMaps[type]?.[status] || status;
    },

    /**
     * 状态CSS类映射
     */
    statusClass(status) {
        const classMap = {
            'completed': 'status-completed',
            'success': 'status-completed',
            'draft': 'status-draft',
            'error': 'status-error',
            'warning': 'status-warning',
            'pending': 'status-pending',
            'online': 'status-online',
            'offline': 'status-offline'
        };
        
        return classMap[status] || 'status-default';
    },

    /**
     * 数字动画
     */
    animateNumber(elementId, start, end, duration = 1500, decimals = 0) {
        const element = typeof elementId === 'string' ? 
            document.getElementById(elementId) : elementId;
        if (!element) return;
        
        const startTime = Date.now();
        const endTime = startTime + duration;
        
        function update() {
            const now = Date.now();
            const progress = Math.min((now - startTime) / duration, 1);
            
            // Easing function (ease-out-quart)
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = start + (end - start) * easeOutQuart;
            
            element.textContent = decimals > 0 ? 
                current.toFixed(decimals) : Math.round(current).toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(update);
            } else {
                // Final value
                element.textContent = decimals > 0 ? 
                    end.toFixed(decimals) : end.toLocaleString();
                element.classList.add('animating-number');
            }
        }
        
        update();
    },

    /**
     * 截断文本
     */
    truncate(text, length = 100, suffix = '...') {
        if (!text || text.length <= length) return text;
        return text.substring(0, length) + suffix;
    },

    /**
     * 高亮搜索关键词
     */
    highlightSearch(text, keyword) {
        if (!keyword || !text) return text;
        const regex = new RegExp(`(${keyword})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
};

window.Format = Format;