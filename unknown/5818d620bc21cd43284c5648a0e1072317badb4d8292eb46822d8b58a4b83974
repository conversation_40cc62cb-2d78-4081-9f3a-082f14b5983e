# AI Live Streamer

> 🎯 AI驱动的智能直播解说系统 - 基于 LangGraph + PydanticAI 的实时内容生成和语音合成平台

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-green.svg)](https://fastapi.tiangolo.com/)
[![LangGraph](https://img.shields.io/badge/LangGraph-0.2.0+-purple.svg)](https://github.com/langchain-ai/langgraph)
[![PydanticAI](https://img.shields.io/badge/PydanticAI-0.0.10+-orange.svg)](https://github.com/pydantic/pydantic-ai)

## 📋 目录

- [系统概述](#系统概述)
- [核心功能](#核心功能)
- [技术架构](#技术架构)
- [快速开始](#快速开始)
- [模块使用指南](#模块使用指南)
- [配置说明](#配置说明)
- [开发指南](#开发指南)
- [性能监控](#性能监控)

## 🎯 系统概述

AI Live Streamer 是一个专业的 AI 驱动直播解说系统，集成了多种先进技术栈，实现自动化内容生成、实时语音合成和智能交互管理。系统特别适用于电商直播、产品展示、教育培训等场景。

### 核心特色

- **🧠 智能内容生成**: 基于 PydanticAI 的个性化脚本规划
- **🔄 状态机编排**: LangGraph 驱动的实时流程控制  
- **🎵 高质量TTS**: Edge TTS 集成的语音合成系统
- **🔍 混合搜索**: Elasticsearch + 向量检索的知识库
- **⚡ 并发处理**: 异步架构支持高并发场景
- **📊 实时监控**: 完整的性能指标和状态追踪

## 🚀 核心功能

### 1. 🎬 自动化脚本生成

- **个性化人设**: 支持多种主播人设和风格定制
- **时段适配**: 根据时段自动调整内容重点和风格
- **卖点整合**: 智能整合产品卖点和营销策略
- **互动预设**: 自动规划问答环节和观众互动

```python
# 启动直播脚本生成
from ai_live_streamer.core.orchestrator import get_orchestrator
from ai_live_streamer.models.persona import PersonaConfig

# 配置主播人设
persona = PersonaConfig(
    persona_id="energetic_host",
    tone="enthusiastic",
    pace={"base_wpm": 180, "sentence_len": "medium"}
)

# 启动直播
session_id = await orchestrator.start_stream(
    persona_config=persona,
    sku_id="PROD001",
    product_name="智能手表",
    brand="TechBrand",
    selling_points=["续航持久", "健康监测", "时尚设计"]
)
```

### 2. 🎵 实时语音合成

- **并发处理**: 支持多路 TTS 请求并发处理
- **优先级队列**: 重要内容优先合成
- **批量优化**: 智能分句和批处理
- **延迟控制**: 严格控制合成延迟在 800ms 内

```python
# TTS 合成使用示例
from ai_live_streamer.services.tts_manager import get_tts_manager
from ai_live_streamer.models.audio import AudioPriority

tts_manager = get_tts_manager()

# 单句合成
chunk_id = await tts_manager.synthesize_text(
    text="欢迎来到我们的直播间！今天为大家推荐这款智能手表。",
    priority=AudioPriority.HIGH
)

# 批量合成
chunk_ids = await tts_manager.synthesize_batch([
    "产品特色：超长续航48小时",
    "健康监测：心率、血氧、睡眠全覆盖", 
    "外观设计：轻薄时尚，多彩表带"
], priority=AudioPriority.NORMAL)

# 等待合成完成
audio_chunk = await tts_manager.wait_for_chunk(chunk_id, timeout_sec=5.0)
```

### 3. 🤖 智能问答交互

- **实时问题处理**: 观众问题实时分析和回答
- **上下文理解**: 基于直播内容的智能问答
- **优雅过渡**: 问答与主播内容的自然衔接
- **多轮对话**: 支持复杂问题的多轮交互

```python
# 添加观众问题
await orchestrator.add_question(
    question_text="这款手表防水吗？",
    priority="high"  # 高优先级问题会打断当前解说
)

# 检查直播状态
status = orchestrator.get_stream_status()
print(f"当前状态: {status['status']}")
print(f"问题队列: {status['question_queue_length']} 个待处理")
```

### 4. 🎛️ 直播控制台

- **Web 操作界面**: 直观的直播管理控制台
- **实时状态监控**: 直播进度、TTS队列、音频播放状态
- **参数动态调整**: 实时修改人设、语速、内容重点
- **紧急控制**: 暂停、恢复、停止等操作

```bash
# 启动控制台服务器
python scripts/run_server.py

# 访问控制台: http://localhost:8000
# - /: 运营配置表单
# - /control: 直播控制面板  
# - /status: 实时状态监控
# - /docs: API 文档
```

## 🏗️ 技术架构

### 核心组件架构

```
┌─────────────────────────────────────────────────────────────┐
│                    AI Live Streamer                        │
├─────────────────────┬───────────────────┬───────────────────┤
│   Frontend Layer    │   API Gateway     │   Control Panel   │
│   (Web Interface)   │   (FastAPI)       │   (Admin Tools)   │
├─────────────────────┼───────────────────┼───────────────────┤
│              Core Orchestrator (LangGraph)                 │
│                (State Machine + Workflow)                  │
├─────────────────────┬───────────────────┬───────────────────┤
│  Content Agents     │   Audio Pipeline  │   Search Engine   │
│  (PydanticAI)       │   (TTS + Player)  │   (Elasticsearch) │
├─────────────────────┼───────────────────┼───────────────────┤
│  Storage Layer      │   Monitoring      │   External APIs   │
│  (Redis + Files)    │   (Metrics)       │   (LLM Providers) │
└─────────────────────┴───────────────────┴───────────────────┘
```

### 技术栈详解

| 组件类别 | 技术选型 | 版本要求 | 用途说明 |
|---------|---------|---------|---------|
| **AI 框架** | LangGraph | 0.2.0+ | 状态机编排和工作流管理 |
| **AI 代理** | PydanticAI | 0.0.10+ | 智能代理和内容生成 |
| **Web 框架** | FastAPI | 0.100+ | RESTful API 和 Web 服务 |
| **搜索引擎** | Elasticsearch | 8.0+ | 文档检索和向量搜索 |
| **缓存数据库** | Redis | 5.0+ | 会话状态和队列管理 |
| **语音合成** | Edge TTS | 6.1+ | 高质量中文语音合成 |
| **LLM 适配** | LiteLLM | 1.0+ | 多模型统一接口 |
| **并发框架** | asyncio | - | 异步编程和并发处理 |

## ⚡ 快速开始

### 环境要求

- Python 3.11+
- Conda 环境管理器
- Elasticsearch 8.x
- Redis 5.x+

### 1️⃣ 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd ai-live-streamer

# 创建 conda 环境
conda create -n aidev python=3.11
conda activate aidev

# 安装依赖
pip install -e .
```

### 2️⃣ 配置环境变量

创建 `.env` 文件（参考 `.env.example`）：

```bash
# 应用配置
APP_ENV=development
DEBUG=true
LOG_LEVEL=INFO
DEFAULT_TIMEZONE=Asia/Shanghai

# 数据库配置
ELASTICSEARCH_URL=http://localhost:9200
REDIS_URL=redis://localhost:6379

# AI 模型配置
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# TTS 配置  
TTS_ENGINE=edge-tts
TTS_VOICE=zh-CN-XiaoxiaoNeural
AUDIO_SAMPLE_RATE=48000

# 性能配置
MAX_TTS_LATENCY_MS=800
MAX_RETRIEVAL_LATENCY_MS=350
```

### 3️⃣ 初始化数据

```bash
# 设置知识库索引
python scripts/test_indices_setup.py

# 测试 ETL 数据管道
python scripts/test_etl_pipeline.py

# 运行完整系统测试
python scripts/complete_system_test.py
```

### 4️⃣ 启动服务

```bash
# 启动 API 服务器
python scripts/run_server.py

# 服务启动后访问:
# - http://localhost:8080 - 操作界面
# - http://localhost:8080/docs - API 文档
# - http://localhost:8080/status - 系统状态
```

### 5️⃣ 使用统一管理控制台

#### 🎯 访问管理控制台
访问：http://localhost:8080

控制台提供统一的界面来管理所有功能：

**主要功能模块**：
- **📊 总览**：系统状态和快速统计
- **📝 配置管理**：创建和管理直播表单配置  
- **📜 脚本预览**：查看和管理生成的直播脚本
- **📈 系统监控**：系统状态和性能监控
- **⚙️ 系统设置**：配置系统参数

#### 🚀 完整操作流程

**步骤 1：创建直播配置**
1. 在控制台点击 "新建配置" 或访问 http://localhost:8080/config
2. 填写 6 个配置部分：
   - **基础信息**：直播标题、类型、时长等
   - **商品信息**：SKU、商品名、价格、规格等  
   - **卖点结构**：核心价值主张、主要卖点
   - **人设配置**：选择主播人设、语音风格
   - **高级设置**：语速、问答超时等
   - **审核提交**：确认并提交配置

**步骤 2：生成和查看脚本**
1. 配置提交成功后，页面会自动显示表单ID和快捷操作按钮
2. 点击"生成脚本"按钮直接生成脚本，或点击"复制"复制表单ID
3. 也可以在控制台"配置管理"中找到对应表单，点击"生成脚本"
4. 脚本生成完成后会自动打开预览页面

**步骤 3：管理配置和脚本**
- 在"配置管理"中查看所有表单，支持编辑和删除
- 在"脚本预览"中查看所有生成的脚本，支持预览和删除
- 在"总览"中查看系统整体状态和统计信息

#### 🔧 命令行操作（可选）

如果需要通过命令行操作，仍然支持API调用：

```bash
# 创建表单
curl -X POST "http://localhost:8080/api/operational/forms" \
  -H "Content-Type: application/json" \
  -d '{"created_by": "admin"}'

# 生成脚本（替换为实际表单ID）
curl -X POST "http://localhost:8080/api/script-preview/generate" \
  -H "Content-Type: application/json" \
  -d '{"form_id": "your_form_id_here"}'

# 查看脚本HTML页面
open "http://localhost:8080/api/script-preview/view/your_form_id_here"
```

#### 📱 控制台功能特色

- **📊 实时统计**：显示表单数量、脚本数量、系统状态
- **🔄 一键操作**：直接在界面中生成脚本、查看预览
- **📋 ID管理**：表单ID一键复制，无需手动输入
- **🚀 快捷生成**：配置页面和控制台都支持直接生成脚本
- **🗑️ 便捷管理**：支持删除表单和脚本，自动同步更新
- **🎨 响应式设计**：支持桌面和移动设备访问
- **⚡ 实时更新**：操作后自动刷新相关数据

## 📚 模块使用指南

### 🎯 核心编排器 (Orchestrator)

编排器是系统的核心控制器，负责协调所有组件的工作。

```python
from ai_live_streamer.core.orchestrator import initialize_orchestrator
from ai_live_streamer.services.search import HybridSearchEngine

# 初始化搜索引擎和编排器
search_engine = HybridSearchEngine()
await search_engine.initialize()

orchestrator = await initialize_orchestrator(search_engine)

# 控制直播流程
session_id = await orchestrator.start_stream(...)
await orchestrator.pause_stream()     # 暂停
await orchestrator.resume_stream()    # 恢复  
await orchestrator.stop_stream()      # 停止
```

### 🧠 智能代理 (Agents)

系统包含三个核心 AI 代理，各司其职：

#### 1. 叙述规划器 (Narrative Planner)

```python
from ai_live_streamer.agents.narrative_planner import create_narrative_planner
from ai_live_streamer.models.persona import PersonaConfig

planner = create_narrative_planner(search_engine)

# 生成直播纲要
context = PersonaContext(
    persona=persona_config,
    sku_id="PROD001", 
    product_name="智能手表",
    brand="TechBrand",
    target_duration_minutes=75
)

outline = await planner.generate_outline(context)
print(f"生成 {len(outline.segments)} 个段落，总时长 {outline.total_duration_minutes} 分钟")
```

#### 2. 问答处理器 (Q&A Handler)

```python
from ai_live_streamer.agents.qa_handler import create_qa_handler

qa_handler = create_qa_handler(search_engine)

# 处理观众问题
response = await qa_handler.handle_question(
    question="这款手表电池能用多久？",
    context=live_stream_state
)
```

#### 3. 过渡生成器 (Transition Generator)

```python
from ai_live_streamer.agents.transition_generator import create_transition_generator

transition_gen = create_transition_generator(search_engine)

# 生成平滑过渡内容
transition = await transition_gen.generate_transition(
    from_context="产品介绍",
    to_context="问答环节", 
    state=live_stream_state
)
```

### 🎵 音频管理 (Audio Pipeline)

音频管道包含 TTS 合成和播放管理两个组件。

#### TTS 管理器

```python
from ai_live_streamer.services.tts_manager import get_tts_manager

tts = get_tts_manager()
await tts.start()

# 不同优先级的合成
urgent_id = await tts.synthesize_text("紧急通知内容", AudioPriority.URGENT)
normal_id = await tts.synthesize_text("常规解说内容", AudioPriority.NORMAL)

# 监控合成状态
stats = tts.get_queue_stats()
print(f"队列中有 {stats['queue_size']} 个任务，{stats['active_requests']} 个正在处理")
```

#### 音频播放器

```python
from ai_live_streamer.services.audio_player import get_audio_player

player = get_audio_player()
player.initialize_audio_player()

# 播放控制
player.queue_audio(audio_chunk)
player.pause_playback()
player.resume_playback()

# 获取播放状态
status = player.get_queue_status()
```

### 🔍 搜索引擎 (Search Engine)

混合搜索引擎结合了传统检索和向量搜索。

```python
from ai_live_streamer.services.search import HybridSearchEngine

search = HybridSearchEngine()
await search.initialize()

# 混合搜索
results = await search.hybrid_search(
    index_name="knowledge_base",
    query_text="智能手表续航",
    field_filters={"category": "electronics"},
    top_k=5,
    rrf_window_size=50
)

# 向量搜索
vector_results = await search.vector_search(
    index_name="style_corpus", 
    query_text="开场白模板",
    top_k=3
)
```

### 📊 数据处理 (ETL Pipeline)

ETL 管道负责知识库数据的提取、转换和加载。

```python
from ai_live_streamer.services.etl.pipeline_manager import PipelineManager

pipeline = PipelineManager()

# 处理产品知识
await pipeline.process_knowledge_documents([
    "product_specs.pdf",
    "user_manual.docx", 
    "marketing_materials.txt"
])

# 处理风格语料
await pipeline.process_style_corpus([
    "host_scripts.txt",
    "interaction_examples.txt"
])

# 获取处理状态
status = pipeline.get_processing_status()
```

### 🎛️ Web API 接口

系统提供完整的 RESTful API 接口。

```python
# 使用 requests 调用 API
import requests

base_url = "http://localhost:8000"

# 提交运营表单
form_data = {
    "sku_id": "PROD001",
    "product_name": "智能手表",
    "brand": "TechBrand", 
    "persona_id": "energetic_host",
    "selling_points": ["续航", "健康", "设计"]
}

response = requests.post(f"{base_url}/api/operational-form", json=form_data)

# 控制直播
requests.post(f"{base_url}/api/control/start")
requests.post(f"{base_url}/api/control/pause") 
requests.post(f"{base_url}/api/control/resume")

# 添加问题
requests.post(f"{base_url}/api/control/question", json={
    "question": "这款手表防水吗？",
    "priority": "high"
})

# 获取状态
status = requests.get(f"{base_url}/api/status").json()
```

## ⚙️ 配置说明

### 环境变量配置

系统严格遵循 Twelve-Factor App 原则，所有配置通过环境变量管理：

```bash
# === 核心配置 ===
APP_ENV=development          # 应用环境: development/staging/production
DEBUG=true                   # 调试模式
LOG_LEVEL=INFO              # 日志级别: DEBUG/INFO/WARNING/ERROR
DEFAULT_TIMEZONE=Asia/Shanghai  # 默认时区

# === 数据库配置 ===
ELASTICSEARCH_URL=http://localhost:9200    # ES 连接地址
ELASTICSEARCH_USERNAME=elastic             # ES 用户名 (可选)
ELASTICSEARCH_PASSWORD=password            # ES 密码 (可选)
REDIS_URL=redis://localhost:6379          # Redis 连接地址

# === AI 模型配置 ===
OPENAI_API_KEY=sk-xxx                     # OpenAI API 密钥
ANTHROPIC_API_KEY=sk-ant-xxx             # Anthropic API 密钥  
LITELLM_MASTER_KEY=sk-xxx                # LiteLLM 主密钥

# === TTS 配置 ===
TTS_ENGINE=edge-tts                       # TTS 引擎: edge-tts/azure-tts
TTS_VOICE=zh-CN-XiaoxiaoNeural           # TTS 声音
AUDIO_SAMPLE_RATE=48000                  # 音频采样率

# === 性能调优 ===
MAX_TTS_LATENCY_MS=800                   # TTS 最大延迟 (毫秒)
MAX_RETRIEVAL_LATENCY_MS=350             # 检索最大延迟 (毫秒)  
MAX_RERANK_LATENCY_MS=300                # 重排最大延迟 (毫秒)
MAX_FIRST_TOKEN_LATENCY_MS=800           # 首Token最大延迟 (毫秒)

# === 功能开关 ===
ENABLE_RERANK=true                       # 启用重排功能
ENABLE_WELCOME_BOT=true                  # 启用欢迎机器人
ENABLE_PROACTIVE_ENGAGEMENT=true         # 启用主动互动
ENABLE_DIVERSITY_ENGINE=true             # 启用多样性引擎

# === 直播配置 ===
STREAM_DURATION_MINUTES=90               # 默认直播时长 (分钟)
VIEWER_COUNT_API_URL=http://localhost:8000/api/viewers  # 观众数API
```

### 人设配置

支持多种主播人设，可通过 JSON 配置：

```json
{
  "persona_id": "energetic_host",
  "name": "活力主播",
  "tone": "enthusiastic",
  "pace": {
    "base_wpm": 180,
    "sentence_len": "medium",
    "pause_duration": "short"
  },
  "style_features": {
    "use_emoji": true,
    "interaction_frequency": "high",
    "energy_level": "very_high"
  },
  "time_adaptations": {
    "morning": ["清新", "活力"],
    "afternoon": ["专业", "详细"],  
    "evening": ["轻松", "互动"]
  }
}
```

## 🛠️ 开发指南

### 代码质量要求

项目严格遵循 CLAUDE.md 中的编码规范：

1. **Fail-Fast 原则**: 所有错误立即抛出，禁止 fallback 机制
2. **文件大小限制**: 单文件不超过 1000 行
3. **类型注解**: 所有函数必须有类型注解  
4. **日志规范**: 禁用 print()，必须使用标准 logging
5. **配置管理**: 通过环境变量管理所有配置

### 开发环境设置

```bash
# 激活开发环境 (必须)
conda activate aidev

# 安装开发依赖
pip install -e ".[dev]"

# 代码质量检查
black --check .                    # 代码格式化检查
ruff check .                       # 代码风格检查  
mypy --strict src/                 # 类型检查

# 运行测试
pytest tests/ --cov=src --cov-report=html
```

### 新功能开发流程

1. **需求分析**: 明确功能需求和技术方案
2. **接口设计**: 设计清晰的模块接口
3. **实现开发**: 遵循编码规范实现功能
4. **单元测试**: 编写完整的单元测试
5. **集成测试**: 验证与其他模块的集成
6. **文档更新**: 更新相关文档和示例

### 测试策略

```bash
# 单元测试
pytest tests/unit/ -v

# 集成测试  
pytest tests/integration/ -v

# 端到端测试
pytest tests/e2e/ -v

# 完整系统测试
python scripts/complete_system_test.py
```

## 📊 性能监控

### 关键性能指标

| 指标类别 | 指标名称 | 目标值 | 监控方法 |
|---------|---------|--------|----------|
| **延迟** | TTS 合成延迟 | < 800ms | `tts_manager.get_queue_stats()` |
| **延迟** | 检索延迟 | < 350ms | `search_engine.get_performance_stats()` |
| **延迟** | 首Token延迟 | < 800ms | Agent 调用监控 |
| **吞吐** | TTS 并发数 | ≤ 4 | 并发限制监控 |
| **队列** | 问题队列长度 | < 10 | `orchestrator.get_stream_status()` |
| **状态** | 系统健康度 | 100% | `/health` 端点检查 |

### 监控接口

```python
# 获取全局系统状态
status = orchestrator.get_stream_status()
"""
{
  "status": "narrating",
  "current_segment": 3,
  "total_segments": 8, 
  "progress_percentage": 37.5,
  "question_queue_length": 2,
  "tts_queue_stats": {"queue_size": 5, "active_requests": 2},
  "audio_player_stats": {"queue_length": 3, "playing": true}
}
"""

# TTS 性能统计
tts_stats = tts_manager.get_queue_stats()
"""
{
  "queue_size": 5,
  "active_requests": 2,  
  "completed_chunks": 128,
  "is_running": true
}
"""

# 搜索引擎性能
search_stats = search_engine.get_performance_stats()
"""
{
  "avg_latency_ms": 245,
  "total_queries": 156,
  "cache_hit_rate": 0.78
}
"""
```

### 性能优化建议

1. **TTS 优化**: 
   - 预分句处理减少合成次数
   - 优先级队列确保重要内容优先处理
   - 并发控制避免资源竞争

2. **搜索优化**:
   - 合理配置索引分片和副本
   - 使用查询缓存减少重复计算
   - 控制返回结果大小

3. **内存优化**:
   - 及时清理完成的音频块
   - 控制队列大小避免内存堆积
   - 使用对象池复用大对象

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！请遵循以下规范：

1. **Issue**: 清楚描述问题和复现步骤
2. **PR**: 包含详细的变更说明和测试用例  
3. **代码**: 严格遵循项目编码规范
4. **测试**: 确保所有测试通过
5. **文档**: 更新相关文档和示例

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

---

<div align="center">

**🎯 AI Live Streamer - 让每一场直播都充满智能的力量**

[⭐ Star](https://github.com/your-repo/ai-live-streamer) | [🐛 Report Bug](https://github.com/your-repo/ai-live-streamer/issues) | [💡 Request Feature](https://github.com/your-repo/ai-live-streamer/issues)

</div>