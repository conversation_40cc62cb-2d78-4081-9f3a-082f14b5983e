<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI直播控制面板</title>
    
    <!-- Global Design System CSS -->
    <link rel="stylesheet" href="./static/css/global-design-system.css?v=1.0.0">
    
    <!-- UI Components Library -->
    <script src="./static/js/ui-components.js?v=1.0.0"></script>
    
    <!-- Session Manager - 统一的会话状态管理 -->
    <script src="./static/js/session_manager.js?v=1.0.0"></script>
    
    <!-- Web Audio Player V2 Modular Scripts -->
    <script src="./static/js/audio_config.js?v=2.2.0"></script>
    <script src="./static/js/connection.js?v=2.2.0"></script>
    <script src="./static/js/audio_player.js?v=2.2.0"></script>
    <script src="./static/js/protocol.js?v=2.2.0"></script>
    <script src="./static/js/web_audio_player_v2.js?v=2.2.0"></script>
    <script src="./static/js/player_loader.js?v=2.2.0"></script>
    
    <!-- WebSocket架构重构：新增的WebSocket管理模块 -->
    <script src="./static/js/websocket_schema.js?v=1.0.0"></script>
    <script src="./static/js/websocket_reconnect.js?v=1.0.0"></script>
    
    <style>
        /* Page Specific Styles */
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-full);
            backdrop-filter: blur(10px);
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #fc8181;
            animation: pulse 2s ease-in-out infinite;
        }
        
        .status-dot.online {
            background: #48bb78;
        }
        
        .status-dot.connecting {
            background: #f6ad55;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
        }
        
        .info-item {
            background: var(--bg-secondary);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            border-left: 3px solid var(--primary-color);
        }
        
        .info-label {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: 1.125rem;
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .script-timeline {
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            max-height: 300px;
            overflow-y: auto;
        }
        
        .timeline-item {
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            border-left: 3px solid transparent;
            transition: all var(--transition-base);
            cursor: pointer;
        }
        
        .timeline-item:hover {
            transform: translateX(5px);
            box-shadow: var(--shadow-card);
        }
        
        .timeline-item.active {
            border-left-color: var(--primary-color);
            background: var(--primary-bg);
        }
        
        .timeline-item.played {
            opacity: 0.6;
            border-left-color: var(--success-color);
        }
        
        .content-preview {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-md);
            font-size: 1rem;
            line-height: 1.8;
            color: var(--text-primary);
            min-height: 200px;
            max-height: 300px;
            overflow: hidden;
            position: relative;
        }
        
        .sentence-item {
            padding: var(--spacing-sm) var(--spacing-md);
            margin-bottom: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: all 0.3s ease;
            opacity: 0.6;
            transform: scale(0.95);
        }
        
        .sentence-item.prev-2 {
            opacity: 0.3;
            transform: scale(0.9) translateY(-5px);
        }
        
        .sentence-item.prev-1 {
            opacity: 0.5;
            transform: scale(0.93);
        }
        
        .sentence-item.current {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
            font-weight: 500;
            opacity: 1;
            transform: scale(1);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }
        
        .sentence-item.next-1 {
            opacity: 0.5;
            transform: scale(0.93);
        }
        
        .sentence-item.next-2 {
            opacity: 0.3;
            transform: scale(0.9) translateY(5px);
        }
        
        @keyframes slideUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-20px);
            }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
        }
        
        .stat-card {
            background: var(--bg-primary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-md);
            text-align: center;
            border: 1px solid var(--border-light);
            transition: all var(--transition-base);
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-card);
        }
        
        .stat-number {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: var(--text-muted);
        }
        
        .question-item {
            background: var(--bg-primary);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-sm);
            border-left: 3px solid var(--border-color);
            transition: all var(--transition-base);
            animation: fadeInUp 0.3s ease;
        }
        
        .question-item.high {
            border-left-color: var(--error-color);
        }
        
        .question-item.medium {
            border-left-color: var(--warning-color);
        }
        
        .question-item.low {
            border-left-color: var(--info-color);
        }
        
        .question-item.processing {
            background: var(--info-bg);
            border-left-color: var(--info-color);
        }
        
        .question-item.answered {
            background: var(--success-bg);
            border-left-color: var(--success-color);
        }
        
        .collapsible-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            user-select: none;
            padding: var(--spacing-sm) 0;
        }
        
        .collapsible-header:hover {
            color: var(--primary-color);
        }
        
        .collapse-icon {
            transition: transform var(--transition-base);
        }
        
        .collapse-icon.expanded {
            transform: rotate(90deg);
        }
        
        .collapsible-content {
            overflow: hidden;
            transition: max-height var(--transition-slow);
            max-height: 0;
        }
        
        .collapsible-content.expanded {
            max-height: 600px;
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <script src="./static/js/live_control_panel.js?v=2.0.1" defer></script>
    
    <!-- Header with Navigation -->
    <div class="header">
        <div class="container">
            <div class="header-content flex justify-between items-center">
                <div>
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; opacity: 0.9; font-size: 0.875rem; color: white;">
                        <a href="./" style="color: inherit; text-decoration: none;">首页</a>
                        <span>></span>
                        <span>直播控制</span>
                    </div>
                    <h1 style="margin: 0; font-size: 1.75rem; color: white;">
                        🎬 AI直播控制面板
                    </h1>
                    <div style="margin-top: 8px; opacity: 0.9; color: white;">
                        实时控制和监控您的AI直播
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 16px;">
                    <div class="status-indicator">
                        <span class="status-dot" id="statusDot"></span>
                        <span id="statusText" style="color: white; font-weight: 500;">系统离线</span>
                    </div>
                    <a href="./" class="btn btn-secondary" style="margin-left: 16px;">返回控制台</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container" style="padding-top: 2rem; padding-bottom: 2rem;">

        <!-- Stream Source Info -->
        <div class="card animate-fadeInUp" id="streamSourceInfo" style="display: none; margin-bottom: var(--spacing-lg);">
            <div class="card-header">
                <h3 class="card-title">🎬 直播脚本信息</h3>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">脚本标题</div>
                        <div class="info-value" id="streamTitle">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">产品</div>
                        <div class="info-value" id="productName">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">公司</div>
                        <div class="info-value" id="companyName">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">来源表单</div>
                        <div class="info-value" id="sourceFormId" style="font-family: var(--font-mono); font-size: 0.875rem;">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">脚本段落数</div>
                        <div class="info-value" id="scriptSegmentsCount">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">播放状态</div>
                        <div class="info-value" id="contentPlaybackStatus">--</div>
                    </div>
                </div>
                
                <!-- 播放状态详情 -->
                <div id="playbackStatusDetails" class="alert alert-info" style="display: none;">
                    <div id="playbackStatusMessage"></div>
                    <div id="playbackWarning" style="color: var(--warning-color); font-size: 0.875rem; margin-top: 4px;"></div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/" class="btn btn-secondary">
                    ← 返回管理控制台
                </a>
            </div>
        </div>

        <!-- 当前播放内容与脚本预览 -->
        <div class="card animate-fadeInUp" id="currentContentInfo" style="animation-delay: 0.1s; animation-fill-mode: both; margin-bottom: var(--spacing-lg);">
            <div class="card-header">
                <div class="collapsible-header" onclick="toggleScriptPreview()">
                    <h3 class="card-title">🎤 当前播放内容与脚本预览</h3>
                    <span class="collapse-icon">▶</span>
                </div>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">内容预览</label>
                    <div class="content-preview" id="sentenceScrollContainer" style="position: relative; overflow: hidden;">
                        <div id="sentenceCarousel" style="position: relative; transition: transform 0.5s ease-out;">
                            <!-- 5句话的容器将通过JS动态生成 -->
                        </div>
                        <div id="sentenceProgress" style="position: absolute; bottom: 10px; right: 10px; font-size: 0.875rem; color: var(--text-muted); background: rgba(255, 255, 255, 0.9); padding: 4px 8px; border-radius: 4px;">
                            等待开始播放...
                        </div>
                    </div>
                    <!-- 保留原有的简单预览作为fallback -->
                    <div id="currentContentPreview" style="display: none;" class="content-preview">--</div>
                </div>
                
                <div class="collapsible-content" id="scriptTimelineContent">
                    <div class="script-timeline" id="scriptTimeline">
                        <div style="text-align: center; color: var(--text-muted); padding: var(--spacing-xl);">
                            正在加载脚本内容...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 音频流控制面板 -->
        <div class="card animate-fadeInUp" style="animation-delay: 0.2s; animation-fill-mode: both; margin-bottom: var(--spacing-lg);">
            <div class="card-header">
                <h3 class="card-title">🎵 音频流控制</h3>
            </div>
            <div class="card-body">
                <div class="form-group" style="margin-bottom: var(--spacing-md);">
                    <label class="form-label">直播模式</label>
                    <div class="flex gap-md">
                        <label class="form-radio">
                            <input type="radio" name="stream-mode" value="script_based" checked>
                            <span>脚本播放 (Script-Based)</span>
                        </label>
                        <label class="form-radio">
                            <input type="radio" name="stream-mode" value="smart">
                            <span>连续生成 (Micro-Loop)</span>
                        </label>
                    </div>
                </div>
                <div class="flex gap-md" style="margin-bottom: var(--spacing-md);">
                    <button class="btn btn-success btn-lg" id="startAudioStreamBtn" onclick="startAudioStreaming()">
                        🎵 启动音频流
                    </button>
                    <button class="btn btn-primary btn-lg" id="enableAudioPlayBtn" onclick="enableAudioPlayback()" style="display: none;">
                        🔊 启用音频系统
                    </button>
                    <button class="btn btn-danger btn-lg" id="gracefulStopBtn" onclick="requestGracefulStop()" style="display: none;">
                        ⏹️ 停止直播
                    </button>
                </div>
                <div class="alert alert-info" style="margin-bottom: 0;">
                    <span id="audioStreamInfo">音频流未启动</span>
                </div>
            </div>
        </div>

        <!-- 问题提交 -->
        <div class="card animate-fadeInUp" style="animation-delay: 0.3s; animation-fill-mode: both;">
            <div class="card-header">
                <h3 class="card-title">💬 观众问题管理</h3>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <div class="flex gap-md">
                        <input type="text" class="form-control" id="questionText" placeholder="输入观众问题..." maxlength="200" style="flex: 1;">
                        <select class="form-control form-select" id="questionPriority" style="width: 150px;">
                            <option value="low">低优先级</option>
                            <option value="medium" selected>中优先级</option>
                            <option value="high">高优先级</option>
                        </select>
                        <button class="btn btn-primary" id="questionSubmitBtn" onclick="submitQuestion()">
                            提交问题
                        </button>
                    </div>
                </div>
                
                <div class="stats-grid" id="questionStats">
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">总处理数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0%</div>
                        <div class="stat-label">成功率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0%</div>
                        <div class="stat-label">音频成功率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0ms</div>
                        <div class="stat-label">平均响应时间</div>
                    </div>
                </div>
                
                <div style="margin-top: var(--spacing-lg);">
                    <h4 style="margin-bottom: var(--spacing-md);">问题队列</h4>
                    <div id="questionQueue">
                        <div style="text-align: center; color: var(--text-muted); padding: var(--spacing-xl); background: var(--bg-secondary); border-radius: var(--radius-md);">
                            暂无问题
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize UI components when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize any tabs if present
            const tabContainers = document.querySelectorAll('[data-tabs]');
            tabContainers.forEach(container => UI.Tabs.init(container));
            
            // Add smooth scrolling to all anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        UI.smoothScroll(target);
                    }
                });
            });
        });
        
        // Script preview toggle function (if not defined in live_control_panel.js)
        function toggleScriptPreview() {
            const content = document.getElementById('scriptTimelineContent');
            const icon = document.querySelector('.collapse-icon');
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                icon.classList.add('expanded');
            }
        }
    </script>
</body>
</html>