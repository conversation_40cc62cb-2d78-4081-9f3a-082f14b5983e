"""系统监控模块

提供全系统的健康检查、性能监控和异常告警功能。

Author: Claude Code
Date: 2025-08-08
"""

import asyncio
import psutil
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
from loguru import logger

from ..core.streaming_config import StreamingConfig


@dataclass
class SystemHealth:
    """系统健康状态"""
    is_healthy: bool
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    active_connections: int
    error_rate: float
    response_time_ms: float
    alerts: List[str]
    last_check: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "is_healthy": self.is_healthy,
            "cpu_percent": round(self.cpu_percent, 2),
            "memory_percent": round(self.memory_percent, 2),
            "disk_percent": round(self.disk_percent, 2),
            "active_connections": self.active_connections,
            "error_rate": round(self.error_rate, 4),
            "response_time_ms": round(self.response_time_ms, 2),
            "alerts": self.alerts,
            "last_check": self.last_check.isoformat()
        }


@dataclass
class ComponentHealth:
    """组件健康状态"""
    name: str
    is_healthy: bool
    status: str
    metrics: Dict[str, Any]
    last_error: Optional[str]
    last_check: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "is_healthy": self.is_healthy,
            "status": self.status,
            "metrics": self.metrics,
            "last_error": self.last_error,
            "last_check": self.last_check.isoformat()
        }


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, config: StreamingConfig):
        self.config = config
        
        # 监控状态
        self._is_running = False
        self._monitor_task: Optional[asyncio.Task] = None
        
        # 历史数据
        self._health_history: List[SystemHealth] = []
        self._component_health: Dict[str, ComponentHealth] = {}
        
        # 阈值配置
        self.thresholds = {
            "cpu_warning": 70.0,
            "cpu_critical": 85.0,
            "memory_warning": 80.0,
            "memory_critical": 90.0,
            "disk_warning": 85.0,
            "disk_critical": 95.0,
            "error_rate_warning": 0.05,  # 5%
            "error_rate_critical": 0.15,  # 15%
            "response_time_warning": 1000.0,  # 1秒
            "response_time_critical": 3000.0   # 3秒
        }
        
        # 统计计数
        self._check_count = 0
        self._alert_count = 0
        self._start_time = datetime.utcnow()
        
        logger.info("系统监控器初始化完成")
        
    async def start(self) -> None:
        """启动监控"""
        if self._is_running:
            logger.warning("系统监控器已在运行")
            return
            
        self._is_running = True
        check_interval = self.config.get('monitoring.health_check_interval_seconds', 30)
        
        self._monitor_task = asyncio.create_task(self._monitoring_loop(check_interval))
        logger.info(f"系统监控器已启动，检查间隔: {check_interval}秒")
        
    async def stop(self) -> None:
        """停止监控"""
        if not self._is_running:
            return
            
        self._is_running = False
        
        if self._monitor_task and not self._monitor_task.done():
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
                
        logger.info("系统监控器已停止")
        
    async def _monitoring_loop(self, interval: int) -> None:
        """监控主循环"""
        logger.info("系统监控循环已启动")
        
        while self._is_running:
            try:
                # 执行健康检查
                health = await self.check_system_health()
                
                # 记录历史
                self._health_history.append(health)
                
                # 保持历史记录大小
                max_history = self.config.get('monitoring.max_history_entries', 1000)
                if len(self._health_history) > max_history:
                    self._health_history = self._health_history[-max_history//2:]
                    
                # 处理告警
                if health.alerts:
                    await self._handle_alerts(health.alerts)
                    
                self._check_count += 1
                
                # 等待下一次检查
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(interval * 2)  # 错误后延长等待
                
        logger.info("系统监控循环已退出")
        
    async def check_system_health(self) -> SystemHealth:
        """检查系统健康状态"""
        check_start = time.time()
        alerts = []
        
        try:
            # 获取系统资源使用情况
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            memory_percent = memory.percent
            disk_percent = disk.percent
            
            # 检查系统资源阈值
            if cpu_percent >= self.thresholds["cpu_critical"]:
                alerts.append(f"严重告警: CPU使用率过高 ({cpu_percent:.1f}%)")
            elif cpu_percent >= self.thresholds["cpu_warning"]:
                alerts.append(f"警告: CPU使用率较高 ({cpu_percent:.1f}%)")
                
            if memory_percent >= self.thresholds["memory_critical"]:
                alerts.append(f"严重告警: 内存使用率过高 ({memory_percent:.1f}%)")
            elif memory_percent >= self.thresholds["memory_warning"]:
                alerts.append(f"警告: 内存使用率较高 ({memory_percent:.1f}%)")
                
            if disk_percent >= self.thresholds["disk_critical"]:
                alerts.append(f"严重告警: 磁盘使用率过高 ({disk_percent:.1f}%)")
            elif disk_percent >= self.thresholds["disk_warning"]:
                alerts.append(f"警告: 磁盘使用率较高 ({disk_percent:.1f}%)")
                
            # 获取应用层指标
            active_connections = await self._get_active_connections_count()
            error_rate = await self._calculate_error_rate()
            response_time_ms = (time.time() - check_start) * 1000
            
            # 检查应用层阈值
            if error_rate >= self.thresholds["error_rate_critical"]:
                alerts.append(f"严重告警: 错误率过高 ({error_rate:.2%})")
            elif error_rate >= self.thresholds["error_rate_warning"]:
                alerts.append(f"警告: 错误率较高 ({error_rate:.2%})")
                
            if response_time_ms >= self.thresholds["response_time_critical"]:
                alerts.append(f"严重告警: 响应时间过长 ({response_time_ms:.1f}ms)")
            elif response_time_ms >= self.thresholds["response_time_warning"]:
                alerts.append(f"警告: 响应时间较长 ({response_time_ms:.1f}ms)")
                
            # 判断整体健康状态
            is_healthy = (
                len(alerts) == 0 or
                not any("严重告警" in alert for alert in alerts)
            )
            
            return SystemHealth(
                is_healthy=is_healthy,
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                active_connections=active_connections,
                error_rate=error_rate,
                response_time_ms=response_time_ms,
                alerts=alerts,
                last_check=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return SystemHealth(
                is_healthy=False,
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_percent=0.0,
                active_connections=0,
                error_rate=1.0,
                response_time_ms=999999.0,
                alerts=[f"健康检查失败: {e}"],
                last_check=datetime.utcnow()
            )
            
    async def check_component_health(self, component_name: str, check_func: callable) -> ComponentHealth:
        """检查组件健康状态"""
        check_start = datetime.utcnow()
        
        try:
            # 执行组件检查
            result = await check_func()
            
            is_healthy = result.get("is_healthy", True)
            status = result.get("status", "unknown")
            metrics = result.get("metrics", {})
            last_error = result.get("last_error")
            
            component_health = ComponentHealth(
                name=component_name,
                is_healthy=is_healthy,
                status=status,
                metrics=metrics,
                last_error=last_error,
                last_check=check_start
            )
            
            # 更新组件健康记录
            self._component_health[component_name] = component_health
            
            return component_health
            
        except Exception as e:
            logger.error(f"组件 {component_name} 健康检查失败: {e}")
            
            component_health = ComponentHealth(
                name=component_name,
                is_healthy=False,
                status="error",
                metrics={},
                last_error=str(e),
                last_check=check_start
            )
            
            self._component_health[component_name] = component_health
            return component_health
            
    async def _get_active_connections_count(self) -> int:
        """获取活跃连接数"""
        try:
            from ..core.dependencies import get_websocket_v2_handler
            handler = get_websocket_v2_handler()
            stats = await handler.get_connection_stats()
            return stats.get("active_connections", 0)
        except Exception:
            return 0
            
    async def _calculate_error_rate(self) -> float:
        """计算错误率"""
        try:
            from ..core.dependencies import get_content_provider
            provider = get_content_provider()
            stats = await provider.get_stats()
            
            total_requests = stats["request_stats"]["total_requests"]
            failed_requests = stats["request_stats"]["failed_requests"]
            
            if total_requests == 0:
                return 0.0
                
            return failed_requests / total_requests
            
        except Exception:
            return 0.0
            
    async def _handle_alerts(self, alerts: List[str]) -> None:
        """处理告警"""
        for alert in alerts:
            if "严重告警" in alert:
                logger.error(f"🚨 {alert}")
                self._alert_count += 1
            else:
                logger.warning(f"⚠️ {alert}")
                
        # 这里可以添加告警通知逻辑（邮件、webhook等）
        
    async def get_comprehensive_health_report(self) -> Dict[str, Any]:
        """获取综合健康报告"""
        try:
            # 获取最新的系统健康状态
            current_health = await self.check_system_health()
            
            # 获取所有组件健康状态
            from ..core.dependencies import (
                get_playlist_manager, get_client_state_tracker,
                get_content_provider, get_proactive_synthesizer
            )
            
            components = [
                ("playlist_manager", get_playlist_manager),
                ("client_tracker", get_client_state_tracker),
                ("content_provider", get_content_provider),
                ("proactive_synthesizer", get_proactive_synthesizer)
            ]
            
            component_reports = {}
            for name, getter in components:
                try:
                    component = getter()
                    if hasattr(component, 'get_health_status'):
                        health_check = lambda: component.get_health_status()
                        component_health = await self.check_component_health(name, health_check)
                        component_reports[name] = component_health.to_dict()
                    else:
                        component_reports[name] = {
                            "name": name,
                            "is_healthy": True,
                            "status": "no_health_check",
                            "metrics": {},
                            "last_error": None,
                            "last_check": datetime.utcnow().isoformat()
                        }
                except Exception as e:
                    component_reports[name] = {
                        "name": name,
                        "is_healthy": False,
                        "status": "error",
                        "metrics": {},
                        "last_error": str(e),
                        "last_check": datetime.utcnow().isoformat()
                    }
                    
            # 计算运行时间统计
            uptime = datetime.utcnow() - self._start_time
            
            return {
                "system": current_health.to_dict(),
                "components": component_reports,
                "monitoring": {
                    "is_running": self._is_running,
                    "uptime_seconds": uptime.total_seconds(),
                    "check_count": self._check_count,
                    "alert_count": self._alert_count,
                    "history_entries": len(self._health_history)
                },
                "summary": {
                    "overall_healthy": current_health.is_healthy and all(
                        comp.get("is_healthy", False) for comp in component_reports.values()
                    ),
                    "critical_alerts": len([
                        alert for alert in current_health.alerts 
                        if "严重告警" in alert
                    ]),
                    "warning_alerts": len([
                        alert for alert in current_health.alerts 
                        if "警告" in alert
                    ])
                }
            }
            
        except Exception as e:
            logger.error(f"生成健康报告失败: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
            
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            # 最近的健康记录
            recent_checks = self._health_history[-100:] if self._health_history else []
            
            if not recent_checks:
                return {"error": "暂无历史数据"}
                
            # 计算平均值
            avg_cpu = sum(h.cpu_percent for h in recent_checks) / len(recent_checks)
            avg_memory = sum(h.memory_percent for h in recent_checks) / len(recent_checks)
            avg_response_time = sum(h.response_time_ms for h in recent_checks) / len(recent_checks)
            avg_error_rate = sum(h.error_rate for h in recent_checks) / len(recent_checks)
            
            # 计算趋势
            if len(recent_checks) >= 2:
                cpu_trend = recent_checks[-1].cpu_percent - recent_checks[-10].cpu_percent if len(recent_checks) >= 10 else 0
                memory_trend = recent_checks[-1].memory_percent - recent_checks[-10].memory_percent if len(recent_checks) >= 10 else 0
            else:
                cpu_trend = memory_trend = 0
                
            return {
                "averages": {
                    "cpu_percent": round(avg_cpu, 2),
                    "memory_percent": round(avg_memory, 2),
                    "response_time_ms": round(avg_response_time, 2),
                    "error_rate": round(avg_error_rate, 4)
                },
                "trends": {
                    "cpu_trend": round(cpu_trend, 2),
                    "memory_trend": round(memory_trend, 2)
                },
                "current": {
                    "cpu_percent": recent_checks[-1].cpu_percent,
                    "memory_percent": recent_checks[-1].memory_percent,
                    "active_connections": recent_checks[-1].active_connections,
                    "timestamp": recent_checks[-1].last_check.isoformat()
                },
                "data_points": len(recent_checks)
            }
            
        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return {"error": str(e)}
            
    def get_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        uptime = datetime.utcnow() - self._start_time
        
        return {
            "is_running": self._is_running,
            "uptime_seconds": uptime.total_seconds(),
            "check_count": self._check_count,
            "alert_count": self._alert_count,
            "history_entries": len(self._health_history),
            "component_count": len(self._component_health),
            "thresholds": self.thresholds.copy()
        }


# 工厂函数

def create_system_monitor(config: StreamingConfig) -> SystemMonitor:
    """创建系统监控器实例"""
    return SystemMonitor(config)