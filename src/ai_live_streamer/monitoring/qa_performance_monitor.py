"""QA性能监控器 - 专用于分析QA插入延迟和并发冲突

用于Phase 2.4: 结构化日志和监控端点

Author: Claude Code
Date: 2025-01-08 (QA修复专用)
"""

import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from collections import defaultdict, deque
from loguru import logger
import json


class QAInsertionResult(Enum):
    """QA插入结果类型"""
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CONFLICT = "conflict"
    REJECTED = "rejected"


@dataclass
class QAPerformanceMetric:
    """QA性能指标记录"""
    qa_id: str
    question_text: str
    request_timestamp: float
    insertion_strategy: str
    
    # 时间指标
    frontend_to_backend_ms: Optional[float] = None
    backend_processing_ms: Optional[float] = None
    playlist_insertion_ms: Optional[float] = None
    player_insertion_ms: Optional[float] = None
    total_latency_ms: Optional[float] = None
    
    # 位置指标
    requested_position: Optional[int] = None
    calculated_position: Optional[int] = None
    final_position: Optional[int] = None
    position_calculation_confidence: Optional[float] = None
    
    # 并发指标
    concurrent_qa_requests: int = 0
    redis_lock_wait_ms: Optional[float] = None
    cache_hit: bool = False
    
    # 结果指标
    result: QAInsertionResult = QAInsertionResult.SUCCESS
    error_message: Optional[str] = None
    
    # 播放影响指标
    playback_interruption: bool = False
    user_perceived_delay_ms: Optional[float] = None
    sentences_inserted: int = 0
    
    # 元数据
    session_id: str = ""
    client_id: str = ""
    server_load_factor: float = 0.0
    
    def calculate_total_latency(self) -> float:
        """计算总延迟"""
        if self.total_latency_ms is not None:
            return self.total_latency_ms
        
        components = [
            self.frontend_to_backend_ms or 0,
            self.backend_processing_ms or 0,
            self.playlist_insertion_ms or 0,
            self.player_insertion_ms or 0
        ]
        
        self.total_latency_ms = sum(components)
        return self.total_latency_ms
    
    def is_successful(self) -> bool:
        """是否成功插入"""
        return self.result == QAInsertionResult.SUCCESS
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "qa_id": self.qa_id,
            "question_text": self.question_text[:50] + "...",  # 截断长文本
            "request_timestamp": self.request_timestamp,
            "insertion_strategy": self.insertion_strategy,
            
            "timing": {
                "frontend_to_backend_ms": self.frontend_to_backend_ms,
                "backend_processing_ms": self.backend_processing_ms,
                "playlist_insertion_ms": self.playlist_insertion_ms,
                "player_insertion_ms": self.player_insertion_ms,
                "total_latency_ms": self.calculate_total_latency(),
                "redis_lock_wait_ms": self.redis_lock_wait_ms
            },
            
            "positioning": {
                "requested_position": self.requested_position,
                "calculated_position": self.calculated_position,
                "final_position": self.final_position,
                "confidence": self.position_calculation_confidence
            },
            
            "concurrency": {
                "concurrent_requests": self.concurrent_qa_requests,
                "cache_hit": self.cache_hit,
                "redis_lock_wait_ms": self.redis_lock_wait_ms
            },
            
            "result": {
                "status": self.result.value,
                "error_message": self.error_message,
                "sentences_inserted": self.sentences_inserted,
                "playback_interruption": self.playback_interruption,
                "user_perceived_delay_ms": self.user_perceived_delay_ms
            },
            
            "context": {
                "session_id": self.session_id,
                "client_id": self.client_id,
                "server_load_factor": self.server_load_factor
            }
        }


class QAPerformanceMonitor:
    """QA性能监控器 - 🚀 实时分析QA插入性能"""
    
    def __init__(self, max_metrics_history: int = 1000):
        self.max_metrics_history = max_metrics_history
        
        # 性能指标存储
        self.metrics_history: deque[QAPerformanceMetric] = deque(maxlen=max_metrics_history)
        self.active_qa_sessions: Dict[str, QAPerformanceMetric] = {}
        
        # 实时统计
        self.stats = {
            "total_qa_requests": 0,
            "successful_insertions": 0,
            "failed_insertions": 0,
            "cache_hits": 0,
            "concurrent_conflicts": 0,
            "average_latency_ms": 0.0,
            "p95_latency_ms": 0.0,
            "p99_latency_ms": 0.0
        }
        
        # 时间窗口统计（滑动窗口）
        self.time_windows = {
            "1min": deque(maxlen=60),    # 1分钟窗口，每秒一个数据点
            "5min": deque(maxlen=300),   # 5分钟窗口
            "1hour": deque(maxlen=3600)  # 1小时窗口
        }
        
        # 性能警报阈值
        self.alert_thresholds = {
            "max_latency_ms": 2000,      # 最大可接受延迟
            "max_concurrent_requests": 10, # 最大并发请求数
            "min_success_rate": 0.95,    # 最小成功率
            "max_cache_miss_rate": 0.3   # 最大缓存未命中率
        }
        
        # 结构化日志
        self.structured_logger = self._setup_structured_logger()
        
        logger.info("🚀 QA性能监控器初始化完成")
    
    def _setup_structured_logger(self):
        """设置结构化日志记录器"""
        # 这里可以配置专门的QA监控日志格式
        return logger.bind(component="qa_performance_monitor")
    
    def start_qa_tracking(
        self, 
        qa_id: str, 
        question_text: str, 
        insertion_strategy: str,
        session_id: str = "",
        client_id: str = ""
    ) -> QAPerformanceMetric:
        """开始追踪QA请求性能 - 🚀 创建性能指标记录"""
        
        metric = QAPerformanceMetric(
            qa_id=qa_id,
            question_text=question_text,
            request_timestamp=time.time() * 1000,
            insertion_strategy=insertion_strategy,
            session_id=session_id,
            client_id=client_id,
            concurrent_qa_requests=len(self.active_qa_sessions)
        )
        
        self.active_qa_sessions[qa_id] = metric
        self.stats["total_qa_requests"] += 1
        
        # 结构化日志记录
        self.structured_logger.info(
            "QA插入开始追踪",
            extra={
                "event": "qa_tracking_start",
                "qa_id": qa_id,
                "question_preview": question_text[:30],
                "strategy": insertion_strategy,
                "concurrent_count": metric.concurrent_qa_requests,
                "timestamp": metric.request_timestamp
            }
        )
        
        return metric
    
    def record_frontend_timing(self, qa_id: str, frontend_to_backend_ms: float):
        """记录前端到后端的时间"""
        if qa_id in self.active_qa_sessions:
            self.active_qa_sessions[qa_id].frontend_to_backend_ms = frontend_to_backend_ms
    
    def record_backend_processing_start(self, qa_id: str):
        """记录后端处理开始"""
        if qa_id in self.active_qa_sessions:
            self.active_qa_sessions[qa_id]._backend_start = time.time() * 1000
    
    def record_backend_processing_end(self, qa_id: str):
        """记录后端处理结束"""
        if qa_id in self.active_qa_sessions and hasattr(self.active_qa_sessions[qa_id], '_backend_start'):
            current_time = time.time() * 1000
            self.active_qa_sessions[qa_id].backend_processing_ms = current_time - self.active_qa_sessions[qa_id]._backend_start
    
    def record_redis_lock_timing(self, qa_id: str, lock_wait_ms: float):
        """记录Redis锁等待时间"""
        if qa_id in self.active_qa_sessions:
            self.active_qa_sessions[qa_id].redis_lock_wait_ms = lock_wait_ms
    
    def record_cache_hit(self, qa_id: str, is_hit: bool):
        """记录缓存命中情况"""
        if qa_id in self.active_qa_sessions:
            self.active_qa_sessions[qa_id].cache_hit = is_hit
            if is_hit:
                self.stats["cache_hits"] += 1
    
    def record_position_calculation(
        self, 
        qa_id: str, 
        requested_position: int, 
        calculated_position: int, 
        final_position: int,
        confidence: float = 0.0
    ):
        """记录位置计算结果"""
        if qa_id in self.active_qa_sessions:
            metric = self.active_qa_sessions[qa_id]
            metric.requested_position = requested_position
            metric.calculated_position = calculated_position
            metric.final_position = final_position
            metric.position_calculation_confidence = confidence
    
    def record_playlist_insertion_timing(self, qa_id: str, insertion_ms: float):
        """记录播放列表插入时间"""
        if qa_id in self.active_qa_sessions:
            self.active_qa_sessions[qa_id].playlist_insertion_ms = insertion_ms
    
    def record_player_insertion_timing(self, qa_id: str, insertion_ms: float):
        """记录播放器插入时间"""
        if qa_id in self.active_qa_sessions:
            self.active_qa_sessions[qa_id].player_insertion_ms = insertion_ms
    
    def finish_qa_tracking(
        self, 
        qa_id: str, 
        result: QAInsertionResult,
        sentences_inserted: int = 0,
        error_message: str = None,
        playback_interruption: bool = False,
        user_perceived_delay_ms: float = None
    ) -> Optional[QAPerformanceMetric]:
        """完成QA请求性能追踪 - 🚀 生成完整的性能报告"""
        
        if qa_id not in self.active_qa_sessions:
            logger.warning(f"⚠️ 尝试完成未开始追踪的QA: {qa_id}")
            return None
        
        metric = self.active_qa_sessions[qa_id]
        
        # 更新结果信息
        metric.result = result
        metric.sentences_inserted = sentences_inserted
        metric.error_message = error_message
        metric.playback_interruption = playback_interruption
        metric.user_perceived_delay_ms = user_perceived_delay_ms
        
        # 计算总延迟
        total_latency = metric.calculate_total_latency()
        
        # 更新统计信息
        if result == QAInsertionResult.SUCCESS:
            self.stats["successful_insertions"] += 1
        else:
            self.stats["failed_insertions"] += 1
        
        # 添加到历史记录
        self.metrics_history.append(metric)
        
        # 更新时间窗口统计
        self._update_time_window_stats(metric)
        
        # 更新聚合统计
        self._update_aggregate_stats()
        
        # 检查性能警报
        self._check_performance_alerts(metric)
        
        # 结构化日志记录
        self.structured_logger.info(
            "QA插入完成追踪",
            extra={
                "event": "qa_tracking_complete",
                "qa_id": qa_id,
                "result": result.value,
                "total_latency_ms": total_latency,
                "sentences_inserted": sentences_inserted,
                "playback_interruption": playback_interruption,
                "error": error_message,
                "performance_summary": metric.to_dict()
            }
        )
        
        # 从活跃会话中移除
        del self.active_qa_sessions[qa_id]
        
        return metric
    
    def _update_time_window_stats(self, metric: QAPerformanceMetric):
        """更新时间窗口统计"""
        current_time = time.time()
        
        stats_point = {
            "timestamp": current_time,
            "latency_ms": metric.calculate_total_latency(),
            "success": metric.is_successful(),
            "cache_hit": metric.cache_hit,
            "concurrent_requests": metric.concurrent_qa_requests
        }
        
        # 添加到各个时间窗口
        for window in self.time_windows.values():
            window.append(stats_point)
    
    def _update_aggregate_stats(self):
        """更新聚合统计信息"""
        if not self.metrics_history:
            return
        
        # 计算延迟percentiles
        latencies = [m.calculate_total_latency() for m in self.metrics_history if m.is_successful()]
        
        if latencies:
            latencies.sort()
            n = len(latencies)
            
            self.stats["average_latency_ms"] = sum(latencies) / n
            self.stats["p95_latency_ms"] = latencies[int(n * 0.95)] if n > 0 else 0
            self.stats["p99_latency_ms"] = latencies[int(n * 0.99)] if n > 0 else 0
    
    def _check_performance_alerts(self, metric: QAPerformanceMetric):
        """检查性能警报"""
        alerts = []
        
        # 延迟警报
        total_latency = metric.calculate_total_latency()
        if total_latency > self.alert_thresholds["max_latency_ms"]:
            alerts.append(f"高延迟警报: {total_latency:.0f}ms > {self.alert_thresholds['max_latency_ms']}ms")
        
        # 并发警报
        if metric.concurrent_qa_requests > self.alert_thresholds["max_concurrent_requests"]:
            alerts.append(f"高并发警报: {metric.concurrent_qa_requests} > {self.alert_thresholds['max_concurrent_requests']}")
        
        # 成功率警报
        success_rate = self.get_success_rate()
        if success_rate < self.alert_thresholds["min_success_rate"]:
            alerts.append(f"低成功率警报: {success_rate:.2f} < {self.alert_thresholds['min_success_rate']}")
        
        # 发送警报
        for alert in alerts:
            self.structured_logger.warning(
                "QA性能警报",
                extra={
                    "event": "performance_alert",
                    "qa_id": metric.qa_id,
                    "alert": alert,
                    "metric_summary": metric.to_dict()
                }
            )
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        total = self.stats["total_qa_requests"]
        if total == 0:
            return 1.0
        return self.stats["successful_insertions"] / total
    
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前统计信息 - 🚀 实时性能仪表板数据"""
        return {
            **self.stats,
            "success_rate": self.get_success_rate(),
            "active_qa_sessions": len(self.active_qa_sessions),
            "metrics_history_size": len(self.metrics_history),
            "cache_hit_rate": (
                self.stats["cache_hits"] / max(self.stats["total_qa_requests"], 1)
            ),
            "concurrent_conflict_rate": (
                self.stats["concurrent_conflicts"] / max(self.stats["total_qa_requests"], 1)
            )
        }
    
    def get_time_window_stats(self, window_name: str = "1min") -> Dict[str, Any]:
        """获取时间窗口统计 - 🚀 趋势分析数据"""
        if window_name not in self.time_windows:
            return {}
        
        window_data = list(self.time_windows[window_name])
        if not window_data:
            return {}
        
        # 计算窗口内的统计
        latencies = [point["latency_ms"] for point in window_data]
        successes = [point["success"] for point in window_data]
        cache_hits = [point["cache_hit"] for point in window_data]
        
        return {
            "window": window_name,
            "data_points": len(window_data),
            "time_range": {
                "start": window_data[0]["timestamp"],
                "end": window_data[-1]["timestamp"]
            },
            "latency": {
                "avg": sum(latencies) / len(latencies) if latencies else 0,
                "min": min(latencies) if latencies else 0,
                "max": max(latencies) if latencies else 0
            },
            "success_rate": sum(successes) / len(successes) if successes else 0,
            "cache_hit_rate": sum(cache_hits) / len(cache_hits) if cache_hits else 0
        }
    
    def export_performance_report(self, last_n_minutes: int = 60) -> Dict[str, Any]:
        """导出性能报告 - 🚀 详细分析报告"""
        cutoff_time = time.time() * 1000 - (last_n_minutes * 60 * 1000)
        
        recent_metrics = [
            m for m in self.metrics_history 
            if m.request_timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {"error": "No metrics in specified time range"}
        
        # 分析结果分布
        result_distribution = defaultdict(int)
        strategy_performance = defaultdict(list)
        
        for metric in recent_metrics:
            result_distribution[metric.result.value] += 1
            strategy_performance[metric.insertion_strategy].append(metric.calculate_total_latency())
        
        # 计算策略性能
        strategy_stats = {}
        for strategy, latencies in strategy_performance.items():
            if latencies:
                strategy_stats[strategy] = {
                    "count": len(latencies),
                    "avg_latency_ms": sum(latencies) / len(latencies),
                    "min_latency_ms": min(latencies),
                    "max_latency_ms": max(latencies)
                }
        
        return {
            "report_period": {
                "last_n_minutes": last_n_minutes,
                "total_metrics": len(recent_metrics),
                "time_range": {
                    "start": min(m.request_timestamp for m in recent_metrics),
                    "end": max(m.request_timestamp for m in recent_metrics)
                }
            },
            "overall_stats": self.get_current_stats(),
            "result_distribution": dict(result_distribution),
            "strategy_performance": strategy_stats,
            "time_windows": {
                name: self.get_time_window_stats(name) 
                for name in self.time_windows.keys()
            },
            "performance_trends": self._analyze_performance_trends(recent_metrics),
            "recommendations": self._generate_recommendations(recent_metrics)
        }
    
    def _analyze_performance_trends(self, metrics: List[QAPerformanceMetric]) -> Dict[str, Any]:
        """分析性能趋势"""
        if len(metrics) < 10:
            return {"note": "Insufficient data for trend analysis"}
        
        # 按时间排序
        sorted_metrics = sorted(metrics, key=lambda m: m.request_timestamp)
        
        # 分析延迟趋势
        latencies = [m.calculate_total_latency() for m in sorted_metrics]
        first_half = latencies[:len(latencies)//2]
        second_half = latencies[len(latencies)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        latency_trend = "improving" if second_avg < first_avg else "degrading"
        
        return {
            "latency_trend": latency_trend,
            "latency_change_pct": ((second_avg - first_avg) / first_avg * 100) if first_avg > 0 else 0,
            "first_half_avg_ms": first_avg,
            "second_half_avg_ms": second_avg
        }
    
    def _generate_recommendations(self, metrics: List[QAPerformanceMetric]) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        # 分析高延迟问题
        high_latency_metrics = [m for m in metrics if m.calculate_total_latency() > 1000]
        if len(high_latency_metrics) / len(metrics) > 0.1:
            recommendations.append("检测到10%以上的请求延迟超过1秒，建议优化后端处理逻辑")
        
        # 分析缓存命中率
        cache_hits = sum(1 for m in metrics if m.cache_hit)
        cache_hit_rate = cache_hits / len(metrics)
        if cache_hit_rate < 0.5:
            recommendations.append(f"缓存命中率较低({cache_hit_rate:.1%})，建议优化缓存策略")
        
        # 分析并发冲突
        high_concurrency_metrics = [m for m in metrics if m.concurrent_qa_requests > 5]
        if high_concurrency_metrics:
            recommendations.append("检测到高并发QA请求，建议增加Redis锁优化或队列处理")
        
        # 分析失败率
        failed_metrics = [m for m in metrics if not m.is_successful()]
        if len(failed_metrics) / len(metrics) > 0.05:
            recommendations.append("QA插入失败率超过5%，建议检查错误日志并优化错误处理")
        
        return recommendations


# 全局监控器实例
_global_qa_monitor: Optional[QAPerformanceMonitor] = None


def get_qa_performance_monitor() -> QAPerformanceMonitor:
    """获取全局QA性能监控器实例"""
    global _global_qa_monitor
    
    if _global_qa_monitor is None:
        _global_qa_monitor = QAPerformanceMonitor()
        logger.info("🚀 全局QA性能监控器已创建")
    
    return _global_qa_monitor


def reset_qa_performance_monitor():
    """重置全局QA性能监控器（主要用于测试）"""
    global _global_qa_monitor
    _global_qa_monitor = None