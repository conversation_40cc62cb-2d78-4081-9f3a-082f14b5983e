"""音频流监控指标

提供音频流性能监控、延迟统计和质量分析功能。
"""

import time
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from loguru import logger
import statistics


@dataclass
class AudioMetrics:
    """音频流监控指标
    
    收集和分析音频流的性能指标，包括延迟、质量、连接稳定性等。
    """
    
    # 延迟指标 (毫秒)
    synthesis_latencies: List[float] = field(default_factory=list)
    transmission_latencies: List[float] = field(default_factory=list)
    end_to_end_latencies: List[float] = field(default_factory=list)
    
    # 质量指标
    audio_chunks_sent: int = 0
    audio_chunks_received: int = 0
    audio_chunks_failed: int = 0
    connection_drops: int = 0
    decode_errors: int = 0
    
    # 性能指标
    active_connections: int = 0
    peak_connections: int = 0
    total_connections: int = 0
    bandwidth_usage_bytes: float = 0.0
    
    # 时间戳
    start_time: float = field(default_factory=time.time)
    last_activity: Optional[float] = None
    
    # 配置
    max_history_size: int = 1000
    
    def record_synthesis_latency(self, latency_ms: float):
        """记录合成延迟
        
        Args:
            latency_ms: 合成延迟（毫秒）
        """
        self.synthesis_latencies.append(latency_ms)
        self._trim_history(self.synthesis_latencies)
        self.last_activity = time.time()
        
        logger.debug(f"Synthesis latency recorded: {latency_ms:.1f}ms")
    
    def record_transmission_latency(self, latency_ms: float):
        """记录传输延迟
        
        Args:
            latency_ms: 传输延迟（毫秒）
        """
        self.transmission_latencies.append(latency_ms)
        self._trim_history(self.transmission_latencies)
        self.last_activity = time.time()
        
        logger.debug(f"Transmission latency recorded: {latency_ms:.1f}ms")
    
    def record_end_to_end_latency(self, latency_ms: float):
        """记录端到端延迟
        
        Args:
            latency_ms: 端到端延迟（毫秒）
        """
        self.end_to_end_latencies.append(latency_ms)
        self._trim_history(self.end_to_end_latencies)
        self.last_activity = time.time()
        
        # 记录延迟警告
        if latency_ms > 500:
            logger.warning(f"High end-to-end latency detected: {latency_ms:.1f}ms")
        
        logger.debug(f"End-to-end latency recorded: {latency_ms:.1f}ms")
    
    def record_audio_chunk_sent(self, chunk_size_bytes: int = 0):
        """记录音频块发送
        
        Args:
            chunk_size_bytes: 音频块大小（字节）
        """
        self.audio_chunks_sent += 1
        self.bandwidth_usage_bytes += chunk_size_bytes
        self.last_activity = time.time()
    
    def record_audio_chunk_received(self):
        """记录音频块接收"""
        self.audio_chunks_received += 1
        self.last_activity = time.time()
    
    def record_audio_chunk_failed(self):
        """记录音频块失败"""
        self.audio_chunks_failed += 1
        self.last_activity = time.time()
        
        logger.warning("Audio chunk transmission failed")
    
    def record_connection_drop(self):
        """记录连接断开"""
        self.connection_drops += 1
        if self.active_connections > 0:
            self.active_connections -= 1
        
        logger.warning(f"Connection dropped, active: {self.active_connections}")
    
    def record_decode_error(self):
        """记录解码错误"""
        self.decode_errors += 1
        logger.error("Audio decode error occurred")
    
    def update_connection_count(self, count: int):
        """更新连接数
        
        Args:
            count: 当前活跃连接数
        """
        self.active_connections = count
        self.peak_connections = max(self.peak_connections, count)
        
        if count > self.total_connections:
            self.total_connections = count
    
    def get_average_latency(self, latency_type: str = "end_to_end") -> float:
        """获取平均延迟
        
        Args:
            latency_type: 延迟类型 ("synthesis", "transmission", "end_to_end")
            
        Returns:
            平均延迟（毫秒）
        """
        latencies = {
            "synthesis": self.synthesis_latencies,
            "transmission": self.transmission_latencies,
            "end_to_end": self.end_to_end_latencies
        }
        
        target_latencies = latencies.get(latency_type, self.end_to_end_latencies)
        
        if not target_latencies:
            return 0.0
        
        return statistics.mean(target_latencies)
    
    def get_latency_percentile(self, percentile: float, latency_type: str = "end_to_end") -> float:
        """获取延迟百分位数
        
        Args:
            percentile: 百分位数 (0-100)
            latency_type: 延迟类型
            
        Returns:
            延迟百分位数（毫秒）
        """
        latencies = {
            "synthesis": self.synthesis_latencies,
            "transmission": self.transmission_latencies,
            "end_to_end": self.end_to_end_latencies
        }
        
        target_latencies = latencies.get(latency_type, self.end_to_end_latencies)
        
        if not target_latencies:
            return 0.0
        
        sorted_latencies = sorted(target_latencies)
        index = int((percentile / 100) * len(sorted_latencies))
        index = min(index, len(sorted_latencies) - 1)
        
        return sorted_latencies[index]
    
    def get_audio_delivery_rate(self) -> float:
        """获取音频传输成功率
        
        Returns:
            成功率 (0.0-1.0)
        """
        total_attempts = self.audio_chunks_sent
        if total_attempts == 0:
            return 1.0
        
        successful = self.audio_chunks_received
        return successful / total_attempts
    
    def get_connection_stability(self) -> float:
        """获取连接稳定性
        
        Returns:
            稳定性评分 (0.0-1.0)
        """
        if self.peak_connections == 0:
            return 1.0
        
        # 基于连接断开率计算稳定性
        drop_rate = self.connection_drops / max(1, self.peak_connections)
        stability = max(0.0, 1.0 - drop_rate)
        
        return stability
    
    def get_bandwidth_mbps(self) -> float:
        """获取带宽使用（Mbps）
        
        Returns:
            带宽使用（Mbps）
        """
        if not self.last_activity:
            return 0.0
        
        elapsed_seconds = max(1, time.time() - self.start_time)
        bytes_per_second = self.bandwidth_usage_bytes / elapsed_seconds
        
        # 转换为Mbps
        mbps = (bytes_per_second * 8) / (1024 * 1024)
        return mbps
    
    def get_metrics_summary(self) -> Dict:
        """获取指标摘要
        
        Returns:
            包含所有关键指标的字典
        """
        return {
            # 延迟指标
            "avg_synthesis_latency_ms": self.get_average_latency("synthesis"),
            "avg_transmission_latency_ms": self.get_average_latency("transmission"),
            "avg_end_to_end_latency_ms": self.get_average_latency("end_to_end"),
            "p95_end_to_end_latency_ms": self.get_latency_percentile(95),
            "p99_end_to_end_latency_ms": self.get_latency_percentile(99),
            
            # 质量指标
            "audio_delivery_rate": self.get_audio_delivery_rate(),
            "audio_chunks_sent": self.audio_chunks_sent,
            "audio_chunks_received": self.audio_chunks_received,
            "audio_chunks_failed": self.audio_chunks_failed,
            "decode_errors": self.decode_errors,
            
            # 连接指标
            "connection_stability": self.get_connection_stability(),
            "active_connections": self.active_connections,
            "peak_connections": self.peak_connections,
            "total_connections": self.total_connections,
            "connection_drops": self.connection_drops,
            
            # 性能指标
            "bandwidth_mbps": self.get_bandwidth_mbps(),
            "uptime_seconds": time.time() - self.start_time,
            "last_activity": self.last_activity,
            
            # 采样统计
            "synthesis_samples": len(self.synthesis_latencies),
            "transmission_samples": len(self.transmission_latencies),
            "end_to_end_samples": len(self.end_to_end_latencies)
        }
    
    def get_health_score(self) -> float:
        """获取健康评分
        
        综合各项指标计算系统健康度。
        
        Returns:
            健康评分 (0.0-1.0)
        """
        # 权重配置
        weights = {
            "latency": 0.3,      # 延迟权重
            "delivery": 0.3,     # 传输成功率权重
            "stability": 0.4     # 连接稳定性权重
        }
        
        # 延迟评分 (低延迟 = 高分)
        avg_latency = self.get_average_latency("end_to_end")
        if avg_latency == 0:
            latency_score = 1.0
        else:
            # 300ms以下为满分，1000ms以上为0分
            latency_score = max(0.0, 1.0 - (avg_latency - 300) / 700)
        
        # 传输成功率评分
        delivery_score = self.get_audio_delivery_rate()
        
        # 连接稳定性评分
        stability_score = self.get_connection_stability()
        
        # 综合评分
        health_score = (
            weights["latency"] * latency_score +
            weights["delivery"] * delivery_score +
            weights["stability"] * stability_score
        )
        
        return max(0.0, min(1.0, health_score))
    
    def reset_metrics(self):
        """重置所有指标"""
        self.synthesis_latencies.clear()
        self.transmission_latencies.clear()
        self.end_to_end_latencies.clear()
        
        self.audio_chunks_sent = 0
        self.audio_chunks_received = 0
        self.audio_chunks_failed = 0
        self.connection_drops = 0
        self.decode_errors = 0
        
        self.active_connections = 0
        self.peak_connections = 0
        self.total_connections = 0
        self.bandwidth_usage_bytes = 0.0
        
        self.start_time = time.time()
        self.last_activity = None
        
        logger.info("Audio metrics reset")
    
    def _trim_history(self, history_list: List[float]):
        """修剪历史记录，保持在最大大小内"""
        if len(history_list) > self.max_history_size:
            # 移除最旧的记录
            del history_list[:-self.max_history_size]


# 全局指标实例
audio_metrics = AudioMetrics()


def get_audio_metrics() -> AudioMetrics:
    """获取全局音频指标实例
    
    Returns:
        AudioMetrics实例
    """
    return audio_metrics


def reset_audio_metrics():
    """重置全局音频指标"""
    global audio_metrics
    audio_metrics.reset_metrics()


def log_metrics_summary():
    """记录指标摘要到日志"""
    summary = audio_metrics.get_metrics_summary()
    health_score = audio_metrics.get_health_score()
    
    logger.info(f"📊 Audio Metrics Summary:")
    logger.info(f"   Health Score: {health_score:.2f}/1.0")
    logger.info(f"   Avg E2E Latency: {summary['avg_end_to_end_latency_ms']:.1f}ms")
    logger.info(f"   P95 Latency: {summary['p95_end_to_end_latency_ms']:.1f}ms")
    logger.info(f"   Delivery Rate: {summary['audio_delivery_rate']:.1%}")
    logger.info(f"   Connection Stability: {summary['connection_stability']:.1%}")
    logger.info(f"   Active Connections: {summary['active_connections']}")
    logger.info(f"   Bandwidth: {summary['bandwidth_mbps']:.2f} Mbps")
    
    # 警告阈值检查
    if health_score < 0.7:
        logger.warning(f"⚠️  Low health score detected: {health_score:.2f}")
    
    if summary['avg_end_to_end_latency_ms'] > 500:
        logger.warning(f"⚠️  High average latency: {summary['avg_end_to_end_latency_ms']:.1f}ms")
    
    if summary['audio_delivery_rate'] < 0.95:
        logger.warning(f"⚠️  Low delivery rate: {summary['audio_delivery_rate']:.1%}")


# 定期指标日志任务
async def start_metrics_logging(interval_seconds: int = 60):
    """启动定期指标日志任务
    
    Args:
        interval_seconds: 日志间隔（秒）
    """
    import asyncio
    
    logger.info(f"Starting metrics logging with {interval_seconds}s interval")
    
    while True:
        try:
            await asyncio.sleep(interval_seconds)
            log_metrics_summary()
        except asyncio.CancelledError:
            logger.info("Metrics logging task cancelled")
            break
        except Exception as e:
            logger.error(f"Error in metrics logging: {e}")
            await asyncio.sleep(10)  # 错误时短暂等待