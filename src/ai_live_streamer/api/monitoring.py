"""Monitoring and Health Check API Endpoints

Provides comprehensive monitoring, health checks, metrics collection,
and system status endpoints for production observability.

Implements Task 3.1.3: 实现状态监控与健康检查API

Author: Claude Code
Date: 2025-08-05
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, status, Depends, Query
from pydantic import BaseModel, Field
from loguru import logger

# V1 imports removed
# from ..core.live_stream_controller import LiveStreamController
# from ..core.live_stream_session import LiveStreamSession
from ..core.exceptions import ServiceError
# from ..core.error_monitor import get_error_monitor  # Removed in simplified architecture
from ..core.deadlock_detector import get_deadlock_detector
# V2: No longer using app_config
# from ..core.app_config import get_app_config_dep, AppConfig - REMOVED
from .control import get_default_session, get_active_sessions


# Response Models
class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = Field(description="Overall health status: healthy, warning, critical")
    timestamp: str = Field(description="Check timestamp in ISO format")
    uptime_seconds: float = Field(description="System uptime in seconds")
    components: Dict[str, Dict[str, Any]] = Field(description="Component-wise health status")
    summary: Dict[str, Any] = Field(description="Health summary")


class MetricsResponse(BaseModel):
    """Metrics response model"""
    timestamp: str = Field(description="Metrics timestamp in ISO format")
    session_metrics: Dict[str, Any] = Field(description="Session-level metrics")
    system_metrics: Dict[str, Any] = Field(description="System-level metrics")
    performance_metrics: Dict[str, Any] = Field(description="Performance metrics")


class PlaybackStatusResponse(BaseModel):
    """Playback status response model"""
    session_id: str = Field(description="Session identifier")
    is_playing: bool = Field(description="Whether content is currently playing")
    is_paused: bool = Field(description="Whether playback is paused")
    current_progress: Dict[str, Any] = Field(description="Current playback progress")
    player_info: Dict[str, Any] = Field(description="Player detailed information")


class QuestionQueueResponse(BaseModel):
    """Question queue status response model"""
    session_id: str = Field(description="Session identifier")
    queue_size: int = Field(description="Number of questions in queue")
    processing_status: Dict[str, Any] = Field(description="QA processing status")
    recent_activity: List[Dict[str, Any]] = Field(description="Recent QA activity")


# Router setup
monitoring_router = APIRouter(prefix="/api", tags=["monitoring"])

# System start time for uptime calculation
_system_start_time = datetime.utcnow()


# Health Check Endpoints
@monitoring_router.get("/health", response_model=HealthCheckResponse)
async def system_health_check() -> HealthCheckResponse:
    """System health check endpoint
    
    Returns comprehensive health status including:
    - Overall system health
    - Component health status  
    - Error rates and recovery status
    - Resource utilization
    - Service availability
    """
    try:
        current_time = datetime.utcnow()
        uptime = (current_time - _system_start_time).total_seconds()
        
        # Component health checks
        components_health = {}
        overall_status = "healthy"
        
        # Check error monitor health
        try:
            # error_monitor = get_error_monitor()  # Removed in simplified architecture
            error_monitor = None
            # error_health = error_monitor.get_health_status()  # Removed in simplified architecture
            error_health = {"overall_status": "simplified", "metrics": {}, "active_alerts": 0, "total_alerts": 0, "resolved_alerts": 0}
            components_health["error_monitor"] = {
                "status": error_health["overall_status"],
                "details": error_health
            }
            
            if error_health["overall_status"] in ["warning", "critical"]:
                overall_status = error_health["overall_status"]
                
        except Exception as e:
            components_health["error_monitor"] = {
                "status": "error",
                "error": str(e)
            }
            overall_status = "critical"
        
        # Check deadlock detector health
        try:
            deadlock_detector = get_deadlock_detector()
            lock_status = deadlock_detector.get_lock_status()
            deadlock_healthy = lock_status["total_active_locks"] < 10  # Threshold
            
            components_health["deadlock_detector"] = {
                "status": "healthy" if deadlock_healthy else "warning",
                "details": lock_status
            }
            
            if not deadlock_healthy and overall_status == "healthy":
                overall_status = "warning"
                
        except Exception as e:
            components_health["deadlock_detector"] = {
                "status": "error", 
                "error": str(e)
            }
            overall_status = "critical"
        
        # Check active sessions health
        try:
            sessions_info = get_active_sessions()
            session_count = len(sessions_info)
            
            session_health = "healthy"
            if session_count > 10:  # Too many sessions
                session_health = "warning"
            elif session_count == 0:  # No active sessions might be normal
                session_health = "healthy"
            
            components_health["sessions"] = {
                "status": session_health,
                "active_count": session_count,
                "details": sessions_info
            }
            
            if session_health == "warning" and overall_status == "healthy":
                overall_status = "warning"
                
        except Exception as e:
            components_health["sessions"] = {
                "status": "error",
                "error": str(e)
            }
            overall_status = "critical"
        
        # V2: Service locator removed - using V2 dependencies
        components_health["dependency_system"] = {
            "status": "healthy",
            "version": "V2",
            "description": "Using V2 dependency injection"
        }
        
        # Build summary
        summary = {
            "total_components": len(components_health),
            "healthy_components": len([c for c in components_health.values() if c["status"] == "healthy"]),
            "warning_components": len([c for c in components_health.values() if c["status"] == "warning"]),
            "critical_components": len([c for c in components_health.values() if c["status"] in ["critical", "error"]]),
            "uptime_hours": uptime / 3600,
            "system_load": "normal"  # Could be enhanced with actual system metrics
        }
        
        return HealthCheckResponse(
            status=overall_status,
            timestamp=current_time.isoformat(),
            uptime_seconds=uptime,
            components=components_health,
            summary=summary
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )


@monitoring_router.get("/health/simple")
async def simple_health_check() -> Dict[str, str]:
    """Simple health check for load balancers
    
    Returns basic OK status for quick health verification.
    """
    return {
        "status": "ok",
        "timestamp": datetime.utcnow().isoformat()
    }


# Metrics Endpoints  
@monitoring_router.get("/metrics", response_model=MetricsResponse)
async def get_system_metrics(
) -> MetricsResponse:
    """Get comprehensive system metrics
    
    Returns detailed metrics including:
    - Session-level performance metrics
    - System resource usage
    - Error rates and recovery statistics
    - QA processing metrics
    """
    try:
        current_time = datetime.utcnow()
        
        # V2: Get metrics from active sessions
        sessions_info = get_active_sessions()
        
        # Session metrics
        session_metrics = {
            "session_id": "main",
            "is_active": len(sessions_info) > 0,
            "uptime_seconds": 0,
            "active_sessions": len(sessions_info)
        }
        
        # System metrics from error monitor
        system_metrics = {}
        try:
            # error_monitor = get_error_monitor()  # Removed in simplified architecture
            error_monitor = None
            # error_health = error_monitor.get_health_status()  # Removed in simplified architecture
            error_health = {"overall_status": "simplified", "metrics": {}, "active_alerts": 0, "total_alerts": 0, "resolved_alerts": 0}
            system_metrics["error_monitoring"] = error_health["metrics"]
            system_metrics["error_summary"] = {
                "overall_status": error_health["overall_status"],
                "critical_metrics": error_health.get("critical_metrics", []),
                "warning_metrics": error_health.get("warning_metrics", [])
            }
        except Exception as e:
            logger.warning(f"Failed to get error metrics: {e}")
            system_metrics["error_monitoring"] = {"error": str(e)}
        
        # Lock and concurrency metrics
        try:
            deadlock_detector = get_deadlock_detector()
            lock_stats = deadlock_detector.get_lock_status()
            system_metrics["concurrency"] = {
                "active_locks": lock_stats["total_active_locks"],
                "lock_stats": lock_stats["stats"],
                "monitoring_active": lock_stats["monitoring_active"]
            }
        except Exception as e:
            logger.warning(f"Failed to get lock metrics: {e}")
            system_metrics["concurrency"] = {"error": str(e)}
        
        # Performance metrics
        performance_metrics = {
            "timestamp": current_time.isoformat(),
            "active_sessions": len(get_active_sessions()),
            "system_uptime_hours": (current_time - _system_start_time).total_seconds() / 3600
        }
        
        # V2: Add session health metrics
        performance_metrics["session_health"] = {
            "is_healthy": True,
            "active_sessions": len(sessions_info)
        }
        
        return MetricsResponse(
            timestamp=current_time.isoformat(),
            session_metrics=session_metrics,
            system_metrics=system_metrics,
            performance_metrics=performance_metrics
        )
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get metrics: {str(e)}"
        )


# Playback Status Endpoint
@monitoring_router.get("/playback", response_model=PlaybackStatusResponse)
async def get_playback_status(
) -> PlaybackStatusResponse:
    """Get detailed playback status
    
    Returns comprehensive playback information including:
    - Current playing state
    - Progress information
    - Player configuration
    - Content details
    """
    try:
        # V2: Get playback status from session info
        sessions_info = get_active_sessions()
        session_info = sessions_info.get("main", {})
        
        # Extract playback information
        is_playing = session_info.get("is_running", False)
        is_paused = session_info.get("is_paused", False)
        
        # Current progress (simplified for V2)
        current_progress = {
            "is_running": is_playing,
            "qa_active": False,
            "session_uptime": 0,
            "questions_handled": 0,
            "total_play_time": 0.0
        }
        
        # Player detailed information
        player_info = {
            "tasks_status": {},
            "concurrency_info": {},
            "last_activity": ""
        }
        
        return PlaybackStatusResponse(
            session_id="main",
            is_playing=is_playing,
            is_paused=is_paused,
            current_progress=current_progress,
            player_info=player_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get playback status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get playback status: {str(e)}"
        )


# Question Queue Status Endpoint
@monitoring_router.get("/questions", response_model=QuestionQueueResponse)
async def get_question_queue_status(
) -> QuestionQueueResponse:
    """Get question queue and QA processing status
    
    Returns detailed information about:
    - Current question queue size
    - QA processing performance
    - Recent question handling activity
    - Processing statistics
    """
    try:
        # V2: Get queue status from session info
        sessions_info = get_active_sessions()
        session_info = sessions_info.get("main", {})
        
        # Queue information (V2 doesn't maintain traditional queue)
        queue_size = 0
        
        # Processing status (simplified for V2)
        processing_status = {
            "qa_active": False,
            "questions_handled": 0,
            "total_qa_time": 0.0,
            "average_qa_time": 0.0,
            "interruptions_count": 0,
            "errors_count": 0
        }
        
        # Recent activity (simplified for V2)
        recent_activity = [
            {
                "type": "session_start",
                "timestamp": session_info.get("started_at", ""),
                "details": {"session_id": "main"}
            }
        ]
        
        # Add last activity if available
        if False:  # V2 doesn't track last_activity
            recent_activity.append({
                "type": "last_activity",
                "timestamp": session_info["last_activity"],
                "details": {"status": session_info.get("current_status", "unknown")}
            })
        
        return QuestionQueueResponse(
            session_id="main",
            queue_size=queue_size,
            processing_status=processing_status,
            recent_activity=recent_activity
        )
        
    except Exception as e:
        logger.error(f"Failed to get question queue status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get question queue status: {str(e)}"
        )


# Additional monitoring endpoints
@monitoring_router.get("/alerts")
async def get_system_alerts(
    resolved: Optional[bool] = Query(None, description="Filter by resolved status"),
    limit: int = Query(50, description="Maximum number of alerts to return")
) -> Dict[str, Any]:
    """Get system alerts and warnings
    
    Returns current alerts from error monitor and deadlock detector.
    """
    try:
        alerts = []
        
        # Get error monitor alerts
        try:
            # error_monitor = get_error_monitor()  # Removed in simplified architecture
            error_monitor = None
            # error_alerts = error_monitor.get_alerts(resolved=resolved, limit=limit//2)  # Removed in simplified architecture
            error_alerts = []
            for alert in error_alerts:
                alert["source"] = "error_monitor"
                alerts.append(alert)
        except Exception as e:
            logger.warning(f"Failed to get error alerts: {e}")
        
        # Get deadlock detector alerts
        try:
            deadlock_detector = get_deadlock_detector()
            deadlock_alerts = deadlock_detector.get_deadlock_alerts(resolved=resolved)
            for alert in deadlock_alerts[:limit//2]:
                alert["source"] = "deadlock_detector"
                alerts.append(alert)
        except Exception as e:
            logger.warning(f"Failed to get deadlock alerts: {e}")
        
        # Sort by timestamp (most recent first)
        alerts.sort(key=lambda x: x.get("timestamp", x.get("detection_time", "")), reverse=True)
        
        return {
            "alerts": alerts[:limit],
            "total_count": len(alerts),
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get alerts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get alerts: {str(e)}"
        )


@monitoring_router.get("/stats")
async def get_system_statistics() -> Dict[str, Any]:
    """Get comprehensive system statistics
    
    Returns aggregated statistics across all components.
    """
    try:
        current_time = datetime.utcnow()
        
        stats = {
            "timestamp": current_time.isoformat(),
            "uptime_seconds": (current_time - _system_start_time).total_seconds(),
            "active_sessions": len(get_active_sessions())
        }
        
        # Error monitor statistics
        try:
            # error_monitor = get_error_monitor()  # Removed in simplified architecture
            error_monitor = None
            # error_health = error_monitor.get_health_status()  # Removed in simplified architecture
            error_health = {"overall_status": "simplified", "metrics": {}, "active_alerts": 0, "total_alerts": 0, "resolved_alerts": 0}
            stats["error_monitoring"] = {
                "total_alerts": error_health["total_alerts"],
                "active_alerts": error_health["active_alerts"],
                "overall_status": error_health["overall_status"]
            }
        except Exception as e:
            stats["error_monitoring"] = {"error": str(e)}
        
        # Deadlock detector statistics
        try:
            deadlock_detector = get_deadlock_detector()
            lock_status = deadlock_detector.get_lock_status()
            stats["concurrency"] = {
                "active_locks": lock_status["total_active_locks"],
                "monitoring_active": lock_status["monitoring_active"],
                **lock_status["stats"]
            }
        except Exception as e:
            stats["concurrency"] = {"error": str(e)}
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get statistics: {str(e)}"
        )