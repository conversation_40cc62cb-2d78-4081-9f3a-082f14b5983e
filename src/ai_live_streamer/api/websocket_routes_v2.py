"""WebSocket v2 路由集成

FastAPI路由定义，集成WebSocket v2协议处理器。

Author: <PERSON> Code
Date: 2025-08-08
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, Depends
from typing import Optional
from loguru import logger

from .websocket_v2 import WebSocketV2Handler
from .enhanced_websocket_v2 import EnhancedWebSocketV2Handler
from .websocket_constants import (
    CLOSE_CODE_SESSION_ENDED,
    CLOSE_CODE_SESSION_TIMEOUT,
    CLOSE_REASONS,
    get_close_message
)
from ..core.dependencies import get_websocket_v2_handler, get_enhanced_websocket_v2_handler


# 创建路由器
router = APIRouter(prefix="/ws/v2", tags=["WebSocket v2"])


@router.websocket("/stream")
async def websocket_v2_stream(
    websocket: WebSocket,
    client_id: str = Query(..., description="客户端唯一标识符"),
    session_id: str = Query(..., description="会话标识符（必需，从/api/control/start-stream获取）"),
    protocol_version: str = Query("2.0", description="协议版本"),
    handler: WebSocketV2Handler = Depends(get_websocket_v2_handler)
):
    """
    WebSocket v2 流式连接端点
    
    支持功能：
    - 请求-响应通信模式
    - QA动态插入
    - 状态恢复
    - 智能预合成
    - 实时监控
    
    Query参数：
    - client_id: 客户端唯一标识符（必需）
    - session_id: 会话标识符（必需，必须先调用/api/control/start-stream）
    - protocol_version: 协议版本，默认2.0
    """
    
    logger.info(f"WebSocket v2连接请求: client_id={client_id}, session_id={session_id}, "
               f"protocol_version={protocol_version}")
    
    # 验证协议版本
    if protocol_version != "2.0":
        logger.warning(f"不支持的协议版本: {protocol_version}")
        await websocket.close(code=1002, reason="unsupported_protocol_version")
        return
    
    # Import needed modules
    from ..api.control import _active_sessions
    from datetime import datetime
    import asyncio
    
    session_info = None
    
    if session_id in _active_sessions:
        # Session exists - normal case
        session_info = _active_sessions[session_id]
        session_status = session_info.get("status", "unknown")
        logger.info(f"✅ Session verified: {session_id} (status: {session_status})")
        
    else:
        # Session not found - it has ended or never existed
        logger.info(f"📭 Session {session_id} not found for client {client_id} - session has ended or never existed")
        
        # Accept connection briefly to send a proper message
        await websocket.accept()
        
        # Send clear message to client about session status
        session_ended_message = {
            "type": "session_ended",
            "session_id": session_id,
            "client_id": client_id,
            "message": get_close_message(CLOSE_CODE_SESSION_ENDED),
            "action": "restart_required",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            await websocket.send_json(session_ended_message)
            logger.debug(f"Sent session_ended message to client {client_id}")
        except Exception as e:
            logger.warning(f"Failed to send session_ended message to client {client_id}: {e}")
        
        # Close with specific code for session ended
        # Client should rely on close code, message is just enhancement
        await websocket.close(
            code=CLOSE_CODE_SESSION_ENDED,
            reason=CLOSE_REASONS[CLOSE_CODE_SESSION_ENDED]
        )
        
        logger.info(f"🚫 Connection closed for client {client_id} with session {session_id} - code {CLOSE_CODE_SESSION_ENDED}")
        return
    
    # Ensure session is ready before proceeding
    if "ready_event" in session_info:
        try:
            await asyncio.wait_for(session_info["ready_event"].wait(), timeout=5.0)
        except asyncio.TimeoutError:
            logger.error(f"⏰ Session ready timeout: {session_id}")
            await websocket.close(
                code=CLOSE_CODE_SESSION_TIMEOUT,
                reason=CLOSE_REASONS[CLOSE_CODE_SESSION_TIMEOUT]
            )
            return
        
    # No recovery info needed as we no longer support session recovery
    recovery_info = {
        "session_recovered": False,
        "recovery_method": "none"
    }
    
    # 委托给处理器，传递恢复信息
    await handler.handle_connection(websocket, client_id, session_id, recovery_info)


@router.websocket("/stream/enhanced")
async def websocket_v2_stream_enhanced(
    websocket: WebSocket,
    client_id: str = Query(..., description="客户端唯一标识符"),
    session_id: str = Query(..., description="会话标识符（必需，从/api/control/start-stream获取）"),
    protocol_version: str = Query("2.0", description="协议版本"),
    handler: EnhancedWebSocketV2Handler = Depends(get_enhanced_websocket_v2_handler)
):
    """
    增强版 WebSocket v2 流式连接端点
    
    支持功能：
    - 请求-响应通信模式
    - QA动态插入 (增强版，支持知识库和智能路由)
    - 状态恢复
    - 智能预合成
    - 实时监控
    - 批量QA处理
    - 优先级排序
    
    Query参数：
    - client_id: 客户端唯一标识符（必需）
    - session_id: 会话标识符（必需，必须先调用/api/control/start-stream）
    - protocol_version: 协议版本，默认2.0
    """
    
    logger.info(f"Enhanced WebSocket v2连接请求: client_id={client_id}, session_id={session_id}, "
               f"protocol_version={protocol_version}")
    
    # 验证协议版本
    if protocol_version != "2.0":
        logger.warning(f"不支持的协议版本: {protocol_version}")
        await websocket.close(code=1002, reason="unsupported_protocol_version")
        return
    
    # Import needed modules
    from ..api.control import _active_sessions
    from datetime import datetime
    import asyncio
    
    session_info = None
    
    if session_id in _active_sessions:
        # Session exists - normal case
        session_info = _active_sessions[session_id]
        session_status = session_info.get("status", "unknown")
        logger.info(f"✅ Enhanced Session verified: {session_id} (status: {session_status})")
        
    else:
        # Session not found - it has ended or never existed
        logger.info(f"📭 Enhanced Session {session_id} not found for client {client_id}")
        
        # Accept connection briefly to send a proper message
        await websocket.accept()
        
        # Send clear message to client about session status
        session_ended_message = {
            "type": "session_ended",
            "session_id": session_id,
            "client_id": client_id,
            "message": get_close_message(CLOSE_CODE_SESSION_ENDED),
            "action": "restart_required",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            await websocket.send_json(session_ended_message)
            logger.debug(f"Sent session_ended message to client {client_id}")
        except Exception as e:
            logger.warning(f"Failed to send session_ended message to client {client_id}: {e}")
        
        # Close with specific code for session ended
        await websocket.close(
            code=CLOSE_CODE_SESSION_ENDED,
            reason=CLOSE_REASONS[CLOSE_CODE_SESSION_ENDED]
        )
        
        logger.info(f"🚫 Enhanced connection closed for client {client_id} with session {session_id}")
        return
    
    # Ensure session is ready before proceeding
    if "ready_event" in session_info:
        try:
            await asyncio.wait_for(session_info["ready_event"].wait(), timeout=5.0)
        except asyncio.TimeoutError:
            logger.error(f"⏰ Enhanced Session ready timeout: {session_id}")
            await websocket.close(
                code=CLOSE_CODE_SESSION_TIMEOUT,
                reason=CLOSE_REASONS[CLOSE_CODE_SESSION_TIMEOUT]
            )
            return
        
    # No recovery info needed as we no longer support session recovery
    recovery_info = {
        "session_recovered": False,
        "recovery_method": "none"
    }
    
    # 委托给增强版处理器
    await handler.handle_connection(websocket, client_id, session_id, recovery_info)


@router.websocket("/admin")
async def websocket_v2_admin(
    websocket: WebSocket,
    admin_token: str = Query(..., description="管理员令牌"),
    handler: WebSocketV2Handler = Depends(get_websocket_v2_handler)
):
    """
    WebSocket v2 管理连接端点
    
    用于系统监控和管理：
    - 实时连接统计
    - 播放列表状态监控
    - 系统健康检查
    - 强制客户端断开
    """
    
    # 简单的令牌验证（实际应用中应使用更安全的验证）
    # TODO: 集成到配置系统中
    if admin_token != "admin_debug_token_2025":
        logger.warning("无效的管理员令牌")
        await websocket.close(code=1008, reason="invalid_admin_token")
        return
        
    logger.info("WebSocket v2管理连接已建立")
    
    try:
        await websocket.accept()
        
        # 发送初始状态
        stats = await handler.get_connection_stats()
        await websocket.send_json({
            "type": "admin_connected",
            "stats": stats,
            "server_time": __import__('time').time()
        })
        
        # 管理消息循环
        while True:
            try:
                message = await websocket.receive_json()
                command = message.get("command")
                
                if command == "get_stats":
                    stats = await handler.get_connection_stats()
                    await websocket.send_json({
                        "type": "stats_response",
                        "stats": stats
                    })
                    
                elif command == "get_clients":
                    clients = handler.get_connected_clients()
                    await websocket.send_json({
                        "type": "clients_response", 
                        "clients": list(clients)
                    })
                    
                elif command == "disconnect_client":
                    client_id = message.get("client_id")
                    reason = message.get("reason", "admin_request")
                    
                    if client_id:
                        success = await handler.force_disconnect_client(client_id, reason)
                        await websocket.send_json({
                            "type": "disconnect_response",
                            "client_id": client_id,
                            "success": success
                        })
                    else:
                        await websocket.send_json({
                            "type": "error",
                            "message": "client_id is required"
                        })
                        
                else:
                    await websocket.send_json({
                        "type": "error",
                        "message": f"unknown command: {command}"
                    })
                    
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"管理连接处理错误: {e}")
                await websocket.send_json({
                    "type": "error",
                    "message": "internal_server_error"
                })
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"管理连接错误: {e}")
    finally:
        logger.info("WebSocket v2管理连接已断开")