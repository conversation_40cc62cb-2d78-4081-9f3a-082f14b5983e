"""WebSocket protocol constants and close codes

This module defines all WebSocket close codes used in the application.
Close codes follow the WebSocket protocol specification:
- 1000-1999: Standard codes defined by RFC 6455
- 4000-4999: Application-specific codes reserved for private use
"""

# Standard WebSocket close codes (1000-1999)
CLOSE_CODE_NORMAL = 1000           # Normal closure
CLOSE_CODE_GOING_AWAY = 1001       # Endpoint is going away
CLOSE_CODE_PROTOCOL_ERROR = 1002   # Protocol error
CLOSE_CODE_UNSUPPORTED_DATA = 1003 # Unsupported data type
CLOSE_CODE_ABNORMAL = 1006         # Abnormal closure (no close frame)
CLOSE_CODE_INVALID_DATA = 1007     # Invalid frame payload data
CLOSE_CODE_POLICY_VIOLATION = 1008 # Policy violation
CLOSE_CODE_MESSAGE_TOO_BIG = 1009  # Message too big
CLOSE_CODE_EXTENSION_ERROR = 1010  # Extension negotiation failure
CLOSE_CODE_INTERNAL_ERROR = 1011   # Internal server error

# Custom application close codes (4000-4999)
# These codes are reserved for application-specific use
CLOSE_CODE_SESSION_NOT_FOUND = 4001  # Session never existed or has been deleted
CLOSE_CODE_SESSION_TIMEOUT = 4002    # Session initialization or ready timeout  
CLOSE_CODE_SESSION_ENDED = 4003      # Session has ended normally (client should not reconnect)
CLOSE_CODE_AUTH_FAILED = 4004        # Authentication failed (reserved for future use)
CLOSE_CODE_RATE_LIMIT = 4005         # Rate limit exceeded (reserved for future use)

# Close code descriptions for logging and debugging
CLOSE_REASONS = {
    # Standard codes
    CLOSE_CODE_NORMAL: "normal_closure",
    CLOSE_CODE_GOING_AWAY: "going_away",
    CLOSE_CODE_PROTOCOL_ERROR: "protocol_error",
    CLOSE_CODE_UNSUPPORTED_DATA: "unsupported_data",
    CLOSE_CODE_ABNORMAL: "abnormal_closure",
    CLOSE_CODE_INVALID_DATA: "invalid_data",
    CLOSE_CODE_POLICY_VIOLATION: "policy_violation",
    CLOSE_CODE_MESSAGE_TOO_BIG: "message_too_big",
    CLOSE_CODE_EXTENSION_ERROR: "extension_error",
    CLOSE_CODE_INTERNAL_ERROR: "internal_error",
    
    # Custom codes
    CLOSE_CODE_SESSION_NOT_FOUND: "session_not_found",
    CLOSE_CODE_SESSION_TIMEOUT: "session_timeout",
    CLOSE_CODE_SESSION_ENDED: "session_ended",
    CLOSE_CODE_AUTH_FAILED: "auth_failed",
    CLOSE_CODE_RATE_LIMIT: "rate_limit_exceeded"
}

# Human-readable messages for client display
CLOSE_MESSAGES = {
    CLOSE_CODE_SESSION_NOT_FOUND: "会话不存在或已被删除",
    CLOSE_CODE_SESSION_TIMEOUT: "会话初始化超时",
    CLOSE_CODE_SESSION_ENDED: "会话已结束，请重新开始直播",
    CLOSE_CODE_AUTH_FAILED: "认证失败",
    CLOSE_CODE_RATE_LIMIT: "请求频率过高，请稍后重试"
}

def get_close_reason(code: int) -> str:
    """Get the reason string for a close code
    
    Args:
        code: WebSocket close code
        
    Returns:
        Reason string for the code, or 'unknown' if not found
    """
    return CLOSE_REASONS.get(code, "unknown")

def get_close_message(code: int) -> str:
    """Get the human-readable message for a close code
    
    Args:
        code: WebSocket close code
        
    Returns:
        Human-readable message for the code, or generic message if not found
    """
    return CLOSE_MESSAGES.get(code, "连接已关闭")