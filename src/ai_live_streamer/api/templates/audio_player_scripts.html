<!-- Web Audio Player V2 模块化脚本 -->
<!-- 按依赖顺序加载 -->
<script src="/static/js/connection.js"></script>
<script src="/static/js/audio_player.js"></script>
<script src="/static/js/protocol.js"></script>
<script src="/static/js/web_audio_player_v2.js"></script>
<script src="/static/js/player_loader.js"></script>

<!-- 向后兼容：创建别名 -->
<script>
// 保持向后兼容，使用V2版本
if (window.WebAudioPlayerV2 && !window.WebAudioPlayer) {
    window.WebAudioPlayer = window.WebAudioPlayerV2;
}
</script>