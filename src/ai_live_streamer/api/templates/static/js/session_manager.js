/**
 * SessionManager - 统一的会话状态管理器
 * 
 * 作为整个应用中会话状态的单一事实源（Single Source of Truth）
 * 负责管理会话的生命周期、状态转换和错误处理
 * 
 * Author: Claude Code
 * Date: 2025-08-11
 */

// WebSocket 关闭码常量（与服务器保持一致）
const WS_CLOSE_CODES = {
    NORMAL: 1000,                // 正常关闭
    GOING_AWAY: 1001,             // 端点离开
    SESSION_NOT_FOUND: 4001,      // 会话不存在
    SESSION_TIMEOUT: 4002,        // 会话超时
    SESSION_ENDED: 4003           // 会话已结束（新增）
};

class SessionManager {
    constructor() {
        // 会话基础信息
        this.sessionId = null;
        this.clientId = null;
        
        // 状态机
        // IDLE: 无会话
        // CONNECTING: 正在建立连接
        // ACTIVE: 会话活跃
        // ENDED: 会话正常结束
        // ERROR: 会话异常结束
        this.state = 'IDLE';
        
        // 事件监听器集合
        this.listeners = new Set();
        
        // 状态历史（用于调试）
        this.stateHistory = [];
        this.maxHistorySize = 20;
        
        console.log('📦 SessionManager initialized');
    }
    
    /**
     * 启动新会话
     * @param {string} sessionId - 会话ID
     * @param {string} clientId - 客户端ID（可选）
     */
    startSession(sessionId, clientId = null) {
        if (!sessionId) {
            console.error('❌ Cannot start session without sessionId');
            return false;
        }
        
        // 如果已有活跃会话，先结束它
        if (this.sessionId && this.state === 'ACTIVE') {
            console.warn('⚠️ Ending existing session before starting new one');
            this.endSession('replaced');
        }
        
        this.sessionId = sessionId;
        this.clientId = clientId || this.generateClientId();
        this.updateState('CONNECTING');
        
        // 持久化到 sessionStorage
        sessionStorage.setItem('currentSessionId', sessionId);
        sessionStorage.setItem('currentClientId', this.clientId);
        
        console.log(`🚀 Session started: ${sessionId} (client: ${this.clientId})`);
        
        this.notifyListeners('session-started', {
            sessionId: this.sessionId,
            clientId: this.clientId,
            timestamp: Date.now()
        });
        
        return true;
    }
    
    /**
     * 激活会话（连接成功后调用）
     */
    activateSession() {
        if (this.state !== 'CONNECTING') {
            console.warn(`⚠️ Cannot activate session from state: ${this.state}`);
            return false;
        }
        
        this.updateState('ACTIVE');
        console.log('✅ Session activated');
        
        this.notifyListeners('session-active', {
            sessionId: this.sessionId,
            clientId: this.clientId,
            timestamp: Date.now()
        });
        
        return true;
    }
    
    /**
     * 结束会话
     * @param {string} reason - 结束原因：normal, error, session-invalid, replaced, cleanup
     */
    endSession(reason = 'normal') {
        if (this.state === 'IDLE') {
            console.log('ℹ️ No active session to end');
            return;
        }
        
        const oldSessionId = this.sessionId;
        const oldClientId = this.clientId;
        
        // 清理状态
        this.sessionId = null;
        this.clientId = null;
        
        // 更新状态机
        const newState = (reason === 'error' || reason === 'session-invalid') ? 'ERROR' : 'ENDED';
        this.updateState(newState);
        
        // 清理持久化存储
        sessionStorage.removeItem('currentSessionId');
        sessionStorage.removeItem('currentClientId');
        
        console.log(`🔚 Session ended: ${oldSessionId} (reason: ${reason})`);
        
        // 显示用户反馈
        this.showUserFeedback(reason);
        
        // 通知所有监听器
        this.notifyListeners('session-ended', {
            sessionId: oldSessionId,
            clientId: oldClientId,
            reason: reason,
            state: newState,
            timestamp: Date.now()
        });
    }
    
    /**
     * 处理WebSocket连接错误
     * @param {number} errorCode - 错误码
     * @param {string} errorReason - 错误原因
     * @returns {boolean} - true表示已处理（不需要重连），false表示可以尝试重连
     */
    handleConnectionError(errorCode, errorReason) {
        console.log(`🔍 Handling connection error: ${errorCode} - ${errorReason}`);
        
        // 会话已结束 - 清理状态并不重连
        if (errorCode === WS_CLOSE_CODES.SESSION_ENDED) {
            console.log('🔴 Session has ended, clearing state');
            this.endSession('session-ended');
            // 可选：显示用户消息提示重新开始
            this.showMessage('会话已结束，请重新开始直播', 'info');
            return true; // 已处理，不重连
        }
        
        // 会话无效或未找到
        if (errorCode === WS_CLOSE_CODES.SESSION_NOT_FOUND || errorCode === 403) {
            console.log('🔴 Session invalid or not found, clearing state');
            this.endSession('session-invalid');
            return true; // 已处理，不重连
        }
        
        // 会话超时
        if (errorCode === WS_CLOSE_CODES.SESSION_TIMEOUT) {
            console.log('⏱️ Session initialization timeout');
            this.endSession('timeout');
            return true; // 已处理，不重连
        }
        
        // 正常关闭
        if (errorCode === WS_CLOSE_CODES.NORMAL) {
            this.endSession('normal');
            return true; // 正常关闭，不重连
        }
        
        // 其他错误可以尝试重连
        return false;
    }
    
    /**
     * 显示用户反馈消息
     * @param {string} reason - 结束原因
     */
    showUserFeedback(reason) {
        let message = '';
        let type = 'info';
        
        switch(reason) {
            case 'session-invalid':
                message = '⚠️ 会话已失效，请重新开始直播';
                type = 'warning';
                break;
            case 'error':
                message = '❌ 连接已断开，请检查网络后重新开始';
                type = 'error';
                break;
            case 'normal':
                message = '✅ 直播已正常结束';
                type = 'success';
                break;
            case 'replaced':
                message = 'ℹ️ 已开始新的直播会话';
                type = 'info';
                break;
            case 'cleanup':
                // 清理时不显示消息
                return;
        }
        
        // 调用全局通知函数（如果存在）
        if (message) {
            if (typeof window.showNotification === 'function') {
                window.showNotification(message, type);
            } else {
                // 降级到 console
                console.log(`[${type.toUpperCase()}] ${message}`);
                
                // 尝试使用简单的 alert 作为最后手段
                if (type === 'error' || type === 'warning') {
                    // 延迟显示，避免阻塞其他操作
                    setTimeout(() => {
                        if (confirm(message + '\n\n点击确定返回主界面')) {
                            // 可以在这里触发返回主界面的逻辑
                            if (typeof window.resetUIToInitialState === 'function') {
                                window.resetUIToInitialState();
                            }
                        }
                    }, 100);
                }
            }
        }
    }
    
    /**
     * 更新状态并记录历史
     * @param {string} newState - 新状态
     */
    updateState(newState) {
        const oldState = this.state;
        this.state = newState;
        
        // 记录状态转换历史
        this.stateHistory.push({
            from: oldState,
            to: newState,
            timestamp: Date.now()
        });
        
        // 限制历史记录大小
        if (this.stateHistory.length > this.maxHistorySize) {
            this.stateHistory.shift();
        }
        
        console.log(`📊 State transition: ${oldState} → ${newState}`);
    }
    
    /**
     * 恢复已存在的会话
     * @param {string} sessionId - 要恢复的会话ID
     * @param {Object} sessionStatus - 从后端获取的会话状态
     * @returns {boolean} - 是否成功开始恢复
     */
    restoreSession(sessionId, sessionStatus) {
        if (!sessionId || !sessionStatus) {
            console.error('❌ Cannot restore session without valid data');
            return false;
        }
        
        // 如果已有活跃会话且不是同一个，先结束它
        if (this.sessionId && this.sessionId !== sessionId && this.state === 'ACTIVE') {
            console.warn('⚠️ Ending existing session before restoring');
            this.endSession('replaced');
        }
        
        // 恢复会话状态
        this.sessionId = sessionId;
        this.clientId = sessionStorage.getItem('currentClientId') || this.generateClientId();
        this.updateState('CONNECTING');
        
        // 确保持久化
        sessionStorage.setItem('currentSessionId', sessionId);
        sessionStorage.setItem('currentClientId', this.clientId);
        
        console.log(`🔄 Restoring session: ${sessionId}`, sessionStatus);
        
        // 触发恢复事件
        this.notifyListeners('session-restoring', {
            sessionId: this.sessionId,
            clientId: this.clientId,
            sessionStatus: sessionStatus,
            timestamp: Date.now()
        });
        
        return true;
    }
    
    /**
     * 生成客户端ID
     * @returns {string} - 客户端ID
     */
    generateClientId() {
        return 'client_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 注册事件监听器
     * @param {Function} callback - 回调函数
     */
    addListener(callback) {
        if (typeof callback === 'function') {
            this.listeners.add(callback);
            console.log(`👂 Listener added (total: ${this.listeners.size})`);
        }
    }
    
    /**
     * 移除事件监听器
     * @param {Function} callback - 回调函数
     */
    removeListener(callback) {
        if (this.listeners.delete(callback)) {
            console.log(`👋 Listener removed (remaining: ${this.listeners.size})`);
        }
    }
    
    /**
     * 通知所有监听器
     * @param {string} event - 事件名称
     * @param {Object} data - 事件数据
     */
    notifyListeners(event, data) {
        console.log(`📡 Broadcasting event: ${event}`, data);
        
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('❌ Listener error:', error);
            }
        });
    }
    
    /**
     * 获取当前状态
     * @returns {Object} - 当前状态信息
     */
    getState() {
        return {
            sessionId: this.sessionId,
            clientId: this.clientId,
            state: this.state,
            isActive: this.state === 'ACTIVE',
            isConnecting: this.state === 'CONNECTING',
            canConnect: this.state === 'IDLE' || this.state === 'ENDED' || this.state === 'ERROR'
        };
    }
    
    /**
     * 获取状态历史（用于调试）
     * @returns {Array} - 状态转换历史
     */
    getStateHistory() {
        return [...this.stateHistory];
    }
    
    /**
     * 重置到初始状态（用于测试或强制重置）
     */
    reset() {
        console.log('🔄 Resetting SessionManager');
        this.endSession('cleanup');
        this.stateHistory = [];
        this.listeners.clear();
    }
}

// 创建全局单例实例
if (typeof window !== 'undefined') {
    window.sessionManager = new SessionManager();
    console.log('✅ Global sessionManager instance created');
}

// 导出类（用于测试或其他模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SessionManager;
}