/**
 * WebSocket Connection Manager
 * 负责WebSocket连接的生命周期管理
 */
class ConnectionManager {
    constructor(config = {}) {
        this.websocket = null;
        this.isConnected = false;
        this.reconnectInterval = config.reconnectInterval || 3000;
        this.maxReconnectAttempts = config.maxReconnectAttempts || 5;
        this.reconnectAttempts = 0;
        this.heartbeatInterval = null;
        this.sessionId = null;
        
        // 回调函数
        this.onMessage = null;
        this.onOpen = null;
        this.onClose = null;
        this.onError = null;
    }
    
    /**
     * 建立WebSocket连接
     */
    async connect(sessionId, queryParams = {}) {
        if (this.isConnected) {
            console.log('⚠️ Already connected');
            return;
        }
        
        this.sessionId = sessionId;
        
        // WebSocket 403修复: 生成client_id如果没有提供
        if (!queryParams.client_id) {
            queryParams.client_id = 'client_' + Math.random().toString(36).substr(2, 9);
            console.log('🆔 Generated client_id:', queryParams.client_id);
        }
        
        // 构建WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        
        // WebSocket 403修复: 确保参数顺序和名称正确
        const params = new URLSearchParams({
            client_id: queryParams.client_id,  // 必需参数，放在前面
            session_id: sessionId,              // 必需参数
            protocol_version: queryParams.protocol_version || '2.0',  // 修正协议版本，默认2.0
            ...queryParams
        });
        
        // WebSocket 403修复: 修正URL路径从 /api/ws/v2/audio 到 /ws/v2/stream
        const url = `${protocol}//${host}/ws/v2/stream?${params}`;
        
        console.log('🔗 Connecting to WebSocket:', url);
        
        try {
            this.websocket = new WebSocket(url);
            this.setupHandlers();
        } catch (error) {
            console.error('❌ Failed to create WebSocket:', error);
            if (this.onError) this.onError(error);
            this.scheduleReconnect();
        }
    }
    
    /**
     * 设置WebSocket事件处理器
     */
    setupHandlers() {
        if (!this.websocket) return;
        
        this.websocket.onopen = (event) => {
            console.log('✅ WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            if (this.onOpen) this.onOpen(event);
        };
        
        this.websocket.onmessage = (event) => {
            if (this.onMessage) this.onMessage(event);
        };
        
        this.websocket.onclose = (event) => {
            console.log('🔌 WebSocket disconnected:', event.code, event.reason);
            this.isConnected = false;
            this.stopHeartbeat();
            
            // 使用 SessionManager 处理会话错误
            let shouldReconnect = true;
            if (window.sessionManager) {
                const handled = window.sessionManager.handleConnectionError(event.code, event.reason);
                if (handled) {
                    // 会话无效或正常结束，不进行重连
                    console.log('📌 Session ended by SessionManager, stopping reconnection');
                    this.sessionId = null;
                    shouldReconnect = false;
                }
            }
            
            if (this.onClose) this.onClose(event);
            
            // 根据 SessionManager 的决定和其他条件决定是否重连
            if (shouldReconnect && 
                event.code !== 1000 && 
                this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            }
        };
        
        this.websocket.onerror = (error) => {
            console.error('❌ WebSocket error:', error);
            if (this.onError) this.onError(error);
        };
    }
    
    /**
     * 发送消息
     */
    send(data) {
        if (!this.isConnected || !this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
            console.warn('⚠️ Cannot send message - WebSocket not connected');
            return false;
        }
        
        try {
            const message = typeof data === 'string' ? data : JSON.stringify(data);
            this.websocket.send(message);
            return true;
        } catch (error) {
            console.error('❌ Failed to send message:', error);
            return false;
        }
    }
    
    /**
     * 断开连接
     */
    disconnect() {
        console.log('🔌 Disconnecting WebSocket...');
        this.stopHeartbeat();
        
        if (this.websocket) {
            this.websocket.close(1000, 'Client disconnect');
            this.websocket = null;
        }
        
        this.isConnected = false;
    }
    
    /**
     * 开始心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({ type: 'heartbeat', timestamp: Date.now() });
            }
        }, 30000); // 每30秒发送一次心跳
    }
    
    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ Max reconnection attempts reached');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1);
        
        console.log(`⏳ Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (this.sessionId) {
                this.connect(this.sessionId);
            }
        }, delay);
    }
}

// 导出给其他模块使用
window.ConnectionManager = ConnectionManager;