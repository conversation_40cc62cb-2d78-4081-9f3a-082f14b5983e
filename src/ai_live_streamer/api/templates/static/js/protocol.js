/**
 * Protocol Handler
 * 负责V2协议消息的解析和处理
 */
class ProtocolHandler {
    constructor() {
        this.sessionId = null;
        this.serverConfig = null;
        this.clientId = this.generateClientId();
        
        // 回调函数
        this.onAudioData = null;
        this.onSessionConfig = null;
        this.onStreamEnd = null;
        this.onError = null;
        this.onContentNext = null;
        this.onQAResponse = null;
        this.onStateUpdate = null;  // WebSocket架构重构：状态更新回调
        this.onPlaylistInfo = null;  // 播放列表信息回调
    }
    
    /**
     * 生成客户端ID
     */
    generateClientId() {
        return 'client_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 处理WebSocket消息
     */
    handleMessage(event) {
        try {
            // 处理二进制音频数据
            if (event.data instanceof Blob || event.data instanceof ArrayBuffer) {
                this.handleBinaryAudio(event.data);
                return;
            }
            
            // 处理JSON消息
            const message = JSON.parse(event.data);
            const messageType = message.type;
            
            console.log(`📨 Received message: ${messageType}`, message);
            
            switch (messageType) {
                case 'connection_established':
                    this.handleConnectionEstablished(message);
                    break;
                    
                case 'session_config':
                    this.handleSessionConfig(message);
                    break;
                    
                case 'audio_chunk':
                    this.handleAudioChunk(message);
                    break;
                    
                case 'content':
                    this.handleContent(message);
                    break;
                    
                case 'content_next':
                    this.handleContentNext(message);
                    break;
                    
                case 'stream_end':
                    this.handleStreamEnd(message);
                    break;
                    
                case 'qa_response':
                    this.handleQAResponse(message);
                    break;
                    
                case 'state_update':
                    // WebSocket架构重构：处理状态更新消息
                    this.handleStateUpdate(message);
                    break;
                    
                case 'item_inserted':
                    // 处理播放列表项目插入事件
                    this.handleItemInserted(message);
                    break;
                    
                case 'playlist_updated':
                    // 处理播放列表更新事件
                    this.handlePlaylistUpdated(message);
                    break;
                    
                case 'playlist_info_response':
                    // 处理播放列表信息响应
                    this.handlePlaylistInfoResponse(message);
                    break;
                    
                    
                case 'error':
                    this.handleError(message);
                    break;
                    
                case 'content_not_ready':
                    this.handleContentNotReady(message);
                    break;
                    
                case 'heartbeat':
                    // 心跳响应，忽略
                    break;
                    
                default:
                    console.warn('⚠️ Unknown message type:', messageType);
            }
        } catch (error) {
            console.error('❌ Failed to handle message:', error);
            if (this.onError) this.onError(error);
        }
    }
    
    /**
     * 处理连接建立
     */
    handleConnectionEstablished(message) {
        this.sessionId = message.session_id;
        
        // 新增：立即处理初始播放列表信息
        if (message.playlist_info) {
            console.log('✅ 连接建立，包含初始播放列表:', {
                version: message.playlist_info.version,
                total_items: message.playlist_info.total_items
            });
            
            // 设置初始播放列表状态
            if (this.player) {
                this.player.currentPlaylistVersion = message.playlist_info.version;
                this.player.totalPlaylistItems = message.playlist_info.total_items;
                this.player.nextIndexToRequest = message.playlist_info.start_index || 0;
            }
        }
        
        console.log('✅ Connection established:', {
            sessionId: this.sessionId,
            serverVersion: message.server_version,
            protocolVersion: 'v2.1',
            playlistVersion: message.playlist_info?.version || 'unknown'
        });
        
        // 发送协议版本声明（可选，增强兼容性）
        if (this.onProtocolReady) {
            this.onProtocolReady({
                type: 'protocol_version',
                version: 'v2.1',
                client_id: this.clientId
            });
        }
    }
    
    /**
     * 处理会话配置
     */
    handleSessionConfig(message) {
        this.serverConfig = message.audio_config;
        
        console.log('🔧 Session config received:', this.serverConfig);
        
        // 通知音频播放器更新配置
        if (this.onSessionConfig) {
            this.onSessionConfig({
                sampleRate: this.serverConfig.sample_rate,
                channels: this.serverConfig.channels,
                bitDepth: this.serverConfig.bit_depth
            });
        }
    }
    
    /**
     * 处理音频数据块 (已废弃 - 统一使用二进制协议)
     */
    handleAudioChunk(message) {
        console.warn('⚠️ Received JSON audio chunk - protocol mismatch! Expected binary frames.');
        // 不再处理Base64编码的音频，要求服务端使用二进制帧
    }
    
    /**
     * 处理二进制音频数据
     */
    async handleBinaryAudio(data) {
        let arrayBuffer;
        
        if (data instanceof Blob) {
            arrayBuffer = await data.arrayBuffer();
        } else {
            arrayBuffer = data;
        }
        
        console.log(`📦 Binary audio received: ${arrayBuffer.byteLength} bytes`);
        
        // 注意：当前协议不应该发送纯二进制音频，所有音频都应该包含在 JSON content 消息中
        console.warn('⚠️ Received pure binary audio - this may indicate a protocol mismatch!');
        
        // 传递给音频播放器，但标记为异常情况
        if (this.onAudioData) {
            this.onAudioData(arrayBuffer, {
                isBinary: true,
                index: undefined,  // 明确标记索引未知
                request_id: 'binary_unknown',
                warning: 'Binary audio without metadata - protocol violation'
            });
        }
    }
    
    /**
     * 处理content消息（包含音频数据）
     */
    handleContent(message) {
        try {
            // 解码base64音频数据（API契约v2.0使用"data"字段）
            const audioDataField = message.data || message.audio_data;  // 兼容旧格式
            if (!audioDataField) {
                throw new Error('No audio data field found in content message');
            }
            const audioBuffer = this.base64ToArrayBuffer(audioDataField);
            
            // 提取请求追踪ID（如果有）
            const requestId = message.request_id || 'unknown';
            
            console.log(`📦 [${requestId}] Content received: index=${message.index}, size=${audioBuffer.byteLength} bytes`);
            
            // 触发音频数据回调，传递完整元数据
            if (this.onAudioData) {
                this.onAudioData(audioBuffer, {
                    index: message.index,
                    request_id: requestId,
                    duration_ms: message.duration_ms,
                    metadata: message.metadata
                });
            }
        } catch (error) {
            console.error('❌ Failed to handle content message:', error);
            if (this.onError) this.onError(error);
        }
    }
    
    /**
     * Base64解码辅助函数（带错误处理）
     */
    base64ToArrayBuffer(base64) {
        try {
            const binaryString = atob(base64);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            return bytes.buffer;
        } catch (error) {
            console.error('❌ Base64 decode failed:', error);
            throw new Error('Invalid audio data format');
        }
    }
    
    /**
     * 处理内容切换
     */
    handleContentNext(message) {
        console.log('➡️ Content next:', message);
        
        if (this.onContentNext) {
            this.onContentNext({
                ready: message.ready,
                waiting: message.waiting,
                contentId: message.content_id,
                message: message.message
            });
        }
    }
    
    /**
     * 处理流结束
     */
    handleStreamEnd(message) {
        console.log('🏁 Stream ended:', message.reason);
        
        // 使用 SessionManager 结束会话
        if (window.sessionManager) {
            // 根据原因确定结束类型
            const endReason = message.reason === 'error' ? 'error' : 'normal';
            window.sessionManager.endSession(endReason);
        }
        
        if (this.onStreamEnd) {
            this.onStreamEnd({
                reason: message.reason,
                stats: message.stats
            });
        }
    }
    
    /**
     * 处理QA响应
     */
    handleQAResponse(message) {
        console.log('💬 QA Response:', message);
        
        if (this.onQAResponse) {
            this.onQAResponse({
                question: message.question,
                answer: message.answer,
                audioData: message.audio_data
            });
        }
    }
    
    /**
     * WebSocket架构重构：处理状态更新
     */
    handleStateUpdate(message) {
        console.log('📊 State update received:', message);
        
        if (this.onStateUpdate) {
            this.onStateUpdate(message.data || message);
        }
    }
    
    /**
     * 处理播放列表项目插入事件
     */
    handleItemInserted(message) {
        console.log('📋 Playlist change detected via item_inserted');
        this.triggerPlaylistSync('item_inserted', message);
    }
    
    /**
     * 处理播放列表更新事件
     */
    handlePlaylistUpdated(message) {
        console.log('📋 Playlist change detected via playlist_updated');
        this.triggerPlaylistSync('playlist_updated', message);
    }
    
    /**
     * 统一的同步触发器
     */
    triggerPlaylistSync(reason, originalMessage) {
        if (!this.player) return;
        
        // 立即进入重新同步状态
        if (typeof this.player.enterResyncState === 'function') {
            this.player.enterResyncState(reason);
        }
        
        // 请求完整的播放列表
        if (typeof this.player.requestFullPlaylistSync === 'function') {
            this.player.requestFullPlaylistSync(reason, originalMessage);
        }
    }
    
    /**
     * 处理播放列表信息响应
     */
    handlePlaylistInfoResponse(message) {
        console.log('📥 Received playlist info response:', message);
        
        // 触发回调
        if (this.onPlaylistInfo) {
            this.onPlaylistInfo(message);
        }
    }
    
    
    /**
     * 处理错误
     */
    handleError(message) {
        console.error('❌ Server error:', message);
        
        if (this.onError) {
            this.onError(new Error(message.message || 'Unknown server error'));
        }
    }
    
    /**
     * 处理内容未准备好响应 (v2.2)
     */
    handleContentNotReady(message) {
        console.log(`⏱️ Content not ready: index=${message.index}, retry_after=${message.retry_after_ms}ms`, message);
        
        // 通知播放器内容尚未准备好，需要重试
        if (this.onContentNotReady) {
            this.onContentNotReady({
                index: message.index,
                request_id: message.request_id,
                retry_after_ms: message.retry_after_ms || 1000,
                item_id: message.item_id,
                message: message.message
            });
        }
    }
    
    /**
     * 构建请求下一段内容的消息
     */
    buildContentRequest(state = {}) {
        // API契约v2.0要求的格式
        return {
            type: 'content_request',
            index: state.index || state.currentIndex || state.playbackPosition || 0,  // API契约要求使用"index"字段，优先使用显式的index
            request_id: state.request_id || `req_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`  // API契约要求的request_id
            // 移除非契约字段，保持消息简洁
        };
    }
    
    /**
     * 构建客户端健康状态消息
     */
    buildHealthReport(metrics = {}) {
        return {
            type: 'client_health',
            client_id: this.clientId,
            session_id: this.sessionId,
            metrics: {
                buffer_size: metrics.bufferSize || 0,
                dropped_frames: metrics.droppedFrames || 0,
                latency: metrics.latency || 0,
                ...metrics
            },
            timestamp: Date.now()
        };
    }
    
    /**
     * 构建QA请求消息
     */
    buildQARequest(question) {
        return {
            type: 'qa_request',
            client_id: this.clientId,
            session_id: this.sessionId,
            question: question,
            timestamp: Date.now()
        };
    }
}

// 导出给其他模块使用
window.ProtocolHandler = ProtocolHandler;