/**
 * Unified Progress Modal Component
 * 统一的进度模态框管理器 - 支持脚本生成和直播初始化
 */

class ProgressModal {
    constructor(modalId, options = {}) {
        this.modalId = modalId;
        this.modal = null;
        this.progressFill = null;
        this.progressPercent = null;
        this.progressStepText = null;
        this.actionBtn = null;
        this.progressInterval = null;
        this.currentProgress = 0;
        this.isActive = false;
        this.abortController = null;
        this.onConfirmCallback = null;
        
        // 配置选项
        this.config = {
            title: options.title || '处理中...',
            icon: options.icon || '🤖',
            steps: options.steps || ['准备中...'],
            cancelable: options.cancelable !== false,
            ...options
        };
    }

    init() {
        this.modal = document.getElementById(this.modalId);
        if (!this.modal) {
            console.error(`Progress modal with id "${this.modalId}" not found`);
            return;
        }

        // 获取子元素
        this.progressFill = this.modal.querySelector('.progress-fill');
        this.progressPercent = this.modal.querySelector('.progress-percent');
        this.progressStepText = this.modal.querySelector('.progress-step');
        this.actionBtn = this.modal.querySelector('.cancel-btn, .confirm-btn');

        // 绑定事件
        if (this.actionBtn) {
            this.actionBtn.addEventListener('click', () => this.handleAction());
        }

        // 点击背景关闭（仅在完成状态时）
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal && !this.isActive) {
                this.hide();
            }
        });
    }

    show() {
        if (!this.modal) return;
        this.modal.style.display = 'flex';
        this.reset();
        this.isActive = true;
    }

    hide() {
        if (!this.modal) return;
        this.modal.style.display = 'none';
        this.cleanup();
    }

    reset() {
        this.currentProgress = 0;
        this.updateProgress(0);
        this.updateStepText(this.config.steps[0] || '准备中...');
        
        const content = this.modal.querySelector('.progress-content');
        if (content) {
            content.className = 'progress-content';
        }

        if (this.actionBtn) {
            this.actionBtn.textContent = this.config.cancelable ? '取消' : '确定';
            this.actionBtn.disabled = false;
            this.actionBtn.style.display = this.config.cancelable ? 'inline-block' : 'none';
        }

        this.onConfirmCallback = null;
    }

    cleanup() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
        this.isActive = false;
        if (this.abortController) {
            this.abortController = null;
        }
    }

    updateProgress(percent) {
        this.currentProgress = Math.min(percent, 100);
        if (this.progressFill) {
            this.progressFill.style.width = this.currentProgress + '%';
        }
        if (this.progressPercent) {
            this.progressPercent.textContent = Math.round(this.currentProgress) + '%';
        }
    }

    updateStepText(text) {
        if (this.progressStepText) {
            this.progressStepText.textContent = text;
        }
    }

    startSimulatedProgress() {
        this.isActive = true;
        let progress = 0;
        let stepIndex = 0;

        this.updateStepText(this.config.steps[0] || '处理中...');

        this.progressInterval = setInterval(() => {
            if (progress < 90) {
                const increment = Math.max(0.5, 8 - progress / 15);
                progress += increment;

                // 更新步骤文本
                const newStepIndex = Math.min(
                    Math.floor(progress / (90 / this.config.steps.length)),
                    this.config.steps.length - 1
                );

                if (newStepIndex !== stepIndex && newStepIndex < this.config.steps.length) {
                    stepIndex = newStepIndex;
                    this.updateStepText(this.config.steps[stepIndex]);
                }

                this.updateProgress(progress);
            }
        }, 600);

        return this.progressInterval;
    }

    jumpToComplete() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
        this.updateProgress(100);
        this.updateStepText('✅ 完成！');
    }

    showSuccess(title, message, onConfirm) {
        this.jumpToComplete();
        
        const content = this.modal.querySelector('.progress-content');
        if (content) {
            content.classList.add('success');
        }

        const titleEl = this.modal.querySelector('.progress-title');
        if (titleEl) {
            titleEl.textContent = title || '操作成功！';
        }

        this.updateStepText(message || '操作已完成');
        
        if (this.actionBtn) {
            this.actionBtn.style.display = 'inline-block';
            this.actionBtn.textContent = '确定';
            this.actionBtn.className = 'confirm-btn';
        }

        this.onConfirmCallback = onConfirm;
        this.isActive = false;
    }

    showError(message = '操作失败') {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }

        const content = this.modal.querySelector('.progress-content');
        if (content) {
            content.classList.add('error');
        }

        this.updateStepText('❌ ' + message);

        if (this.actionBtn) {
            this.actionBtn.style.display = 'inline-block';
            this.actionBtn.textContent = '关闭';
            this.actionBtn.className = 'cancel-btn';
            this.actionBtn.style.background = '#e53e3e';
        }

        this.isActive = false;
    }

    handleAction() {
        if (this.isActive && this.config.cancelable && this.abortController) {
            // 取消操作
            this.abortController.abort();
            this.updateStepText('⏹️ 正在取消...');
            this.actionBtn.disabled = true;
        } else if (this.onConfirmCallback) {
            // 确认回调
            this.onConfirmCallback();
            this.hide();
        } else {
            // 普通关闭
            this.hide();
        }
    }

    // 设置取消控制器
    setAbortController(controller) {
        this.abortController = controller;
    }
}

// 创建预配置的实例
window.ScriptProgressManager = new ProgressModal('scriptProgressModal', {
    title: '正在生成直播脚本...',
    icon: '🤖',
    steps: [
        '📋 正在分析表单内容...',
        '🤖 正在调用AI生成脚本...',
        '📝 正在优化内容结构...',
        '⚡ 正在生成互动环节...',
        '🎯 正在完善销售要点...',
        '🔧 正在进行最终调整...'
    ],
    cancelable: true
});

window.StreamInitManager = new ProgressModal('streamInitModal', {
    title: '正在初始化直播间...',
    icon: '🎥',
    steps: [
        '🔧 正在准备直播环境...',
        '📝 正在解析脚本内容...',
        '🎯 正在配置直播参数...',
        '🎤 正在初始化音频系统...',
        '📡 正在建立直播连接...',
        '✅ 正在完成最终设置...'
    ],
    cancelable: false
});

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.ScriptProgressManager) {
        window.ScriptProgressManager.init();
    }
    if (window.StreamInitManager) {
        window.StreamInitManager.init();
    }
});