/**
 * Audio Player Core
 * 负责PCM音频数据的解码和播放
 */
class AudioPlayer {
    constructor(config = {}) {
        this.audioContext = null;
        this.isPlaying = false;
        this.nextBufferStartTime = 0;
        
        // 音频参数（可动态更新）
        this.sampleRate = config.sampleRate || 24000;
        this.channels = config.channels || 1;
        this.bitDepth = config.bitDepth || 16;
        
        // 播放队列
        this.bufferQueue = [];
        this.activeSourceNodes = [];
        
        // 监控统计
        this.stats = {
            totalChunks: 0,
            totalBytes: 0,
            playedChunks: 0,
            droppedChunks: 0
        };
        
        // 回调函数
        this.onStateChange = null;
        this.onError = null;
        this.onBufferLow = null;
    }
    
    /**
     * 初始化音频上下文
     */
    async initialize() {
        if (this.audioContext) {
            console.log('⚠️ AudioContext already initialized');
            return true;
        }
        
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.sampleRate,
                latencyHint: 'interactive'
            });
            
            console.log('✅ AudioContext initialized:', {
                sampleRate: this.audioContext.sampleRate,
                state: this.audioContext.state,
                baseLatency: this.audioContext.baseLatency
            });
            
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize AudioContext:', error);
            if (this.onError) this.onError(error);
            return false;
        }
    }
    
    /**
     * 更新音频参数（用于协商）
     */
    updateConfig(config) {
        const needsReInit = config.sampleRate && config.sampleRate !== this.sampleRate;
        
        this.sampleRate = config.sampleRate || this.sampleRate;
        this.channels = config.channels || this.channels;
        this.bitDepth = config.bitDepth || this.bitDepth;
        
        console.log('🔧 Audio config updated:', {
            sampleRate: this.sampleRate,
            channels: this.channels,
            bitDepth: this.bitDepth
        });
        
        // 如果采样率变化，需要重新初始化AudioContext
        if (needsReInit && this.audioContext) {
            console.log('🔄 Reinitializing AudioContext with new sample rate...');
            this.audioContext.close();
            this.audioContext = null;
            return this.initialize();
        }
        
        return Promise.resolve(true);
    }
    
    /**
     * 处理PCM数据块
     */
    async processPCMChunk(arrayBuffer) {
        if (!this.audioContext) {
            console.warn('⚠️ AudioContext not initialized');
            return;
        }
        
        // 恢复暂停的上下文
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
            console.log('▶️ AudioContext resumed');
        }
        
        this.stats.totalChunks++;
        this.stats.totalBytes += arrayBuffer.byteLength;
        
        try {
            // PCM数据转换为AudioBuffer
            const audioBuffer = await this.pcmToAudioBuffer(arrayBuffer);
            
            if (audioBuffer) {
                // 推入缓冲队列（修改：不再立即播放）
                this.bufferQueue.push(audioBuffer);
                console.log(`📥 Audio buffered, queue size: ${this.bufferQueue.length}`);
                
                // 处理缓冲队列
                this.processBufferQueue();
            }
        } catch (error) {
            console.error('❌ Failed to process PCM chunk:', error);
            this.stats.droppedChunks++;
            if (this.onError) this.onError(error);
        }
    }
    
    /**
     * 处理缓冲队列（新增）
     */
    processBufferQueue() {
        // 限制并发播放的音频源数量
        const MAX_CONCURRENT_SOURCES = 3;
        
        // 如果活动源太多，等待
        if (this.activeSourceNodes.length >= MAX_CONCURRENT_SOURCES) {
            return;
        }
        
        // 从队列取出音频进行播放
        while (this.bufferQueue.length > 0 && 
               this.activeSourceNodes.length < MAX_CONCURRENT_SOURCES) {
            
            const audioBuffer = this.bufferQueue.shift();
            this.schedulePlayback(audioBuffer);
            this.stats.playedChunks++;
        }
        
        // 检查缓冲区状态
        if (this.bufferQueue.length < 2 && this.onBufferLow) {
            this.onBufferLow();
        }
    }
    
    /**
     * PCM数据转换为AudioBuffer
     */
    async pcmToAudioBuffer(arrayBuffer) {
        const samples = arrayBuffer.byteLength / 2; // 16-bit = 2 bytes per sample
        
        // 创建AudioBuffer
        const audioBuffer = this.audioContext.createBuffer(
            this.channels,
            samples,
            this.sampleRate
        );
        
        // 转换PCM数据
        const dataView = new DataView(arrayBuffer);
        const channelData = audioBuffer.getChannelData(0);
        
        for (let i = 0; i < samples; i++) {
            // 读取16-bit PCM采样（小端序）
            const sample = dataView.getInt16(i * 2, true);
            // 归一化到[-1, 1]范围
            channelData[i] = sample / 32768.0;
        }
        
        return audioBuffer;
    }
    
    /**
     * 调度音频播放
     */
    schedulePlayback(audioBuffer) {
        if (!this.audioContext) return;
        
        const source = this.audioContext.createBufferSource();
        source.buffer = audioBuffer;
        source.connect(this.audioContext.destination);
        
        // 计算播放时间
        const currentTime = this.audioContext.currentTime;
        const startTime = Math.max(currentTime + 0.01, this.nextBufferStartTime);
        
        // 更新下一个缓冲区的开始时间
        this.nextBufferStartTime = startTime + audioBuffer.duration;
        
        // 播放
        source.start(startTime);
        
        // 跟踪活动的源节点
        this.activeSourceNodes.push(source);
        
        // 播放结束后清理
        source.onended = () => {
            const index = this.activeSourceNodes.indexOf(source);
            if (index > -1) {
                this.activeSourceNodes.splice(index, 1);
            }
            
            // 尝试处理更多缓冲（新增）
            this.processBufferQueue();
            
            // 如果没有活动节点且缓冲队列为空，进入idle状态
            if (this.activeSourceNodes.length === 0 && this.bufferQueue.length === 0) {
                this.nextBufferStartTime = 0;
                this.isPlaying = false;
                if (this.onStateChange) this.onStateChange('idle');
            }
        };
        
        this.isPlaying = true;
        if (this.onStateChange) this.onStateChange('playing');
        
        console.log(`🎵 Scheduled playback: ${audioBuffer.duration.toFixed(2)}s at ${startTime.toFixed(2)}s`);
    }
    
    /**
     * 停止播放
     */
    stop() {
        // 停止所有活动的源节点
        this.activeSourceNodes.forEach(source => {
            try {
                source.stop();
            } catch (e) {
                // 忽略已停止的节点
            }
        });
        
        this.activeSourceNodes = [];
        this.bufferQueue = [];
        this.nextBufferStartTime = 0;
        this.isPlaying = false;
        
        if (this.onStateChange) this.onStateChange('stopped');
        
        console.log('⏹️ Playback stopped');
    }
    
    /**
     * 获取播放统计
     */
    getStats() {
        return {
            ...this.stats,
            isPlaying: this.isPlaying,
            activeBuffers: this.activeSourceNodes.length,
            queuedBuffers: this.bufferQueue.length,
            contextState: this.audioContext?.state
        };
    }
    
    /**
     * 清理资源
     */
    async cleanup() {
        this.stop();
        
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }
        
        console.log('🧹 AudioPlayer cleaned up');
    }
}

// 导出给其他模块使用
window.AudioPlayer = AudioPlayer;