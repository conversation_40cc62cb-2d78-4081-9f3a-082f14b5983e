/**
 * <PERSON><PERSON> Console Styles
 * AI直播运营控制台专用样式
 * 
 * 基于 global-design-system.css，提供管理控制台特定的样式
 * Author: Claude Code
 * Version: 1.0.0
 */

/* ============================
   1. Admin Header 
   ============================ */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 0;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.25);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 50%;
    height: 200%;
    background: rgba(255, 255, 255, 0.05);
    transform: rotate(45deg);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* ============================
   2. Navigation Tabs
   ============================ */
.nav-container {
    background: white;
    border-bottom: 1px solid #e1e5e9;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-tabs {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    padding: 0 20px;
}

.nav-tab {
    padding: 15px 25px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    color: #667eea;
    background-color: #f8f9ff;
}

.nav-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background-color: #f8f9ff;
}

/* ============================
   3. Main Layout
   ============================ */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* ============================
   4. Dashboard Components
   ============================ */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border: 1px solid #e8ecf1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
    border-color: #d4d9f2;
}

.dashboard-card:hover::before {
    transform: scaleX(1);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.card-description {
    color: #718096;
    margin-bottom: 20px;
}

.card-actions {
    display: flex;
    gap: 10px;
}

/* ============================
   5. Workflow Guide
   ============================ */
.workflow-guide {
    background: white;
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e1e5e9;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    animation: slideInFromTop 0.6s ease-out;
}

.workflow-header {
    text-align: center;
    margin-bottom: 35px;
}

.workflow-title {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.workflow-subtitle {
    color: #718096;
    font-size: 14px;
}

.workflow-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 0 20px;
}

.workflow-step {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.workflow-step:hover {
    transform: translateY(-5px);
}

.workflow-step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    color: #4a5568;
    margin-bottom: 12px;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.workflow-step.active .workflow-step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.workflow-step.completed .workflow-step-number {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.workflow-step-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.workflow-step-title {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
    text-align: center;
}

.workflow-step-desc {
    font-size: 12px;
    color: #718096;
    text-align: center;
    max-width: 120px;
}

.workflow-connector {
    position: absolute;
    top: 25px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e2e8f0;
    z-index: 1;
}

.workflow-connector-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.5s ease;
}

.workflow-actions {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 15px;
}

.workflow-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.workflow-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.workflow-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* ============================
   6. Achievement Dashboard
   ============================ */
.achievements-container {
    margin-top: 60px;
    clear: both;
}

.achievement-header {
    text-align: center;
    margin-bottom: 40px;
    animation: fadeInUp 0.6s ease-out;
}

.achievement-title {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 10px;
}

.achievement-subtitle {
    font-size: 16px;
    color: #718096;
}

/* Hero Metrics */
.hero-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.hero-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 32px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

.hero-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

.hero-card:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 10px 30px rgba(245, 87, 108, 0.3);
    animation-delay: 0.1s;
}

.hero-card:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
    animation-delay: 0.2s;
}

.hero-icon {
    font-size: 48px;
    margin-bottom: 16px;
    filter: drop-shadow(0 4px 6px rgba(0,0,0,0.1));
}

.hero-value {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
}

.hero-label {
    font-size: 18px;
    opacity: 0.95;
    font-weight: 500;
}

.hero-unit {
    font-size: 14px;
    opacity: 0.8;
    margin-top: 4px;
}

/* Assets Grid */
.assets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.asset-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    cursor: pointer;
    animation: fadeInUp 0.6s ease-out;
}

.asset-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    border-color: #667eea;
}

.asset-icon {
    font-size: 36px;
    margin-bottom: 12px;
}

.asset-value {
    font-size: 32px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
}

.asset-label {
    font-size: 14px;
    color: #718096;
}

.asset-action {
    margin-top: 12px;
    font-size: 12px;
    color: #667eea;
    text-decoration: none;
    display: inline-block;
}

/* Quick Stats Bar */
.stats-bar {
    background: linear-gradient(135deg, #f6f8fb 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
    animation: fadeInUp 0.6s ease-out 0.8s both;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #4a5568;
}

.stat-label {
    font-size: 12px;
    color: #718096;
    margin-top: 4px;
}

.data-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

/* ============================
   7. Form & Grid Layouts
   ============================ */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
}

.form-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-card h3 {
    margin-bottom: 10px;
    color: #2d3748;
}

.form-meta {
    color: #718096;
    font-size: 14px;
    margin-bottom: 15px;
}

.form-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-completed {
    background: #c6f6d5;
    color: #22543d;
}

.status-draft {
    background: #fed7d7;
    color: #742a2a;
}

/* Scripts Grid */
.scripts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.script-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.script-preview {
    background: #f7fafc;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.4;
    color: #4a5568;
    max-height: 150px;
    overflow-y: auto;
}

/* ============================
   8. Status Indicators
   ============================ */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-online {
    background: #c6f6d5;
    color: #22543d;
}

.status-offline {
    background: #fed7d7;
    color: #742a2a;
}

/* Color scheme for different modules */
.config-card .card-icon { background: #e6fffa; color: #319795; }
.scripts-card .card-icon { background: #fef5e7; color: #d69e2e; }
.monitor-card .card-icon { background: #e3f2fd; color: #1976d2; }
.system-card .card-icon { background: #f3e5f5; color: #7b1fa2; }
.control-card .card-icon { background: #e8f4fd; color: #2b77e5; }

/* ============================
   8. Button Extensions (补充按钮样式)
   ============================ */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
}

.btn-secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.btn-success {
    background: #38a169;
    color: white;
}

.btn-success:hover {
    background: #2f855a;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background: #c53030;
}

/* ============================
   9. Progress Modals
   ============================ */
.progress-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

.progress-content {
    background: white;
    padding: 40px;
    border-radius: 16px;
    min-width: 480px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
    position: relative;
}

.progress-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.progress-icon {
    font-size: 32px;
    margin-right: 15px;
    animation: bounce 2s infinite;
}

.progress-title {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
}

.progress-bar-container {
    margin: 25px 0;
}

.progress-bar {
    height: 12px;
    background: #f7fafc;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    width: 0%;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 100%
    );
    animation: shimmer 2s infinite;
}

.progress-percent {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: #4a5568;
    margin-top: 10px;
}

.progress-step {
    text-align: center;
    font-size: 16px;
    color: #718096;
    margin: 20px 0;
    min-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-actions {
    text-align: center;
    margin-top: 30px;
}

.cancel-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.cancel-btn:hover {
    background: #c53030;
}

.cancel-btn:disabled {
    background: #a0aec0;
    cursor: not-allowed;
}

.confirm-btn {
    background: #38a169;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.confirm-btn:hover {
    background: #2f855a;
}

/* Success and error states */
.progress-content.success .progress-icon {
    color: #38a169;
}

.progress-content.success .progress-fill {
    background: linear-gradient(90deg, #38a169, #2f855a);
}

.progress-content.error .progress-icon {
    color: #e53e3e;
}

.progress-content.error .progress-fill {
    background: linear-gradient(90deg, #e53e3e, #c53030);
}

/* ============================
   10. Animations
   ============================ */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animating-number {
    animation: countUp 0.5s ease-out;
}

/* ============================
   11. Responsive Design
   ============================ */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .nav-tabs {
        overflow-x: auto;
        padding: 0 10px;
    }

    .nav-tab {
        white-space: nowrap;
        padding: 15px 20px;
    }

    .main-container {
        padding: 20px 15px;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .workflow-steps {
        flex-direction: column;
        padding: 0;
    }

    .workflow-step {
        margin-bottom: 30px;
        width: 100%;
    }

    .workflow-connector {
        display: none;
    }

    .workflow-step-desc {
        max-width: 200px;
    }
}