* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: #f7f8fa;
    color: #1e1e20;
    line-height: 1.6;
}

/* Header */
.header {
    background: #ffffff;
    color: #1e1e20;
    padding: 16px 0;
    box-shadow: 0 1px 2px rgba(0,0,0,.04);
    border-bottom: 1px solid rgba(0,0,0,.06);
}

.header-content {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 20px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #50525a;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background-color: #00c49a;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.4; }
    100% { opacity: 1; }
}

/* Main Container */
.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 24px;
}

/* Control Panel */
.control-panel {
    background: #ffffff;
    border-radius: 4px;
    padding: 24px;
    box-shadow: 0 1px 2px rgba(0,0,0,.04);
    margin-bottom: 16px;
}

.control-section {
    margin-bottom: 24px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #1e1e20;
    border-bottom: 1px solid rgba(0,0,0,.06);
    padding-bottom: 8px;
}

/* Control Buttons */
.control-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    transition: opacity 150ms ease-out;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.btn:hover {
    transform: none;
    box-shadow: none;
    opacity: 0.85;
}

.btn:focus {
    opacity: 0.85;
    outline: 2px solid #2e7ff2;
    outline-offset: 2px;
}

.btn:active {
    transform: none;
}

.btn-primary {
    background: #2e7ff2;
    color: #ffffff;
}

.btn-success {
    background: #00c49a;
    color: #ffffff;
}

.btn-warning {
    background: #2e7ff2;
    color: #ffffff;
}

.btn-danger {
    background: #50525a;
    color: #ffffff;
}

.btn:disabled {
    background: #f7f8fa;
    color: #50525a;
    cursor: not-allowed;
    transform: none;
    opacity: 1;
}

/* Status Display */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
}

/* Collapsible Panel */
.collapsible-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    user-select: none;
}

.collapsible-header:hover h4 {
    color: #2e7ff2;
}

.collapse-icon {
    font-size: 20px;
    transition: transform 150ms ease-out;
}

.collapsible-header.expanded .collapse-icon {
    transform: rotate(90deg);
}

.collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 150ms ease-out;
}

.collapsible-content.expanded {
    max-height: 2000px;
    transition: max-height 150ms ease-out;
}

/* Script Timeline */
.script-timeline {
    margin-top: 16px;
    padding: 16px;
    background: #f7f8fa;
    border-radius: 4px;
    max-height: 500px;
    overflow-y: auto;
}

.script-segment {
    background: #ffffff;
    border: 1px solid rgba(0,0,0,.06);
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 8px;
    transition: all 150ms ease-out;
    cursor: pointer;
    position: relative;
}

.script-segment:hover {
    border-color: #2e7ff2;
    box-shadow: 0 1px 2px rgba(0,0,0,.04);
}

.script-segment.current {
    border-color: #2e7ff2;
    border-width: 2px;
    background: #ffffff;
}

.script-segment.current::before {
    content: '▶';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    color: #2e7ff2;
    font-size: 14px;
}

.segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.segment-title {
    font-weight: 600;
    color: #1e1e20;
    font-size: 14px;
}

.segment-duration {
    font-size: 14px;
    color: #50525a;
    background: #f7f8fa;
    padding: 2px 8px;
    border-radius: 4px;
}

.segment-content-preview {
    font-size: 14px;
    color: #50525a;
    line-height: 1.5;
    margin-bottom: 8px;
    white-space: pre-line;
}

.segment-meta {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #50525a;
}

.segment-type {
    background: #f7f8fa;
    color: #00c49a;
    padding: 2px 8px;
    border-radius: 4px;
}

.status-card {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 1px 2px rgba(0,0,0,.04);
    border-left: 4px solid #2e7ff2;
}

.status-card h4 {
    font-size: 14px;
    color: #50525a;
    margin-bottom: 8px;
    text-transform: none;
    letter-spacing: 0;
}

.status-value {
    font-size: 24px;
    font-weight: 600;
    color: #1e1e20;
}

/* Progress Bar */
.progress-container {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 1px 2px rgba(0,0,0,.04);
    margin-bottom: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f7f8fa;
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-fill {
    height: 100%;
    background: #2e7ff2;
    width: 0%;
    transition: width 150ms ease-out;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #50525a;
}

.progress-details {
    margin-top: 16px;
    padding-top: 8px;
    border-top: 1px solid rgba(0,0,0,.06);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #50525a;
    margin-bottom: 8px;
}

.progress-info span {
    flex: 1;
}

/* Question Queue */
.question-section {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 1px 2px rgba(0,0,0,.04);
}

.question-input {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.question-input input {
    flex: 1;
    padding: 8px;
    border: 1px solid rgba(0,0,0,.06);
    border-radius: 4px;
    font-size: 14px;
}

.question-queue {
    max-height: 200px;
    overflow-y: auto;
}

.question-item {
    padding: 8px;
    border-bottom: 1px solid rgba(0,0,0,.06);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.question-item:last-child {
    border-bottom: none;
}

.question-text {
    flex: 1;
    margin-right: 8px;
}

.question-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
    background: #f7f8fa;
    color: #50525a;
    min-width: 60px;
    text-align: center;
}

.response-time {
    font-size: 14px;
    color: #00c49a;
    font-weight: 600;
    min-width: 50px;
}

.question-priority {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
}

.priority-high { background: #f7f8fa; color: #50525a; }
.priority-medium { background: #f7f8fa; color: #2e7ff2; }
.priority-low { background: #f7f8fa; color: #00c49a; }

/* Question Statistics */
.question-stats {
    margin: 16px 0;
    background: #f7f8fa;
    border-radius: 4px;
    padding: 16px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 8px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid rgba(0,0,0,.06);
}

.stat-number {
    font-size: 20px;
    font-weight: 600;
    color: #1e1e20;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #50525a;
    font-weight: 400;
}

/* Enhanced Question Items */
.queue-header, .history-header {
    margin: 8px 0 8px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0,0,0,.06);
}

.queue-header h4, .history-header h4 {
    margin: 0;
    font-size: 14px;
    color: #1e1e20;
    font-weight: 600;
}

.question-meta {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 8px;
    flex-wrap: wrap;
}

.question-meta span {
    font-size: 14px;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 400;
}

.viewer-name {
    background: #f7f8fa;
    color: #2e7ff2;
}

.response-time {
    background: #f7f8fa;
    color: #50525a;
}

.processed-time {
    background: #f7f8fa;
    color: #00c49a;
}

.audio-failed {
    background: #f7f8fa;
    color: #50525a;
}

.question-answer {
    margin-top: 8px;
    padding: 8px;
    background: #f7f8fa;
    border-radius: 4px;
    font-size: 14px;
    color: #1e1e20;
    border-left: 4px solid #00c49a;
}

.history-item {
    opacity: 0.8;
    border-left: 2px solid rgba(0,0,0,.06);
}

/* Optimistic UI styles */
.optimistic-question {
    background: #ffffff;
    border-left: 4px solid #2e7ff2;
    animation: optimistic-glow 2s ease-in-out infinite alternate;
}

.optimistic-submitting {
    border-left-color: #2e7ff2;
    background: #ffffff;
}

.optimistic-submitting .question-status {
    background: #f7f8fa;
    color: #2e7ff2;
    animation: pulse-status 1.5s ease-in-out infinite;
}

.optimistic-submitted {
    border-left-color: #00c49a;
    background: #ffffff;
}

.optimistic-submitted .question-status {
    background: #f7f8fa;
    color: #00c49a;
}

.optimistic-error {
    border-left-color: #50525a;
    background: #ffffff;
}

.optimistic-error .question-status {
    background: #f7f8fa;
    color: #50525a;
}

.optimistic-question .question-time {
    font-size: 14px;
    color: #2e7ff2;
    font-weight: 400;
    min-width: 60px;
    text-align: right;
}

.error-detail {
    font-size: 14px;
    color: #50525a;
    margin-top: 4px;
    padding: 4px 8px;
    background: #f7f8fa;
    border-radius: 4px;
    border: 1px solid rgba(0,0,0,.06);
}

/* Animation effects */
@keyframes optimistic-glow {
    0% { box-shadow: 0 1px 2px rgba(0,0,0,.04); }
    100% { box-shadow: 0 1px 2px rgba(0,0,0,.04); }
}

@keyframes pulse-status {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(0.98); }
    100% { opacity: 1; transform: scale(1); }
}

/* Button disabled state optimization */
.btn:disabled {
    background-color: #f7f8fa !important;
    border-color: rgba(0,0,0,.06) !important;
    color: #50525a !important;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none !important;
}

.btn:disabled:hover {
    background-color: #f7f8fa !important;
    border-color: rgba(0,0,0,.06) !important;
    transform: none !important;
    opacity: 0.6;
}

/* Status Classes */
.status-pending {
    border-left-color: #2e7ff2;
}

.status-processing {
    border-left-color: #2e7ff2;
    animation: pulse 2s infinite;
}

.status-answered {
    border-left-color: #00c49a;
}

.status-warning {
    border-left-color: #2e7ff2;
}

.status-ignored {
    border-left-color: #50525a;
}

.status-unknown {
    border-left-color: rgba(0,0,0,.06);
}

/* Sentence-level display styles */
.sentence-item {
    padding: 8px;
    margin: 4px 0;
    border-radius: 4px;
    transition: all 150ms ease-out;
    cursor: pointer;
    border-left: 4px solid transparent;
}

.sentence-item:hover {
    background: #f7f8fa;
}

.sentence-item.current {
    background: #f7f8fa;
    border-left-color: #2e7ff2;
    font-weight: 600;
    transform: none;
    box-shadow: 0 1px 2px rgba(0,0,0,.04);
}

.sentence-item.completed {
    background: #f7f8fa;
    color: #50525a;
    border-left-color: #00c49a;
}

.sentence-progress-bar {
    height: 2px;
    background: rgba(0,0,0,.06);
    border-radius: 4px;
    margin-top: 8px;
    overflow: hidden;
}

.sentence-progress-fill {
    height: 100%;
    background: #2e7ff2;
    width: 0%;
    transition: width 150ms ease-out;
}

@keyframes sentence-highlight {
    0% { background: #f7f8fa; }
    50% { background: #f7f8fa; }
    100% { background: #f7f8fa; }
}

.sentence-item.highlighting {
    animation: sentence-highlight 1s ease-in-out;
}

/* Responsive */
@media (max-width: 1280px) {
    .status-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 16px;
    }
    
    .control-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Audio Mode Indicator Styles */
.audio-mode-indicator {
    font-weight: 600;
    transition: all 150ms ease-out;
}

.audio-mode-indicator.live-mode {
    background-color: #f7f8fa;
    color: #00c49a;
    border: 1px solid rgba(0,0,0,.06);
}

.audio-mode-indicator.qa-mode {
    background-color: #f7f8fa;
    color: #2e7ff2;
    border: 1px solid rgba(0,0,0,.06);
    animation: pulse-qa 2s infinite;
}

.audio-mode-indicator.paused-mode {
    background-color: #f7f8fa;
    color: #50525a;
    border: 1px solid rgba(0,0,0,.06);
}

@keyframes pulse-qa {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}