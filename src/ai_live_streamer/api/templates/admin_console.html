<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI直播运营控制台</title>
    
    <!-- 引入设计系统和管理控制台样式 -->
    <link rel="stylesheet" href="/static/css/global-design-system.css">
    <link rel="stylesheet" href="/static/css/admin.css">
    
    <!-- 引入UI组件库 -->
    <script src="/static/js/ui-components.js"></script>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">🤖 AI直播运营控制台</div>
            <div class="user-info">
                <span>管理员</span>
                <div class="status-indicator status-online">
                    <span>●</span> 在线
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-container">
        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="dashboard">📊 总览</button>
            <button class="nav-tab" data-tab="products">🛍️ 商品管理</button>
            <button class="nav-tab" data-tab="forms">📝 配置管理</button>
            <button class="nav-tab" data-tab="scripts">📜 脚本预览</button>
            <button class="nav-tab" data-tab="monitor">📈 系统监控</button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-container">
        
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <h2 style="margin-bottom: 30px; color: #2d3748;">系统总览</h2>
            
            <!-- Workflow Guide -->
            <div class="workflow-guide">
                <div class="workflow-header">
                    <div class="workflow-title">🚀 快速开始使用</div>
                    <div class="workflow-subtitle">按照以下步骤配置并启动您的AI直播</div>
                </div>
                
                <div class="workflow-steps">
                    <div class="workflow-connector">
                        <div class="workflow-connector-fill" id="workflowProgress"></div>
                    </div>
                    
                    <div class="workflow-step" data-action="navigate-to-step" data-step="product">
                        <div class="workflow-step-number">1</div>
                        <div class="workflow-step-icon">🛍️</div>
                        <div class="workflow-step-title">创建商品</div>
                        <div class="workflow-step-desc">配置商品信息和卖点</div>
                    </div>
                    
                    <div class="workflow-step" data-action="navigate-to-step" data-step="config">
                        <div class="workflow-step-number">2</div>
                        <div class="workflow-step-icon">⚙️</div>
                        <div class="workflow-step-title">生成直播配置</div>
                        <div class="workflow-step-desc">设置直播参数</div>
                    </div>
                    
                    <div class="workflow-step" data-action="navigate-to-step" data-step="script">
                        <div class="workflow-step-number">3</div>
                        <div class="workflow-step-icon">📜</div>
                        <div class="workflow-step-title">生成脚本预览</div>
                        <div class="workflow-step-desc">预览和调整脚本</div>
                    </div>
                    
                    <div class="workflow-step" data-action="navigate-to-step" data-step="live">
                        <div class="workflow-step-number">4</div>
                        <div class="workflow-step-icon">🎬</div>
                        <div class="workflow-step-title">开始直播</div>
                        <div class="workflow-step-desc">启动AI直播流</div>
                    </div>
                </div>
                
                <div class="workflow-actions">
                    <a href="/config" class="workflow-btn workflow-btn-primary">
                        <span>🚀</span>
                        <span>立即开始配置</span>
                    </a>
                </div>
            </div>

            <!-- 🏆 成就仪表盘 (Achievements Dashboard) -->
            <div class="achievements-container">
                <!-- Header -->
                <div class="achievement-header">
                    <div class="achievement-title">🏆 成就仪表盘</div>
                    <div class="achievement-subtitle">展示系统累计价值和核心资产</div>
                </div>
                
                <!-- Hero Metrics -->
                <div class="hero-metrics" id="heroMetrics">
                    <div class="hero-card">
                        <div class="data-badge" id="dataSourceBadge">加载中...</div>
                        <div class="hero-icon">🎬</div>
                        <div class="hero-value" id="totalSessions">-</div>
                        <div class="hero-label">累计直播场次</div>
                        <div class="hero-unit">场</div>
                    </div>
                    
                    <div class="hero-card">
                        <div class="hero-icon">⏱️</div>
                        <div class="hero-value" id="totalDuration">-</div>
                        <div class="hero-label">累计直播时长</div>
                        <div class="hero-unit">小时</div>
                    </div>
                    
                    <div class="hero-card">
                        <div class="hero-icon">💬</div>
                        <div class="hero-value" id="totalQuestions">-</div>
                        <div class="hero-label">累计回答问题</div>
                        <div class="hero-unit">个</div>
                    </div>
                </div>

                <!-- Assets Grid -->
                <div class="assets-grid" id="assetsGrid">
                    <div class="asset-card" data-action="show-tab" data-tab="products">
                        <div class="asset-icon">🛍️</div>
                        <div class="asset-value" id="totalProducts">-</div>
                        <div class="asset-label">商品管理</div>
                        <a href="#" class="asset-action">查看商品 →</a>
                    </div>
                    
                    <div class="asset-card" data-action="show-tab" data-tab="forms">
                        <div class="asset-icon">📝</div>
                        <div class="asset-value" id="totalConfigs">-</div>
                        <div class="asset-label">直播配置</div>
                        <a href="#" class="asset-action">查看配置 →</a>
                    </div>
                    
                    <div class="asset-card" data-action="show-tab" data-tab="scripts">
                        <div class="asset-icon">📜</div>
                        <div class="asset-value" id="totalScriptsCount">-</div>
                        <div class="asset-label">脚本管理</div>
                        <a href="#" class="asset-action">查看脚本 →</a>
                    </div>
                    
                    <div class="asset-card" data-action="show-tab" data-tab="monitor">
                        <div class="asset-icon">📊</div>
                        <div class="asset-value">✓</div>
                        <div class="asset-label">系统监控</div>
                        <a href="#" class="asset-action">查看状态 →</a>
                    </div>
                    
                    <div class="asset-card" data-action="open-link" data-url="/control">
                        <div class="asset-icon">🎛️</div>
                        <div class="asset-value">✓</div>
                        <div class="asset-label">直播控制</div>
                        <a href="#" class="asset-action">打开控制面板 →</a>
                    </div>
                </div>
                
                <!-- Quick Stats Bar -->
                <div class="stats-bar">
                    <div class="stat-item">
                        <div class="stat-value" id="avgDuration">-</div>
                        <div class="stat-label">平均直播时长</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="avgQuestions">-</div>
                        <div class="stat-label">场均互动问答</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="systemUptime">-</div>
                        <div class="stat-label">系统运行天数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="systemStatusBadge" style="color: #38a169;">✓ 运行中</div>
                        <div class="stat-label">系统状态</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Tab -->
        <div id="products" class="tab-content">
            <h2 style="margin-bottom: 30px; color: #2d3748;">商品管理</h2>
            
            <!-- Toolbar -->
            <div style="margin-bottom: 20px; display: flex; justify-content: space-between; flex-wrap: wrap; gap: 10px;">
                <div style="display: flex; gap: 10px; align-items: center;">
                    <button class="btn btn-primary" data-action="open-create-product-modal">
                        + 新建商品
                    </button>
                    <input type="text" id="productSearchInput" data-action="handle-product-search" placeholder="搜索商品名称、SKU..." 
                           style="padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; min-width: 250px;">
                    <select id="productCategoryFilter" data-action="handle-product-filter" 
                            style="padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; background: white;">
                        <option value="">所有分类</option>
                        <option value="electronics">电子产品</option>
                        <option value="clothing">服装</option>
                        <option value="home">家居用品</option>
                        <option value="beauty">美妆护肤</option>
                        <option value="food">食品饮料</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-secondary" data-action="load-products">🔄 刷新</button>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div id="productsGrid" class="form-grid">
                <div style="text-align: center; color: #718096; grid-column: 1 / -1;">
                    加载中...
                </div>
            </div>
        </div>

        <!-- Forms Tab -->
        <div id="forms" class="tab-content">
            <h2 style="margin-bottom: 30px; color: #2d3748;">配置管理</h2>
            <div style="margin-bottom: 20px;">
                <a href="/config" class="btn btn-primary">+ 新建配置</a>
            </div>
            <div id="formsGrid" class="form-grid">
                <div style="text-align: center; color: #718096; grid-column: 1 / -1;">
                    加载中...
                </div>
            </div>
        </div>

        <!-- Scripts Tab -->
        <div id="scripts" class="tab-content">
            <h2 style="margin-bottom: 30px; color: #2d3748;">脚本预览</h2>
            
            <!-- Toolbar -->
            <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                <button class="btn btn-primary" data-action="show-generate-script-modal">
                    + 生成新脚本
                </button>
                <button class="btn btn-secondary" data-action="load-scripts">🔄 刷新</button>
            </div>
            
            <div id="scriptsGrid" class="scripts-grid">
                <div style="text-align: center; color: #718096; grid-column: 1 / -1;">
                    加载中...
                </div>
            </div>
        </div>

        <!-- Monitor Tab -->
        <div id="monitor" class="tab-content">
            <h2 style="margin-bottom: 30px; color: #2d3748;">系统监控</h2>
            <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.08);">
                <p style="color: #718096;">系统监控功能开发中...</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" disabled style="opacity: 0.5; cursor: not-allowed;">查看系统状态</button>
                </div>
            </div>
        </div>

    </div>

    <!-- Progress Modals -->
    <div id="scriptProgressModal" class="progress-modal" style="display: none;">
        <div class="progress-content">
            <div class="progress-header">
                <span class="progress-icon">🤖</span>
                <span class="progress-title">正在生成直播脚本...</span>
            </div>
            
            <div class="progress-bar-container">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressPercent" class="progress-percent">0%</div>
            </div>
            
            <div id="progressStepText" class="progress-step">📋 准备开始生成...</div>
            
            <div class="progress-actions">
                <button id="cancelGeneration" class="cancel-btn">取消生成</button>
            </div>
        </div>
    </div>

    <!-- 直播初始化进度浮层 -->
    <div id="streamInitModal" class="progress-modal" style="display: none;">
        <div class="progress-content">
            <div class="progress-header">
                <span class="progress-icon">🎥</span>
                <span class="progress-title">正在初始化直播间...</span>
            </div>
            
            <div class="progress-bar-container">
                <div class="progress-bar">
                    <div id="streamProgressFill" class="progress-fill"></div>
                </div>
                <div id="streamProgressPercent" class="progress-percent">0%</div>
            </div>
            
            <div id="streamProgressStepText" class="progress-step">🔧 准备直播环境...</div>
            
            <div class="progress-actions">
                <button id="confirmInitSuccess" class="confirm-btn" style="display: none;">确定</button>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="/static/js/admin/utils/dom.js"></script>
    <script src="/static/js/admin/utils/format.js"></script>
    <script src="/static/js/admin/api.js"></script>
    <script src="/static/js/admin/components/progress_modal.js"></script>
    <script src="/static/js/admin/admin_console.js"></script>
</body>
</html>