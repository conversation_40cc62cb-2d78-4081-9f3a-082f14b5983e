"""WebSocket v2 协议处理器

基于请求-响应模型的新一代流式协议，支持智能播放列表和QA插入。

Author: Claude Code
Date: 2025-08-08
"""

import asyncio
import json
import time
import uuid
import traceback
from typing import Dict, Any, Optional, Set, TYPE_CHECKING
from datetime import datetime
from enum import Enum
from loguru import logger
from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState
from pydantic import ValidationError

from .websocket_constants import (
    CLOSE_CODE_SESSION_TIMEOUT,
    CLOSE_CODE_NORMAL,
    CLOSE_CODE_GOING_AWAY,
    CLOSE_REASONS
)
from ..core.streaming_config import StreamingConfig
from ..models.client_state_models import ClientState
from ..models.playlist_models import QAInsertionRequest, QAInsertionResult, QAInsertionStrategy
# 导入WebSocket消息模型进行契约验证
from ..models.websocket_messages import (
    validate_client_message,
    ContentRequest,
    ContentResponse,
    ErrorMessage,
    ConnectionEstablished
)

if TYPE_CHECKING:
    from ..services.playlist_manager import PlaylistManager
    from ..services.client_state_tracker import ClientStateTracker
    from ..services.streaming_content_provider import StreamingContentProvider
    from ..services.proactive_synthesizer import ProactiveSynthesizer


class ConnectionState(Enum):
    """WebSocket连接状态枚举"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"


class WebSocketV2Handler:
    """
    WebSocket v2 协议处理器
    
    核心功能：
    1. 处理客户端连接和断开
    2. 实现请求-响应通信模型
    3. 支持QA插入和状态同步
    4. 提供连接恢复和错误处理
    """
    
    def __init__(self, 
                 playlist_manager: 'PlaylistManager',
                 client_tracker: 'ClientStateTracker',
                 content_provider: 'StreamingContentProvider',
                 proactive_synthesizer: 'ProactiveSynthesizer',
                 config: StreamingConfig,
                 audio_proxy,  # 不再是可选参数，移除 =None
                 state_broadcaster):  # 新增：状态广播器依赖
        # 强制依赖检查（Fail-Fast）
        assert audio_proxy is not None, "AudioStreamingProxy is a required dependency"
        assert state_broadcaster is not None, "StateBroadcaster is a required dependency"
        
        self.playlist_manager = playlist_manager
        self.client_tracker = client_tracker
        self.content_provider = content_provider
        self.proactive_synthesizer = proactive_synthesizer
        self.config = config
        self.audio_proxy = audio_proxy  # 必需的音频代理
        self.state_broadcaster = state_broadcaster  # 必需的状态广播器
        
        # 连接管理
        self._connections: Dict[str, WebSocket] = {}
        self._client_sessions: Dict[str, Dict[str, Any]] = {}
        self._connection_states: Dict[str, ConnectionState] = {}  # 连接状态管理
        
        # 全局心跳管理
        self._client_last_activity: Dict[str, float] = {}
        self._heartbeat_interval = 5  # 5秒检查一次
        self._heartbeat_timeout = 30  # 30秒无响应视为死亡
        self._global_heartbeat_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self._stats = {
            "total_connections": 0,
            "active_connections": 0,
            "total_messages_sent": 0,
            "total_messages_received": 0,
            "total_content_requests": 0,
            "total_qa_requests": 0,
            "connection_errors": 0,
            "protocol_errors": 0
        }
        
        logger.info("WebSocket v2 处理器初始化完成")
        
    async def handle_connection(self, websocket: WebSocket, client_id: str, 
                              session_id: Optional[str] = None,
                              recovery_info: Optional[Dict[str, Any]] = None) -> None:
        """
        处理客户端连接
        
        Args:
            websocket: WebSocket连接
            client_id: 客户端标识符
            session_id: 会话标识符
            recovery_info: 会话恢复信息（用于向客户端通知恢复状态）
        """
        # 生成会话ID
        if not session_id:
            session_id = f"session_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
        logger.info(f"🔗 新的WebSocket v2连接: client_id={client_id}, session_id={session_id}")
        logger.debug(f"🔍 WebSocketV2Handler实例状态: playlist_manager={self.playlist_manager is not None}, audio_proxy={self.audio_proxy is not None}")
        
        # 设置连接状态为CONNECTING
        self._connection_states[client_id] = ConnectionState.CONNECTING
        
        connection_registered = False  # 跟踪注册状态
        
        try:
            # Check if session exists and wait for it to be ready
            from ..api.control import _active_sessions
            session_info = _active_sessions.get(session_id)
            logger.debug(f"🔍 会话信息查找: {session_id} -> {session_info is not None}")
            
            if session_info:
                session_status = session_info.get("status", "unknown")
                logger.info(f"📋 会话状态: {session_id} -> {session_status}")
                
                if session_status == "initializing":
                    logger.info(f"Session {session_id} is initializing, waiting for ready signal...")
                    
                    ready_event = session_info.get("ready_event")
                    if ready_event:
                        try:
                            # Wait up to 10 seconds for session initialization
                            await asyncio.wait_for(ready_event.wait(), timeout=10.0)
                            logger.info(f"✅ Session {session_id} is now ready!")
                        except asyncio.TimeoutError:
                            logger.error(f"Timeout waiting for session {session_id} to initialize")
                            await websocket.close(
                                code=CLOSE_CODE_SESSION_TIMEOUT,
                                reason=CLOSE_REASONS[CLOSE_CODE_SESSION_TIMEOUT]
                            )
                            return
                    else:
                        logger.warning(f"⚠️ No ready_event for initializing session {session_id}")
            else:
                logger.warning(f"⚠️ 会话信息未找到: {session_id}")
                logger.debug(f"📋 现有会话: {list(_active_sessions.keys())}")
                
            
            # 接受连接
            logger.info(f"🎯 About to accept WebSocket for {client_id}")
            try:
                await websocket.accept()
                logger.info(f"✅ WebSocket accepted for {client_id}, state: {websocket.client_state if hasattr(websocket, 'client_state') else 'unknown'}")
            except Exception as accept_error:
                logger.error(f"❌ WebSocket accept failed: {accept_error}")
                # 可能已经被FastAPI接受了，继续处理
                logger.info(f"⚠️ Continuing despite accept error - WebSocket might already be accepted")
                
            
            # 原子性注册块 - 确保所有注册要么全部成功，要么全部回滚
            try:
                # 更新统计
                self._stats["total_connections"] += 1
                self._stats["active_connections"] += 1
                
                # 存储连接信息（协议层）
                self._connections[client_id] = websocket
                self._client_sessions[client_id] = {
                    "session_id": session_id,
                    "connected_at": datetime.utcnow(),
                    "last_activity": datetime.utcnow(),
                    "protocol_version": "2.0"
                }
                
                # 注册到音频广播系统（音频层）- 关键修复
                logger.info(f"🎯 About to register to audio proxy: {client_id}")
                await self.audio_proxy.add_client(websocket)
                logger.info(f"✅ Audio proxy registration complete: {client_id}")
                
                # 📡 注册到状态广播器（架构解耦的核心）
                logger.info(f"📡 About to register to state broadcaster: {client_id}")
                await self.state_broadcaster.register_client(client_id, websocket)
                logger.info(f"✅ State broadcaster registration complete: {client_id}")
                
                # 获取客户端信息用于日志
                client_info = ""
                if hasattr(websocket, 'client') and websocket.client:
                    client_info = f" from {websocket.client.host}:{websocket.client.port}"
                
                logger.info(f"✅ Client {client_id}{client_info} registered to both audio and state broadcast systems")
                
                # 设置连接状态为CONNECTED
                self._connection_states[client_id] = ConnectionState.CONNECTED
                self._client_last_activity[client_id] = time.time()  # 记录最后活动时间
                
                # 注册客户端状态
                # TEMPORARY: Bypass client_tracker to debug connection issues
                logger.warning(f"⚠️ TEMPORARY: Bypassing client_tracker registration for debugging")
                # logger.debug(f"📝 Registering client state: {client_id}")
                # logger.debug(f"client_tracker instance: {self.client_tracker}")
                # logger.debug(f"About to call register_client for {client_id}")
                # try:
                #     client_state = await self.client_tracker.register_client(client_id, session_id)
                #     logger.debug(f"✅ Client state registered: {client_id}")
                # except Exception as tracker_error:
                #     logger.error(f"❌ Client tracker registration failed: {tracker_error}")
                #     logger.error(f"Full traceback: {traceback.format_exc()}")
                #     # 即使客户端追踪器注册失败，也继续处理（非关键功能）
                #     logger.warning(f"⚠️ Continuing without client state tracking for {client_id}")
                
                connection_registered = True  # 标记注册成功
                
                # 启动全局心跳监控器（如果还没启动）
                await self._start_global_heartbeat_monitor()
                
            except Exception as reg_error:
                # 注册失败，需要回滚
                logger.critical(f"🚨 CRITICAL: Registration failed for {client_id}")
                logger.error(f"❌ Failed to register client {client_id}: {reg_error}")
                logger.error(f"Exception type: {type(reg_error).__name__}")
                logger.error(f"Exception details: {str(reg_error)}")
                
                # 清理部分注册的状态
                if client_id in self._connections:
                    del self._connections[client_id]
                if client_id in self._client_sessions:
                    del self._client_sessions[client_id]
                    self._stats["active_connections"] -= 1
                
                # 尝试从音频代理移除（如果已添加）
                try:
                    await self.audio_proxy.remove_client(websocket)
                except:
                    pass  # 忽略清理错误
                
                # 重新抛出异常
                raise
            
            logger.debug(f"🎯 Registration block completed successfully for {client_id}")
            logger.info(f"🔥 About to send connection response for {client_id}")
            
            # 发送连接确认（不再尝试恢复状态）
            await self._send_connection_response(websocket, client_id, session_id, recovery_info or {})
            logger.debug(f"Sent connection_established response to {client_id}")
            
            # 启动消息处理循环
            logger.debug(f"开始消息处理循环: {client_id}")
            await self._message_loop(websocket, client_id, session_id)
            
        except WebSocketDisconnect:
            logger.info(f"客户端断开连接: {client_id}")
        except Exception as e:
            logger.critical(f"🚨 CRITICAL: WebSocket handler crashed for {client_id}")
            logger.error(f"WebSocket连接处理错误: {client_id}, 错误: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(f"Exception message: {str(e)}")
            logger.error(f"完整错误堆栈:\n{traceback.format_exc()}")
            self._stats["connection_errors"] += 1
        finally:
            # 🔧 修复僵尸客户端问题：无条件清理音频代理连接
            # 无论连接是否成功注册，都必须确保从音频代理中移除
            try:
                # 尝试从音频代理移除（这是最关键的清理）
                await self.audio_proxy.remove_client(websocket)
                logger.debug(f"✅ 音频代理清理完成: {client_id}")
            except Exception as e:
                # 即使清理失败也不能影响后续清理
                logger.warning(f"⚠️ 音频代理清理失败: {client_id}, error: {e}")
            
            # 执行完整的连接清理（如果已注册）
            if connection_registered:
                await self._cleanup_connection(client_id)
            else:
                # 连接未完全建立，只需关闭WebSocket
                try:
                    await websocket.close()
                except:
                    pass
            
    async def _send_connection_response(self, websocket: WebSocket, client_id: str,
                                      session_id: str, recovery_info: Dict[str, Any]) -> None:
        """发送连接响应，包含会话恢复状态信息"""
        playlist_info = await self.playlist_manager.get_playlist_info()
        
        response = {
            "type": "connection_established",
            "client_id": client_id,
            "session_id": session_id,
            "server_time": time.time(),
            "protocol_version": "2.1",
            "server_version": "2.1.0",
            "playlist_info": {
                "version": playlist_info.version,
                "total_items": playlist_info.total_items,
                "start_index": 0,  # 或基于历史记录计算
                "checksum": playlist_info.checksum
            },
            "playlist": {  # 保留旧字段以保持兼容性
                "version": playlist_info.version,
                "total_items": playlist_info.total_items,
                "checksum": playlist_info.checksum
            },
            "config": {
                "buffer_health_thresholds": {
                    "healthy_ms": self.config.client_buffer_healthy_ms,
                    "at_risk_ms": self.config.client_buffer_at_risk_ms
                },
                "request_timeout_ms": self.config.websocket_message_timeout * 1000
            },
            "audio_config": {
                "sample_rate": 24000,
                "channels": 1,
                "bit_depth": 16,
                "encoding": "pcm",
                "endianness": "little"  # 明确字节序
            }
        }
        
        # 添加会话恢复信息
        recovery_method = recovery_info.get("recovery_method", "normal")
        session_recovered = recovery_info.get("session_recovered", False)
        
        if session_recovered:
            response["session_recovery"] = {
                "recovered": True,
                "recovery_method": recovery_method,
                "message": self._get_recovery_message(recovery_method),
                "timestamp": recovery_info.get("recovery_timestamp") or recovery_info.get("creation_timestamp"),
                "suggested_start_index": 0  # Start from beginning for recovered/auto-created sessions
            }
            
            if recovery_method == "auto_created":
                response["session_recovery"]["creation_reason"] = recovery_info.get("creation_reason", "unknown")
        else:
            response["session_recovery"] = {
                "recovered": False,
                "recovery_method": "normal",
                "message": "Session active - no recovery needed",
                "suggested_start_index": 0
            }
            
        await self._send_message(websocket, response)
        
        # Start monitoring for client request activity
        await self._start_client_activity_monitoring(client_id)
        
    def _get_recovery_message(self, recovery_method: str) -> str:
        """获取恢复方法对应的用户友好消息"""
        messages = {
            "snapshot": "Session recovered from previous state snapshot",
            "auto_created": "New session created - server was restarted",
            "normal": "Session active - no recovery needed"
        }
        return messages.get(recovery_method, f"Session restored using {recovery_method}")
        
    def _is_connection_error(self, exception: Exception, websocket: WebSocket) -> bool:
        """
        判断是否为连接断开相关的异常
        
        采用多重检查策略提高健壮性：
        1. 异常类型检查
        2. 错误消息文本匹配 
        3. WebSocket状态检查
        
        Args:
            exception: 捕获到的异常
            websocket: WebSocket连接对象
            
        Returns:
            bool: True表示连接错误，需要终止循环
        """
        # 检查明确的连接异常类型
        if isinstance(exception, (WebSocketDisconnect, ConnectionResetError)):
            return True
        
        # 检查WebSocket状态
        if hasattr(websocket, 'client_state'):
            from fastapi.websockets import WebSocketState
            if websocket.client_state in [WebSocketState.DISCONNECTED, WebSocketState.CONNECTING]:
                return True
        
        # 检查常见的连接错误消息（作为后备检查）
        error_msg = str(exception).lower()
        connection_error_patterns = [
            "websocket is not connected",
            "close message has been sent", 
            "connection closed",
            "connection reset",
            "broken pipe"
        ]
        
        return any(pattern in error_msg for pattern in connection_error_patterns)
        
    async def _message_loop(self, websocket: WebSocket, client_id: str, session_id: str) -> None:
        """消息处理主循环 - 增强版异常处理"""
        logger.info(f"📨 开始消息处理循环: client={client_id}, session={session_id}")
        logger.debug(f"WebSocket状态: {websocket.client_state}")
        
        while self._connection_states.get(client_id) == ConnectionState.CONNECTED:
            try:
                # 防御性检查：确保WebSocket连接状态正常
                if hasattr(websocket, 'client_state'):
                    from fastapi.websockets import WebSocketState
                    if websocket.client_state != WebSocketState.CONNECTED:
                        logger.info(f"检测到WebSocket状态异常，终止循环: client={client_id}, "
                                  f"state={websocket.client_state}")
                        break
                
                # 接收消息
                logger.debug(f"等待接收消息: {client_id}")
                message = await websocket.receive_text()
                self._stats["total_messages_received"] += 1
                logger.info(f"📥 收到消息: client={client_id}, size={len(message)} bytes")
                
                # 更新活动时间
                if client_id in self._client_sessions:
                    self._client_sessions[client_id]["last_activity"] = datetime.utcnow()
                
                # 更新心跳时间
                self._client_last_activity[client_id] = time.time()
                    
                # 解析消息
                try:
                    data = json.loads(message)
                except json.JSONDecodeError as e:
                    logger.warning(f"无效JSON消息: {client_id}, 错误: {e}")
                    await self._send_error(websocket, "invalid_json", "消息格式错误")
                    continue
                    
                # 处理消息
                await self._handle_message(websocket, client_id, session_id, data)
                
            except WebSocketDisconnect:
                # 标准连接断开（客户端主动）
                logger.info(f"客户端主动断开: client={client_id}, session={session_id}")
                break
                
            except ConnectionResetError:
                # 连接重置（网络层）
                logger.info(f"连接被重置: client={client_id}, session={session_id}")
                break
                
            except Exception as e:
                # 使用增强的连接错误检测
                if self._is_connection_error(e, websocket):
                    # 连接类异常：立即终止循环
                    logger.info(f"检测到连接断开异常: client={client_id}, session={session_id}, "
                              f"error={type(e).__name__}: {e}")
                    break
                else:
                    # 数据类异常：记录错误但继续处理
                    logger.error(f"消息处理错误: client={client_id}, session={session_id}, "
                               f"error={type(e).__name__}: {e}")
                    self._stats["protocol_errors"] += 1
                    
                    # 尝试发送错误响应（仅在连接仍活跃时）
                    if (self._connection_states.get(client_id) == ConnectionState.CONNECTED and
                        hasattr(websocket, 'client_state')):
                        from fastapi.websockets import WebSocketState
                        if websocket.client_state == WebSocketState.CONNECTED:
                            try:
                                await self._send_error(websocket, "internal_error", "服务器内部错误")
                            except Exception as send_error:
                                # 发送错误响应失败，说明连接确实断开了
                                logger.info(f"发送错误响应失败，连接已断开: client={client_id}, "
                                          f"error={send_error}")
                                break
                        else:
                            # WebSocket状态已断开
                            logger.info(f"WebSocket状态已断开，终止循环: client={client_id}")
                            break
                    else:
                        # 内部连接状态异常，终止循环
                        logger.warning(f"内部连接状态异常，终止循环: client={client_id}")
                        break
                    
    async def _handle_message(self, websocket: WebSocket, client_id: str, 
                            session_id: str, data: Dict[str, Any]) -> None:
        """处理单个消息（带API契约验证）"""
        message_type = data.get("type")
        
        if not message_type:
            await self._send_error(websocket, "missing_type", "消息类型缺失")
            return
            
        logger.debug(f"处理消息: {client_id}, 类型: {message_type}")
        
        # 对需要严格验证的消息类型进行契约验证
        if message_type in ["content_request", "qa_insert", "state_sync"]:
            try:
                # 验证消息是否符合API契约v2.0
                validated_message = validate_client_message(data)
                logger.debug(f"✅ 消息通过API契约验证: {message_type}")
                # 使用验证后的数据继续处理
                data = validated_message.dict()
            except ValidationError as e:
                logger.error(f"❌ 消息违反API契约: {message_type}, 错误: {e}")
                await self._send_error(
                    websocket, 
                    "CONTRACT_VIOLATION", 
                    f"消息格式不符合API契约v2.0: {e.errors()[0]['msg']}",
                    request_id=data.get("request_id")
                )
                return
            except Exception as e:
                logger.error(f"❌ 消息验证失败: {e}")
                await self._send_error(
                    websocket,
                    "VALIDATION_ERROR",
                    f"消息验证失败: {str(e)}",
                    request_id=data.get("request_id")
                )
                return
        
        # 路由消息到对应处理器
        if message_type == "ping":
            await self._handle_ping(websocket, data)
        elif message_type == "heartbeat":
            await self._handle_heartbeat(websocket, data)
        elif message_type == "content_request":
            await self._handle_content_request(websocket, client_id, data)
        elif message_type == "qa_request":
            await self._handle_qa_request(websocket, client_id, data)
        elif message_type == "state_sync":
            await self._handle_state_sync(websocket, client_id, data)
        elif message_type == "playlist_info_request":
            await self._handle_playlist_info_request(websocket, client_id, data)
        elif message_type == "reconnect":
            await self._handle_reconnect(websocket, client_id, data)
        elif message_type == "audio_error_report":
            await self._handle_audio_error_report(websocket, client_id, data)
        else:
            await self._send_error(websocket, "unknown_type", f"未知消息类型: {message_type}")
            
    async def _handle_ping(self, websocket: WebSocket, data: Dict[str, Any]) -> None:
        """处理心跳消息（旧格式，保持兼容性）"""
        response = {
            "type": "pong",
            "timestamp": data.get("timestamp", time.time()),
            "server_time": time.time()
        }
        await self._send_message(websocket, response)
        
    async def _handle_heartbeat(self, websocket: WebSocket, data: Dict[str, Any]) -> None:
        """处理心跳消息（标准格式）"""
        response = {
            "type": "heartbeat_ack",
            "timestamp": data.get("timestamp", time.time()),
            "server_time": time.time(),
            "client_time": data.get("timestamp"),
            "status": "alive"
        }
        await self._send_message(websocket, response)
        
    async def _handle_content_request(self, websocket: WebSocket, client_id: str, 
                                    data: Dict[str, Any]) -> None:
        """处理内容请求"""
        self._stats["total_content_requests"] += 1
        
        # 记录客户端请求活动
        self._record_client_request(client_id, "content_request")
        
        try:
            # 验证请求参数 - 支持两种格式以保持兼容性
            if "index" not in data and "current_index" not in data:
                await self._send_error(websocket, "missing_params", "缺少index参数")
                return
            
            # 获取索引值（优先使用index字段，与API契约一致）
            requested_index = data.get("index", data.get("current_index"))
            
            # 确保data中有current_index字段（为了兼容content_provider）
            if "current_index" not in data:
                data["current_index"] = requested_index
                
            # 📡 版本号校验机制：检查客户端已知版本
            known_version = data.get("known_version", 0)
            if known_version > 0:  # 只有在客户端提供版本号时才校验
                version_mismatch = await self.state_broadcaster.ensure_client_version_sync(
                    client_id, known_version
                )
                if version_mismatch:
                    logger.info(f"📋 版本不一致已处理: client={client_id}, known_version={known_version}")
                    # 版本同步消息已由broadcaster发送，继续处理内容请求
            
            # 检查content_provider是否初始化
            if not self.content_provider:
                logger.error(f"❌ content_provider未初始化!")
                await self._send_error(websocket, "server_error", "内容提供器未初始化")
                return
                
            logger.debug(f"📤 处理内容请求: client_id={client_id}, index={requested_index}, data={data}")
            
            # 处理内容请求
            response = await self.content_provider.handle_next_content_request(client_id, data)
            
            response_type = response.get('type', 'unknown')
            logger.debug(f"📥 内容请求响应: {response_type} for client {client_id}")
            
            # 特殊处理 content_not_ready 响应
            if response_type == "content_not_ready":
                logger.info(f"⏱️ 内容生成中，建议客户端重试: client={client_id}, "
                          f"index={response.get('index')}, retry_after={response.get('retry_after_ms')}ms")
                # 不更新成功统计，因为这是临时状态
                self._stats["content_not_ready_count"] = self._stats.get("content_not_ready_count", 0) + 1
            
            # 添加请求ID以便客户端匹配响应
            if "request_id" in data:
                response["request_id"] = data["request_id"]
                logger.debug(f"✅ Added request_id to response: {data['request_id']}")
            else:
                logger.warning(f"⚠️ No request_id in client request from {client_id}")
                
            # 验证响应中确实包含request_id（防御性检查）
            if "request_id" not in response:
                logger.error(f"❌ Response missing request_id! This violates API contract. Client: {client_id}")
                # 尝试从原始请求中恢复
                if "request_id" in data:
                    response["request_id"] = data["request_id"]
                    logger.info(f"🔧 Recovered request_id from original request: {data['request_id']}")
                
            await self._send_message(websocket, response)
            
        except Exception as e:
            logger.error(f"内容请求处理失败: {client_id}, 错误: {e}", exc_info=True)
            await self._send_error(websocket, "content_error", str(e))
            
    async def _handle_qa_request(self, websocket: WebSocket, client_id: str, 
                               data: Dict[str, Any]) -> None:
        """处理QA插入请求 - 🚀 带WebSocket关联缓存的智能去重版本"""
        self._stats["total_qa_requests"] += 1
        
        try:
            # 🚀 使用WebSocket关联缓存进行智能去重
            from ..services.websocket_qa_cache import get_qa_cache, QARequestStatus
            
            qa_cache = get_qa_cache()
            
            # 确保WebSocket客户端已注册
            qa_cache.register_websocket_client(client_id, websocket)
            
            question_text = data.get("question", "").strip()
            session_id = data.get("session_id", "default")  # 从WebSocket数据中获取会话ID
            request_id = data.get("request_id")
            
            # # 🎯 调试信息：QA问题接收时打印当前正在播放的句子全文
            # try:
            #     current_item = await self.playlist_manager.get_item_at(self.playlist_manager._current_playback_index)
            #     if current_item:
            #         logger.info(f"[QA调试-问题接收] 当前正在播放的句子全文: '{current_item.content}'")
            #         logger.info(f"[QA调试-问题接收] 当前播放索引: {self.playlist_manager._current_playback_index}")
            #     else:
            #         logger.info(f"[QA调试-问题接收] 当前播放索引 {self.playlist_manager._current_playback_index} 无对应句子")
            # except Exception as debug_error:
            #     logger.warning(f"[QA调试-问题接收] 获取当前播放句子失败: {debug_error}")
            
            if not question_text:
                await self._send_error(websocket, "qa_error", "问题内容不能为空")
                return
            
            # 提交QA请求到缓存系统
            cache_result = await qa_cache.submit_qa_request(
                session_id=session_id,
                question_text=question_text,
                client_id=client_id,
                request_id=request_id
            )
            
            # 如果是缓存命中，直接返回缓存结果
            if cache_result["cache_hit"]:
                # 🚀 Phase 2.4: 缓存命中的性能监控
                from ..monitoring.qa_performance_monitor import get_qa_performance_monitor, QAInsertionResult
                qa_monitor = get_qa_performance_monitor()
                
                cached_qa_id = cache_result.get("request_id")
                performance_metric = qa_monitor.start_qa_tracking(
                    qa_id=cached_qa_id,
                    question_text=question_text,
                    insertion_strategy="websocket_v2_cached",
                    session_id=session_id,
                    client_id=client_id
                )
                
                # 记录缓存命中
                qa_monitor.record_cache_hit(cached_qa_id, True)
                
                # 立即完成追踪（缓存命中延迟极低）
                qa_monitor.finish_qa_tracking(
                    cached_qa_id,
                    result=QAInsertionResult.SUCCESS,
                    sentences_inserted=0,  # 缓存命中不需要插入
                    error_message=None
                )
                
                response = {
                    "type": "qa_response",
                    "qa_id": cache_result.get("request_id"),
                    "success": True,
                    "cached": True,
                    "status": cache_result["status"],
                    "answer": cache_result.get("answer"),
                    "client_count": cache_result["client_count"],
                    "server_time": time.time()
                }
                
                if request_id:
                    response["request_id"] = request_id
                
                await self._send_message(websocket, response)
                logger.info(f"📋 QA请求缓存命中，直接返回结果: {cache_result['question_hash']}")
                return
            
            # 缓存未命中，需要处理新请求
            qa_request_id = cache_result["request_id"]
            
            # 🚀 Phase 2.4: 开始WebSocket QA性能监控
            from ..monitoring.qa_performance_monitor import get_qa_performance_monitor
            qa_monitor = get_qa_performance_monitor()
            
            performance_metric = qa_monitor.start_qa_tracking(
                qa_id=qa_request_id,
                question_text=question_text,
                insertion_strategy="websocket_v2",
                session_id=session_id,
                client_id=client_id
            )
            
            # 记录缓存未命中
            qa_monitor.record_cache_hit(qa_request_id, False)
            
            # 更新状态为处理中
            await qa_cache.update_qa_request_status(
                qa_request_id, 
                QARequestStatus.PROCESSING
            )
            
            # 如果请求中已包含答案，直接插入
            if data.get("answer"):
                answer = data.get("answer")
                logger.info(f"🤖 使用WebSocket提供的QA答案: {answer[:50]}...")
            else:
                # 需要生成答案（这里可以调用QA生成服务）
                # 暂时使用默认回答
                answer = f"这是对问题 '{question_text}' 的自动回答。"
                logger.info(f"🤖 使用默认QA答案: {answer}")
            
            # 构建QA插入请求
            qa_request = QAInsertionRequest(
                qa_id=qa_request_id,
                question=question_text,
                answer=answer,  # 这里的answer已经是包含过渡语的完整回答
                strategy=QAInsertionStrategy(data.get("strategy", "min")),
                priority=data.get("priority", 1)
            )
            
            # 执行QA插入
            qa_result = await self.playlist_manager.handle_qa_event(qa_request, self.client_tracker)
            
            # # 🎯 调试信息：QA答案合成后打印当前正在播放的句子全文
            # if qa_result.success:
            #     try:
            #         current_item = await self.playlist_manager.get_item_at(self.playlist_manager._current_playback_index)
            #         if current_item:
            #             logger.info(f"[QA调试-答案合成后] 当前正在播放的句子全文: '{current_item.content}'")
            #             logger.info(f"[QA调试-答案合成后] 当前播放索引: {self.playlist_manager._current_playback_index}")
            #             logger.info(f"[QA调试-答案合成后] QA插入位置: {qa_result.insertion_index}, 插入项目数: {len(qa_result.items_inserted)}")
            #         else:
            #             logger.info(f"[QA调试-答案合成后] 当前播放索引 {self.playlist_manager._current_playback_index} 无对应句子")
            #     except Exception as debug_error:
            #         logger.warning(f"[QA调试-答案合成后] 获取当前播放句子失败: {debug_error}")
            
            # 🚀 Phase 2.4: 完成WebSocket QA性能监控
            from ..monitoring.qa_performance_monitor import QAInsertionResult as MonitoringResult
            
            if qa_result.success:
                # 如果插入成功，触发预合成（使用返回的同一批items）
                await self.proactive_synthesizer.trigger_qa_synthesis(qa_result.items_for_pre_synthesis)
                
                # 更新缓存状态为完成
                await qa_cache.update_qa_request_status(
                    qa_request_id,
                    QARequestStatus.COMPLETED,
                    answer=answer,
                    qa_id=qa_request.qa_id
                )
                
                # 获取更新后的播放列表信息
                playlist_info = await self.playlist_manager.get_playlist_info()
                playlist_version = playlist_info.version
                
                # 完成性能追踪
                qa_monitor.finish_qa_tracking(
                    qa_request_id,
                    result=MonitoringResult.SUCCESS,
                    sentences_inserted=len(qa_result.items_inserted),
                    error_message=None
                )
                
                logger.info(f"✅ QA插入成功: {qa_request_id}")
                
            else:
                # 插入失败，更新缓存状态
                await qa_cache.update_qa_request_status(
                    qa_request_id,
                    QARequestStatus.FAILED,
                    error_message=qa_result.error_message or "播放列表插入失败"
                )
                playlist_version = None
                
                # 完成性能追踪
                qa_monitor.finish_qa_tracking(
                    qa_request_id,
                    result=MonitoringResult.FAILED,
                    sentences_inserted=0,
                    error_message="播放列表插入失败"
                )
                
                logger.error(f"❌ QA插入失败: {qa_request_id}")
            
            # 发送响应（缓存系统会自动通知所有关联客户端）
            response = {
                "type": "qa_response",
                "qa_id": qa_request.qa_id,
                "success": qa_result.success,
                "cached": False,
                "status": "completed" if qa_result.success else "failed",
                "inserted": qa_result.success,
                "server_time": time.time()
            }
            
            if request_id:
                response["request_id"] = request_id
                
            if qa_result.success and playlist_version:
                response["playlist_version"] = playlist_version
                
            await self._send_message(websocket, response)
            
        except Exception as e:
            logger.error(f"❌ QA请求处理失败: {client_id}, 错误: {e}")
            await self._send_error(websocket, "qa_error", str(e))
            
    async def _handle_state_sync(self, websocket: WebSocket, client_id: str, 
                               data: Dict[str, Any]) -> None:
        """处理状态同步"""
        try:
            # 更新客户端状态
            success = await self.client_tracker.update_client_state(client_id, data)
            
            if success:
                # 获取更新后的状态
                client_state = await self.client_tracker.get_client_state(client_id)
                
                response = {
                    "type": "state_sync_response",
                    "success": True,
                    "client_state": {
                        "buffer_health": client_state.buffer_health.value,
                        "connection_status": client_state.connection_status.value,
                        "session_duration_minutes": client_state.session_duration_minutes
                    } if client_state else {},
                    "server_time": time.time()
                }
            else:
                response = {
                    "type": "state_sync_response",
                    "success": False,
                    "error": "状态更新失败",
                    "server_time": time.time()
                }
                
            if "request_id" in data:
                response["request_id"] = data["request_id"]
                
            await self._send_message(websocket, response)
            
        except Exception as e:
            logger.error(f"状态同步处理失败: {client_id}, 错误: {e}")
            await self._send_error(websocket, "state_sync_error", str(e))
            
            
    async def _handle_playlist_info_request(self, websocket: WebSocket, client_id: str, 
                                          data: Dict[str, Any]) -> None:
        """处理播放列表信息请求，包含智能索引计算"""
        try:
            # 获取客户端信息
            client_version = data.get('client_version', 0)
            current_index = data.get('current_playing_index', 0)
            sync_reason = data.get('sync_reason', 'unknown')
            request_id = data.get('request_id')
            
            playlist_info = await self.playlist_manager.get_playlist_info()
            playlist = await self.playlist_manager.get_current_playlist()
            current_version = playlist_info.version
            
            # 智能计算建议的恢复索引
            suggested_index = self._calculate_suggested_index_smart(
                current_index, 
                playlist,
                sync_reason,
                client_version,
                current_version
            )
            
            response = {
                "type": "playlist_info_response",
                "request_id": request_id,
                "version": current_version,
                "total_items": playlist_info.total_items,
                "suggested_resume_index": suggested_index,
                "version_diff": current_version - client_version,
                "script_items": playlist_info.script_items,
                "qa_sequences": playlist_info.qa_sequences,
                "checksum": playlist_info.checksum,
                "last_modified": playlist_info.last_modified.isoformat(),
                "estimated_duration_ms": playlist_info.total_estimated_duration_ms,
                "server_time": time.time()
            }
            
            await self._send_message(websocket, response)
            logger.info(f"播放列表同步响应: v{client_version}→v{current_version}, 建议索引: {suggested_index}")
            
        except Exception as e:
            logger.error(f"播放列表信息请求处理失败: {e}")
            await self._send_error(websocket, "playlist_info_error", str(e), request_id=request_id)
            
    async def _handle_reconnect(self, websocket: WebSocket, client_id: str, 
                              data: Dict[str, Any]) -> None:
        """处理重连请求"""
        try:
            last_played_index = data.get("last_played_index", 0)
            
            # 处理重连
            reconnect_info = await self.client_tracker.handle_client_reconnect(
                client_id, last_played_index
            )
            
            response = {
                "type": "reconnect_response",
                **reconnect_info,
                "server_time": time.time()
            }
            
            if "request_id" in data:
                response["request_id"] = data["request_id"]
                
            await self._send_message(websocket, response)
            
        except Exception as e:
            logger.error(f"重连处理失败: {client_id}, 错误: {e}")
            await self._send_error(websocket, "reconnect_error", str(e))
    
    async def _handle_audio_error_report(self, websocket: WebSocket, client_id: str, 
                                       data: Dict[str, Any]) -> None:
        """处理客户端音频错误报告"""
        try:
            error_type = data.get("error_type", "unknown")
            details = data.get("details", {})
            audio_state = data.get("audio_state", {})
            
            # 记录错误到日志
            logger.error(f"🔊 客户端音频错误报告: client={client_id}, type={error_type}")
            logger.error(f"  错误详情: {details}")
            logger.error(f"  音频状态: context_state={audio_state.get('context_state')}, "
                        f"sample_rate={audio_state.get('sample_rate')}, "
                        f"is_playing={audio_state.get('is_playing')}")
            
            # 更新统计
            if "audio_errors" not in self._stats:
                self._stats["audio_errors"] = {}
            
            if error_type not in self._stats["audio_errors"]:
                self._stats["audio_errors"][error_type] = 0
            
            self._stats["audio_errors"][error_type] += 1
            
            # 可选：根据错误类型触发恢复逻辑
            if error_type == "audio_context_init_error":
                logger.warning(f"客户端 {client_id} 音频初始化失败，可能需要用户交互")
                # 可以发送特殊指令要求用户点击按钮等
                
            elif error_type == "pcm_processing_error":
                logger.warning(f"客户端 {client_id} PCM处理失败，检查音频格式")
                # 可以考虑切换到不同的音频格式或重发数据
            
            # 发送确认响应
            response = {
                "type": "audio_error_ack",
                "error_type": error_type,
                "received": True,
                "server_time": time.time()
            }
            
            await self._send_message(websocket, response)
            
        except Exception as e:
            logger.error(f"处理音频错误报告失败: {client_id}, 错误: {e}")
            # 不发送错误响应，避免循环
            
    async def _send_message(self, websocket: WebSocket, data: Dict[str, Any]) -> None:
        """发送消息到客户端"""
        try:
            # 记录要发送的消息类型和关键信息
            message_type = data.get("type", "unknown")
            logger.debug(f"准备发送消息: type={message_type}, client_id={data.get('client_id', 'N/A')}")
            
            if websocket.client_state == WebSocketState.CONNECTED:
                message = json.dumps(data, ensure_ascii=False)
                await websocket.send_text(message)
                self._stats["total_messages_sent"] += 1
                logger.info(f"✅ 成功发送消息: type={message_type}, size={len(message)} bytes")
            else:
                logger.warning(f"尝试向已断开的连接发送消息: type={message_type}, state={websocket.client_state}")
        except Exception as e:
            logger.error(f"发送消息失败: type={data.get('type', 'unknown')}, error={e}")
            raise
            
    def _calculate_suggested_index_smart(self, current_index: int, playlist: Any, 
                                        sync_reason: str, client_version: int, 
                                        server_version: int) -> int:
        """
        智能计算建议的恢复索引
        
        策略：
        1. 如果是 item_inserted 触发，查找插入点之后的安全恢复位置
        2. 如果版本差异较大，建议从当前位置重新开始
        3. 如果是普通同步，保持当前索引
        """
        # 版本差异分析
        version_diff = server_version - client_version
        
        if sync_reason == 'item_inserted' and hasattr(playlist, 'last_insertion_index'):
            # QA插入场景：如果当前播放位置在插入点之后，需要调整
            insertion_index = playlist.last_insertion_index
            insertion_count = playlist.last_insertion_count
            
            if current_index >= insertion_index:
                # 客户端正在播放插入点之后的内容，需要调整
                suggested = current_index + insertion_count
                logger.info(f"QA插入调整: {current_index} → {suggested}")
            else:
                # 客户端还未到达插入点，保持当前索引
                suggested = current_index
        elif version_diff > 5:
            # 版本差异过大，可能错过了多次更新，建议重新开始
            suggested = max(0, current_index - 2)  # 稍微回退以确保连续性
            logger.warning(f"版本差异过大({version_diff})，建议回退到: {suggested}")
        else:
            # 正常情况，保持当前索引
            suggested = current_index
        
        # 边界检查
        total_items = playlist.total_items if hasattr(playlist, 'total_items') else 100
        suggested = max(0, min(suggested, total_items - 1))
        
        return suggested
    
    async def _send_error(self, websocket: WebSocket, error_code: str, message: str, 
                         request_id: Optional[str] = None) -> None:
        """发送错误消息（符合API契约v2.0）"""
        error_response = {
            "type": "error",
            "error_code": error_code.upper(),  # API契约使用大写错误码
            "message": message,
            "request_id": request_id,  # 关联请求ID
            "recoverable": error_code.upper() not in ["FATAL", "SYSTEM_ERROR"],
            "server_time": time.time()
        }
        
        try:
            await self._send_message(websocket, error_response)
        except:
            pass  # 忽略发送错误响应时的异常
            
    async def _cleanup_connection(self, client_id: str) -> None:
        """清理连接资源 - 音频代理清理已在 finally 块中完成"""
        # 立即设置状态防止重复清理
        if self._connection_states.get(client_id) == ConnectionState.DISCONNECTING:
            logger.debug(f"连接已在清理中，跳过重复清理: client={client_id}")
            return
        
        if self._connection_states.get(client_id) == ConnectionState.DISCONNECTED:
            logger.debug(f"连接已清理完成，跳过重复清理: client={client_id}")
            return
            
        session_id = self._client_sessions.get(client_id, {}).get('session_id', 'unknown')
        logger.info(f"🧹 开始清理客户端: client={client_id}, session={session_id}")
        
        self._connection_states[client_id] = ConnectionState.DISCONNECTING
        
        try:
            # 🔧 注意：音频代理清理已经在 handle_connection 的 finally 块中无条件执行
            # 这里不再重复清理，避免重复调用 remove_client
            
            # 📡 从状态广播器注销客户端（架构解耦）
            try:
                await self.state_broadcaster.unregister_client(client_id)
                logger.debug(f"✅ 客户端已从状态广播器注销: {client_id}")
            except Exception as e:
                logger.warning(f"⚠️ 状态广播器注销失败: {client_id}, error: {e}")
            
            # 更新统计
            if client_id in self._connections:
                self._stats["active_connections"] -= 1
                
            # 清理客户端活动监控
            self._cleanup_client_monitoring(client_id)
                
            # 移除连接
            self._connections.pop(client_id, None)
            self._client_sessions.pop(client_id, None)
            self._client_last_activity.pop(client_id, None)
            
            # 处理客户端断开
            await self.client_tracker.handle_client_disconnect(client_id)
            
            # 🚀 从QA缓存中注销WebSocket客户端
            try:
                from ..services.websocket_qa_cache import get_qa_cache
                qa_cache = get_qa_cache()
                qa_cache.unregister_websocket_client(client_id)
                logger.debug(f"📱 QA缓存中的WebSocket客户端已注销: {client_id}")
            except Exception as e:
                logger.warning(f"⚠️ QA缓存注销失败: {client_id}, error: {e}")
            
            logger.info(f"✅ 完成清理客户端: client={client_id}, session={session_id}")
            
        except Exception as e:
            logger.error(f"清理失败: client={client_id}, session={session_id}, error={e}")
        finally:
            self._connection_states[client_id] = ConnectionState.DISCONNECTED
            
    async def broadcast_state_update(self, state_data: Dict[str, Any]) -> int:
        """
        WebSocket架构重构：广播状态更新到所有连接的客户端（委托给StateBroadcaster）
        
        Args:
            state_data: 状态更新数据
        
        Returns:
            成功广播的客户端数量
        """
        # 📡 委托给统一的状态广播器（架构解耦）
        return await self.state_broadcaster.broadcast_state_update(state_data)
    
    async def broadcast_playlist_update(self, playlist_version: int) -> int:
        """
        广播播放列表更新通知（委托给StateBroadcaster）
        
        Args:
            playlist_version: 新的播放列表版本
            
        Returns:
            成功广播的客户端数量
        """
        # 📡 委托给统一的状态广播器，使用playlist_updated消息
        return await self.state_broadcaster.broadcast_playlist_update(
            "manual_update", 
            total_items=playlist_version,
            action_required="client_should_sync"
        )
        
    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        active_sessions = []
        
        for client_id, session_info in self._client_sessions.items():
            active_sessions.append({
                "client_id": client_id[:8] + "...",  # 隐私保护
                "session_id": session_info["session_id"],
                "connected_duration_minutes": (
                    datetime.utcnow() - session_info["connected_at"]
                ).total_seconds() / 60,
                "last_activity_seconds_ago": (
                    datetime.utcnow() - session_info["last_activity"]
                ).total_seconds(),
                "protocol_version": session_info["protocol_version"]
            })
            
        return {
            **self._stats,
            "active_sessions": active_sessions,
            "memory_usage_estimate_bytes": len(self._connections) * 4096 + len(self._client_sessions) * 1024
        }
        
    async def force_disconnect_client(self, client_id: str, reason: str = "server_request") -> bool:
        """
        强制断开客户端连接
        
        Args:
            client_id: 客户端标识符
            reason: 断开原因
            
        Returns:
            是否成功断开
        """
        websocket = self._connections.get(client_id)
        if not websocket:
            return False
            
        try:
            # 发送断开通知
            disconnect_message = {
                "type": "force_disconnect",
                "reason": reason,
                "server_time": time.time()
            }
            
            await self._send_message(websocket, disconnect_message)
            
            # 关闭连接
            await websocket.close(code=CLOSE_CODE_NORMAL, reason=reason)
            
            logger.info(f"强制断开客户端: {client_id}, 原因: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"强制断开客户端失败: {client_id}, 错误: {e}")
            return False
            
    def get_connected_clients(self) -> Set[str]:
        """获取已连接客户端列表"""
        return set(self._connections.keys())
    
    # ==================== Global Heartbeat Monitoring ====================
    
    async def _start_global_heartbeat_monitor(self) -> None:
        """启动全局心跳监控器"""
        if self._global_heartbeat_task is None or self._global_heartbeat_task.done():
            self._global_heartbeat_task = asyncio.create_task(self._global_heartbeat_loop())
            logger.info("💓 全局心跳监控器已启动")
            
    async def _global_heartbeat_loop(self) -> None:
        """全局心跳检查循环"""
        while True:
            try:
                await asyncio.sleep(self._heartbeat_interval)
                
                current_time = time.time()
                dead_clients = []
                
                # 检查所有活跃连接
                for client_id in list(self._connections.keys()):
                    if self._connection_states.get(client_id) != ConnectionState.CONNECTED:
                        continue
                        
                    last_activity = self._client_last_activity.get(client_id, 0)
                    if current_time - last_activity > self._heartbeat_timeout:
                        session_id = self._client_sessions.get(client_id, {}).get('session_id', 'unknown')
                        logger.warning(f"⚠️ 检测到僵尸连接: client={client_id}, "
                                     f"session={session_id}, "
                                     f"最后活动: {current_time - last_activity:.1f}秒前")
                        dead_clients.append(client_id)
                
                # 清理僵尸连接
                for client_id in dead_clients:
                    await self._handle_zombie_connection(client_id)
                    
            except asyncio.CancelledError:
                logger.info("💓 全局心跳监控器已停止")
                break
            except Exception as e:
                logger.error(f"全局心跳监控错误: {e}")
                
    async def _handle_zombie_connection(self, client_id: str) -> None:
        """处理僵尸连接"""
        session_id = self._client_sessions.get(client_id, {}).get('session_id', 'unknown')
        logger.info(f"🧟 开始清理僵尸连接: client={client_id}, session={session_id}")
        
        websocket = self._connections.get(client_id)
        if websocket:
            try:
                await websocket.close(code=CLOSE_CODE_GOING_AWAY, reason="heartbeat_timeout")
            except:
                pass
                
        await self._cleanup_connection(client_id)
        logger.info(f"✅ 完成清理僵尸连接: client={client_id}, session={session_id}")
        
    async def cleanup(self) -> None:
        """清理所有连接和资源"""
        logger.info("开始清理WebSocket v2连接...")
        
        # 停止全局心跳监控器
        if self._global_heartbeat_task and not self._global_heartbeat_task.done():
            self._global_heartbeat_task.cancel()
            try:
                await self._global_heartbeat_task
            except asyncio.CancelledError:
                pass
            logger.info("💓 全局心跳监控器已停止")
        
        # 断开所有连接
        disconnect_tasks = []
        for client_id in list(self._connections.keys()):
            task = self.force_disconnect_client(client_id, "server_shutdown")
            disconnect_tasks.append(task)
            
        if disconnect_tasks:
            await asyncio.gather(*disconnect_tasks, return_exceptions=True)
            
        # 清理数据结构
        self._connections.clear()
        self._client_sessions.clear()
        self._connection_states.clear()
        self._client_last_activity.clear()
        
        logger.info("WebSocket v2连接清理完成")
        
    # ==================== Client Activity Monitoring ====================
    
    async def _start_client_activity_monitoring(self, client_id: str) -> None:
        """开始监控客户端活动"""
        logger.debug(f"开始监控客户端活动: {client_id}")
        
        # 初始化客户端监控状态
        if not hasattr(self, '_client_monitors'):
            self._client_monitors = {}
            
        self._client_monitors[client_id] = {
            "connection_time": time.time(),
            "last_content_request": None,
            "total_requests": 0,
            "warning_sent": False
        }
        
        # 启动监控任务
        monitor_task = asyncio.create_task(self._monitor_client_activity(client_id))
        self._client_monitors[client_id]["monitor_task"] = monitor_task
        
    async def _monitor_client_activity(self, client_id: str) -> None:
        """监控客户端活动的后台任务"""
        try:
            # 等待10秒检查客户端是否发送了初始内容请求
            await asyncio.sleep(10)
            
            if client_id not in self._client_monitors:
                return  # 客户端已断开
                
            monitor_info = self._client_monitors[client_id]
            
            # 检查是否收到了内容请求
            if monitor_info["total_requests"] == 0:
                logger.warning(f"⚠️ 客户端 {client_id} 连接10秒后仍未发送任何content_request消息")
                logger.warning(f"🔍 这可能表明客户端使用了V1协议或实现不完整")
                logger.warning(f"📍 V2协议要求客户端主动请求内容，请检查客户端实现")
                
                # 发送警告消息给客户端
                await self._send_protocol_warning(client_id)
                monitor_info["warning_sent"] = True
                
                # 继续监控30秒，如果还是没有请求就记录错误
                await asyncio.sleep(30)
                
                if client_id in self._client_monitors and monitor_info["total_requests"] == 0:
                    logger.error(f"❌ 客户端 {client_id} 连接40秒后仍无content_request - V1/V2协议不匹配")
                    logger.error(f"💡 建议: 更新客户端以支持V2协议的content_request消息")
                    
        except asyncio.CancelledError:
            logger.debug(f"客户端活动监控任务已取消: {client_id}")
        except Exception as e:
            logger.error(f"客户端活动监控错误: {client_id}, {e}")
            
    async def _send_protocol_warning(self, client_id: str) -> None:
        """向客户端发送协议警告"""
        if client_id not in self._connections:
            return
            
        websocket = self._connections[client_id]
        warning_message = {
            "type": "protocol_warning",
            "warning_code": "missing_content_request",
            "message": "V2协议要求客户端主动请求内容。请发送content_request消息开始播放。",
            "details": {
                "expected_message_type": "content_request",
                "protocol_version": "2.0",
                "connection_duration_seconds": 10
            },
            "server_time": time.time()
        }
        
        try:
            await self._send_message(websocket, warning_message)
            logger.info(f"📨 已向客户端 {client_id} 发送协议警告")
        except Exception as e:
            logger.error(f"发送协议警告失败: {client_id}, {e}")
            
    def _record_client_request(self, client_id: str, request_type: str) -> None:
        """记录客户端请求活动"""
        if hasattr(self, '_client_monitors') and client_id in self._client_monitors:
            monitor_info = self._client_monitors[client_id]
            
            if request_type == "content_request":
                if monitor_info["last_content_request"] is None:
                    # 第一次内容请求
                    time_to_first_request = time.time() - monitor_info["connection_time"]
                    logger.info(f"✅ 客户端 {client_id} 发送了第一个content_request (连接后 {time_to_first_request:.1f}s)")
                    
                monitor_info["last_content_request"] = time.time()
                monitor_info["total_requests"] += 1
                
    def _cleanup_client_monitoring(self, client_id: str) -> None:
        """清理客户端监控资源"""
        if not hasattr(self, '_client_monitors'):
            return
            
        if client_id in self._client_monitors:
            monitor_info = self._client_monitors[client_id]
            
            # 取消监控任务
            if "monitor_task" in monitor_info:
                monitor_task = monitor_info["monitor_task"]
                if not monitor_task.done():
                    monitor_task.cancel()
                    
            # 移除监控信息
            del self._client_monitors[client_id]
            logger.debug(f"客户端监控资源已清理: {client_id}")


# 工厂函数

def create_websocket_v2_handler(playlist_manager: 'PlaylistManager',
                               client_tracker: 'ClientStateTracker',
                               content_provider: 'StreamingContentProvider',
                               proactive_synthesizer: 'ProactiveSynthesizer',
                               config: StreamingConfig,
                               audio_proxy,
                               state_broadcaster) -> WebSocketV2Handler:
    """创建WebSocket v2处理器实例"""
    return WebSocketV2Handler(
        playlist_manager, client_tracker, content_provider,
        proactive_synthesizer, config, audio_proxy,
        state_broadcaster
    )