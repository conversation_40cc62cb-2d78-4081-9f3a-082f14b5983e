"""QA监控API端点 - 提供QA插入性能的实时监控和历史分析

Phase 2.4: 结构化日志和监控端点

Author: Claude Code
Date: 2025-01-08 (QA修复专用)
"""

import time
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from datetime import datetime, timedelta
from loguru import logger

from ..monitoring.qa_performance_monitor import (
    get_qa_performance_monitor, 
    QAPerformanceMonitor,
    QAInsertionResult
)


# 创建QA监控路由器
qa_monitoring_router = APIRouter(
    prefix="/api/qa-monitoring",
    tags=["QA Performance Monitoring"]
)


def get_monitor() -> QAPerformanceMonitor:
    """依赖注入：获取QA性能监控器"""
    return get_qa_performance_monitor()


@qa_monitoring_router.get("/stats")
async def get_qa_stats(monitor: QAPerformanceMonitor = Depends(get_monitor)):
    """获取QA插入的实时统计信息 - 🚀 仪表板核心数据"""
    try:
        stats = monitor.get_current_stats()
        
        return JSONResponse({
            "success": True,
            "timestamp": time.time(),
            "data": {
                "overview": stats,
                "active_sessions": len(monitor.active_qa_sessions),
                "alert_status": _check_alert_status(stats),
                "health_score": _calculate_health_score(stats)
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取QA统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@qa_monitoring_router.get("/trends/{window}")
async def get_qa_trends(
    window: str,
    monitor: QAPerformanceMonitor = Depends(get_monitor)
):
    """获取QA插入的趋势数据 - 🚀 时间序列分析
    
    Args:
        window: 时间窗口 (1min, 5min, 1hour)
    """
    try:
        if window not in ["1min", "5min", "1hour"]:
            raise HTTPException(status_code=400, detail="Invalid time window")
        
        trend_data = monitor.get_time_window_stats(window)
        
        return JSONResponse({
            "success": True,
            "timestamp": time.time(),
            "data": {
                "window": window,
                "trend_data": trend_data,
                "data_points": trend_data.get("data_points", 0)
            }
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 获取QA趋势数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@qa_monitoring_router.get("/report")
async def get_qa_performance_report(
    last_n_minutes: int = Query(default=60, ge=1, le=1440),
    monitor: QAPerformanceMonitor = Depends(get_monitor)
):
    """生成QA性能详细报告 - 🚀 深度分析报告
    
    Args:
        last_n_minutes: 分析最近N分钟的数据（1-1440分钟）
    """
    try:
        report = monitor.export_performance_report(last_n_minutes)
        
        return JSONResponse({
            "success": True,
            "timestamp": time.time(),
            "data": {
                "report": report,
                "generation_time": datetime.utcnow().isoformat(),
                "analysis_period_minutes": last_n_minutes
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 生成QA性能报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@qa_monitoring_router.get("/active-sessions")
async def get_active_qa_sessions(
    monitor: QAPerformanceMonitor = Depends(get_monitor)
):
    """获取当前活跃的QA会话 - 🚀 实时会话监控"""
    try:
        active_sessions = []
        
        for qa_id, metric in monitor.active_qa_sessions.items():
            session_data = {
                "qa_id": qa_id,
                "question_preview": metric.question_text[:50] + "...",
                "start_time": metric.request_timestamp,
                "elapsed_ms": time.time() * 1000 - metric.request_timestamp,
                "strategy": metric.insertion_strategy,
                "session_id": metric.session_id,
                "client_id": metric.client_id,
                "concurrent_count": metric.concurrent_qa_requests
            }
            active_sessions.append(session_data)
        
        # 按开始时间排序
        active_sessions.sort(key=lambda x: x["start_time"], reverse=True)
        
        return JSONResponse({
            "success": True,
            "timestamp": time.time(),
            "data": {
                "active_sessions": active_sessions,
                "total_count": len(active_sessions),
                "oldest_session_age_ms": (
                    max(s["elapsed_ms"] for s in active_sessions) 
                    if active_sessions else 0
                )
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取活跃QA会话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@qa_monitoring_router.get("/metrics/latency")
async def get_latency_metrics(
    percentiles: str = Query(default="50,90,95,99"),
    last_n_minutes: int = Query(default=30, ge=1, le=1440),
    monitor: QAPerformanceMonitor = Depends(get_monitor)
):
    """获取延迟指标分析 - 🚀 延迟性能专项分析
    
    Args:
        percentiles: 要计算的百分位数（逗号分隔）
        last_n_minutes: 分析时间范围
    """
    try:
        # 解析百分位数参数
        try:
            percentile_list = [float(p.strip()) for p in percentiles.split(",")]
            percentile_list = [p for p in percentile_list if 0 <= p <= 100]
        except:
            percentile_list = [50, 90, 95, 99]
        
        # 获取时间范围内的指标
        cutoff_time = time.time() * 1000 - (last_n_minutes * 60 * 1000)
        recent_metrics = [
            m for m in monitor.metrics_history 
            if m.request_timestamp >= cutoff_time and m.is_successful()
        ]
        
        if not recent_metrics:
            return JSONResponse({
                "success": True,
                "data": {"note": "No data in specified time range"}
            })
        
        # 计算延迟统计
        latencies = [m.calculate_total_latency() for m in recent_metrics]
        latencies.sort()
        
        percentile_values = {}
        for p in percentile_list:
            index = int(len(latencies) * p / 100)
            index = min(index, len(latencies) - 1)
            percentile_values[f"p{int(p)}"] = latencies[index]
        
        # 分解延迟组件
        component_analysis = {
            "frontend_to_backend": _analyze_component(
                recent_metrics, lambda m: m.frontend_to_backend_ms
            ),
            "backend_processing": _analyze_component(
                recent_metrics, lambda m: m.backend_processing_ms
            ),
            "playlist_insertion": _analyze_component(
                recent_metrics, lambda m: m.playlist_insertion_ms
            ),
            "player_insertion": _analyze_component(
                recent_metrics, lambda m: m.player_insertion_ms
            ),
            "redis_lock_wait": _analyze_component(
                recent_metrics, lambda m: m.redis_lock_wait_ms
            )
        }
        
        return JSONResponse({
            "success": True,
            "timestamp": time.time(),
            "data": {
                "time_range_minutes": last_n_minutes,
                "total_samples": len(recent_metrics),
                "latency_percentiles": percentile_values,
                "basic_stats": {
                    "min_ms": min(latencies),
                    "max_ms": max(latencies),
                    "avg_ms": sum(latencies) / len(latencies),
                    "median_ms": latencies[len(latencies) // 2]
                },
                "component_breakdown": component_analysis
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 获取延迟指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@qa_monitoring_router.get("/health")
async def get_qa_health_check(
    monitor: QAPerformanceMonitor = Depends(get_monitor)
):
    """QA系统健康检查 - 🚀 系统健康状态评估"""
    try:
        stats = monitor.get_current_stats()
        
        # 计算健康指标
        health_indicators = {
            "success_rate": stats.get("success_rate", 0),
            "average_latency_ms": stats.get("average_latency_ms", 0),
            "cache_hit_rate": stats.get("cache_hit_rate", 0),
            "active_sessions": len(monitor.active_qa_sessions),
            "concurrent_conflict_rate": stats.get("concurrent_conflict_rate", 0)
        }
        
        # 健康评分算法
        health_score = _calculate_health_score(stats)
        health_status = _get_health_status(health_score)
        
        # 检查具体问题
        issues = []
        if health_indicators["success_rate"] < 0.95:
            issues.append(f"成功率较低: {health_indicators['success_rate']:.1%}")
        
        if health_indicators["average_latency_ms"] > 1500:
            issues.append(f"平均延迟过高: {health_indicators['average_latency_ms']:.0f}ms")
        
        if health_indicators["cache_hit_rate"] < 0.6:
            issues.append(f"缓存命中率低: {health_indicators['cache_hit_rate']:.1%}")
        
        if health_indicators["active_sessions"] > 10:
            issues.append(f"活跃会话过多: {health_indicators['active_sessions']}")
        
        return JSONResponse({
            "success": True,
            "timestamp": time.time(),
            "data": {
                "health_status": health_status,
                "health_score": health_score,
                "health_indicators": health_indicators,
                "issues": issues,
                "recommendations": monitor._generate_recommendations(
                    list(monitor.metrics_history)[-50:]  # 最近50个指标
                ) if monitor.metrics_history else []
            }
        })
        
    except Exception as e:
        logger.error(f"❌ QA健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@qa_monitoring_router.post("/alerts/test")
async def trigger_test_alert(
    monitor: QAPerformanceMonitor = Depends(get_monitor)
):
    """触发测试警报 - 🚀 用于测试监控系统"""
    try:
        # 创建一个测试指标
        test_metric = monitor.start_qa_tracking(
            qa_id="test_alert_" + str(int(time.time())),
            question_text="这是一个测试警报",
            insertion_strategy="test"
        )
        
        # 设置高延迟来触发警报
        test_metric.backend_processing_ms = 3000  # 3秒延迟
        test_metric.concurrent_qa_requests = 15   # 高并发
        
        # 完成追踪以触发警报检查
        monitor.finish_qa_tracking(
            test_metric.qa_id,
            QAInsertionResult.SUCCESS,
            sentences_inserted=2
        )
        
        return JSONResponse({
            "success": True,
            "message": "测试警报已触发，请检查日志",
            "test_metric_id": test_metric.qa_id
        })
        
    except Exception as e:
        logger.error(f"❌ 触发测试警报失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 辅助函数

def _check_alert_status(stats: Dict[str, Any]) -> Dict[str, Any]:
    """检查警报状态"""
    alerts = []
    
    if stats.get("average_latency_ms", 0) > 1500:
        alerts.append({
            "type": "high_latency",
            "severity": "warning",
            "message": f"平均延迟过高: {stats['average_latency_ms']:.0f}ms"
        })
    
    if stats.get("success_rate", 1) < 0.95:
        alerts.append({
            "type": "low_success_rate", 
            "severity": "critical",
            "message": f"成功率过低: {stats['success_rate']:.1%}"
        })
    
    return {
        "active_alerts": alerts,
        "alert_count": len(alerts),
        "highest_severity": "critical" if any(a["severity"] == "critical" for a in alerts) else 
                          "warning" if alerts else "none"
    }


def _calculate_health_score(stats: Dict[str, Any]) -> float:
    """计算健康评分 (0-100)"""
    score = 100.0
    
    # 成功率权重: 40%
    success_rate = stats.get("success_rate", 0)
    score -= (1 - success_rate) * 40
    
    # 延迟权重: 30%
    avg_latency = stats.get("average_latency_ms", 0)
    if avg_latency > 500:
        latency_penalty = min((avg_latency - 500) / 1500, 1) * 30
        score -= latency_penalty
    
    # 缓存命中率权重: 20%
    cache_hit_rate = stats.get("cache_hit_rate", 0)
    score -= (1 - cache_hit_rate) * 20
    
    # 并发冲突权重: 10%
    conflict_rate = stats.get("concurrent_conflict_rate", 0)
    score -= conflict_rate * 10
    
    return max(0, min(100, score))


def _get_health_status(health_score: float) -> str:
    """根据健康评分获取状态"""
    if health_score >= 90:
        return "excellent"
    elif health_score >= 80:
        return "good"
    elif health_score >= 70:
        return "fair"
    elif health_score >= 50:
        return "poor"
    else:
        return "critical"


def _analyze_component(metrics: List, extractor_func) -> Dict[str, Any]:
    """分析延迟组件"""
    values = []
    for metric in metrics:
        try:
            value = extractor_func(metric)
            if value is not None and value > 0:
                values.append(value)
        except:
            continue
    
    if not values:
        return {"note": "No data available"}
    
    values.sort()
    return {
        "count": len(values),
        "min_ms": min(values),
        "max_ms": max(values),
        "avg_ms": sum(values) / len(values),
        "median_ms": values[len(values) // 2],
        "p95_ms": values[int(len(values) * 0.95)] if len(values) > 5 else max(values)
    }


# 导出路由器供主应用使用
__all__ = ["qa_monitoring_router"]