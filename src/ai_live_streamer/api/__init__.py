"""API layer for AI Live Streamer system

Provides REST endpoints for operational input, control, and monitoring
of the live streaming system.
"""

from .operational_forms import operational_router
from .control import router as control_router
# status router removed as part of architecture simplification
# app.py removed - using app_v2.py as main application

__all__ = [
    "operational_router",
    "control_router",
]