"""
Script Persistence Models

Data models for persistent storage of generated streaming scripts,
including comprehensive metadata, usage tracking, and analytics.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from pydantic import BaseModel, Field
import json


class GenerationMethod(Enum):
    """Script generation methods"""
    LLM = "llm"
    TEMPLATE = "template"
    HYBRID = "hybrid"


class ScriptStatus(Enum):
    """Script lifecycle status"""
    GENERATED = "generated"
    VALIDATED = "validated"
    ACTIVE = "active"
    USED = "used"
    ARCHIVED = "archived"
    DELETED = "deleted"


@dataclass
class PersistedScriptSegment:
    """Individual script segment with persistence metadata"""
    segment_id: str
    script_id: str
    segment_type: str
    title: str
    content: str
    duration_seconds: int
    priority: str
    segment_order: int
    triggers: List[str]
    variables: Dict[str, Any]
    generation_metadata: Dict[str, Any]
    
    # Persistence metadata
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None
    usage_count: int = 0
    last_used_at: Optional[datetime] = None


@dataclass
class PersistedScript:
    """Persistent script with comprehensive metadata"""
    script_id: str
    form_id: str
    generated_at: datetime
    generation_method: GenerationMethod
    total_duration_seconds: int
    segment_count: int
    generation_time_ms: int
    html_preview: Optional[str]
    
    # Core script data
    segments: List[PersistedScriptSegment]
    interaction_points: List[int]
    break_points: List[int]
    adaptation_rules: Dict[str, Any]
    
    # Metadata and analytics
    estimated_metrics: Dict[str, Any]
    generation_warnings: List[str]
    persona_info: Dict[str, Any]
    form_snapshot: Dict[str, Any]  # Key form data at generation time
    
    # Lifecycle and usage
    status: ScriptStatus = ScriptStatus.GENERATED
    usage_count: int = 0
    last_used_at: Optional[datetime] = None
    created_by: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    
    # Quality and performance metrics
    quality_score: Optional[float] = None
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    user_feedback: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StreamingSession:
    """Tracking data for script usage in live streams"""
    session_id: str
    script_id: str
    started_at: datetime
    ended_at: Optional[datetime]
    
    # Usage metrics
    segments_used: int
    segments_skipped: int
    total_viewers: int
    peak_viewers: int
    average_engagement: float
    
    # Performance data
    generation_to_use_delay_minutes: int
    actual_vs_planned_duration_ratio: float
    viewer_retention_rate: float
    
    # Engagement metrics
    questions_received: int
    questions_answered: int
    interaction_rate: float
    conversion_metrics: Dict[str, Any]
    
    # Technical metadata
    stream_title: str
    persona_used: str
    platform_data: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration_minutes(self) -> Optional[int]:
        """Calculate session duration in minutes"""
        if self.ended_at:
            delta = self.ended_at - self.started_at
            return int(delta.total_seconds() / 60)
        return None
    
    @property
    def engagement_score(self) -> float:
        """Calculate overall engagement score"""
        factors = [
            min(1.0, self.interaction_rate / 0.1),  # Target 10% interaction rate
            min(1.0, self.viewer_retention_rate),
            min(1.0, self.average_engagement),
            min(1.0, (self.questions_answered / max(self.questions_received, 1)))
        ]
        return sum(factors) / len(factors)


class ScriptQuery(BaseModel):
    """Query parameters for script search and filtering"""
    form_id: Optional[str] = None
    generation_method: Optional[GenerationMethod] = None
    status: Optional[ScriptStatus] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    min_duration_seconds: Optional[int] = None
    max_duration_seconds: Optional[int] = None
    min_quality_score: Optional[float] = None
    tags: Optional[List[str]] = None
    used_after: Optional[datetime] = None
    created_by: Optional[str] = None
    
    # Pagination
    offset: int = 0
    limit: int = 50
    order_by: str = "generated_at"
    order_desc: bool = True


class ScriptAnalytics(BaseModel):
    """Analytics data for script generation and usage"""
    
    # Generation analytics
    total_scripts: int
    scripts_by_method: Dict[GenerationMethod, int]
    average_generation_time_ms: float
    generation_success_rate: float
    
    # Usage analytics  
    total_sessions: int
    total_usage_minutes: int
    average_session_duration_minutes: float
    scripts_reuse_rate: float
    
    # Quality metrics
    average_quality_score: float
    quality_distribution: Dict[str, int]  # score ranges
    top_performing_scripts: List[str]  # script_ids
    
    # Engagement metrics
    average_engagement_score: float
    average_interaction_rate: float
    average_retention_rate: float
    
    # Time-based trends
    generation_trend_7d: List[int]
    usage_trend_7d: List[int]
    quality_trend_7d: List[float]
    
    # Performance insights
    generation_efficiency_score: float
    content_utilization_rate: float
    user_satisfaction_score: float


class ScriptExportFormat(Enum):
    """Export format options"""
    JSON = "json"
    TEXT = "text" 
    HTML = "html"
    CSV = "csv"
    DOCX = "docx"


@dataclass
class ScriptExportRequest:
    """Request for script export"""
    script_ids: List[str]
    export_format: ScriptExportFormat
    include_metadata: bool = True
    include_segments: bool = True
    include_analytics: bool = False
    include_usage_history: bool = False
    
    # Export filtering
    segment_types: Optional[List[str]] = None
    date_range: Optional[tuple] = None  # (start_date, end_date)


@dataclass 
class ScriptValidationResult:
    """Result of script content validation"""
    is_valid: bool
    quality_score: float
    validation_warnings: List[str]
    validation_errors: List[str]
    
    # Content quality metrics
    content_length_score: float
    coherence_score: float
    engagement_score: float
    technical_score: float
    
    # Recommendations
    improvement_suggestions: List[str]
    estimated_performance_impact: Dict[str, float]


class PersistenceConfig(BaseModel):
    """Configuration for script persistence system"""
    
    # Database settings
    database_path: str = "data/scripts.db"
    connection_pool_size: int = 10
    enable_wal_mode: bool = True
    
    # Retention policies
    max_scripts_per_user: int = 1000
    auto_archive_after_days: int = 90
    auto_delete_after_days: int = 365
    
    # Performance settings
    batch_insert_size: int = 100
    cache_size_mb: int = 64
    enable_async_operations: bool = True
    
    # Analytics settings
    enable_usage_tracking: bool = True
    enable_performance_monitoring: bool = True
    analytics_retention_days: int = 180
    
    # Export settings
    max_export_batch_size: int = 500
    export_timeout_seconds: int = 300
    
    # Quality assurance
    enable_quality_validation: bool = True
    min_quality_threshold: float = 0.6
    enable_duplicate_detection: bool = True


class ScriptSearchIndex(BaseModel):
    """Search index for efficient script discovery"""
    script_id: str
    form_id: str
    title: str
    content_keywords: List[str]
    tags: List[str]
    product_name: str
    brand: str
    category: str
    generation_date: datetime
    quality_score: float
    usage_count: int
    
    # Full-text search support
    searchable_content: str  # Combined content from segments
    
    def matches_query(self, query: str) -> bool:
        """Check if script matches search query"""
        query_lower = query.lower()
        return (
            query_lower in self.title.lower() or
            query_lower in self.product_name.lower() or
            query_lower in self.brand.lower() or
            query_lower in self.searchable_content.lower() or
            any(query_lower in tag.lower() for tag in self.tags) or
            any(query_lower in keyword.lower() for keyword in self.content_keywords)
        )


# Database schema constants for migration scripts
DATABASE_SCHEMA = {
    "persisted_scripts": """
        CREATE TABLE IF NOT EXISTS persisted_scripts (
            script_id TEXT PRIMARY KEY,
            form_id TEXT NOT NULL,
            generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            generation_method TEXT NOT NULL,
            total_duration_seconds INTEGER NOT NULL,
            segment_count INTEGER NOT NULL,
            generation_time_ms INTEGER NOT NULL,
            html_preview TEXT,
            
            -- Script structure data
            interaction_points TEXT, -- JSON array
            break_points TEXT, -- JSON array  
            adaptation_rules TEXT, -- JSON object
            estimated_metrics TEXT, -- JSON object
            generation_warnings TEXT, -- JSON array
            persona_info TEXT, -- JSON object
            form_snapshot TEXT, -- JSON object
            
            -- Lifecycle and metadata
            status TEXT DEFAULT 'generated',
            usage_count INTEGER DEFAULT 0,
            last_used_at TIMESTAMP,
            created_by TEXT,
            tags TEXT, -- JSON array
            quality_score REAL,
            performance_metrics TEXT, -- JSON object
            user_feedback TEXT, -- JSON object
            
            -- Indexes
            INDEX(form_id),
            INDEX(generated_at),
            INDEX(generation_method),
            INDEX(status),
            INDEX(created_by),
            INDEX(quality_score)
        )
    """,
    
    "script_segments": """
        CREATE TABLE IF NOT EXISTS script_segments (
            segment_id TEXT PRIMARY KEY,
            script_id TEXT NOT NULL,
            segment_type TEXT NOT NULL,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            duration_seconds INTEGER NOT NULL,
            priority TEXT NOT NULL,
            segment_order INTEGER NOT NULL,
            
            -- Segment metadata  
            triggers TEXT, -- JSON array
            variables TEXT, -- JSON object
            generation_metadata TEXT, -- JSON object
            
            -- Usage tracking
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP,
            usage_count INTEGER DEFAULT 0,
            last_used_at TIMESTAMP,
            
            FOREIGN KEY(script_id) REFERENCES persisted_scripts(script_id) ON DELETE CASCADE,
            INDEX(script_id),
            INDEX(segment_type),
            INDEX(segment_order)
        )
    """,
    
    "streaming_sessions": """
        CREATE TABLE IF NOT EXISTS streaming_sessions (
            session_id TEXT PRIMARY KEY,
            script_id TEXT NOT NULL,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ended_at TIMESTAMP,
            
            -- Usage metrics
            segments_used INTEGER DEFAULT 0,
            segments_skipped INTEGER DEFAULT 0,
            total_viewers INTEGER DEFAULT 0,
            peak_viewers INTEGER DEFAULT 0,
            average_engagement REAL DEFAULT 0.0,
            
            -- Performance metrics
            generation_to_use_delay_minutes INTEGER DEFAULT 0,
            actual_vs_planned_duration_ratio REAL DEFAULT 1.0,
            viewer_retention_rate REAL DEFAULT 0.0,
            
            -- Engagement data
            questions_received INTEGER DEFAULT 0,
            questions_answered INTEGER DEFAULT 0,
            interaction_rate REAL DEFAULT 0.0,
            conversion_metrics TEXT, -- JSON object
            
            -- Context data
            stream_title TEXT,
            persona_used TEXT,
            platform_data TEXT, -- JSON object
            
            FOREIGN KEY(script_id) REFERENCES persisted_scripts(script_id) ON DELETE CASCADE,
            INDEX(script_id),
            INDEX(started_at),
            INDEX(ended_at)
        )
    """,
    
    "script_search_index": """
        CREATE VIRTUAL TABLE IF NOT EXISTS script_search_index USING fts5 (
            script_id,
            form_id,
            title,
            content,
            tags,
            product_name,
            brand,
            category,
            content=''
        )
    """,
    
    "analytics_cache": """
        CREATE TABLE IF NOT EXISTS analytics_cache (
            cache_key TEXT PRIMARY KEY,
            cache_data TEXT NOT NULL, -- JSON object
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            
            INDEX(expires_at)
        )
    """
}