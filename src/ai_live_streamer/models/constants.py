"""Constants and configuration mappings for AI Live Streamer

All magic numbers are extracted as named constants following coding standards.
"""

from typing import Dict, Any
from enum import Enum


# === Pace Configuration Mapping ===
# 5-tier viewer count to pace parameters mapping
PACE_MAPPING: Dict[str, Dict[str, Any]] = {
    "very_low": {  # 0-10 viewers
        "viewer_min": 0,
        "viewer_max": 10,
        "words_per_minute": 140,
        "pause_duration_ms": 800,
        "segment_length_words": 25,
        "qa_frequency_ratio": 0.3,
    },
    "low": {  # 11-50 viewers  
        "viewer_min": 11,
        "viewer_max": 50,
        "words_per_minute": 160,
        "pause_duration_ms": 600,
        "segment_length_words": 35,
        "qa_frequency_ratio": 0.4,
    },
    "medium": {  # 51-200 viewers
        "viewer_min": 51,
        "viewer_max": 200,
        "words_per_minute": 180,
        "pause_duration_ms": 400,
        "segment_length_words": 45,
        "qa_frequency_ratio": 0.5,
    },
    "high": {  # 201-1000 viewers
        "viewer_min": 201,
        "viewer_max": 1000,
        "words_per_minute": 200,
        "pause_duration_ms": 300,
        "segment_length_words": 55,
        "qa_frequency_ratio": 0.6,
    },
    "very_high": {  # 1000+ viewers
        "viewer_min": 1001,
        "viewer_max": 999999,
        "words_per_minute": 220,
        "pause_duration_ms": 200,
        "segment_length_words": 65,
        "qa_frequency_ratio": 0.7,
    },
}


# === Time Contextualization Configuration ===
# Asia/Tokyo timezone-aware time contexts
TIME_CONTEXTS: Dict[str, Dict[str, Any]] = {
    "early_morning": {  # 6:00-9:00
        "hour_min": 6,
        "hour_max": 8,
        "greeting_style": "温和唤醒",
        "energy_level": "平缓",
        "pace_modifier": 0.9,  # 10% slower
        "common_topics": ["早安问候", "新的一天", "简单介绍"],
    },
    "morning": {  # 9:00-12:00
        "hour_min": 9,
        "hour_max": 11,
        "greeting_style": "活力充沛",
        "energy_level": "中等",
        "pace_modifier": 1.0,  # Normal pace
        "common_topics": ["上午好", "工作日常", "产品介绍"],
    },
    "afternoon": {  # 12:00-18:00
        "hour_min": 12,
        "hour_max": 17,
        "greeting_style": "热情洋溢",
        "energy_level": "高",
        "pace_modifier": 1.1,  # 10% faster
        "common_topics": ["下午好", "详细讲解", "互动问答"],
    },
    "evening": {  # 18:00-22:00
        "hour_min": 18,
        "hour_max": 21,
        "greeting_style": "温暖亲切",
        "energy_level": "中等",
        "pace_modifier": 1.0,  # Normal pace
        "common_topics": ["晚上好", "休闲介绍", "总结回顾"],
    },
    "night": {  # 22:00-6:00
        "hour_min": 22,
        "hour_max": 5,
        "greeting_style": "轻柔舒缓",
        "energy_level": "低",
        "pace_modifier": 0.8,  # 20% slower
        "common_topics": ["晚安", "简短总结", "明日预告"],
    },
}


# === Audio Processing Constants ===
class AudioPriority(Enum):
    """Audio queue priority levels"""
    EMERGENCY = 1    # System errors, critical alerts
    URGENT = 2       # Live Q&A responses
    HIGH = 3         # Transitions, welcome messages
    NORMAL = 4       # Regular narration content


AUDIO_PRIORITIES = {
    "emergency": AudioPriority.EMERGENCY.value,
    "urgent": AudioPriority.URGENT.value, 
    "high": AudioPriority.HIGH.value,
    "normal": AudioPriority.NORMAL.value,
}


# === TTS Configuration Constants ===
DEFAULT_SAMPLE_RATE = 48_000
DEFAULT_CROSSFADE_MS = 150
MAX_CONCURRENT_TTS_REQUESTS = 3
TTS_CHUNK_SIZE_WORDS = 20


# === RAG System Constants ===
MAX_RETRIEVAL_CANDIDATES = 150
RRF_K_PARAMETER = 60
DEFAULT_EMBEDDING_DIMENSION = 1536
MAX_CONTEXT_LENGTH_CHARS = 4000


# === State Machine Constants ===
MAX_BREAK_DURATION_MS = 10_000
DEFAULT_SEGMENT_DURATION_SECONDS = 180
MAX_PENDING_QUESTIONS = 20
CONVERSATION_MEMORY_LIMIT = 50


# === Performance Thresholds ===
MAX_RETRIEVAL_LATENCY_MS = 350
MAX_RERANK_LATENCY_MS = 300
MAX_TTS_LATENCY_MS = 800
MAX_FIRST_TOKEN_LATENCY_MS = 800
MAX_QA_RESPONSE_TIME_SEC = 60.0

# === QA Audio Processing Constants ===
MAX_QA_AUDIO_RETRY_ATTEMPTS = 2
QA_AUDIO_RETRY_DELAY_SEC = 0.5
QA_PLAYER_AUTO_RECOVERY_ENABLED = True


# === Diversity Engine Constants ===
MAX_COSINE_SIMILARITY_THRESHOLD = 0.88
TOPIC_COOLDOWN_SECONDS = 300
RECENT_TOPICS_WINDOW_SIZE = 10
STYLE_VARIATION_TEMPERATURE = 0.7


# === Script Generation Constants ===
MIN_SENTENCE_LENGTH_WORDS = 8
MAX_SENTENCE_LENGTH_WORDS = 25
DEFAULT_INFORMATION_DENSITY = 0.7
SCRIPT_PREVIEW_DURATION_MINUTES = 10