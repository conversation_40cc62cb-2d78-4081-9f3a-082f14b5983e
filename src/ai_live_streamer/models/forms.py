"""Operational form data models

Defines the 6-section form structure for operational input including
basic info, product details, selling points, persona configuration,
and validation rules.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime, time
from enum import Enum
from pydantic import BaseModel, Field, validator
from decimal import Decimal

from .persona import VoiceStyle
from .constants import TIME_CONTEXTS


class StreamType(Enum):
    """Type of live stream"""
    PRODUCT_LAUNCH = "product_launch"
    DAILY_STREAM = "daily_stream"
    FLASH_SALE = "flash_sale"
    SPECIAL_EVENT = "special_event"
    Q_AND_A = "q_and_a"


class PriorityLevel(Enum):
    """Priority levels for selling points"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class ValidationStatus(Enum):
    """Form validation status"""
    VALID = "valid"
    INVALID = "invalid"
    PENDING = "pending"


# Section 1: Basic Information
class BasicInformation(BaseModel):
    """Basic stream information section"""
    
    stream_title: str = Field(
        min_length=10,
        max_length=100,
        description="Stream title displayed to viewers"
    )
    stream_type: StreamType = Field(description="Type of live stream")
    planned_duration_minutes: int = Field(
        ge=30,
        le=180,
        default=90,
        description="Planned stream duration in minutes"
    )
    start_time: Optional[datetime] = Field(
        default=None,
        description="Scheduled start time (optional)"
    )
    target_timezone: str = Field(
        default="Asia/Tokyo",
        description="Target timezone for time-aware content"
    )
    language: str = Field(
        default="zh-CN",
        description="Primary language for the stream"
    )
    
    # Stream settings
    enable_auto_interaction: bool = Field(
        default=True,
        description="Enable automatic viewer interaction"
    )
    enable_proactive_engagement: bool = Field(
        default=True,
        description="Enable proactive engagement during low activity"
    )
    max_concurrent_questions: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum concurrent questions to handle"
    )


# Section 2: Product Information
class ProductInformation(BaseModel):
    """Product details section"""
    
    # 支持两种数据源：产品选择器或手动输入
    selected_product_id: Optional[int] = Field(
        default=None,
        description="Selected product ID from product management system"
    )
    use_custom_price: bool = Field(
        default=False,
        description="Whether to use custom live streaming price"
    )
    custom_streaming_price: Optional[Decimal] = Field(
        default=None,
        description="Custom price for live streaming (if different from product price)"
    )
    
    # 传统手动输入字段（向后兼容）
    primary_sku: str = Field(
        min_length=3,
        max_length=50,
        description="Primary product SKU being featured"
    )
    product_name: str = Field(
        min_length=5,
        max_length=200,
        description="Full product name"
    )
    brand: str = Field(
        min_length=2,
        max_length=50,
        description="Product brand"
    )
    category: str = Field(
        min_length=3,
        max_length=50,
        description="Product category"
    )
    
    # Pricing information
    current_price: Decimal = Field(
        gt=0,
        description="Current selling price"
    )
    original_price: Optional[Decimal] = Field(
        default=None,
        description="Original price (if on sale)"
    )
    currency: str = Field(
        default="CNY",
        description="Currency code"
    )
    
    # Product details
    key_specifications: List[str] = Field(
        min_items=1,
        max_items=10,
        description="Key product specifications"
    )
    product_images: List[str] = Field(
        default_factory=list,
        description="Product image URLs"
    )
    product_video_url: Optional[str] = Field(
        default=None,
        description="Product demonstration video URL"
    )
    
    # Additional products
    related_skus: List[str] = Field(
        default_factory=list,
        max_items=5,
        description="Related product SKUs to mention"
    )
    
    @validator('current_price', 'original_price')
    def validate_positive_price(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Price must be positive')
        return v
    
    @validator('original_price')
    def validate_original_price(cls, v, values):
        if v is not None and 'current_price' in values:
            if v <= values['current_price']:
                raise ValueError('Original price must be higher than current price')
        return v


# Section 3: Selling Points Structure
class SellingPoint(BaseModel):
    """Individual selling point with supporting facts"""
    
    point_id: str = Field(description="Unique identifier for this selling point")
    title: str = Field(
        min_length=5,
        max_length=100,
        description="Selling point title"
    )
    description: str = Field(
        min_length=10,
        max_length=500,
        description="Detailed description of the selling point"
    )
    priority: PriorityLevel = Field(
        default=PriorityLevel.MEDIUM,
        description="Priority level for this selling point"
    )
    
    # Supporting evidence
    supporting_facts: List[str] = Field(
        min_items=1,
        max_items=5,
        description="Facts that support this selling point"
    )
    evidence_sources: List[str] = Field(
        default_factory=list,
        description="Sources of supporting evidence (URLs, documents)"
    )
    
    # Usage constraints
    max_mentions: int = Field(
        default=3,
        ge=1,
        le=10,
        description="Maximum times this point should be mentioned"
    )
    suitable_contexts: List[str] = Field(
        default_factory=lambda: ["product_demo", "feature_highlight"],
        description="Contexts where this selling point is most effective"
    )
    
    # Validation status
    compliance_checked: bool = Field(
        default=False,
        description="Whether content complies with regulations"
    )


class SellingPointsStructure(BaseModel):
    """Complete selling points section"""
    
    primary_value_proposition: str = Field(
        min_length=20,
        max_length=300,
        description="Main value proposition for the product"
    )
    
    selling_points: List[SellingPoint] = Field(
        min_items=3,
        max_items=8,
        description="List of key selling points"
    )
    
    competitive_advantages: List[str] = Field(
        min_items=1,
        max_items=5,
        description="Key competitive advantages"
    )
    
    urgency_factors: List[str] = Field(
        default_factory=list,
        description="Urgency and scarcity factors to create time pressure"
    )
    
    social_proof: List[str] = Field(
        default_factory=list,
        description="Social proof elements like reviews, testimonials, user counts"
    )
    
    objection_handling: Dict[str, str] = Field(
        default_factory=dict,
        description="Common objections and responses"
    )
    
    call_to_actions: List[str] = Field(
        min_items=2,
        max_items=5,
        description="Call-to-action phrases to use"
    )
    
    @validator('selling_points')
    def validate_unique_point_ids(cls, v):
        point_ids = [point.point_id for point in v]
        if len(point_ids) != len(set(point_ids)):
            raise ValueError('Selling point IDs must be unique')
        return v


# Section 4: Persona Configuration
class PersonaConfiguration(BaseModel):
    """Persona and voice configuration section"""
    
    selected_persona_id: str = Field(description="ID of selected persona")
    persona_name: str = Field(description="Human-readable persona name")
    voice_style: VoiceStyle = Field(
        default=VoiceStyle.WARM,
        description="Voice style characteristics"
    )
    
    # TTS settings
    tts_voice_name: str = Field(
        default="zh-CN-XiaoxiaoNeural",
        description="TTS voice identifier"
    )
    speaking_rate: float = Field(
        default=1.0,
        ge=0.5,
        le=2.0,
        description="Speaking rate multiplier"
    )
    volume_level: float = Field(
        default=0.8,
        ge=0.1,
        le=1.0,
        description="Audio volume level"
    )
    
    # Style preferences
    formality_level: str = Field(
        default="conversational",
        description="Formality level (formal/conversational/casual)"
    )
    energy_level: str = Field(
        default="medium",
        description="Energy level (low/medium/high)"
    )
    interaction_style: str = Field(
        default="friendly",
        description="Interaction style with viewers"
    )
    
    # Time-based adjustments
    time_sensitive_adjustments: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Adjustments for different time periods"
    )
    
    # Custom phrases
    custom_greetings: List[str] = Field(
        default_factory=list,
        max_items=5,
        description="Custom greeting phrases"
    )
    custom_transitions: List[str] = Field(
        default_factory=list,
        max_items=10,
        description="Custom transition phrases"
    )
    custom_closings: List[str] = Field(
        default_factory=list,
        max_items=5,
        description="Custom closing phrases"
    )


# Section 5: Advanced Settings
class AdvancedSettings(BaseModel):
    """Advanced configuration options"""
    
    # Pacing and timing
    words_per_minute_override: Optional[int] = Field(
        default=None,
        ge=100,
        le=300,
        description="Override automatic WPM calculation"
    )
    pause_duration_override: Optional[int] = Field(
        default=None,
        ge=100,
        le=2000,
        description="Override pause duration in milliseconds"
    )
    segment_length_override: Optional[int] = Field(
        default=None,
        ge=20,
        le=100,
        description="Override segment length in words"
    )
    
    # Content settings
    enable_fact_checking: bool = Field(
        default=True,
        description="Enable real-time fact checking"
    )
    enable_price_updates: bool = Field(
        default=True,
        description="Enable real-time price updates"
    )
    enable_stock_updates: bool = Field(
        default=False,
        description="Enable real-time stock level updates"
    )
    
    # Interaction settings
    question_response_timeout_seconds: int = Field(
        default=300,
        ge=60,
        le=900,
        description="Timeout for answering viewer questions"
    )
    max_question_queue_size: int = Field(
        default=20,
        ge=5,
        le=100,
        description="Maximum questions in queue"
    )
    
    # Content diversity
    enable_diversity_engine: bool = Field(
        default=True,
        description="Enable content diversity checking"
    )
    topic_cooldown_minutes: int = Field(
        default=5,
        ge=1,
        le=30,
        description="Cooldown period for repeated topics"
    )
    
    # Backup and fallback
    fallback_content_enabled: bool = Field(
        default=False,
        description="Enable fallback content (violates fail-fast principle)"
    )
    emergency_stop_phrases: List[str] = Field(
        default_factory=lambda: ["紧急停止", "立即停止", "emergency stop"],
        description="Phrases that trigger emergency stop"
    )


# Section 6: Review and Validation
class ValidationRule(BaseModel):
    """Individual validation rule"""
    
    rule_id: str = Field(description="Unique rule identifier")
    rule_name: str = Field(description="Human-readable rule name")
    description: str = Field(description="Rule description")
    is_mandatory: bool = Field(
        default=True,
        description="Whether this rule must pass for form submission"
    )
    validation_status: ValidationStatus = Field(
        default=ValidationStatus.PENDING,
        description="Current validation status"
    )
    error_message: Optional[str] = Field(
        default=None,
        description="Error message if validation fails"
    )


class ReviewAndValidation(BaseModel):
    """Review and validation section"""
    
    # Validation rules
    content_validation_rules: List[ValidationRule] = Field(
        default_factory=list,
        description="Content validation rules and their status"
    )
    compliance_validation_rules: List[ValidationRule] = Field(
        default_factory=list,
        description="Compliance validation rules and their status"
    )
    technical_validation_rules: List[ValidationRule] = Field(
        default_factory=list,
        description="Technical validation rules and their status"
    )
    
    # Review status
    content_review_completed: bool = Field(
        default=False,
        description="Whether content review is completed"
    )
    compliance_review_completed: bool = Field(
        default=False,
        description="Whether compliance review is completed"
    )
    technical_review_completed: bool = Field(
        default=False,
        description="Whether technical review is completed"
    )
    
    # Approval
    approved_by: Optional[str] = Field(
        default=None,
        description="User who approved the configuration"
    )
    approved_at: Optional[datetime] = Field(
        default=None,
        description="Approval timestamp"
    )
    approval_notes: Optional[str] = Field(
        default=None,
        description="Approval notes or comments"
    )
    
    # Submission readiness
    ready_for_submission: bool = Field(
        default=False,
        description="Whether form is ready for submission"
    )
    submission_blocked_reasons: List[str] = Field(
        default_factory=list,
        description="Reasons preventing submission"
    )


# Complete Form Model
class OperationalForm(BaseModel):
    """Complete operational input form with all 6 sections"""
    
    # Form metadata
    form_id: str = Field(description="Unique form identifier")
    form_version: str = Field(
        default="1.0",
        description="Form version for tracking changes"
    )
    created_by: str = Field(description="User who created the form")
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Form creation timestamp"
    )
    last_modified_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Last modification timestamp"
    )
    
    # Form sections
    basic_information: BasicInformation = Field(description="Section 1: Basic Information")
    product_information: ProductInformation = Field(description="Section 2: Product Information")
    selling_points_structure: SellingPointsStructure = Field(description="Section 3: Selling Points")
    persona_configuration: PersonaConfiguration = Field(description="Section 4: Persona Configuration")
    advanced_settings: AdvancedSettings = Field(description="Section 5: Advanced Settings")
    review_and_validation: ReviewAndValidation = Field(description="Section 6: Review and Validation")
    
    # Form status
    current_section: int = Field(
        default=1,
        ge=1,
        le=6,
        description="Currently active section (1-6)"
    )
    completion_percentage: float = Field(
        default=0.0,
        ge=0.0,
        le=100.0,
        description="Form completion percentage"
    )
    is_complete: bool = Field(
        default=False,
        description="Whether all sections are complete"
    )
    is_submitted: bool = Field(
        default=False,
        description="Whether form has been submitted"
    )
    
    # Processing status
    is_processed: bool = Field(
        default=False,
        description="Whether form has been processed"
    )
    processed_at: Optional[datetime] = Field(
        default=None,
        description="Processing completion timestamp"
    )
    processing_status: str = Field(
        default="pending",
        description="Current processing status"
    )
    processing_errors: List[str] = Field(
        default_factory=list,
        description="Processing error messages if any"
    )
    
    def calculate_completion_percentage(self) -> float:
        """Calculate form completion percentage based on filled fields with actual user data"""
        total_sections = 6
        completed_sections = 0
        template_indicators = [
            "待填写", "TBD", "待确认", "待分类", "待添加", 
            "请修改", "请输入", "请选择", "默认"
        ]
        
        # Enhanced validation for each section - must have user data, not template data
        basic_text = str(self.basic_information.dict())
        if (self.basic_information.stream_title and 
            self.basic_information.stream_type and
            not any(indicator in basic_text for indicator in template_indicators)):
            completed_sections += 1
            
        product_text = str(self.product_information.dict())
        if (self.product_information.primary_sku and 
            self.product_information.product_name and 
            self.product_information.current_price > 0 and
            not any(indicator in product_text for indicator in template_indicators)):
            completed_sections += 1
            
        selling_text = str(self.selling_points_structure.dict())
        if (self.selling_points_structure.primary_value_proposition and 
            len(self.selling_points_structure.selling_points) >= 3 and
            not any(indicator in selling_text for indicator in template_indicators)):
            completed_sections += 1
            
        persona_text = str(self.persona_configuration.dict())
        if (self.persona_configuration.selected_persona_id and 
            self.persona_configuration.persona_name and
            not any(indicator in persona_text for indicator in template_indicators)):
            completed_sections += 1
            
        # Advanced settings is optional, always considered complete
        completed_sections += 1
        
        if (self.review_and_validation.content_review_completed and
            self.review_and_validation.compliance_review_completed and
            self.review_and_validation.technical_review_completed):
            completed_sections += 1
        
        percentage = (completed_sections / total_sections) * 100.0
        self.completion_percentage = percentage
        self.is_complete = percentage >= 100.0
        
        return percentage
    
    def has_template_data(self) -> bool:
        """Check if form contains template/placeholder data
        
        Returns:
            True if form contains template data, False if contains user data
        """
        template_indicators = [
            "待填写", "TBD", "待确认", "待分类", "待添加", 
            "请修改", "请输入", "请选择", "默认"
        ]
        
        # Convert form to string representation for checking
        form_text = str(self.dict())
        
        return any(indicator in form_text for indicator in template_indicators)
    
    def get_template_data_fields(self) -> List[str]:
        """Get list of fields that still contain template data
        
        Returns:
            List of field names with template data
        """
        template_fields = []
        template_indicators = [
            "待填写", "TBD", "待确认", "待分类", "待添加", 
            "请修改", "请输入", "请选择", "默认"
        ]
        
        # Check each section for template data
        if any(indicator in str(self.basic_information.dict()) for indicator in template_indicators):
            template_fields.append("基础信息")
            
        if any(indicator in str(self.product_information.dict()) for indicator in template_indicators):
            template_fields.append("商品信息")
            
        if any(indicator in str(self.selling_points_structure.dict()) for indicator in template_indicators):
            template_fields.append("卖点结构")
            
        if any(indicator in str(self.persona_configuration.dict()) for indicator in template_indicators):
            template_fields.append("人设配置")
            
        return template_fields
    
    def calculate_data_quality_score(self) -> float:
        """Calculate data quality score (0-100) based on user vs template data
        
        Returns:
            Score from 0 (all template) to 100 (all user data)
        """
        if self.has_template_data():
            template_fields = self.get_template_data_fields()
            total_sections = 4  # Basic, Product, Selling Points, Persona
            user_sections = total_sections - len(template_fields)
            return (user_sections / total_sections) * 100.0
        return 100.0
    
    def validate_for_submission(self) -> List[str]:
        """Validate form for submission readiness
        
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check mandatory fields
        if not self.basic_information.stream_title.strip():
            errors.append("Stream title is required")
            
        if not self.product_information.primary_sku.strip():
            errors.append("Primary SKU is required")
            
        if len(self.selling_points_structure.selling_points) < 3:
            errors.append("At least 3 selling points are required")
            
        # Check selling point validation
        for point in self.selling_points_structure.selling_points:
            if not point.compliance_checked:
                errors.append(f"Selling point '{point.title}' compliance not checked")
        
        # Check review completion
        if not self.review_and_validation.content_review_completed:
            errors.append("Content review not completed")
            
        if not self.review_and_validation.compliance_review_completed:
            errors.append("Compliance review not completed")
            
        if not self.review_and_validation.technical_review_completed:
            errors.append("Technical review not completed")
        
        # Check validation rules
        for rule in (self.review_and_validation.content_validation_rules + 
                    self.review_and_validation.compliance_validation_rules + 
                    self.review_and_validation.technical_validation_rules):
            if rule.is_mandatory and rule.validation_status != ValidationStatus.VALID:
                errors.append(f"Mandatory validation rule failed: {rule.rule_name}")
        
        self.review_and_validation.submission_blocked_reasons = errors
        self.review_and_validation.ready_for_submission = len(errors) == 0
        
        return errors
    
    def update_modification_time(self) -> None:
        """Update last modification timestamp"""
        self.last_modified_at = datetime.utcnow()


# Session Management
class FormSession(BaseModel):
    """Form editing session management"""
    
    session_id: str = Field(description="Unique session identifier")
    form_id: str = Field(description="Associated form ID")
    user_id: str = Field(description="User ID for this session")
    
    # Session state
    started_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Session start time"
    )
    last_activity_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Last activity timestamp"
    )
    expires_at: datetime = Field(description="Session expiration time")
    
    # Session data
    current_section: int = Field(
        default=1,
        ge=1,
        le=6,
        description="Currently active section"
    )
    unsaved_changes: bool = Field(
        default=False,
        description="Whether there are unsaved changes"
    )
    auto_save_enabled: bool = Field(
        default=True,
        description="Whether auto-save is enabled"
    )
    
    def is_expired(self) -> bool:
        """Check if session has expired"""
        return datetime.utcnow() > self.expires_at
    
    def extend_session(self, minutes: int = 30) -> None:
        """Extend session expiration"""
        from datetime import timedelta
        self.expires_at = datetime.utcnow() + timedelta(minutes=minutes)
        self.last_activity_at = datetime.utcnow()
    
    def mark_activity(self) -> None:
        """Mark user activity to prevent session timeout"""
        self.last_activity_at = datetime.utcnow()