"""客户端状态相关数据模型

定义客户端播放状态、缓冲健康度等核心数据结构。

Author: <PERSON> Code
Date: 2025-08-08
"""

import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, List, Tuple


class ClientBufferHealth(Enum):
    """客户端缓冲健康度枚举"""
    HEALTHY = "healthy"         # 缓冲充足 (>2秒)
    AT_RISK = "at_risk"        # 缓冲不足 (0.5-2秒)
    DEPLETED = "depleted"      # 缓冲耗尽 (<0.5秒)
    
    def __str__(self) -> str:
        return self.value
        
    @property
    def priority(self) -> int:
        """健康度优先级，数字越小优先级越高"""
        priorities = {
            ClientBufferHealth.DEPLETED: 1,
            ClientBufferHealth.AT_RISK: 2,
            ClientBufferHealth.HEALTHY: 3
        }
        return priorities[self]
        
    @property
    def display_name(self) -> str:
        """显示名称"""
        names = {
            ClientBufferHealth.HEALTHY: "健康",
            ClientBufferHealth.AT_RISK: "风险",
            ClientBufferHealth.DEPLETED: "耗尽"
        }
        return names[self]
        
    @property
    def color(self) -> str:
        """状态颜色（用于UI显示）"""
        colors = {
            ClientBufferHealth.HEALTHY: "green",
            ClientBufferHealth.AT_RISK: "yellow", 
            ClientBufferHealth.DEPLETED: "red"
        }
        return colors[self]


class ConnectionStatus(Enum):
    """连接状态枚举"""
    CONNECTED = "connected"         # 已连接
    RECONNECTING = "reconnecting"   # 重连中
    DISCONNECTED = "disconnected"   # 已断开
    ERROR = "error"                # 错误状态
    
    @property
    def is_active(self) -> bool:
        """是否为活跃状态"""
        return self in [ConnectionStatus.CONNECTED, ConnectionStatus.RECONNECTING]


@dataclass
class NetworkMetrics:
    """网络指标"""
    latency_ms: float = 0.0               # 网络延迟（毫秒）
    jitter_ms: float = 0.0                # 延迟抖动（毫秒）
    packet_loss_rate: float = 0.0         # 丢包率（0-1）
    bandwidth_kbps: float = 0.0           # 带宽（千比特/秒）
    last_ping_time: Optional[datetime] = None  # 最后ping时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "latency_ms": round(self.latency_ms, 2),
            "jitter_ms": round(self.jitter_ms, 2),
            "packet_loss_rate": round(self.packet_loss_rate, 4),
            "bandwidth_kbps": round(self.bandwidth_kbps, 2),
            "last_ping_time": self.last_ping_time.isoformat() if self.last_ping_time else None,
            "quality_score": self.calculate_quality_score()
        }
        
    def calculate_quality_score(self) -> float:
        """计算网络质量评分（0-1，1为最佳）"""
        # 基于延迟、抖动和丢包率计算综合评分
        latency_score = max(0, 1 - (self.latency_ms / 1000))  # 1000ms为最差
        jitter_score = max(0, 1 - (self.jitter_ms / 100))     # 100ms为最差
        loss_score = max(0, 1 - (self.packet_loss_rate * 10)) # 10%为最差
        
        return (latency_score + jitter_score + loss_score) / 3


@dataclass
class ClientPerformanceStats:
    """客户端性能统计"""
    total_requests: int = 0               # 总请求数
    successful_requests: int = 0          # 成功请求数
    failed_requests: int = 0              # 失败请求数
    average_response_time_ms: float = 0.0  # 平均响应时间
    cache_hit_rate: float = 0.0           # 缓存命中率
    total_bytes_received: int = 0         # 总接收字节数
    playback_interruptions: int = 0       # 播放中断次数
    buffer_underruns: int = 0             # 缓冲区下溢次数
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": round(self.success_rate, 4),
            "average_response_time_ms": round(self.average_response_time_ms, 2),
            "cache_hit_rate": round(self.cache_hit_rate, 4),
            "total_bytes_received": self.total_bytes_received,
            "total_mb_received": round(self.total_bytes_received / 1024 / 1024, 2),
            "playback_interruptions": self.playback_interruptions,
            "buffer_underruns": self.buffer_underruns
        }


@dataclass
class ClientState:
    """客户端播放状态"""
    client_id: str                              # 客户端标识符
    current_playing_index: int                  # 当前播放的句子索引
    buffered_until_index: int                   # 已缓冲到的索引
    remaining_buffer_ms: float                  # 剩余缓冲时间（毫秒）
    buffer_health: ClientBufferHealth           # 缓冲健康度
    connection_status: ConnectionStatus = ConnectionStatus.CONNECTED
    last_update_time: datetime = field(default_factory=datetime.utcnow)
    network_metrics: NetworkMetrics = field(default_factory=NetworkMetrics)
    performance_stats: ClientPerformanceStats = field(default_factory=ClientPerformanceStats)
    
    # 会话信息
    session_id: Optional[str] = None            # 所属会话ID
    session_start_time: datetime = field(default_factory=datetime.utcnow)
    reconnect_count: int = 0                    # 重连次数
    total_played_items: int = 0                 # 总播放项目数
    
    # 用户信息
    user_agent: Optional[str] = None            # 用户代理
    client_version: Optional[str] = None        # 客户端版本
    platform: Optional[str] = None             # 平台信息
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.client_id:
            self.client_id = str(uuid.uuid4())
            
    @property
    def network_latency_ms(self) -> float:
        """网络延迟（毫秒）- 向后兼容属性"""
        return self.network_metrics.latency_ms
        
    @property
    def is_active(self) -> bool:
        """是否为活跃客户端"""
        if not self.connection_status.is_active:
            return False
            
        # 检查最后更新时间（超过5分钟认为不活跃）
        time_since_update = datetime.utcnow() - self.last_update_time
        return time_since_update.total_seconds() < 300
        
    @property
    def session_duration_minutes(self) -> float:
        """会话持续时间（分钟）"""
        duration = datetime.utcnow() - self.session_start_time
        return duration.total_seconds() / 60
        
    @property
    def buffer_utilization(self) -> float:
        """缓冲区利用率（0-1）"""
        # 基于当前缓冲时间和目标缓冲时间计算
        target_buffer_ms = 3000  # 目标3秒缓冲
        return min(1.0, self.remaining_buffer_ms / target_buffer_ms)
        
    def get_insertion_delay(self, config: 'StreamingConfig') -> int:
        """
        根据当前状态获取QA插入延迟句数
        
        Args:
            config: 配置管理器
            
        Returns:
            延迟句数
        """
        base_delay = config.get_health_based_delay(self.buffer_health.value)
        
        # 网络延迟补偿
        network_compensation = int(self.network_metrics.latency_ms / 100)  # 每100ms补偿1句
        
        return base_delay + network_compensation
        
    def calculate_safe_insertion_point(self, config: 'StreamingConfig') -> int:
        """计算安全的QA插入点"""
        delay = self.get_insertion_delay(config)
        return self.current_playing_index + delay
        
    def update_from_request(self, request_data: Dict[str, Any]) -> None:
        """从客户端请求数据更新状态"""
        # 更新基本播放信息
        self.current_playing_index = request_data.get("current_index", self.current_playing_index)
        self.buffered_until_index = request_data.get("buffered_until", self.current_playing_index)
        self.remaining_buffer_ms = request_data.get("remaining_buffer_ms", 0)
        
        # 更新网络指标
        if "latency_ms" in request_data:
            self.network_metrics.latency_ms = request_data["latency_ms"]
        if "jitter_ms" in request_data:
            self.network_metrics.jitter_ms = request_data["jitter_ms"]
            
        # 更新性能统计
        self.performance_stats.total_requests += 1
        if request_data.get("request_successful", True):
            self.performance_stats.successful_requests += 1
        else:
            self.performance_stats.failed_requests += 1
            
        # 更新客户端信息
        if "user_agent" in request_data:
            self.user_agent = request_data["user_agent"]
        if "client_version" in request_data:
            self.client_version = request_data["client_version"]
        if "platform" in request_data:
            self.platform = request_data["platform"]
            
        self.last_update_time = datetime.utcnow()
        
    def update_buffer_health(self, healthy_threshold: int = 2000, 
                           at_risk_threshold: int = 500) -> bool:
        """
        更新缓冲健康度
        
        Args:
            healthy_threshold: 健康阈值（毫秒）
            at_risk_threshold: 风险阈值（毫秒）
            
        Returns:
            健康度是否发生变化
        """
        old_health = self.buffer_health
        
        if self.remaining_buffer_ms >= healthy_threshold:
            self.buffer_health = ClientBufferHealth.HEALTHY
        elif self.remaining_buffer_ms >= at_risk_threshold:
            self.buffer_health = ClientBufferHealth.AT_RISK
        else:
            self.buffer_health = ClientBufferHealth.DEPLETED
            
        return old_health != self.buffer_health
        
    def record_buffer_underrun(self) -> None:
        """记录缓冲区下溢"""
        self.performance_stats.buffer_underruns += 1
        self.performance_stats.playback_interruptions += 1
        
    def record_reconnect(self) -> None:
        """记录重连"""
        self.reconnect_count += 1
        self.connection_status = ConnectionStatus.RECONNECTING
        
    def mark_connected(self) -> None:
        """标记为已连接"""
        self.connection_status = ConnectionStatus.CONNECTED
        self.last_update_time = datetime.utcnow()
        
    def mark_disconnected(self) -> None:
        """标记为已断开"""
        self.connection_status = ConnectionStatus.DISCONNECTED
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "client_id": self.client_id,
            "current_playing_index": self.current_playing_index,
            "buffered_until_index": self.buffered_until_index,
            "remaining_buffer_ms": round(self.remaining_buffer_ms, 2),
            "buffer_health": self.buffer_health.value,
            "buffer_health_display": self.buffer_health.display_name,
            "buffer_utilization": round(self.buffer_utilization, 2),
            "connection_status": self.connection_status.value,
            "is_active": self.is_active,
            "last_update_time": self.last_update_time.isoformat(),
            "session_info": {
                "session_id": self.session_id,
                "session_start_time": self.session_start_time.isoformat(),
                "session_duration_minutes": round(self.session_duration_minutes, 1),
                "reconnect_count": self.reconnect_count,
                "total_played_items": self.total_played_items
            },
            "network_metrics": self.network_metrics.to_dict(),
            "performance_stats": self.performance_stats.to_dict(),
            "client_info": {
                "user_agent": self.user_agent,
                "client_version": self.client_version,
                "platform": self.platform
            }
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClientState':
        """从字典创建实例"""
        # 解析网络指标
        network_data = data.get("network_metrics", {})
        network_metrics = NetworkMetrics(
            latency_ms=network_data.get("latency_ms", 0.0),
            jitter_ms=network_data.get("jitter_ms", 0.0),
            packet_loss_rate=network_data.get("packet_loss_rate", 0.0),
            bandwidth_kbps=network_data.get("bandwidth_kbps", 0.0)
        )
        
        # 解析性能统计
        perf_data = data.get("performance_stats", {})
        performance_stats = ClientPerformanceStats(
            total_requests=perf_data.get("total_requests", 0),
            successful_requests=perf_data.get("successful_requests", 0),
            failed_requests=perf_data.get("failed_requests", 0),
            average_response_time_ms=perf_data.get("average_response_time_ms", 0.0),
            cache_hit_rate=perf_data.get("cache_hit_rate", 0.0),
            total_bytes_received=perf_data.get("total_bytes_received", 0),
            playback_interruptions=perf_data.get("playback_interruptions", 0),
            buffer_underruns=perf_data.get("buffer_underruns", 0)
        )
        
        # 解析会话信息
        session_info = data.get("session_info", {})
        client_info = data.get("client_info", {})
        
        return cls(
            client_id=data["client_id"],
            current_playing_index=data["current_playing_index"],
            buffered_until_index=data["buffered_until_index"],
            remaining_buffer_ms=data["remaining_buffer_ms"],
            buffer_health=ClientBufferHealth(data["buffer_health"]),
            connection_status=ConnectionStatus(data.get("connection_status", "connected")),
            last_update_time=datetime.fromisoformat(data["last_update_time"]),
            network_metrics=network_metrics,
            performance_stats=performance_stats,
            session_id=session_info.get("session_id"),
            session_start_time=datetime.fromisoformat(
                session_info.get("session_start_time", datetime.utcnow().isoformat())
            ),
            reconnect_count=session_info.get("reconnect_count", 0),
            total_played_items=session_info.get("total_played_items", 0),
            user_agent=client_info.get("user_agent"),
            client_version=client_info.get("client_version"),
            platform=client_info.get("platform")
        )


@dataclass
class ClientStateHistory:
    """客户端状态历史记录"""
    client_id: str
    health_history: List[Tuple[datetime, ClientBufferHealth]] = field(default_factory=list)
    connection_history: List[Tuple[datetime, ConnectionStatus]] = field(default_factory=list)
    max_entries: int = 100  # 最大记录条数
    
    def add_health_record(self, health: ClientBufferHealth) -> None:
        """添加健康度记录"""
        self.health_history.append((datetime.utcnow(), health))
        if len(self.health_history) > self.max_entries:
            self.health_history = self.health_history[-self.max_entries//2:]
            
    def add_connection_record(self, status: ConnectionStatus) -> None:
        """添加连接状态记录"""
        self.connection_history.append((datetime.utcnow(), status))
        if len(self.connection_history) > self.max_entries:
            self.connection_history = self.connection_history[-self.max_entries//2:]
            
    def get_health_distribution(self, hours: int = 1) -> Dict[str, int]:
        """获取指定时间段内的健康度分布"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_records = [
            health for timestamp, health in self.health_history
            if timestamp >= cutoff_time
        ]
        
        distribution = {health.value: 0 for health in ClientBufferHealth}
        for health in recent_records:
            distribution[health.value] += 1
            
        return distribution
        
    def get_connection_uptime(self, hours: int = 1) -> float:
        """获取指定时间段内的连接正常时间比例"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_records = [
            (timestamp, status) for timestamp, status in self.connection_history
            if timestamp >= cutoff_time
        ]
        
        if not recent_records:
            return 1.0
            
        total_time = 0
        connected_time = 0
        
        for i, (timestamp, status) in enumerate(recent_records[:-1]):
            next_timestamp = recent_records[i + 1][0]
            duration = (next_timestamp - timestamp).total_seconds()
            total_time += duration
            
            if status == ConnectionStatus.CONNECTED:
                connected_time += duration
                
        return connected_time / total_time if total_time > 0 else 1.0


@dataclass
class AggregatedClientStats:
    """聚合客户端统计信息"""
    active_clients: int                         # 活跃客户端数
    total_clients: int                          # 总客户端数
    health_distribution: Dict[str, int]         # 健康度分布
    average_latency_ms: float                   # 平均延迟
    playing_index_range: Tuple[int, int]        # 播放索引范围 (min, max)
    total_requests: int                         # 总请求数
    total_bytes_served: int                     # 总服务字节数
    average_session_duration_minutes: float    # 平均会话时长
    
    @property
    def healthy_percentage(self) -> float:
        """健康客户端百分比"""
        if self.active_clients == 0:
            return 100.0
        return self.health_distribution.get("healthy", 0) / self.active_clients * 100
        
    @property
    def at_risk_percentage(self) -> float:
        """风险客户端百分比"""
        if self.active_clients == 0:
            return 0.0
        return self.health_distribution.get("at_risk", 0) / self.active_clients * 100
        
    @property
    def depleted_percentage(self) -> float:
        """耗尽客户端百分比"""
        if self.active_clients == 0:
            return 0.0
        return self.health_distribution.get("depleted", 0) / self.active_clients * 100
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "active_clients": self.active_clients,
            "total_clients": self.total_clients,
            "health_distribution": self.health_distribution,
            "health_percentages": {
                "healthy": round(self.healthy_percentage, 1),
                "at_risk": round(self.at_risk_percentage, 1),
                "depleted": round(self.depleted_percentage, 1)
            },
            "average_latency_ms": round(self.average_latency_ms, 2),
            "playing_index_range": {
                "min": self.playing_index_range[0],
                "max": self.playing_index_range[1],
                "spread": self.playing_index_range[1] - self.playing_index_range[0]
            },
            "total_requests": self.total_requests,
            "total_bytes_served": self.total_bytes_served,
            "total_mb_served": round(self.total_bytes_served / 1024 / 1024, 2),
            "average_session_duration_minutes": round(self.average_session_duration_minutes, 1)
        }