#!/usr/bin/env python3
"""
播放器状态模型 - 形式化状态机定义

定义播放器的显式状态机，取代隐式状态跟踪，提升可调试性和可靠性。
基于专业架构反馈设计的核心状态模型。

Author: Claude Code  
Date: 2025-08-07 (状态机优化版)
"""

from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel
import time


class PlayerState(Enum):
    """播放器主状态枚举 - 表示播放器当前的主要活动"""
    
    IDLE = "idle"                           # 空闲状态，未播放任何内容
    GENERATING_AUDIO = "generating_audio"   # TTS合成中，生成音频数据
    STREAMING_AUDIO = "streaming_audio"     # 音频流传输中，发送到客户端
    WAITING_PLAYBACK = "waiting_playback"   # 等待播放完成确认
    PAUSED = "paused"                      # 暂停状态
    STOPPED = "stopped"                    # 已停止，清理状态


class PlaybackPhase(Enum):
    """播放阶段枚举 - 表示用户感知的播放阶段"""
    
    BETWEEN_SENTENCES = "between_sentences"  # 句子间隙，安全中断点
    PLAYING_AUDIO = "playing_audio"          # 正在播放音频
    WAITING_COMPLETION = "waiting_completion" # 等待播放完成确认


class StateTransition(BaseModel):
    """状态转换记录"""
    
    from_state: PlayerState
    to_state: PlayerState  
    timestamp_ms: int                        # 单调时钟时间戳（毫秒）
    trigger: str                            # 触发原因
    context: Optional[Dict[str, Any]] = None # 额外上下文信息


class PlayerStateManager:
    """播放器状态管理器 - 统一管理状态转换和查询"""
    
    def __init__(self):
        self._current_state: PlayerState = PlayerState.IDLE
        self._current_phase: PlaybackPhase = PlaybackPhase.BETWEEN_SENTENCES
        self._last_transition_time_ms: int = self._get_monotonic_time_ms()
        self._state_history: list[StateTransition] = []
        self._max_history_size = 100  # 保留最近100次状态转换
        
        # 播放进度状态
        self._current_sentence_index: int = 0
        self._last_completed_index: int = -1
        self._audio_start_time_ms: Optional[int] = None
        self._expected_completion_time_ms: Optional[int] = None
    
    @staticmethod
    def _get_monotonic_time_ms() -> int:
        """获取单调时钟时间戳（毫秒）- 抗NTP干扰"""
        return int(time.monotonic() * 1000)
    
    def set_state(self, new_state: PlayerState, trigger: str = "unknown", context: Optional[Dict[str, Any]] = None) -> None:
        """设置播放器状态 - 记录状态转换历史"""
        if new_state == self._current_state:
            return  # 状态未变化，跳过
        
        old_state = self._current_state
        transition_time = self._get_monotonic_time_ms()
        
        # 记录状态转换
        transition = StateTransition(
            from_state=old_state,
            to_state=new_state,
            timestamp_ms=transition_time,
            trigger=trigger,
            context=context
        )
        
        # 更新状态
        self._current_state = new_state
        self._last_transition_time_ms = transition_time
        
        # 记录历史（保持最近N条记录）
        self._state_history.append(transition)
        if len(self._state_history) > self._max_history_size:
            self._state_history.pop(0)
        
        # 自动更新播放阶段
        self._update_playback_phase(new_state, trigger)
        
        from loguru import logger
        logger.info(f"🔄 状态转换: {old_state.value} → {new_state.value} (触发: {trigger})")
    
    def _update_playback_phase(self, new_state: PlayerState, trigger: str) -> None:
        """基于主状态自动更新播放阶段"""
        if new_state == PlayerState.IDLE:
            self._current_phase = PlaybackPhase.BETWEEN_SENTENCES
        elif new_state in [PlayerState.GENERATING_AUDIO, PlayerState.STREAMING_AUDIO]:
            self._current_phase = PlaybackPhase.PLAYING_AUDIO
        elif new_state == PlayerState.WAITING_PLAYBACK:
            self._current_phase = PlaybackPhase.WAITING_COMPLETION
        elif new_state == PlayerState.PAUSED:
            # 暂停时保持当前阶段
            pass
        elif new_state == PlayerState.STOPPED:
            self._current_phase = PlaybackPhase.BETWEEN_SENTENCES
    
    def get_current_state(self) -> PlayerState:
        """获取当前状态"""
        return self._current_state
    
    def get_current_phase(self) -> PlaybackPhase:
        """获取当前播放阶段"""
        return self._current_phase
    
    def is_at_safe_interruption_point(self) -> bool:
        """检查是否处于安全中断点"""
        return self._current_phase == PlaybackPhase.BETWEEN_SENTENCES
    
    def is_playing_audio(self) -> bool:
        """检查是否正在播放音频（用户感知）"""
        return self._current_phase in [PlaybackPhase.PLAYING_AUDIO, PlaybackPhase.WAITING_COMPLETION]
    
    def can_accept_qa_insertion(self) -> bool:
        """检查是否可以接受QA插入"""
        # 在空闲或句子间隙时可以立即插入，其他状态需要排队
        return self._current_state in [PlayerState.IDLE] or self._current_phase == PlaybackPhase.BETWEEN_SENTENCES
    
    def set_audio_timing(self, start_time_ms: int, expected_completion_ms: int) -> None:
        """设置音频播放时间信息"""
        self._audio_start_time_ms = start_time_ms
        self._expected_completion_time_ms = expected_completion_ms
    
    def clear_audio_timing(self) -> None:
        """清除音频播放时间信息"""
        self._audio_start_time_ms = None
        self._expected_completion_time_ms = None
    
    def update_sentence_progress(self, current_index: int, last_completed_index: int = None) -> None:
        """更新句子播放进度"""
        self._current_sentence_index = current_index
        if last_completed_index is not None:
            self._last_completed_index = last_completed_index
    
    def get_user_perceived_sentence_index(self) -> int:
        """获取用户感知的当前句子索引"""
        if self.is_playing_audio():
            # 如果正在播放，用户听到的是当前正在处理的句子
            return self._current_sentence_index
        else:
            # 如果在句子间隙，用户听到的是最后完成的句子的下一句
            return self._last_completed_index + 1 if self._last_completed_index >= 0 else 0
    
    def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细状态信息"""
        current_time_ms = self._get_monotonic_time_ms()
        
        # 计算状态持续时间
        state_duration_ms = current_time_ms - self._last_transition_time_ms
        
        # 计算音频播放进度（如果有的话）
        audio_progress_info = {}
        if self._audio_start_time_ms and self._expected_completion_time_ms:
            elapsed_audio_ms = current_time_ms - self._audio_start_time_ms
            total_audio_duration_ms = self._expected_completion_time_ms - self._audio_start_time_ms
            audio_progress_percent = min(100.0, (elapsed_audio_ms / total_audio_duration_ms) * 100) if total_audio_duration_ms > 0 else 0.0
            
            audio_progress_info = {
                "audio_elapsed_ms": elapsed_audio_ms,
                "audio_total_ms": total_audio_duration_ms,
                "audio_progress_percent": round(audio_progress_percent, 1),
                "audio_remaining_ms": max(0, self._expected_completion_time_ms - current_time_ms)
            }
        
        return {
            # 基础状态
            "player_state": self._current_state.value,
            "playback_phase": self._current_phase.value,
            "state_duration_ms": state_duration_ms,
            
            # 句子进度
            "current_sentence_index": self._current_sentence_index,
            "last_completed_index": self._last_completed_index,
            "user_perceived_index": self.get_user_perceived_sentence_index(),
            
            # 音频播放进度
            **audio_progress_info,
            
            # 状态判断
            "at_safe_point": self.is_at_safe_interruption_point(),
            "is_playing_audio": self.is_playing_audio(),
            "can_accept_qa": self.can_accept_qa_insertion(),
            
            # 时间信息
            "current_time_ms": current_time_ms,
            "last_transition_time_ms": self._last_transition_time_ms
        }
    
    def get_recent_transitions(self, count: int = 10) -> list[StateTransition]:
        """获取最近的状态转换记录"""
        return self._state_history[-count:] if count > 0 else self._state_history
    
    def reset(self) -> None:
        """重置状态管理器"""
        self._current_state = PlayerState.IDLE
        self._current_phase = PlaybackPhase.BETWEEN_SENTENCES
        self._last_transition_time_ms = self._get_monotonic_time_ms()
        self._state_history.clear()
        
        self._current_sentence_index = 0
        self._last_completed_index = -1
        self.clear_audio_timing()


# 工具函数
def get_monotonic_time_ms() -> int:
    """获取单调时钟时间戳（毫秒）- 全局工具函数"""
    return int(time.monotonic() * 1000)


# 导出主要类型
__all__ = [
    "PlayerState",
    "PlaybackPhase", 
    "StateTransition",
    "PlayerStateManager",
    "get_monotonic_time_ms"
]