"""Persona configuration and template models

Supports both script-based Style Corpus and template-based persona definitions
with consistent voice and style management across the live stream.
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from enum import Enum


class PersonaType(Enum):
    """Type of persona implementation"""
    SCRIPT_BASED = "script_based"  # Uses Style Corpus few-shot examples
    TEMPLATE_BASED = "template_based"  # Uses prompt templates


class VoiceStyle(Enum):
    """Voice characteristics for TTS"""
    GENTLE = "gentle"           # 温和
    ENERGETIC = "energetic"     # 活力充沛  
    WARM = "warm"              # 温暖亲切
    PROFESSIONAL = "professional"  # 专业
    CASUAL = "casual"          # 随性
    SOOTHING = "soothing"      # 轻柔舒缓


class PersonaLexicon(BaseModel):
    """Persona lexicon configuration"""
    preferred: List[str] = Field(default_factory=list, description="Preferred words and phrases")
    banned: List[str] = Field(default_factory=list, description="Words and phrases to avoid")


class PersonaRhetoric(BaseModel):
    """Persona rhetoric style configuration"""
    cta_style: str = Field(default="friendly", description="Call-to-action style")


class PersonaPace(BaseModel):
    """Persona pacing configuration"""
    sentence_len: tuple = Field(default=(10, 25), description="Sentence length range")


class PersonaTemplate(BaseModel):
    """Template-based persona definition with prompt templates"""
    
    name: str = Field(description="Persona template name")
    description: str = Field(description="Human-readable persona description")
    
    # Prompt templates for different contexts
    greeting_template: str = Field(description="Template for greeting messages")
    narration_template: str = Field(description="Template for main narration")
    qa_template: str = Field(description="Template for Q&A responses")
    transition_template: str = Field(description="Template for transitions")
    closing_template: str = Field(description="Template for closing remarks")
    
    # Style parameters
    tone_keywords: List[str] = Field(
        default_factory=list,
        description="Keywords describing the tone (friendly, professional, etc.)"
    )
    speaking_patterns: List[str] = Field(
        default_factory=list,
        description="Common speaking patterns or phrases"
    )
    
    # Constraints
    max_sentence_length: int = Field(
        default=25,
        ge=10,
        le=50,
        description="Maximum words per sentence"
    )
    preferred_vocabulary_level: str = Field(
        default="conversational",
        description="Vocabulary complexity level"
    )


class StyleCorpusConfig(BaseModel):
    """Configuration for script-based Style Corpus persona"""
    
    corpus_index_name: str = Field(description="Elasticsearch index name for Style Corpus")
    few_shot_examples_count: int = Field(
        default=3,
        ge=1,
        le=10,
        description="Number of few-shot examples to retrieve"
    )
    similarity_threshold: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="Minimum similarity for style matching"
    )
    style_categories: List[str] = Field(
        default_factory=lambda: ["opening", "selling_point", "objection_handling", "closing", "cta"],
        description="Available style categories in corpus"
    )


class PersonaConfig(BaseModel):
    """Complete persona configuration supporting both implementation types"""
    
    # Basic identification
    persona_id: str = Field(description="Unique persona identifier")
    name: str = Field(description="Human-readable persona name")
    description: str = Field(description="Detailed persona description")
    
    # Implementation type and configuration
    persona_type: PersonaType = Field(description="Type of persona implementation")
    template_config: Optional[PersonaTemplate] = Field(
        default=None,
        description="Template configuration (if template-based)"
    )
    style_corpus_config: Optional[StyleCorpusConfig] = Field(
        default=None,
        description="Style Corpus configuration (if script-based)"
    )
    
    # QA Handler expected attributes (auto-derived from other settings)
    tone: str = Field(default="friendly", description="Persona tone style (derived from interaction_style)")
    lexicon: PersonaLexicon = Field(default_factory=PersonaLexicon, description="Lexicon preferences")
    rhetoric: PersonaRhetoric = Field(default_factory=PersonaRhetoric, description="Rhetoric style (derived from energy_level)")
    pace: PersonaPace = Field(default_factory=PersonaPace, description="Pacing configuration (derived from speaking_rate)")
    
    # Voice and audio settings
    voice_style: VoiceStyle = Field(default=VoiceStyle.WARM)
    tts_voice_name: str = Field(
        default="zh-CN-XiaoxiaoNeural",
        description="TTS voice identifier"
    )
    speaking_rate: float = Field(
        default=1.0,
        ge=0.5,
        le=2.0,
        description="TTS speaking rate multiplier"
    )
    
    # Time-aware adjustments
    time_sensitive_adjustments: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Adjustments based on time context (morning/evening/etc.)"
    )
    
    # Consistency parameters
    consistency_temperature: float = Field(
        default=0.3,
        ge=0.0,
        le=1.0,
        description="LLM temperature for consistent voice"
    )
    max_style_deviation: float = Field(
        default=0.2,
        ge=0.0,
        le=1.0,
        description="Maximum allowed style variation"
    )
    
    # Transition handling
    transition_smoothing_enabled: bool = Field(
        default=True,
        description="Enable smooth persona transitions"
    )
    max_transition_sentences: int = Field(
        default=2,
        ge=1,
        le=5,
        description="Maximum sentences for persona transition"
    )
    
    def get_time_adjusted_config(self, time_context: str) -> Dict[str, Any]:
        """Get persona configuration adjusted for current time context
        
        Args:
            time_context: Current time context (morning/afternoon/evening)
            
        Returns:
            Adjusted configuration parameters
        """
        base_config = {
            "speaking_rate": self.speaking_rate,
            "consistency_temperature": self.consistency_temperature,
            "voice_style": self.voice_style.value,
        }
        
        # Apply time-specific adjustments if available
        if time_context in self.time_sensitive_adjustments:
            adjustments = self.time_sensitive_adjustments[time_context]
            base_config.update(adjustments)
        
        return base_config
    
    def __init__(self, **data):
        """Initialize PersonaConfig with auto-derived attributes"""
        # Extract form-specific fields and derive attributes immediately
        form_interaction_style = data.pop('_interaction_style', None)
        form_energy_level = data.pop('_energy_level', None)
        
        # Apply derived values before calling super()
        if form_interaction_style:
            data['tone'] = form_interaction_style
        
        # Initialize default objects if not provided
        if 'rhetoric' not in data:
            data['rhetoric'] = PersonaRhetoric()
        if 'pace' not in data:
            data['pace'] = PersonaPace()
        if 'lexicon' not in data:
            data['lexicon'] = PersonaLexicon()
        
        # Apply energy level mapping to rhetoric
        if form_energy_level:
            if form_energy_level == 'low':
                data['rhetoric'].cta_style = 'gentle'
            elif form_energy_level == 'high':
                data['rhetoric'].cta_style = 'enthusiastic'
            else:
                data['rhetoric'].cta_style = 'friendly'
        
        super().__init__(**data)
        self._update_derived_attributes()
    
    def _update_derived_attributes(self) -> None:
        """Update derived attributes based on current configuration"""
        # Auto-derive pace.sentence_len from speaking_rate
        if self.speaking_rate < 0.8:
            self.pace.sentence_len = (15, 30)  # Slower = longer sentences
        elif self.speaking_rate > 1.2:
            self.pace.sentence_len = (8, 20)   # Faster = shorter sentences
        else:
            self.pace.sentence_len = (10, 25)  # Default
        
        # Set basic lexicon defaults
        if not self.lexicon.banned:
            self.lexicon.banned = ["违法", "欺诈", "虚假", "假冒", "诈骗"]
    
    @classmethod
    def from_form_config(cls, persona_config: Dict[str, Any]) -> 'PersonaConfig':
        """Create PersonaConfig from form configuration data
        
        Args:
            persona_config: Configuration from operational form
            
        Returns:
            PersonaConfig instance with mapped attributes
        """
        # Extract form fields
        interaction_style = persona_config.get('interaction_style', 'friendly')
        energy_level = persona_config.get('energy_level', 'medium')
        speaking_rate = persona_config.get('speaking_rate', 1.0)
        
        # Create base config
        config_data = {
            'persona_id': persona_config.get('selected_persona_id', 'default'),
            'name': persona_config.get('persona_name', 'Default Persona'),
            'description': f"Persona with {interaction_style} style and {energy_level} energy",
            'persona_type': PersonaType.TEMPLATE_BASED,
            'voice_style': VoiceStyle(persona_config.get('voice_style', VoiceStyle.WARM.value)),
            'speaking_rate': speaking_rate,
            'tts_voice_name': persona_config.get('tts_voice_name', 'zh-CN-XiaoxiaoNeural'),
            # Store form fields for attribute derivation
            '_interaction_style': interaction_style,
            '_energy_level': energy_level
        }
        
        instance = cls(**config_data)
        return instance

    def validate_configuration(self) -> bool:
        """Validate persona configuration completeness
        
        Returns:
            True if configuration is valid and complete
            
        Raises:
            ValueError: If configuration is invalid
        """
        if self.persona_type == PersonaType.TEMPLATE_BASED:
            if self.template_config is None:
                raise ValueError("Template configuration required for template-based persona")
            
        elif self.persona_type == PersonaType.SCRIPT_BASED:
            if self.style_corpus_config is None:
                raise ValueError("Style Corpus configuration required for script-based persona")
            
        return True


class PersonaManager(BaseModel):
    """Manager for multiple persona configurations"""
    
    available_personas: Dict[str, PersonaConfig] = Field(
        default_factory=dict,
        description="Available persona configurations by ID"
    )
    default_persona_id: str = Field(
        default="default",
        description="Default persona to use if none specified"
    )
    current_persona_id: Optional[str] = Field(
        default=None,
        description="Currently active persona ID"
    )
    
    def add_persona(self, persona: PersonaConfig) -> None:
        """Add a new persona configuration
        
        Args:
            persona: PersonaConfig instance to add
        """
        persona.validate_configuration()
        self.available_personas[persona.persona_id] = persona
    
    def get_current_persona(self) -> Optional[PersonaConfig]:
        """Get currently active persona configuration
        
        Returns:
            Current PersonaConfig or None if none active
        """
        if self.current_persona_id is None:
            return None
        return self.available_personas.get(self.current_persona_id)
    
    def switch_persona(self, persona_id: str) -> bool:
        """Switch to a different persona
        
        Args:
            persona_id: ID of persona to switch to
            
        Returns:
            True if switch successful, False if persona not found
        """
        if persona_id not in self.available_personas:
            return False
        
        self.current_persona_id = persona_id
        return True
    
    def list_available_personas(self) -> List[Dict[str, str]]:
        """List all available personas with basic info
        
        Returns:
            List of persona info dictionaries
        """
        return [
            {
                "persona_id": pid,
                "name": persona.name,
                "description": persona.description,
                "type": persona.persona_type.value,
            }
            for pid, persona in self.available_personas.items()
        ]