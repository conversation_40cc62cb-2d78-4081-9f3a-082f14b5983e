"""可播放项接口定义

定义决策者-执行者模式中的核心接口，用于统一所有可播放内容。

Author: Claude Code
Date: 2025-08-08
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime


class IPlayableItem(ABC):
    """可播放项的抽象接口
    
    这是决策者-执行者架构的核心接口。所有可以被播放的内容
    （主脚本、QA回答、广告、提示音等）都必须实现此接口。
    
    设计原则：
    1. 播放器（执行者）只需要知道这个接口，不关心具体类型
    2. 播放列表管理器（决策者）负责创建和管理这些项目
    3. 每个项目都有唯一标识符，便于追踪和调试
    """
    
    @property
    @abstractmethod
    def item_id(self) -> str:
        """获取项目的唯一标识符
        
        用于日志记录、状态追踪和调试。格式建议：
        - 主内容：script_{index}_{uuid}
        - QA回答：qa_{qa_id}_answer_{uuid}
        - 过渡语：qa_{qa_id}_transition_{uuid}
        """
        pass
    
    @property
    @abstractmethod
    def content(self) -> str:
        """获取要播放的文本内容
        
        这是将被送入TTS引擎的实际文本。
        """
        pass
    
    @property
    @abstractmethod
    def item_type(self) -> str:
        """获取项目类型标识
        
        用于区分不同类型的内容，如：
        - 'script': 主脚本内容
        - 'qa_answer': QA回答
        - 'qa_transition': QA过渡语
        - 'ad': 广告（未来扩展）
        - 'notification': 紧急通知（未来扩展）
        """
        pass
    
    @property
    @abstractmethod
    def metadata(self) -> Dict[str, Any]:
        """获取项目的元数据
        
        可包含任何附加信息，如：
        - priority: 优先级
        - source: 数据来源
        - qa_id: 关联的QA会话ID
        - estimated_duration_ms: 预估播放时长
        """
        pass
    
    @abstractmethod
    def get_cache_key(self) -> str:
        """获取用于TTS缓存的键值
        
        应该基于内容生成，确保相同内容可以复用缓存。
        """
        pass
    
    @abstractmethod
    def estimate_duration_ms(self) -> int:
        """估算播放时长（毫秒）
        
        用于预测和调度，实际时长由TTS引擎提供。
        """
        pass


@dataclass
class PlaybackStatus:
    """播放状态信息
    
    用于 player 向 manager 报告播放结果。
    """
    last_played_item_id: Optional[str]  # 最后播放的项目ID
    status: str  # 'completed', 'failed', 'skipped', 'interrupted'
    timestamp: datetime = None  # 状态产生的时间
    error: Optional[Exception] = None  # 如果失败，记录异常
    actual_duration_ms: Optional[int] = None  # 实际播放时长
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
    
    @property
    def is_success(self) -> bool:
        """是否成功完成播放"""
        return self.status == 'completed'
    
    @property
    def is_failure(self) -> bool:
        """是否播放失败"""
        return self.status == 'failed'
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'last_played_item_id': self.last_played_item_id,
            'status': self.status,
            'timestamp': self.timestamp.isoformat(),
            'error': str(self.error) if self.error else None,
            'actual_duration_ms': self.actual_duration_ms
        }


# 扩展现有的 PlaylistItem 以实现 IPlayableItem
# 这将在 playlist_models.py 中完成，这里只是示例
class PlayableItemAdapter:
    """适配器示例：将现有类型适配到 IPlayableItem 接口
    
    这个类展示了如何将现有的数据结构适配到新接口。
    实际实现应该直接修改 PlaylistItem 类。
    """
    
    def __init__(self, playlist_item):
        self._item = playlist_item
    
    @property
    def item_id(self) -> str:
        return self._item.item_id
    
    @property
    def content(self) -> str:
        return self._item.content
    
    @property
    def item_type(self) -> str:
        return self._item.type.value
    
    @property
    def metadata(self) -> Dict[str, Any]:
        return self._item.metadata
    
    def get_cache_key(self) -> str:
        return self._item.get_cache_key()
    
    def estimate_duration_ms(self) -> int:
        return self._item.estimate_duration_ms()