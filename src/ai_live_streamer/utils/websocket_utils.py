"""WebSocket 连接状态检查工具

提供统一的 WebSocket 连接状态检查和关闭操作，
支持不同的 WebSocket 实现（FastAPI WebSocket, websockets 库等）
"""

from typing import Any, Optional
from loguru import logger


def is_websocket_connected(websocket: Any) -> bool:
    """统一的 WebSocket 连接状态检查
    
    支持多种 WebSocket 实现：
    - FastAPI WebSocket (application_state, client_state)
    - websockets 库 (closed 属性)
    - 其他实现的回退处理
    
    Args:
        websocket: WebSocket 连接对象
        
    Returns:
        bool: 连接是否有效
    """
    if not websocket:
        return False
        
    try:
        # FastAPI WebSocket - application_state
        if hasattr(websocket, 'application_state'):
            return websocket.application_state == 'CONNECTED'
        
        # FastAPI WebSocket - client_state
        if hasattr(websocket, 'client_state'):
            return str(websocket.client_state) not in ['DISCONNECTED', 'CLOSED']
        
        # websockets 库 - closed 属性
        if hasattr(websocket, 'closed'):
            return not websocket.closed
        
        # 未知实现 - 假设连接有效，让实际操作处理错误
        # logger.debug("Unknown WebSocket implementation, assuming connected")
        return True
        
    except AttributeError as e:
        logger.debug(f"Could not check WebSocket connection state: {e}")
        return True  # Fail-Fast: 假设连接有效，让后续操作失败时处理


def is_websocket_closed(websocket: Any) -> bool:
    """检查 WebSocket 是否已关闭
    
    Args:
        websocket: WebSocket 连接对象
        
    Returns:
        bool: 连接是否已关闭
    """
    return not is_websocket_connected(websocket)


async def safe_close_websocket(websocket: Any, code: int = 1001, reason: str = "Connection closed") -> bool:
    """安全关闭 WebSocket 连接
    
    Args:
        websocket: WebSocket 连接对象
        code: 关闭代码
        reason: 关闭原因
        
    Returns:
        bool: 是否成功关闭
    """
    if not websocket:
        return True
        
    try:
        # 检查是否已经关闭
        if is_websocket_closed(websocket):
            logger.debug("WebSocket already closed")
            return True
        
        # 尝试正常关闭
        if hasattr(websocket, 'close'):
            if hasattr(websocket, 'application_state'):
                # FastAPI WebSocket
                await websocket.close(code=code, reason=reason)
            else:
                # websockets 库
                await websocket.close(code=code, reason=reason)
            
            logger.debug(f"WebSocket closed successfully: {reason}")
            return True
        else:
            logger.warning("WebSocket object has no close method")
            return False
            
    except AttributeError as e:
        logger.warning(f"WebSocket close attribute not available: {e}")
        
        # 回退：尝试直接调用 close()
        try:
            await websocket.close()
            logger.debug("WebSocket closed (fallback)")
            return True
        except Exception as close_error:
            logger.warning(f"Error closing WebSocket (fallback): {close_error}")
            return False
            
    except Exception as e:
        logger.warning(f"Error closing WebSocket: {e}")
        return False