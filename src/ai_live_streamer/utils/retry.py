"""Retry utilities for handling transient failures

Following fail-fast principle with intelligent retry for recoverable errors only.
"""

import asyncio
import functools
import json
import time
from typing import Any, Callable, Optional, Type, Union, Tuple
from loguru import logger


class RetryableError(Exception):
    """Base class for errors that should trigger a retry"""
    pass


class ThrottlingError(RetryableError):
    """Rate limiting or throttling errors that should be retried with backoff"""
    pass


def is_throttling_error(error: Exception) -> bool:
    """Check if an error is a throttling/rate limit error
    
    Args:
        error: The exception to check
        
    Returns:
        True if this is a throttling error that should be retried
    """
    error_str = str(error).lower()
    error_indicators = [
        'throttling',
        'rate limit',
        'ratequota',
        'too many requests',
        '429',
        'quota exceeded'
    ]
    
    # Check error message
    if any(indicator in error_str for indicator in error_indicators):
        return True
    
    # Check for specific error codes in JSON responses
    try:
        if 'error_code' in error_str:
            # Try to extract JSON from error message
            import re
            json_match = re.search(r'\{.*\}', str(error), re.DOTALL)
            if json_match:
                error_data = json.loads(json_match.group())
                if isinstance(error_data, dict):
                    # Check header error_code
                    header = error_data.get('header', {})
                    if header.get('error_code') in ['Throttling.RateQuota', 'Throttling']:
                        return True
    except:
        pass
    
    return False


def retry_with_exponential_backoff(
    max_attempts: int = 3,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    retryable_exceptions: Optional[Tuple[Type[Exception], ...]] = None,
    should_retry: Optional[Callable[[Exception], bool]] = None
):
    """Decorator for retrying async functions with exponential backoff
    
    Args:
        max_attempts: Maximum number of retry attempts (including initial try)
        initial_delay: Initial delay in seconds before first retry
        max_delay: Maximum delay between retries
        backoff_factor: Multiplier for delay after each retry
        retryable_exceptions: Tuple of exception types that should trigger retry
        should_retry: Optional function to determine if an exception should trigger retry
        
    Returns:
        Decorated function with retry logic
    """
    if retryable_exceptions is None:
        retryable_exceptions = (RetryableError, ConnectionError, TimeoutError)
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            delay = initial_delay
            
            for attempt in range(max_attempts):
                try:
                    # Log attempt if not the first one
                    if attempt > 0:
                        logger.debug(f"Retry attempt {attempt}/{max_attempts - 1} for {func.__name__}")
                    
                    # Execute the function
                    result = await func(*args, **kwargs)
                    
                    # Success - log if it was a retry
                    if attempt > 0:
                        logger.info(f"Retry successful for {func.__name__} after {attempt} attempts")
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    
                    # Check if we should retry this exception
                    should_retry_error = False
                    
                    # Check if it's a retryable exception type
                    if isinstance(e, retryable_exceptions):
                        should_retry_error = True
                    
                    # Check custom retry condition
                    if should_retry and should_retry(e):
                        should_retry_error = True
                    
                    # Check for throttling errors
                    if is_throttling_error(e):
                        should_retry_error = True
                        logger.warning(f"Throttling error detected in {func.__name__}: {e}")
                    
                    # If not retryable or last attempt, raise immediately
                    if not should_retry_error or attempt == max_attempts - 1:
                        if attempt > 0:
                            logger.error(f"All {max_attempts} attempts failed for {func.__name__}: {e}")
                        raise
                    
                    # Log the retry
                    logger.warning(
                        f"Attempt {attempt + 1}/{max_attempts} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.1f}s..."
                    )
                    
                    # Wait before retry
                    await asyncio.sleep(delay)
                    
                    # Increase delay for next retry (with cap)
                    delay = min(delay * backoff_factor, max_delay)
            
            # This should never be reached, but just in case
            if last_exception:
                raise last_exception
            else:
                raise RuntimeError(f"Unexpected retry loop exit in {func.__name__}")
        
        return wrapper
    
    return decorator


def retry_with_jitter(
    max_attempts: int = 3,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    jitter_range: float = 0.3
):
    """Decorator with exponential backoff and jitter to prevent thundering herd
    
    Args:
        max_attempts: Maximum number of retry attempts
        initial_delay: Initial delay in seconds
        max_delay: Maximum delay between retries
        jitter_range: Random jitter range (0.3 = ±30% of delay)
        
    Returns:
        Decorated function with retry logic and jitter
    """
    import random
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            delay = initial_delay
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                    
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise
                    
                    # Add jitter to delay
                    jitter = delay * random.uniform(-jitter_range, jitter_range)
                    actual_delay = max(0.1, delay + jitter)  # Minimum 0.1s
                    
                    logger.warning(
                        f"Attempt {attempt + 1}/{max_attempts} failed: {e}. "
                        f"Retrying in {actual_delay:.1f}s..."
                    )
                    
                    await asyncio.sleep(actual_delay)
                    delay = min(delay * 2, max_delay)
        
        return wrapper
    
    return decorator