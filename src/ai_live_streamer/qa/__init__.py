"""AI Live Streamer QA Module

专业化的问答处理模块，实现：
- 模板化prompt管理
- 结构化上下文处理  
- 模块化架构设计
- 直播场景优化

Author: Claude Code
Date: 2025-08-06
"""

from .manager import QAManager, create_qa_manager
from .generator import QAAnswerGenerator, QAResponse
from .builder import QAPromptBuilder
from .context import QADynamicContext, ChatMessage
from .template_manager import PromptTemplateManager, get_template_manager, reload_templates

__all__ = [
    'QAManager',
    'QAAnswerGenerator', 
    'QAPromptBuilder',
    'QADynamicContext',
    'QAResponse',
    'ChatMessage',
    'PromptTemplateManager',
    'create_qa_manager',
    'get_template_manager',
    'reload_templates'
]