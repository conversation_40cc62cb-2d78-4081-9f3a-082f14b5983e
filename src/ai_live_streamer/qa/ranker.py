"""问题优先级排序器模块"""

import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import jieba
import logging
import time
import math

logger = logging.getLogger(__name__)


class QARanker:
    """问题优先级排序器，用于高并发时的智能排序"""
    
    def __init__(self, topic_weight: float = 0.3,
                 intent_weight: float = 0.4,
                 novelty_weight: float = 0.2,
                 urgency_weight: float = 0.1):
        
        # 权重配置
        self.topic_weight = topic_weight
        self.intent_weight = intent_weight
        self.novelty_weight = novelty_weight
        self.urgency_weight = urgency_weight
        
        # 验证权重总和
        total_weight = topic_weight + intent_weight + novelty_weight + urgency_weight
        if abs(total_weight - 1.0) > 0.01:
            logger.warning(f"权重总和不为1.0: {total_weight}")
        
        # 购买意图关键词
        self.purchase_keywords = {
            # 价格相关
            '价格', '多少钱', '贵不贵', '便宜', '费用', '成本', '收费',
            '优惠', '折扣', '打折', '促销', '特价', '活动',
            # 购买相关
            '怎么买', '在哪买', '购买', '下单', '订购', '预订',
            '买', '要', '想要', '需要', '订单',
            # 支付相关
            '支付', '付款', '结算', '付钱', '微信', '支付宝',
            # 链接相关
            '链接', '店铺', '商店', '官网'
        }
        
        # 紧急关键词
        self.urgent_keywords = {
            '快', '马上', '立即', '现在', '赶紧', '急', '赶快',
            '马上要', '立刻', '即刻', '速度', '快点',
            '等不及', '着急', '赶时间', '来不及'
        }
        
        # 高价值关键词（表示用户高度关注）
        self.high_value_keywords = {
            '质量', '品质', '效果', '保证', '保障', '售后',
            '服务', '专业', '权威', '认证', '推荐'
        }
        
        # 问题类型权重
        self.question_type_weights = {
            'price_inquiry': 1.0,      # 价格咨询
            'purchase_inquiry': 1.0,   # 购买咨询  
            'feature_inquiry': 0.8,    # 功能特性
            'usage_inquiry': 0.7,      # 使用方法
            'shipping_inquiry': 0.6,   # 物流配送
            'general_inquiry': 0.5     # 一般咨询
        }
        
        # 历史问题缓存(用于计算新颖度)
        self.recent_questions = []
        self.max_history = 100
        self.question_timestamps = {}  # 记录问题的时间戳
        
        # 统计信息
        self.stats = {
            'total_ranked': 0,
            'high_priority_count': 0,
            'medium_priority_count': 0,
            'low_priority_count': 0
        }
        
    def rank_questions(self, questions: List[Dict[str, Any]],
                      context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        对问题列表进行优先级排序
        
        Args:
            questions: 待排序的问题列表
            context: 直播上下文信息（当前话题、商品信息等）
            
        Returns:
            排序后的问题列表
        """
        if not questions:
            return []
            
        start_time = time.time()
        scored_questions = []
        
        for question in questions:
            try:
                score = self._calculate_priority_score(question, context)
                scored_questions.append({
                    **question,
                    'priority_score': score,
                    'ranking_metadata': self._get_scoring_details(question, context)
                })
            except Exception as e:
                logger.error(f"计算问题优先级失败: {e}")
                # 给失败的问题一个默认低分数
                scored_questions.append({
                    **question,
                    'priority_score': 0.1,
                    'ranking_metadata': {'error': str(e)}
                })
        
        # 按优先级分数降序排列
        ranked_questions = sorted(scored_questions, 
                                key=lambda x: x['priority_score'], 
                                reverse=True)
        
        # 更新统计信息
        self._update_stats(ranked_questions)
        
        processing_time = time.time() - start_time
        logger.debug(f"排序{len(questions)}个问题，耗时{processing_time:.3f}秒")
        
        return ranked_questions
        
    def _calculate_priority_score(self, question: Dict[str, Any],
                                context: Dict[str, Any]) -> float:
        """计算问题的优先级分数"""
        question_text = question.get('text', '')
        if not question_text:
            return 0.0
            
        # 1. 主题相似度评分
        topic_score = self._calculate_topic_similarity(question_text, context)
        
        # 2. 购买意图评分
        intent_score = self._calculate_purchase_intent(question_text)
        
        # 3. 新颖度评分
        novelty_score = self._calculate_novelty_score(question_text)
        
        # 4. 紧急度评分
        urgency_score = self._calculate_urgency_score(question_text)
        
        # 5. 额外加分项
        bonus_score = self._calculate_bonus_score(question_text, question)
        
        # 加权计算总分
        total_score = (
            topic_score * self.topic_weight +
            intent_score * self.intent_weight +
            novelty_score * self.novelty_weight +
            urgency_score * self.urgency_weight
        ) + bonus_score
        
        # 确保分数在0-1范围内
        total_score = max(0.0, min(1.0, total_score))
        
        logger.debug(f"问题评分 - 主题:{topic_score:.2f}, 意图:{intent_score:.2f}, "
                    f"新颖:{novelty_score:.2f}, 紧急:{urgency_score:.2f}, "
                    f"加分:{bonus_score:.2f}, 总分:{total_score:.2f}")
        
        return total_score
        
    def _calculate_topic_similarity(self, question: str, context: Dict) -> float:
        """计算与当前直播主题的相似度"""
        current_topic = context.get('current_topic', '')
        if not current_topic:
            return 0.5  # 没有主题信息时给中等分数
            
        try:
            # 分词
            topic_words = set(jieba.cut(current_topic))
            question_words = set(jieba.cut(question))
            
            # 过滤停用词
            stopwords = {'的', '是', '在', '有', '和', '就', '都', '而', '及', '与', '或', '但', '不', '了'}
            topic_words = {w for w in topic_words if len(w) > 1 and w not in stopwords}
            question_words = {w for w in question_words if len(w) > 1 and w not in stopwords}
            
            if not topic_words or not question_words:
                return 0.3
            
            # Jaccard相似度
            intersection = topic_words & question_words
            union = topic_words | question_words
            jaccard_similarity = len(intersection) / len(union) if union else 0
            
            # 考虑产品相关性
            product_keywords = context.get('product_keywords', [])
            product_match_score = 0.0
            if product_keywords:
                product_words = set(product_keywords)
                product_matches = question_words & product_words
                product_match_score = len(product_matches) / len(product_words) if product_words else 0
                
            # 综合相似度
            similarity = 0.7 * jaccard_similarity + 0.3 * product_match_score
            return min(1.0, similarity)
            
        except Exception as e:
            logger.warning(f"计算主题相似度失败: {e}")
            return 0.5
            
    def _calculate_purchase_intent(self, question: str) -> float:
        """识别购买意图强度"""
        question_lower = question.lower()
        
        # 统计购买意图关键词
        intent_matches = sum(1 for keyword in self.purchase_keywords 
                           if keyword in question_lower)
        
        # 检查问号和感叹号（表示询问强度）
        question_marks = question.count('?') + question.count('？')
        exclamations = question.count('!') + question.count('！')
        
        # 计算意图强度
        base_score = min(intent_matches / 3.0, 1.0)  # 最多3个关键词得满分
        punctuation_bonus = min((question_marks + exclamations) * 0.1, 0.2)
        
        # 特殊模式匹配
        pattern_bonus = 0.0
        purchase_patterns = [
            r'多少钱',
            r'怎么买',
            r'在哪.*买',
            r'给.*链接',
            r'想要.*个',
            r'需要.*个'
        ]
        
        for pattern in purchase_patterns:
            if re.search(pattern, question_lower):
                pattern_bonus = 0.2
                break
                
        total_intent = base_score + punctuation_bonus + pattern_bonus
        return min(1.0, total_intent)
        
    def _calculate_novelty_score(self, question: str) -> float:
        """计算问题的新颖度"""
        if not self.recent_questions:
            self._add_to_history(question)
            return 1.0
            
        try:
            # 计算与历史问题的相似度
            question_words = set(jieba.cut(question))
            question_words = {w for w in question_words if len(w) > 1}
            
            if not question_words:
                return 0.5
                
            max_similarity = 0.0
            recent_count = min(len(self.recent_questions), 20)  # 只看最近20个问题
            
            for recent_q in self.recent_questions[-recent_count:]:
                recent_words = set(jieba.cut(recent_q))
                recent_words = {w for w in recent_words if len(w) > 1}
                
                if recent_words:
                    intersection = question_words & recent_words
                    union = question_words | recent_words
                    similarity = len(intersection) / len(union) if union else 0
                    max_similarity = max(max_similarity, similarity)
            
            # 时间衰减：最近的问题相似度影响更大
            novelty = 1.0 - max_similarity
            
            # 考虑问题频率
            question_hash = hash(question)
            current_time = time.time()
            if question_hash in self.question_timestamps:
                last_time = self.question_timestamps[question_hash]
                time_diff = current_time - last_time
                if time_diff < 300:  # 5分钟内重复问题降低新颖度
                    novelty *= 0.3
            
            # 更新历史
            self._add_to_history(question)
            self.question_timestamps[question_hash] = current_time
            
            return max(0.0, novelty)
            
        except Exception as e:
            logger.warning(f"计算新颖度失败: {e}")
            return 0.5
            
    def _calculate_urgency_score(self, question: str) -> float:
        """计算问题的紧急度"""
        question_lower = question.lower()
        
        # 紧急关键词匹配
        urgency_matches = sum(1 for keyword in self.urgent_keywords 
                            if keyword in question_lower)
        
        # 标点符号强度
        question_marks = question.count('?') + question.count('？')
        exclamations = question.count('!') + question.count('！')
        
        # 重复字符（如"快快快"）
        repeated_chars = len(re.findall(r'(.)\1{2,}', question_lower))
        
        # 全大写比例（英文）
        if question:
            uppercase_ratio = sum(1 for c in question if c.isupper()) / len(question)
        else:
            uppercase_ratio = 0
            
        # 综合计算紧急度
        urgency = (
            urgency_matches * 0.4 +
            (question_marks + exclamations) * 0.3 +
            repeated_chars * 0.2 +
            uppercase_ratio * 0.1
        )
        
        return min(1.0, urgency)
        
    def _calculate_bonus_score(self, question_text: str, question: Dict) -> float:
        """计算额外加分项"""
        bonus = 0.0
        
        # 高价值关键词加分
        question_lower = question_text.lower()
        value_matches = sum(1 for keyword in self.high_value_keywords 
                          if keyword in question_lower)
        bonus += min(value_matches * 0.05, 0.15)
        
        # VIP用户加分
        user_level = question.get('user_level', 'normal')
        if user_level == 'vip':
            bonus += 0.1
        elif user_level == 'svip':
            bonus += 0.15
            
        # 消费历史加分
        purchase_history = question.get('purchase_history', 0)
        if purchase_history > 0:
            bonus += min(purchase_history * 0.02, 0.1)
            
        # 问题长度加分（适中长度更有价值）
        question_length = len(question_text)
        if 10 <= question_length <= 100:
            bonus += 0.05
            
        return bonus
        
    def _get_scoring_details(self, question: Dict, context: Dict) -> Dict:
        """获取评分详情，用于调试和分析"""
        question_text = question.get('text', '')
        
        return {
            'topic_score': self._calculate_topic_similarity(question_text, context),
            'intent_score': self._calculate_purchase_intent(question_text),
            'novelty_score': self._calculate_novelty_score(question_text),
            'urgency_score': self._calculate_urgency_score(question_text),
            'bonus_score': self._calculate_bonus_score(question_text, question),
            'question_type': self._get_question_type(question_text),
            'keywords_matched': self._get_matched_keywords(question_text)
        }
        
    def _get_question_type(self, question: str) -> str:
        """识别问题类型"""
        question_lower = question.lower()
        
        # 价格相关
        if any(kw in question_lower for kw in ['价格', '多少钱', '贵', '便宜', '费用']):
            return 'price_inquiry'
            
        # 购买相关
        if any(kw in question_lower for kw in ['怎么买', '在哪买', '购买', '下单']):
            return 'purchase_inquiry'
            
        # 功能特性
        if any(kw in question_lower for kw in ['特点', '功能', '作用', '效果']):
            return 'feature_inquiry'
            
        # 使用方法
        if any(kw in question_lower for kw in ['怎么用', '如何使用', '用法']):
            return 'usage_inquiry'
            
        # 配送物流
        if any(kw in question_lower for kw in ['配送', '物流', '快递', '发货']):
            return 'shipping_inquiry'
            
        return 'general_inquiry'
        
    def _get_matched_keywords(self, question: str) -> Dict[str, List[str]]:
        """获取匹配的关键词"""
        question_lower = question.lower()
        
        return {
            'purchase': [kw for kw in self.purchase_keywords if kw in question_lower],
            'urgent': [kw for kw in self.urgent_keywords if kw in question_lower],
            'high_value': [kw for kw in self.high_value_keywords if kw in question_lower]
        }
        
    def _add_to_history(self, question: str):
        """添加问题到历史记录"""
        self.recent_questions.append(question)
        if len(self.recent_questions) > self.max_history:
            self.recent_questions.pop(0)
            
    def _update_stats(self, ranked_questions: List[Dict]):
        """更新统计信息"""
        self.stats['total_ranked'] = len(ranked_questions)
        
        high_count = sum(1 for q in ranked_questions if q['priority_score'] >= 0.7)
        medium_count = sum(1 for q in ranked_questions if 0.3 <= q['priority_score'] < 0.7)
        low_count = len(ranked_questions) - high_count - medium_count
        
        self.stats.update({
            'high_priority_count': high_count,
            'medium_priority_count': medium_count,
            'low_priority_count': low_count
        })
        
    def get_stats(self) -> Dict:
        """获取排序统计信息"""
        return self.stats.copy()
        
    def clear_history(self):
        """清空历史记录"""
        self.recent_questions.clear()
        self.question_timestamps.clear()
        logger.info("已清空问题历史记录")
        
    def update_weights(self, topic_weight: float = None, intent_weight: float = None,
                      novelty_weight: float = None, urgency_weight: float = None):
        """动态更新权重"""
        if topic_weight is not None:
            self.topic_weight = topic_weight
        if intent_weight is not None:
            self.intent_weight = intent_weight
        if novelty_weight is not None:
            self.novelty_weight = novelty_weight
        if urgency_weight is not None:
            self.urgency_weight = urgency_weight
            
        # 验证权重总和
        total_weight = self.topic_weight + self.intent_weight + self.novelty_weight + self.urgency_weight
        if abs(total_weight - 1.0) > 0.01:
            logger.warning(f"权重总和不为1.0: {total_weight}")
            
        logger.info(f"更新排序权重 - 主题:{self.topic_weight}, 意图:{self.intent_weight}, "
                   f"新颖:{self.novelty_weight}, 紧急:{self.urgency_weight}")
                   
    def batch_rank_with_limit(self, questions: List[Dict], context: Dict, 
                            max_process: int = 5) -> List[Dict]:
        """批量排序但限制处理数量"""
        ranked = self.rank_questions(questions, context)
        return ranked[:max_process]