"""QA Answer Generator

专门负责生成QA回答，集成prompt构建、LLM调用和回答质量控制，
为直播场景提供高质量的问答体验。

Author: Claude Code  
Date: 2025-08-06
"""

import asyncio
import time
from typing import Optional, Dict, Any
from datetime import datetime
from loguru import logger

from .context import QADynamicContext
from .builder import QAPromptBuilder
from ..core.exceptions import ServiceError, TimeoutError
from ..services.llm_adapters.base import BaseLLMAdapter, LLMMessage


class QAResponse:
    """QA回答结果"""
    
    def __init__(
        self,
        answer: str,
        confidence: float = 0.8,
        sources: list = None,
        response_type: str = "normal",
        processing_time_ms: float = 0.0,
        context_summary: Optional[Dict[str, Any]] = None
    ):
        self.answer = answer
        self.confidence = confidence
        self.sources = sources or ["llm"]
        self.response_type = response_type
        self.processing_time_ms = processing_time_ms
        self.context_summary = context_summary or {}
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "answer": self.answer,
            "confidence": self.confidence,
            "sources": self.sources,
            "response_type": self.response_type,
            "processing_time_ms": self.processing_time_ms,
            "context_summary": self.context_summary,
            "timestamp": self.timestamp.isoformat()
        }


class QAAnswerGenerator:
    """QA回答生成器
    
    负责整合prompt构建和LLM调用，生成高质量的QA回答。
    包含回答质量控制、超时处理和错误恢复机制。
    """
    
    def __init__(
        self,
        llm_adapter: BaseLLMAdapter,
        prompt_builder: Optional[QAPromptBuilder] = None
    ):
        """初始化回答生成器
        
        Args:
            llm_adapter: LLM适配器实例
            prompt_builder: Prompt构建器实例，为空时创建默认实例
        """
        if not llm_adapter:
            raise ValueError("LLM adapter is required")
        
        self.llm_adapter = llm_adapter
        self.prompt_builder = prompt_builder or QAPromptBuilder()
        
        # 配置参数
        self.default_timeout = 30.0  # 默认超时时间（秒）
        self.max_retries = 2  # 最大重试次数
        self.min_answer_length = 5  # 最小回答长度
        self.max_answer_length = 200  # 最大回答长度
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "successful_responses": 0,
            "failed_responses": 0,
            "timeout_responses": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0
        }
        
        logger.info(f"✅ QAAnswerGenerator initialized with {llm_adapter.provider_name}")
    
    async def generate_answer(
        self,
        context: QADynamicContext,
        timeout_seconds: Optional[float] = None
    ) -> QAResponse:
        """生成QA回答
        
        Args:
            context: QA动态上下文
            timeout_seconds: 超时时间，为空时使用默认值
            
        Returns:
            QAResponse: QA回答结果
            
        Raises:
            ServiceError: 生成失败时抛出
            TimeoutError: 超时时抛出
        """
        start_time = time.time()
        timeout = timeout_seconds or self.default_timeout
        
        try:
            self.stats["total_requests"] += 1
            
            logger.info(f"🤖 Generating answer for: {context.question.text[:50]}...")
            
            # 使用超时控制包装整个生成过程
            response = await asyncio.wait_for(
                self._generate_answer_internal(context),
                timeout=timeout
            )
            
            # 更新统计信息
            processing_time = (time.time() - start_time) * 1000
            response.processing_time_ms = processing_time
            
            self.stats["successful_responses"] += 1
            self.stats["total_processing_time"] += processing_time
            self._update_average_processing_time()
            
            logger.info(f"✅ Answer generated successfully in {processing_time:.1f}ms")
            return response
            
        except asyncio.TimeoutError:
            self.stats["timeout_responses"] += 1
            processing_time = (time.time() - start_time) * 1000
            
            logger.warning(f"⏰ Answer generation timeout after {timeout}s")
            
            # 返回超时备用回答
            return self._create_timeout_response(context, processing_time)
            
        except Exception as e:
            self.stats["failed_responses"] += 1
            processing_time = (time.time() - start_time) * 1000
            
            logger.error(f"❌ Answer generation failed: {e}")
            
            # 返回错误备用回答
            return self._create_error_response(context, str(e), processing_time)
    
    async def _generate_answer_internal(self, context: QADynamicContext) -> QAResponse:
        """内部回答生成逻辑
        
        Args:
            context: QA动态上下文
            
        Returns:
            QAResponse: 回答结果
        """
        for attempt in range(self.max_retries + 1):
            try:
                # 1. 构建prompt
                logger.debug(f"🔨 Building prompt (attempt {attempt + 1})...")
                system_prompt, user_prompt = self.prompt_builder.build_full_prompt(context)
                
                # 2. 调用LLM生成回答
                logger.debug(f"🧠 Calling LLM for answer generation...")
                
                # 准备LLM消息
                messages = [
                    LLMMessage(role="system", content=system_prompt),
                    LLMMessage(role="user", content=user_prompt)
                ]
                
                # 调用LLM
                llm_response = await self.llm_adapter.generate(messages)
                raw_answer = llm_response.content.strip()
                
                # 3. 后处理和验证
                processed_answer = self._post_process_answer(raw_answer, context)
                
                if self._validate_answer(processed_answer, context):
                    # 创建成功响应
                    return QAResponse(
                        answer=processed_answer,
                        confidence=self._calculate_confidence(processed_answer, context),
                        sources=["llm", self.llm_adapter.provider_name],
                        response_type="normal",
                        context_summary=context.to_dict()
                    )
                else:
                    raise ServiceError("Generated answer failed validation")
                    
            except Exception as e:
                logger.warning(f"⚠️ Answer generation attempt {attempt + 1} failed: {e}")
                
                if attempt < self.max_retries:
                    # 等待后重试
                    await asyncio.sleep(0.5 * (attempt + 1))
                else:
                    # 所有重试都失败，抛出异常
                    raise ServiceError(f"All {self.max_retries + 1} attempts failed: {e}")
    
    def _post_process_answer(self, raw_answer: str, context: QADynamicContext) -> str:
        """后处理回答文本
        
        Args:
            raw_answer: LLM原始回答
            context: QA上下文
            
        Returns:
            str: 处理后的回答
        """
        if not raw_answer:
            return ""
        
        # 基本清理
        answer = raw_answer.strip()
        
        # 移除可能的格式标记
        answer = answer.replace("**", "").replace("*", "")
        
        # 长度控制
        max_length = context.max_response_length or self.max_answer_length
        if len(answer) > max_length:
            # 尝试在句子边界截断
            sentences = answer.split('。')
            truncated = ""
            for sentence in sentences:
                if len(truncated + sentence + '。') <= max_length:
                    truncated += sentence + '。'
                else:
                    break
            
            if truncated:
                answer = truncated.rstrip('。') + '。'
            else:
                # 强制截断
                answer = answer[:max_length-1] + '...'
        
        # 确保直播友好性
        answer = self._ensure_stream_friendly(answer)
        
        logger.debug(f"📝 Post-processed answer: {len(answer)} chars")
        return answer
    
    def _ensure_stream_friendly(self, answer: str) -> str:
        """确保回答适合直播场景
        
        Args:
            answer: 原始回答
            
        Returns:
            str: 直播友好的回答
        """
        # 移除可能暴露AI身份的词汇
        ai_phrases = ["我是AI", "作为AI", "人工智能", "我是机器人", "算法", "模型"]
        for phrase in ai_phrases:
            if phrase in answer:
                logger.debug(f"🔧 Removing AI-revealing phrase: {phrase}")
                answer = answer.replace(phrase, "我")
        
        # 确保语调自然
        if not answer.endswith(('。', '！', '？', '~', '呢', '哦', '啊')):
            answer += '。'
        
        return answer
    
    def _validate_answer(self, answer: str, context: QADynamicContext) -> bool:
        """验证回答质量
        
        Args:
            answer: 回答文本
            context: QA上下文
            
        Returns:
            bool: 是否通过验证
        """
        # 长度检查
        if len(answer) < self.min_answer_length:
            logger.warning("⚠️ Answer too short")
            return False
        
        if len(answer) > self.max_answer_length * 2:  # 允许一定超长
            logger.warning("⚠️ Answer too long")
            return False
        
        # 内容检查
        if not answer.strip():
            logger.warning("⚠️ Empty answer")
            return False
        
        # 相关性检查（简单实现）
        question_text = context.question.text.lower()
        answer_text = answer.lower()
        
        # 如果问题包含关键词，回答中应该有相关内容
        key_words = ["价格", "多少钱", "怎么用", "效果", "质量"]
        for word in key_words:
            if word in question_text:
                # 简单检查回答是否包含相关回应
                if word == "价格" and any(x in answer_text for x in ["价格", "钱", "元", "优惠", "便宜"]):
                    break
                elif word in ["怎么用", "效果"] and any(x in answer_text for x in ["使用", "效果", "功能", "方法"]):
                    break
        
        logger.debug("✅ Answer validation passed")
        return True
    
    def _calculate_confidence(self, answer: str, context: QADynamicContext) -> float:
        """计算回答置信度
        
        Args:
            answer: 回答文本
            context: QA上下文
            
        Returns:
            float: 置信度 (0-1)
        """
        confidence = 0.7  # 基础置信度
        
        # 根据回答长度调整
        if self.min_answer_length <= len(answer) <= context.max_response_length:
            confidence += 0.1
        
        # 根据是否有知识库信息调整
        if context.knowledge_base_info:
            confidence += 0.1
        
        # 根据人设信息完整性调整
        if context.persona_name and context.persona_style:
            confidence += 0.05
        
        # 限制在合理范围内
        return min(max(confidence, 0.1), 0.95)
    
    def _create_timeout_response(self, context: QADynamicContext, processing_time: float) -> QAResponse:
        """创建超时响应
        
        Args:
            context: QA上下文
            processing_time: 处理时间
            
        Returns:
            QAResponse: 超时响应
        """
        fallback_answer = self.prompt_builder.template_manager.get_fallback_response("timeout_response")
        
        return QAResponse(
            answer=fallback_answer,
            confidence=0.1,
            sources=["timeout_fallback"],
            response_type="timeout",
            processing_time_ms=processing_time,
            context_summary=context.to_dict()
        )
    
    def _create_error_response(self, context: QADynamicContext, error_msg: str, processing_time: float) -> QAResponse:
        """创建错误响应
        
        Args:
            context: QA上下文
            error_msg: 错误消息
            processing_time: 处理时间
            
        Returns:
            QAResponse: 错误响应
        """
        fallback_answer = self.prompt_builder.template_manager.get_fallback_response("error_response")
        
        return QAResponse(
            answer=fallback_answer,
            confidence=0.0,
            sources=["error_fallback"],
            response_type="error",
            processing_time_ms=processing_time,
            context_summary=context.to_dict()
        )
    
    def _update_average_processing_time(self) -> None:
        """更新平均处理时间"""
        if self.stats["successful_responses"] > 0:
            self.stats["average_processing_time"] = (
                self.stats["total_processing_time"] / self.stats["successful_responses"]
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取生成器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        total = self.stats["total_requests"]
        success_rate = (self.stats["successful_responses"] / total * 100) if total > 0 else 0
        
        return {
            **self.stats,
            "success_rate_percent": success_rate,
            "llm_provider": self.llm_adapter.provider_name,
            "llm_model": getattr(self.llm_adapter, 'model_name', 'unknown')
        }