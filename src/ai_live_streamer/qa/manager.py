"""QA Manager - 重构版

基于新架构的QA管理器，实现职责清晰的分离和模块化设计。
作为协调器角色，整合prompt构建、回答生成和结果处理。

Author: Claude Code
Date: 2025-08-06
"""

import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger

from .context import QADynamicContext
from .generator import QAAnswerGenerator
from .builder import QAPromptBuilder
from ..models.persona import PersonaConfig
from ..core.exceptions import ServiceError, TimeoutError
from ..models.constants import MAX_QA_RESPONSE_TIME_SEC
from ..services.llm_adapters.base import BaseLLMAdapter

# Define IQAManager interface locally since V1 is archived
from abc import ABC, abstractmethod
from typing import Dict, Any

class IQAManager(ABC):
    """Interface for QA managers"""
    @abstractmethod
    async def handle_qa(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        pass


class QAManager(IQAManager):
    """重构版QA管理器
    
    作为协调器角色，负责：
    1. 整合QA处理流程
    2. 管理组件依赖关系
    3. 提供统一的接口
    4. 处理错误和统计
    """
    
    def __init__(
        self,
        llm_adapter: BaseLLMAdapter,
        prompt_builder: Optional[QAPromptBuilder] = None,
        default_persona: Optional[PersonaConfig] = None
    ):
        """初始化QA管理器
        
        Args:
            llm_adapter: LLM适配器实例
            prompt_builder: Prompt构建器实例，为空时创建默认实例
            default_persona: 默认人设配置，为空时创建默认实例
        """
        if not llm_adapter:
            raise ValueError("LLM adapter is required")
        
        self.llm_adapter = llm_adapter
        self.prompt_builder = prompt_builder or QAPromptBuilder()
        self.answer_generator = QAAnswerGenerator(llm_adapter, self.prompt_builder)
        self.default_persona = default_persona or self._create_default_persona()
        
        # 配置参数
        self.max_response_time = MAX_QA_RESPONSE_TIME_SEC
        
        # 统计信息
        self.stats = {
            "total_questions": 0,
            "successful_responses": 0,
            "failed_responses": 0,
            "timeout_responses": 0,
            "average_response_time": 0.0,
            "total_response_time": 0.0,
            "last_activity": datetime.utcnow()
        }
        
        logger.info(f"✅ QAManager (v2) initialized with {llm_adapter.provider_name}")
    
    def _create_default_persona(self) -> PersonaConfig:
        """创建默认人设配置"""
        from ..models.persona import PersonaLexicon
        
        return PersonaConfig(
            persona_id="default_streamer",
            name="默认主播",
            description="用于QA问答的默认主播人设，友好亲切的直播风格",
            persona_type="template_based",
            tone="friendly",
            lexicon=PersonaLexicon(
                preferred=["亲爱的朋友们", "大家好", "非常感谢", "希望能帮到大家"],
                banned=["绝对", "肯定", "保证", "一定", "100%"]
            ),
        )
    
    async def handle_qa(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个 QA 交互（保持原接口兼容性）
        
        Args:
            question_data: 问题数据，包含以下字段：
                - text: 问题文本
                - session_id: 会话ID（可选）
                - persona: 人设配置（可选）
                - product_info: 产品信息（可选）
                - max_response_time: 最大响应时间（可选）
                - live_context: 直播上下文（可选，新增）
                
        Returns:
            QA 响应数据，包含：
                - answer: 回答文本
                - confidence: 置信度
                - response_time: 响应时间
                - sources: 信息来源
                - response_type: 响应类型
        """
        start_time = datetime.utcnow()
        
        try:
            # 解析和验证问题数据
            question_text = question_data.get("text", "").strip()
            if not question_text:
                raise ServiceError(
                    "Question text is empty",
                    "qa_manager", 
                    "EMPTY_QUESTION"
                )
            
            self.stats["total_questions"] += 1
            
            # 构建QA动态上下文
            context = self._build_dynamic_context(question_data)
            
            # 生成回答
            logger.info(f"🤔 Processing QA: {question_text[:50]}...")
            
            # 设置超时时间
            timeout = question_data.get("max_response_time", self.max_response_time)
            
            # 调用回答生成器
            qa_response = await self.answer_generator.generate_answer(
                context=context,
                timeout_seconds=timeout
            )
            
            # 计算总响应时间
            end_time = datetime.utcnow()
            total_response_time = (end_time - start_time).total_seconds()
            
            # 更新统计信息
            if qa_response.response_type in ["normal", "timeout"]:
                self._update_stats(total_response_time, success=True)
            else:
                self._update_stats(total_response_time, success=False)
                
            if qa_response.response_type == "timeout":
                self.stats["timeout_responses"] += 1
            
            # 构建兼容的返回格式
            result = {
                "answer": qa_response.answer,
                "confidence": qa_response.confidence,
                "response_time": total_response_time,
                "sources": qa_response.sources,
                "response_type": qa_response.response_type,
                "follow_up_suggestions": [],  # 保持兼容性
                "timestamp": end_time.isoformat(),
                "question": question_text,
                "session_id": question_data.get("session_id"),
                # 新增字段
                "processing_time_ms": qa_response.processing_time_ms,
                "context_summary": qa_response.context_summary
            }
            
            logger.info(f"✅ QA completed in {total_response_time:.2f}s: {qa_response.answer[:50]}...")
            return result
            
        except TimeoutError as e:
            # 超时错误特殊处理
            response_time = (datetime.utcnow() - start_time).total_seconds()
            self._update_stats(response_time, success=False)
            self.stats["timeout_responses"] += 1
            
            logger.warning(f"⏰ QA timeout after {response_time:.2f}s: {question_data.get('text', '')[:50]}...")
            
            return self._create_timeout_result(question_data, response_time)
            
        except Exception as e:
            # 一般错误处理
            response_time = (datetime.utcnow() - start_time).total_seconds()
            self._update_stats(response_time, success=False)
            
            logger.error(f"❌ QA error: {e}")
            
            return self._create_error_result(question_data, str(e), response_time)
    
    def _build_dynamic_context(self, question_data: Dict[str, Any]) -> QADynamicContext:
        """构建QA动态上下文
        
        Args:
            question_data: 问题数据
            
        Returns:
            QADynamicContext: 动态上下文实例
        """
        try:
            # 解析人设配置
            persona_data = question_data.get("persona")
            if persona_data and isinstance(persona_data, dict):
                try:
                    persona = PersonaConfig.model_validate(persona_data)
                except Exception as e:
                    logger.warning(f"⚠️ Invalid persona data, using default: {e}")
                    persona = self.default_persona
            else:
                persona = self.default_persona
            
            # 获取直播上下文（新增支持）
            live_context = question_data.get("live_context", {})
            
            # 获取产品信息
            product_info = question_data.get("product_info", {})
            
            # 使用工厂方法创建上下文
            context = QADynamicContext.from_question_data(
                question_data=question_data,
                persona=persona,
                additional_context={
                    "live_topic": live_context.get("topic", ""),
                    "current_script": live_context.get("current_script", ""),
                    "chat_history": question_data.get("conversation_history", [])
                }
            )
            
            # 设置最大回答长度
            max_response_time = question_data.get("max_response_time", self.max_response_time)
            context.max_response_length = 50  # 默认长度
            
            logger.debug(f"📋 Built dynamic context: {context.to_dict()}")
            return context
            
        except Exception as e:
            logger.error(f"❌ Failed to build dynamic context: {e}")
            raise ServiceError(f"Context building failed: {e}")
    
    def _create_timeout_result(self, question_data: Dict[str, Any], response_time: float) -> Dict[str, Any]:
        """创建超时结果"""
        fallback_answer = self.prompt_builder.template_manager.get_fallback_response("timeout_response")
        
        return {
            "answer": fallback_answer,
            "confidence": 0.1,
            "response_time": response_time,
            "sources": ["timeout"],
            "response_type": "timeout",
            "follow_up_suggestions": ["请重新提问"],
            "timestamp": datetime.utcnow().isoformat(),
            "question": question_data.get("text", ""),
            "session_id": question_data.get("session_id"),
            "error": "timeout"
        }
    
    def _create_error_result(self, question_data: Dict[str, Any], error_msg: str, response_time: float) -> Dict[str, Any]:
        """创建错误结果"""
        fallback_answer = self.prompt_builder.template_manager.get_fallback_response("error_response")
        
        return {
            "answer": fallback_answer,
            "confidence": 0.0,
            "response_time": response_time,
            "sources": ["error"],
            "response_type": "error",
            "follow_up_suggestions": ["请重新提问"],
            "timestamp": datetime.utcnow().isoformat(),
            "question": question_data.get("text", ""),
            "session_id": question_data.get("session_id"),
            "error": error_msg
        }
    
    def _update_stats(self, response_time: float, success: bool) -> None:
        """更新统计信息"""
        self.stats["total_response_time"] += response_time
        self.stats["last_activity"] = datetime.utcnow()
        
        if success:
            self.stats["successful_responses"] += 1
        else:
            self.stats["failed_responses"] += 1
        
        # 更新平均响应时间
        total_completed = self.stats["successful_responses"] + self.stats["failed_responses"]
        if total_completed > 0:
            self.stats["average_response_time"] = (
                self.stats["total_response_time"] / total_completed
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total = self.stats["total_questions"]
        if total == 0:
            success_rate = 0.0
        else:
            success_rate = self.stats["successful_responses"] / total * 100
        
        # 整合子组件统计
        generator_stats = self.answer_generator.get_stats()
        
        return {
            **self.stats,
            "success_rate_percent": success_rate,
            "failure_rate_percent": 100 - success_rate,
            "timeout_rate_percent": (self.stats["timeout_responses"] / total * 100) if total > 0 else 0,
            # 子组件统计
            "generator_stats": generator_stats,
            "llm_provider": self.llm_adapter.provider_name,
            "template_file": self.prompt_builder.template_manager.template_file,
            "knowledge_base_enabled": self.prompt_builder.enable_knowledge_retrieval
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_info = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {}
        }
        
        try:
            # 检查LLM适配器
            if hasattr(self.llm_adapter, 'health_check'):
                llm_healthy = await self.llm_adapter.health_check()
                health_info["components"]["llm_adapter"] = "healthy" if llm_healthy else "unhealthy"
            else:
                health_info["components"]["llm_adapter"] = "unknown"
            
            # 检查prompt构建器
            if self.prompt_builder.template_manager.validate_templates():
                health_info["components"]["prompt_builder"] = "healthy"
            else:
                health_info["components"]["prompt_builder"] = "unhealthy"
                health_info["status"] = "degraded"
            
            # 检查回答生成器
            generator_stats = self.answer_generator.get_stats()
            if generator_stats["success_rate_percent"] > 50 or generator_stats["total_requests"] == 0:
                health_info["components"]["answer_generator"] = "healthy"
            else:
                health_info["components"]["answer_generator"] = "degraded"
                health_info["status"] = "degraded"
            
        except Exception as e:
            health_info["status"] = "unhealthy"
            health_info["error"] = str(e)
        
        return health_info
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            # 清理LLM适配器
            if hasattr(self.llm_adapter, 'cleanup'):
                await self.llm_adapter.cleanup()
        except Exception as e:
            logger.warning(f"QAManager cleanup error: {e}")
        
        logger.debug("🧹 QAManager cleanup completed")
    
    def reload_templates(self) -> None:
        """重新加载模板（支持运营热更新）"""
        try:
            self.prompt_builder.reload_templates()
            logger.info("🔄 QA templates reloaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to reload QA templates: {e}")
            raise ServiceError(f"Template reload failed: {e}")


# 工厂函数
def create_qa_manager(
    llm_adapter: BaseLLMAdapter,
    prompt_builder: Optional[QAPromptBuilder] = None,
    default_persona: Optional[PersonaConfig] = None
) -> QAManager:
    """创建QAManager实例
    
    Args:
        llm_adapter: LLM适配器实例
        prompt_builder: Prompt构建器实例
        default_persona: 默认人设配置
        
    Returns:
        QAManager: QA管理器实例
    """
    return QAManager(llm_adapter, prompt_builder, default_persona)


# 保持与原代码的兼容性
def get_qa_manager(llm_adapter: BaseLLMAdapter) -> QAManager:
    """获取QAManager实例（工厂函数）"""
    return create_qa_manager(llm_adapter)