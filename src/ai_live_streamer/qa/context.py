"""QA Dynamic Context Module

定义QA处理过程中使用的结构化上下文数据模型，
确保prompt构建和回答生成有充分的信息支撑。

Author: Claude Code
Date: 2025-08-06
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from datetime import datetime

from ..models.persona import PersonaConfig
from ..models.content import Question


@dataclass
class ChatMessage:
    """聊天消息结构"""
    role: str  # "viewer" 或 "streamer"
    content: str
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class QADynamicContext:
    """QA动态上下文数据模型
    
    包含构建高质量QA回答所需的所有上下文信息，
    支持模板化prompt生成和个性化回答。
    """
    
    # 核心问题信息
    question: Question
    
    # 人设相关信息
    persona: PersonaConfig
    persona_name: str = ""
    persona_style: str = ""
    
    # 直播相关信息  
    live_topic: str = ""
    current_script_segment: str = ""
    
    # 产品相关信息
    product_name: str = ""
    sku_id: str = "default"
    product_features: List[str] = field(default_factory=list)
    
    # 对话历史 (最近N轮对话)
    chat_history: List[ChatMessage] = field(default_factory=list)
    
    # 知识库检索结果 (为RAG预留)
    knowledge_base_info: str = ""
    related_topics: List[str] = field(default_factory=list)
    
    # 元信息
    session_id: str = ""
    timestamp: datetime = field(default_factory=datetime.utcnow)
    max_response_length: int = 50  # 适合语音播报的字数限制
    
    def __post_init__(self):
        """后处理：从PersonaConfig中提取简化信息"""
        if self.persona:
            self.persona_name = getattr(self.persona, 'name', self.persona.persona_id)
            self.persona_style = getattr(self.persona, 'tone', 'friendly')
    
    def get_recent_chat_history(self, limit: int = 3) -> List[ChatMessage]:
        """获取最近的对话历史
        
        Args:
            limit: 返回最近N轮对话
            
        Returns:
            List[ChatMessage]: 最近的对话消息
        """
        # 返回最近limit*2条消息 (limit轮问答)
        return self.chat_history[-(limit * 2):] if self.chat_history else []
    
    def format_chat_history_for_prompt(self, limit: int = 3) -> str:
        """格式化对话历史用于prompt
        
        Args:
            limit: 包含最近N轮对话
            
        Returns:
            str: 格式化的对话历史文本
        """
        recent_messages = self.get_recent_chat_history(limit)
        
        if not recent_messages:
            return "（无对话历史）"
        
        formatted_lines = []
        for msg in recent_messages:
            role_display = "观众" if msg.role == "viewer" else "主播"
            formatted_lines.append(f"{role_display}: {msg.content}")
        
        return "\n".join(formatted_lines)
    
    def add_chat_message(self, role: str, content: str) -> None:
        """添加聊天消息到历史记录
        
        Args:
            role: 消息角色 ("viewer" 或 "streamer")
            content: 消息内容
        """
        message = ChatMessage(role=role, content=content)
        self.chat_history.append(message)
        
        # 保持历史记录在合理范围内 (最近10轮对话)
        if len(self.chat_history) > 20:  # 10轮 * 2条消息
            self.chat_history = self.chat_history[-20:]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志和调试
        
        Returns:
            Dict[str, Any]: 上下文数据字典
        """
        return {
            "question_text": self.question.text if self.question else "",
            "persona_name": self.persona_name,
            "persona_style": self.persona_style,
            "live_topic": self.live_topic,
            "product_name": self.product_name,
            "sku_id": self.sku_id,
            "chat_history_count": len(self.chat_history),
            "knowledge_base_available": bool(self.knowledge_base_info),
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat()
        }
    
    @classmethod
    def from_question_data(
        cls, 
        question_data: Dict[str, Any], 
        persona: PersonaConfig,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> "QADynamicContext":
        """从问题数据创建上下文实例
        
        Args:
            question_data: 问题数据字典
            persona: 人设配置
            additional_context: 额外上下文信息
            
        Returns:
            QADynamicContext: 上下文实例
        """
        from ..models.content import Question, QuestionPriority
        
        # 创建Question对象
        question = Question(
            text=question_data.get("text", ""),
            question_id=question_data.get("question_id", f"q_{int(datetime.utcnow().timestamp())}"),
            priority=QuestionPriority.MEDIUM,
            timestamp=datetime.utcnow(),
            source="live_stream"
        )
        
        # 创建基础上下文
        context = cls(
            question=question,
            persona=persona,
            session_id=question_data.get("session_id", ""),
            sku_id=question_data.get("sku_id", "default"),
            product_name=question_data.get("product_name", "当前商品")
        )
        
        # 添加额外上下文
        if additional_context:
            context.live_topic = additional_context.get("live_topic", "")
            context.current_script_segment = additional_context.get("current_script", "")
            
            # 处理对话历史
            history_data = additional_context.get("chat_history", [])
            if isinstance(history_data, list):
                for item in history_data:
                    if isinstance(item, dict) and "role" in item and "content" in item:
                        context.add_chat_message(item["role"], item["content"])
        
        return context