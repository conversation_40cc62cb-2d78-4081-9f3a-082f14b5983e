"""锁管理器抽象接口和实现

设计原则:
- 简化优先: 仅保留核心锁操作接口
- Fail-Fast: 不提供自动降级机制
- 异步友好: 正确处理阻塞I/O

Author: Claude Code
Date: 2025-01-08
"""

from abc import ABC, abstractmethod
from typing import Optional


class AbstractLockManager(ABC):
    """锁管理器抽象基类 - 最小化接口设计"""
    
    @abstractmethod
    async def acquire_lock(
        self,
        lock_key: str,
        holder_id: str,
        timeout_seconds: int = 60
    ) -> bool:
        """获取锁
        
        Args:
            lock_key: 锁的唯一标识
            holder_id: 锁持有者ID
            timeout_seconds: 获取锁的超时时间（秒）
            
        Returns:
            bool: 是否成功获取锁
        """
        pass
    
    @abstractmethod
    async def release_lock(
        self,
        lock_key: str,
        holder_id: str
    ) -> bool:
        """释放锁
        
        Args:
            lock_key: 锁的唯一标识
            holder_id: 锁持有者ID
            
        Returns:
            bool: 是否成功释放锁
        """
        pass
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        pass