"""Base LLM Adapter interface

Defines the interface that all LLM adapters must implement to provide
a unified interface for different LLM providers.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, AsyncGenerator
from dataclasses import dataclass


@dataclass
class LLMMessage:
    """Standardized message format"""
    role: str  # system, user, assistant
    content: str
    name: Optional[str] = None


@dataclass
class LLMResponse:
    """Standardized response format"""
    content: str
    model: str
    usage: Dict[str, int]  # token usage
    finish_reason: str
    response_time_ms: float


class BaseLLMAdapter(ABC):
    """Base class for all LLM adapters"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize LLM adapter with configuration
        
        Args:
            config: Provider-specific configuration dictionary
        """
        self.config = config
        self.is_initialized = False
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the LLM adapter
        
        This should set up API clients, validate credentials, etc.
        Must be called before making requests.
        
        Raises:
            ServiceError: If initialization fails
        """
        pass
    
    @abstractmethod
    async def generate(
        self, 
        messages: List[LLMMessage], 
        **kwargs
    ) -> LLMResponse:
        """Generate a response from the LLM
        
        Args:
            messages: List of messages in conversation
            **kwargs: Additional generation parameters
            
        Returns:
            LLMResponse with generated content
            
        Raises:
            ServiceError: If generation fails
        """
        pass
    
    @abstractmethod
    async def generate_streaming(
        self, 
        messages: List[LLMMessage], 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming response from the LLM
        
        Args:
            messages: List of messages in conversation
            **kwargs: Additional generation parameters
            
        Yields:
            Partial response chunks as they become available
            
        Raises:
            ServiceError: If generation fails
        """
        pass
    
    @abstractmethod
    async def generate_answer(self, question: str) -> str:
        """Generate an answer for Q&A scenarios
        
        This is a specialized method for question-answering use cases,
        providing a simplified interface that takes a question string
        and returns an answer string.
        
        Args:
            question: The user's question
            
        Returns:
            The LLM's answer as a string
            
        Raises:
            ServiceError: If generation fails
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the adapter"""
        pass
    
    @property
    @abstractmethod
    def supports_streaming(self) -> bool:
        """Whether this adapter supports streaming responses"""
        pass
    
    @property
    def supports_caching(self) -> bool:
        """Whether this adapter supports caching
        
        Override this in adapters that support caching.
        Default implementation returns False.
        """
        return False
    
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Name of this LLM provider"""
        pass
    
    @property
    @abstractmethod
    def model_name(self) -> str:
        """Name of the model being used"""
        pass
    
    def validate_messages(self, messages: List[LLMMessage]) -> bool:
        """Validate message format
        
        Args:
            messages: Messages to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not messages:
            return False
        
        for msg in messages:
            if not msg.role or not msg.content:
                return False
            if msg.role not in ['system', 'user', 'assistant']:
                return False
        
        return True
    
    def get_default_parameters(self) -> Dict[str, Any]:
        """Get default generation parameters
        
        Returns:
            Dictionary of default parameters
        """
        return {
            'max_tokens': self.config.get('max_tokens', 4096),
            'temperature': self.config.get('temperature', 0.7),
            'stream': self.config.get('stream', True)
        }