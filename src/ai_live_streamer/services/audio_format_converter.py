"""音频格式转换器

将DashScope TTS返回的原始PCM数据转换为浏览器MediaSource兼容的格式。
解决Raw PCM数据与WebM容器格式不匹配的架构性问题。

功能：
- PCM to WebM/Opus转换
- PCM to MP3转换  
- 流式转换支持
- 低延迟处理

创建日期：2025-08-06
修复问题：DashScope PCM数据无法被前端MediaSource解析
"""

import io
import time
from typing import Optional, Dict, Any, List
from loguru import logger
from pydub import AudioSegment
from pydub.exceptions import CouldntDecodeError

from ..core.exceptions import ServiceError


class AudioFormatConverter:
    """音频格式转换器
    
    专门处理DashScope TTS返回的PCM数据转换为浏览器兼容格式。
    """
    
    def __init__(self):
        """初始化音频格式转换器"""
        self.supported_output_formats = {
            "webm": {"codec": "opus", "extension": "webm"},
            "mp3": {"codec": "mp3", "extension": "mp3"},
            "ogg": {"codec": "opus", "extension": "ogg"}
        }
        
        # DashScope PCM规格
        self.dashscope_sample_rate = 24000
        self.dashscope_channels = 1
        self.dashscope_bit_depth = 16
        
        logger.info("✅ AudioFormatConverter initialized - ready to convert PCM to browser-compatible formats (Default: MP3)")
    
    def pcm_to_webm_opus(self, pcm_data: bytes, sample_rate: int = 24000, channels: int = 1) -> bytes:
        """将PCM数据转换为WebM/Opus格式
        
        这是解决MediaSource兼容性问题的核心方法。
        
        Args:
            pcm_data: 原始PCM音频数据（16位，小端序）
            sample_rate: 采样率（默认24000Hz，匹配DashScope）
            channels: 声道数（默认1，单声道）
            
        Returns:
            bytes: WebM格式的音频数据
            
        Raises:
            ServiceError: 转换失败时抛出
        """
        if not pcm_data or len(pcm_data) == 0:
            logger.warning("Empty PCM data provided for WebM conversion")
            return b""
            
        try:
            start_time = time.time()
            
            # 验证PCM数据长度合理性
            expected_sample_count = len(pcm_data) // (channels * 2)  # 16-bit = 2 bytes per sample
            duration_ms = (expected_sample_count / sample_rate) * 1000
            
            if duration_ms > 30000:  # 超过30秒的音频块可能有问题
                logger.warning(f"⚠️ Unusually long audio chunk: {duration_ms:.1f}ms")
            
            # 创建AudioSegment从PCM数据
            audio_segment = AudioSegment(
                data=pcm_data,
                sample_width=2,  # 16-bit = 2 bytes
                frame_rate=sample_rate,
                channels=channels
            )
            
            logger.debug(f"📊 PCM audio loaded: {len(pcm_data)} bytes -> {duration_ms:.1f}ms")
            
            # 导出为WebM/Opus格式
            output_buffer = io.BytesIO()
            
            # 使用较高质量的Opus编码参数，优化流式播放
            export_params = {
                "format": "webm",
                "codec": "opus",
                "parameters": [
                    "-acodec", "libopus",
                    "-b:a", "96k",      # 96kbps比特率，平衡质量和大小
                    "-vbr", "on",       # 可变比特率
                    "-compression_level", "5",  # 压缩级别(0-10)
                    "-frame_duration", "20",    # 20ms帧长度，适合流式播放
                    "-packet_loss", "1"         # 包丢失恢复
                ]
            }
            
            audio_segment.export(output_buffer, **export_params)
            webm_data = output_buffer.getvalue()
            output_buffer.close()
            
            conversion_time_ms = (time.time() - start_time) * 1000
            compression_ratio = len(pcm_data) / len(webm_data) if webm_data else 0
            
            logger.info(f"✅ PCM -> WebM conversion: {len(pcm_data)} -> {len(webm_data)} bytes "
                       f"(ratio: {compression_ratio:.1f}x, time: {conversion_time_ms:.1f}ms)")
            
            return webm_data
            
        except CouldntDecodeError as e:
            error_msg = f"PCM decoding failed: {e}"
            logger.error(f"❌ {error_msg}")
            raise ServiceError(error_msg, "audio_format_converter", "PCM_DECODE_ERROR")
            
        except Exception as e:
            error_msg = f"PCM to WebM conversion failed: {e}"
            logger.error(f"❌ {error_msg}")
            raise ServiceError(error_msg, "audio_format_converter", "CONVERSION_ERROR")
    
    def pcm_to_mp3(self, pcm_data: bytes, sample_rate: int = 24000, channels: int = 1) -> bytes:
        """将PCM数据转换为MP3格式
        
        备选方案，用于不支持WebM/Opus的浏览器。
        
        Args:
            pcm_data: 原始PCM音频数据
            sample_rate: 采样率
            channels: 声道数
            
        Returns:
            bytes: MP3格式的音频数据
            
        Raises:
            ServiceError: 转换失败时抛出
        """
        if not pcm_data or len(pcm_data) == 0:
            logger.warning("Empty PCM data provided for MP3 conversion")
            return b""
            
        try:
            start_time = time.time()
            
            # 创建AudioSegment从PCM数据
            audio_segment = AudioSegment(
                data=pcm_data,
                sample_width=2,  # 16-bit
                frame_rate=sample_rate,
                channels=channels
            )
            
            # 导出为MP3格式 - 优化流式播放参数
            output_buffer = io.BytesIO()
            audio_segment.export(
                output_buffer,
                format="mp3",
                bitrate="96k",   # 降低码率减少延迟，仍保持良好质量
                parameters=[
                    "-q:a", "3",        # VBR质量级别（平衡质量与速度）
                    "-joint_stereo", "0",  # 禁用联合立体声（单声道更简单）
                    "-reservoir", "0",     # 禁用bit reservoir（减少延迟）
                    "-write_xing", "0"      # 不写Xing头（流式播放友好）
                ]
            )
            
            mp3_data = output_buffer.getvalue()
            output_buffer.close()
            
            conversion_time_ms = (time.time() - start_time) * 1000
            compression_ratio = len(pcm_data) / len(mp3_data) if mp3_data else 0
            
            logger.info(f"✅ PCM -> MP3 conversion: {len(pcm_data)} -> {len(mp3_data)} bytes "
                       f"(ratio: {compression_ratio:.1f}x, time: {conversion_time_ms:.1f}ms)")
            
            return mp3_data
            
        except Exception as e:
            error_msg = f"PCM to MP3 conversion failed: {e}"
            logger.error(f"❌ {error_msg}")
            raise ServiceError(error_msg, "audio_format_converter", "CONVERSION_ERROR")
    
    def convert_dashscope_pcm(self, pcm_data: bytes, target_format: str = "mp3") -> bytes:
        """转换DashScope TTS返回的PCM数据
        
        这是主要的转换入口点，专门处理DashScope的PCM格式。
        
        Args:
            pcm_data: DashScope返回的PCM数据
            target_format: 目标格式（webm, mp3, ogg）
            
        Returns:
            bytes: 转换后的音频数据
            
        Raises:
            ServiceError: 转换失败或不支持的格式
        """
        if target_format not in self.supported_output_formats:
            supported = list(self.supported_output_formats.keys())
            raise ServiceError(
                f"Unsupported target format: {target_format}. Supported: {supported}",
                "audio_format_converter",
                "UNSUPPORTED_FORMAT"
            )
        
        if not pcm_data:
            logger.debug("Empty PCM data - returning empty result")
            return b""
        
        logger.debug(f"🔄 Converting DashScope PCM data: {len(pcm_data)} bytes -> {target_format}")
        
        if target_format == "webm":
            return self.pcm_to_webm_opus(
                pcm_data, 
                self.dashscope_sample_rate, 
                self.dashscope_channels
            )
        elif target_format == "mp3":
            return self.pcm_to_mp3(
                pcm_data,
                self.dashscope_sample_rate,
                self.dashscope_channels
            )
        elif target_format == "ogg":
            # OGG/Opus格式（与WebM/Opus类似，但容器不同）
            return self._pcm_to_ogg_opus(pcm_data)
        else:
            # 不应该到达这里，但为了fail-fast原则
            raise ServiceError(
                f"Format conversion not implemented: {target_format}",
                "audio_format_converter", 
                "NOT_IMPLEMENTED"
            )
    
    def _pcm_to_ogg_opus(self, pcm_data: bytes) -> bytes:
        """将PCM转换为OGG/Opus格式"""
        try:
            audio_segment = AudioSegment(
                data=pcm_data,
                sample_width=2,
                frame_rate=self.dashscope_sample_rate,
                channels=self.dashscope_channels
            )
            
            output_buffer = io.BytesIO()
            audio_segment.export(
                output_buffer,
                format="ogg",
                codec="opus",
                parameters=["-acodec", "libopus", "-b:a", "96k"]
            )
            
            ogg_data = output_buffer.getvalue()
            output_buffer.close()
            
            return ogg_data
            
        except Exception as e:
            error_msg = f"PCM to OGG conversion failed: {e}"
            logger.error(f"❌ {error_msg}")
            raise ServiceError(error_msg, "audio_format_converter", "CONVERSION_ERROR")
    
    def get_mime_type(self, format_name: str) -> str:
        """获取格式对应的MIME类型
        
        Args:
            format_name: 格式名称（webm, mp3, ogg）
            
        Returns:
            str: MIME类型字符串
        """
        mime_types = {
            "webm": "audio/webm; codecs=\"opus\"",
            "mp3": "audio/mpeg",
            "ogg": "audio/ogg; codecs=\"opus\""
        }
        
        return mime_types.get(format_name, "application/octet-stream")
    
    def validate_pcm_data(self, pcm_data: bytes, sample_rate: int = 24000, channels: int = 1) -> Dict[str, Any]:
        """验证PCM数据的有效性
        
        Args:
            pcm_data: PCM数据
            sample_rate: 采样率
            channels: 声道数
            
        Returns:
            Dict: 验证结果和统计信息
        """
        if not pcm_data:
            return {
                "valid": False,
                "error": "Empty PCM data",
                "size_bytes": 0,
                "duration_ms": 0
            }
        
        try:
            sample_size = 2 * channels  # 16-bit * channels
            sample_count = len(pcm_data) // sample_size
            duration_ms = (sample_count / sample_rate) * 1000
            
            # 基本有效性检查
            is_valid = (
                len(pcm_data) % sample_size == 0 and  # 数据长度必须是样本大小的整数倍
                duration_ms > 0 and                   # 必须有正的时长
                duration_ms < 60000                   # 不应超过60秒
            )
            
            return {
                "valid": is_valid,
                "size_bytes": len(pcm_data),
                "sample_count": sample_count,
                "duration_ms": duration_ms,
                "sample_rate": sample_rate,
                "channels": channels,
                "bit_depth": 16
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "size_bytes": len(pcm_data),
                "duration_ms": 0
            }


# 全局转换器实例
_audio_converter = None


def get_audio_converter() -> AudioFormatConverter:
    """获取全局音频格式转换器实例
    
    Returns:
        AudioFormatConverter: 转换器实例
    """
    global _audio_converter
    if _audio_converter is None:
        _audio_converter = AudioFormatConverter()
    return _audio_converter