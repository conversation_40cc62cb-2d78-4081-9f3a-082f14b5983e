"""CosyVoice Token管理器

提供安全的临时Token生成和管理，避免在前端暴露API Key。
实现自动刷新和Token生命周期管理。
"""

import time
import asyncio
from typing import Optional, Dict, Any
from loguru import logger
import httpx

from ...core.exceptions import ServiceError


class CosyVoiceTokenManager:
    """CosyVoice临时Token管理器
    
    管理CosyVoice API的临时访问Token，提供以下功能：
    - 自动Token刷新
    - 线程安全的Token获取
    - 过期时间管理
    - 错误重试机制
    """
    
    def __init__(self, api_key: str):
        """初始化Token管理器
        
        Args:
            api_key: CosyVoice API密钥
        """
        if not api_key:
            raise ValueError("API key cannot be empty")
            
        self.api_key = api_key
        self.current_token: Optional[str] = None
        self.token_expires_at: float = 0
        self.token_lock = asyncio.Lock()
        
        # Token配置
        self.token_duration = 60  # Token有效期60秒
        self.refresh_threshold = 10  # 提前10秒刷新
        
        logger.info("CosyVoice Token Manager initialized")
        
    async def get_valid_token(self) -> str:
        """获取有效的API Key
        
        直接返回API Key，不再使用官方临时Token API。
        保持接口兼容性，为将来升级到官方临时Token做准备。
        
        Returns:
            API Key字符串
            
        Raises:
            ServiceError: API Key获取失败
        """
        # 直接返回API Key，不需要刷新逻辑
        if not self.api_key:
            raise ServiceError(
                "API key not available", 
                "token_manager", 
                "API_KEY_ERROR"
            )
        
        logger.debug("Returning API key for authentication")
        return self.api_key
    
    # 注意：原_refresh_token方法已移除
    # 现在直接使用API Key，不再调用官方临时Token API
    # 这是临时解决方案，当找到正确的官方端点时可以恢复
    
    def is_token_valid(self) -> bool:
        """检查当前API Key是否有效
        
        Returns:
            bool: API Key是否可用
        """
        return bool(self.api_key)
    
    def get_token_info(self) -> dict:
        """获取API Key状态信息
        
        Returns:
            包含API Key状态的字典
        """
        return {
            "has_token": bool(self.api_key),
            "is_valid": self.is_token_valid(),
            "expires_in": 0,  # API Key不过期
            "expires_at": None  # API Key不过期
        }
    
    async def invalidate_token(self):
        """使当前API Key失效
        
        用于兼容性，实际上API Key不会失效。
        """
        logger.debug("API Key invalidation requested (no-op)")
    
    async def test_api_connectivity(self) -> Dict[str, Any]:
        """测试API Key连通性
        
        Returns:
            包含连通性测试结果的字典
        """
        test_result = {
            "api_key_valid": False,
            "connectivity_ok": True,  # 不测试网络连接，仅验证API Key
            "token_generation_ok": False,
            "latency_ms": None,
            "error": None
        }
        
        start_time = time.time()
        
        try:
            # 测试API Key获取
            api_key = await self.get_valid_token()
            
            # 验证API Key格式
            if api_key and api_key.startswith('sk-'):
                test_result["token_generation_ok"] = True
                test_result["api_key_valid"] = True
                test_result["latency_ms"] = int((time.time() - start_time) * 1000)
                
                logger.info(f"API Key validation passed in {test_result['latency_ms']}ms")
            else:
                test_result["error"] = "Invalid API Key format"
                test_result["latency_ms"] = int((time.time() - start_time) * 1000)
                logger.error("API Key format validation failed")
            
        except ServiceError as e:
            test_result["error"] = str(e)
            test_result["latency_ms"] = int((time.time() - start_time) * 1000)
            logger.error(f"API Key test failed: {e}")
            
        except Exception as e:
            test_result["error"] = str(e)
            test_result["latency_ms"] = int((time.time() - start_time) * 1000)
            logger.error(f"API Key test error: {e}")
        
        return test_result


# 全局Token管理器实例
_token_manager: Optional[CosyVoiceTokenManager] = None


def get_token_manager() -> CosyVoiceTokenManager:
    """获取全局Token管理器实例
    
    延迟初始化模式，首次调用时根据配置创建实例。
    
    Returns:
        CosyVoiceTokenManager实例
        
    Raises:
        ServiceError: 配置错误或初始化失败
    """
    global _token_manager
    
    if _token_manager is None:
        try:
            import os
            from ...core.config import cfg
            
            # 严格按照配置读取API Key，fail-fast原则
            api_key = os.getenv('COSYVOICE_V2_API_KEY')
            
            if not api_key:
                raise ServiceError(
                    "COSYVOICE_V2_API_KEY environment variable not set", 
                    "token_manager", 
                    "CONFIG_ERROR"
                )
            
            # 验证API Key格式
            if not api_key.startswith('sk-'):
                raise ServiceError(
                    f"Invalid CosyVoice API key format: '{api_key[:10]}...'. API key must start with 'sk-'", 
                    "token_manager", 
                    "INVALID_API_KEY_FORMAT"
                )
            
            _token_manager = CosyVoiceTokenManager(api_key)
            logger.info("Global CosyVoice Token Manager created")
            
        except Exception as e:
            if isinstance(e, ServiceError):
                raise
            
            raise ServiceError(
                f"Failed to initialize token manager: {str(e)}", 
                "token_manager", 
                "INIT_ERROR"
            )
    
    return _token_manager


async def cleanup_token_manager():
    """清理全局Token管理器
    
    用于系统关闭时的资源清理。
    """
    global _token_manager
    
    if _token_manager:
        await _token_manager.invalidate_token()
        _token_manager = None
        logger.info("Token manager cleaned up")