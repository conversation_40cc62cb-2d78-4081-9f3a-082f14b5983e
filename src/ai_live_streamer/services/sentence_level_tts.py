"""Sentence-Level TTS Player with Interruption Support

基于design.md的句边界中断机制实现。支持精确的暂停和恢复播放功能。
"""

import asyncio
import re
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Callable, Dict, Any
from loguru import logger

from ..core.exceptions import ServiceError
from ..models.state import LiveStreamState
from ..models.content import Segment


@dataclass
class InterruptionPoint:
    """中断点数据类"""
    segment_idx: int
    sentence_idx: int
    timestamp: datetime
    reason: str = "qa_interruption"


@dataclass
class SentenceInfo:
    """句子信息"""
    text: str
    segment_idx: int
    sentence_idx: int
    duration_estimate_ms: int = 0


class SentenceLevelTTSPlayer:
    """支持句边界中断的TTS播放器
    
    核心功能：
    1. 分句播放：将段落分解为句子，逐句合成播放
    2. 中断检查：每句话前检查是否有中断请求
    3. 精确恢复：保存并从中断点精确恢复播放位置
    4. 状态管理：跟踪当前播放状态和进度
    """
    
    def __init__(self):
        self.logger = logger.bind(component="sentence_level_tts")
        
        # 播放状态
        self.is_playing = False
        self.is_paused = False
        self.current_segment_idx = 0
        self.current_sentence_idx = 0
        
        
        # 播放内容
        self.segments: List[Segment] = []
        self.current_sentences: List[SentenceInfo] = []
        
        # 外部依赖（将在后续集成）
        self.tts_engine = None  # 实际的TTS引擎
        self.audio_player = None  # 音频播放器
        
        self.logger.info("✅ SentenceLevelTTSPlayer initialized")
    
    def _split_text_into_sentences(self, text: str) -> List[str]:
        """将文本分解为句子
        
        使用正则表达式识别句子边界，支持中文和英文标点符号。
        
        Args:
            text: 输入文本
            
        Returns:
            句子列表
        """
        try:
            # 匹配中文和英文的句子结束符
            sentence_pattern = r'[.!?。！？；;]+\s*'
            
            # 分割句子
            sentences = re.split(sentence_pattern, text.strip())
            
            # 清理空句子和过短的句子
            clean_sentences = []
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 3:  # 过滤过短的句子
                    clean_sentences.append(sentence)
            
            self.logger.debug(f"Split text into {len(clean_sentences)} sentences")
            return clean_sentences
            
        except Exception as e:
            self.logger.error(f"❌ Failed to split text into sentences: {e}")
            # 回退：返回整个文本作为一个句子
            return [text.strip()] if text.strip() else []
    
    def _prepare_sentences(self, segments: List[Segment]) -> List[SentenceInfo]:
        """准备所有句子信息
        
        Args:
            segments: 段落列表
            
        Returns:
            句子信息列表
        """
        try:
            all_sentences = []
            
            for segment_idx, segment in enumerate(segments):
                segment_text = getattr(segment, 'content', str(segment))
                sentences = self._split_text_into_sentences(segment_text)
                
                for sentence_idx, sentence_text in enumerate(sentences):
                    sentence_info = SentenceInfo(
                        text=sentence_text,
                        segment_idx=segment_idx,
                        sentence_idx=sentence_idx,
                        duration_estimate_ms=len(sentence_text) * 60  # 粗略估算：1字符60ms
                    )
                    all_sentences.append(sentence_info)
            
            self.logger.info(f"Prepared {len(all_sentences)} sentences from {len(segments)} segments")
            return all_sentences
            
        except Exception as e:
            self.logger.error(f"❌ Failed to prepare sentences: {e}")
            return []
    
    async def play_with_interruption_support(
        self, 
        segments: List[Segment],
        start_segment_idx: int = 0,
        start_sentence_idx: int = 0
    ) -> Optional[InterruptionPoint]:
        """可中断的分句播放
        
        Args:
            segments: 要播放的段落列表
            start_segment_idx: 开始段落索引
            start_sentence_idx: 开始句子索引
            
        Returns:
            中断点信息（如果被中断）或None（如果完整播放）
        """
        try:
            self.logger.info(f"🎬 Starting sentence-level playback from segment {start_segment_idx}, sentence {start_sentence_idx}")
            
            self.segments = segments
            self.current_sentences = self._prepare_sentences(segments)
            
            if not self.current_sentences:
                self.logger.warning("No sentences to play")
                return None
            
            # 设置播放状态
            self.is_playing = True
            self.is_paused = False
            self._interrupt_point = None
            
            # 找到开始位置
            start_position = self._find_sentence_position(start_segment_idx, start_sentence_idx)
            if start_position == -1:
                self.logger.error(f"Invalid start position: segment {start_segment_idx}, sentence {start_sentence_idx}")
                return None
            
            # 逐句播放
            for i in range(start_position, len(self.current_sentences)):
                # 边界检查，防止并发访问导致的索引错误
                if i >= len(self.current_sentences):
                    self.logger.warning(f"Index {i} out of range for sentences list (length: {len(self.current_sentences)})")
                    break
                
                sentence_info = self.current_sentences[i]
                
                # 关键：每句话前检查中断请求
                if await self._check_interruption_request():
                    # 保存精确的中断点
                    self._interrupt_point = InterruptionPoint(
                        segment_idx=sentence_info.segment_idx,
                        sentence_idx=sentence_info.sentence_idx,
                        timestamp=datetime.utcnow(),
                        reason="qa_interruption"
                    )
                    
                    self.logger.info(f"⏸️ Playback interrupted at segment {sentence_info.segment_idx}, sentence {sentence_info.sentence_idx}")
                    self.is_playing = False
                    return self._interrupt_point
                
                # 播放当前句子
                await self._synthesize_and_play_sentence(sentence_info)
                
                # 更新当前位置
                self.current_segment_idx = sentence_info.segment_idx
                self.current_sentence_idx = sentence_info.sentence_idx
            
            # 播放完成
            self.logger.info("✅ Sentence-level playback completed successfully")
            self.is_playing = False
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Playback failed: {e}")
            self.is_playing = False
            raise ServiceError(f"Sentence-level playback failed: {e}", "sentence_level_tts")
    
    def _find_sentence_position(self, segment_idx: int, sentence_idx: int) -> int:
        """查找句子在总列表中的位置
        
        Args:
            segment_idx: 段落索引
            sentence_idx: 句子索引
            
        Returns:
            句子在总列表中的位置，-1表示未找到
        """
        try:
            for i, sentence_info in enumerate(self.current_sentences):
                if (sentence_info.segment_idx == segment_idx and 
                    sentence_info.sentence_idx == sentence_idx):
                    return i
            return -1
            
        except Exception as e:
            self.logger.error(f"❌ Failed to find sentence position: {e}")
            return -1
    
    async def _check_interruption_request(self) -> bool:
        """检查是否有中断请求
        
        Returns:
            True如果需要中断，False如果继续播放
        """
        try:
            # 检查中断标志
            # Deprecated interrupt mechanism removed
            if False:
                return True
            
            # 检查外部中断回调
            if self._interrupt_callback:
                should_interrupt = await self._interrupt_callback()
                if should_interrupt:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Interruption check failed: {e}")
            return False
    
    async def _synthesize_and_play_sentence(self, sentence_info: SentenceInfo) -> bool:
        """合成并播放单个句子
        
        Args:
            sentence_info: 句子信息
            
        Returns:
            是否成功播放
        """
        try:
            self.logger.debug(f"🎵 Playing sentence {sentence_info.segment_idx}-{sentence_info.sentence_idx}: {sentence_info.text[:50]}...")
            
            # TODO: 这里将集成实际的TTS合成和播放逻辑
            # 当前为模拟实现
            
            # 模拟合成时间
            synthesis_time = min(sentence_info.duration_estimate_ms / 1000, 2.0)  # 最多2秒
            await asyncio.sleep(0.2)  # 模拟合成延迟
            
            # 模拟播放时间  
            play_time = sentence_info.duration_estimate_ms / 1000
            await asyncio.sleep(0.3)  # 模拟播放（实际会是非阻塞的）
            
            self.logger.debug(f"✅ Sentence played successfully: {sentence_info.text[:30]}...")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to play sentence: {e}")
            return False
    
    async def resume_from_interruption(self, interruption_point: InterruptionPoint) -> Optional[InterruptionPoint]:
        """从中断点精确恢复播放
        
        Args:
            interruption_point: 之前保存的中断点
            
        Returns:
            新的中断点（如果再次被中断）或None（如果完整播放）
        """
        try:
            self.logger.info(f"🔄 Resuming playback from segment {interruption_point.segment_idx}, sentence {interruption_point.sentence_idx}")
            
            if not self.segments:
                self.logger.error("No segments loaded for resumption")
                return None
            
            # 从中断点继续播放
            return await self.play_with_interruption_support(
                self.segments,
                start_segment_idx=interruption_point.segment_idx,
                start_sentence_idx=interruption_point.sentence_idx
            )
            
        except Exception as e:
            self.logger.error(f"❌ Failed to resume from interruption: {e}")
            raise ServiceError(f"Resume playback failed: {e}", "sentence_level_tts")
    
    def request_interruption(self, callback: Optional[Callable] = None) -> bool:
        """请求中断当前播放
        
        Args:
            callback: 中断回调函数
            
        Returns:
            是否成功请求中断
        """
        try:
            if not self.is_playing:
                self.logger.warning("Cannot interrupt: not currently playing")
                return False
            
            if callback:
                pass  # Deprecated interrupt callback mechanism removed
            
            self.logger.info("⏸️ Interruption requested")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to request interruption: {e}")
            return False
    
    def get_current_position(self) -> Dict[str, Any]:
        """获取当前播放位置
        
        Returns:
            当前位置信息
        """
        return {
            "segment_idx": self.current_segment_idx,
            "sentence_idx": self.current_sentence_idx,
            "is_playing": self.is_playing,
            "is_paused": self.is_paused,
            "total_sentences": len(self.current_sentences),
            "timestamp": datetime.utcnow()
        }
    
    def get_progress_info(self) -> Dict[str, Any]:
        """获取播放进度信息
        
        Returns:
            进度信息
        """
        if not self.current_sentences:
            return {"progress_percent": 0.0, "sentences_completed": 0, "total_sentences": 0}
        
        # 找到当前位置
        current_position = self._find_sentence_position(self.current_segment_idx, self.current_sentence_idx)
        if current_position == -1:
            current_position = 0
        
        progress_percent = (current_position / len(self.current_sentences)) * 100.0
        
        return {
            "progress_percent": progress_percent,
            "sentences_completed": current_position,
            "total_sentences": len(self.current_sentences),
            "current_segment_idx": self.current_segment_idx,
            "current_sentence_idx": self.current_sentence_idx
        }


# 全局实例
sentence_level_tts_player = SentenceLevelTTSPlayer()