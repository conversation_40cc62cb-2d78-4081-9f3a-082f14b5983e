"""Base ETL components and task definitions

Provides foundation classes for Prefect-based ETL workflows with
standardized error handling, logging, and state management.
"""

from typing import Dict, List, Optional, Any, Type, Union
from datetime import datetime
from abc import ABC, abstractmethod
from enum import Enum
from pydantic import BaseModel, Field
from prefect import task, flow, get_run_logger
from prefect.blocks.system import Secret
from prefect.client.schemas import TaskRun, FlowRun
from loguru import logger

from ...core.exceptions import ServiceError, ValidationError
from ...core.config import cfg


class PipelineStatus(Enum):
    """ETL pipeline execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"


class ETLTaskResult(BaseModel):
    """Standardized ETL task result"""
    
    task_name: str = Field(description="Name of executed task")
    status: PipelineStatus = Field(description="Task execution status")
    processed_count: int = Field(default=0, description="Number of items processed")
    error_count: int = Field(default=0, description="Number of errors encountered")
    execution_time_seconds: float = Field(description="Task execution duration")
    
    # Data tracking
    input_data_size: int = Field(default=0, description="Size of input data")
    output_data_size: int = Field(default=0, description="Size of output data")
    
    # Error details
    errors: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Detailed error information"
    )
    
    # Metadata
    started_at: datetime = Field(description="Task start timestamp")
    completed_at: Optional[datetime] = Field(default=None, description="Task completion timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    def mark_completed(self) -> None:
        """Mark task as completed"""
        self.completed_at = datetime.utcnow()
        if self.error_count == 0:
            self.status = PipelineStatus.COMPLETED
        else:
            self.status = PipelineStatus.FAILED if self.error_count > self.processed_count else PipelineStatus.COMPLETED


class ETLBase(ABC):
    """Base class for ETL processors with common functionality"""
    
    def __init__(self, name: str) -> None:
        """Initialize ETL processor
        
        Args:
            name: Processor name for logging and identification
        """
        self.name = name
        self.logger = logger.bind(processor=name)
        
    @abstractmethod
    async def extract(self, source_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract data from source
        
        Args:
            source_config: Source configuration parameters
            
        Returns:
            List of extracted data items
            
        Raises:
            ServiceError: If extraction fails
        """
        pass
    
    @abstractmethod
    async def transform(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform extracted data
        
        Args:
            data: Raw extracted data
            
        Returns:
            List of transformed data items
            
        Raises:
            ValidationError: If transformation fails
        """
        pass
    
    @abstractmethod
    async def load(self, data: List[Dict[str, Any]], target_config: Dict[str, Any]) -> int:
        """Load transformed data to target
        
        Args:
            data: Transformed data to load
            target_config: Target configuration parameters
            
        Returns:
            Number of items successfully loaded
            
        Raises:
            ServiceError: If loading fails
        """
        pass
    
    async def validate_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate data quality and completeness
        
        Args:
            data: Data to validate
            
        Returns:
            List of validated data items
            
        Raises:
            ValidationError: If validation fails
        """
        validated_data = []
        errors = []
        
        for i, item in enumerate(data):
            try:
                # Basic validation
                if not isinstance(item, dict):
                    raise ValidationError(f"Item {i} is not a dictionary")
                
                if not item:
                    raise ValidationError(f"Item {i} is empty")
                
                # Add item-specific validation here
                validated_item = await self._validate_item(item)
                validated_data.append(validated_item)
                
            except Exception as e:
                error_info = {
                    "item_index": i,
                    "error": str(e),
                    "item_data": item if isinstance(item, dict) else str(item)
                }
                errors.append(error_info)
                self.logger.warning(f"Validation error for item {i}: {str(e)}")
        
        if errors and len(errors) > len(data) * 0.1:  # > 10% error rate
            raise ValidationError(
                f"Validation failed with {len(errors)} errors out of {len(data)} items",
                context={"errors": errors[:10]}  # Limit error details
            )
        
        self.logger.info(f"Validated {len(validated_data)} items with {len(errors)} errors")
        return validated_data
    
    async def _validate_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Validate individual data item
        
        Args:
            item: Data item to validate
            
        Returns:
            Validated item (may be modified)
            
        Raises:
            ValidationError: If item validation fails
        """
        # Default implementation - override in subclasses
        return item
    
    def create_result(self, task_name: str, started_at: datetime) -> ETLTaskResult:
        """Create ETL task result object
        
        Args:
            task_name: Name of the task
            started_at: Task start time
            
        Returns:
            ETLTaskResult instance
        """
        return ETLTaskResult(
            task_name=task_name,
            status=PipelineStatus.RUNNING,
            started_at=started_at,
            execution_time_seconds=0.0
        )


class ETLTask:
    """Decorator for creating Prefect ETL tasks with standardized error handling"""
    
    def __init__(
        self,
        name: str,
        retries: int = 3,
        retry_delay_seconds: int = 60,
        timeout_seconds: Optional[int] = None
    ) -> None:
        """Initialize ETL task decorator
        
        Args:
            name: Task name
            retries: Number of retry attempts
            retry_delay_seconds: Delay between retries
            timeout_seconds: Task timeout
        """
        self.name = name
        self.retries = retries
        self.retry_delay_seconds = retry_delay_seconds
        self.timeout_seconds = timeout_seconds
    
    def __call__(self, func):
        """Apply decorator to function"""
        @task(
            name=self.name,
            retries=self.retries,
            retry_delay_seconds=self.retry_delay_seconds,
            timeout_seconds=self.timeout_seconds,
            log_prints=True
        )
        async def wrapper(*args, **kwargs):
            task_logger = get_run_logger()
            started_at = datetime.utcnow()
            
            try:
                task_logger.info(f"Starting ETL task: {self.name}")
                
                # Execute the task
                result = await func(*args, **kwargs)
                
                # Calculate execution time
                execution_time = (datetime.utcnow() - started_at).total_seconds()
                task_logger.info(f"ETL task {self.name} completed in {execution_time:.2f}s")
                
                # Return result with timing info
                if isinstance(result, ETLTaskResult):
                    result.execution_time_seconds = execution_time
                    result.mark_completed()
                    return result
                else:
                    # Wrap simple results
                    task_result = ETLTaskResult(
                        task_name=self.name,
                        status=PipelineStatus.COMPLETED,
                        started_at=started_at,
                        execution_time_seconds=execution_time,
                        metadata={"raw_result": result}
                    )
                    return task_result
                
            except Exception as e:
                execution_time = (datetime.utcnow() - started_at).total_seconds()
                task_logger.error(f"ETL task {self.name} failed after {execution_time:.2f}s: {str(e)}")
                
                # Create failed result
                failed_result = ETLTaskResult(
                    task_name=self.name,
                    status=PipelineStatus.FAILED,
                    started_at=started_at,
                    execution_time_seconds=execution_time,
                    error_count=1,
                    errors=[{
                        "error_type": type(e).__name__,
                        "error_message": str(e),
                        "timestamp": datetime.utcnow().isoformat()
                    }]
                )
                
                # Re-raise for Prefect to handle retries
                raise ServiceError(
                    f"ETL task {self.name} failed: {str(e)}",
                    "etl_task",
                    "TASK_EXECUTION_ERROR"
                )
        
        return wrapper


# Utility functions for ETL workflows

async def get_etl_secret(secret_name: str) -> str:
    """Get secret value from Prefect Secret block
    
    Args:
        secret_name: Name of the secret
        
    Returns:
        Secret value
        
    Raises:
        ServiceError: If secret not found
    """
    try:
        secret_block = await Secret.load(secret_name)
        return secret_block.get()
    except Exception as e:
        raise ServiceError(
            f"Failed to retrieve secret {secret_name}: {str(e)}",
            "etl_secrets",
            "SECRET_ACCESS_ERROR"
        )


def create_etl_flow(
    name: str,
    description: str,
    timeout_minutes: int = 60,
    retries: int = 1
):
    """Decorator for creating ETL flows with standardized configuration
    
    Args:
        name: Flow name
        description: Flow description
        timeout_minutes: Flow timeout in minutes
        retries: Number of flow retries
    """
    def decorator(func):
        return flow(
            name=name,
            description=description,
            timeout_seconds=timeout_minutes * 60,
            retries=retries,
            log_prints=True
        )(func)
    
    return decorator


class ETLMetrics(BaseModel):
    """ETL pipeline execution metrics"""
    
    pipeline_name: str = Field(description="Pipeline name")
    flow_run_id: str = Field(description="Prefect flow run ID")
    started_at: datetime = Field(description="Pipeline start time")
    completed_at: Optional[datetime] = Field(default=None, description="Pipeline completion time")
    
    # Execution metrics
    total_tasks: int = Field(default=0, description="Total number of tasks")
    successful_tasks: int = Field(default=0, description="Number of successful tasks")
    failed_tasks: int = Field(default=0, description="Number of failed tasks")
    
    # Data metrics
    total_items_processed: int = Field(default=0, description="Total items processed")
    total_errors: int = Field(default=0, description="Total error count")
    
    # Performance metrics
    total_execution_time_seconds: float = Field(default=0.0, description="Total execution time")
    average_task_time_seconds: float = Field(default=0.0, description="Average task execution time")
    
    # Resource usage
    peak_memory_mb: Optional[float] = Field(default=None, description="Peak memory usage")
    cpu_time_seconds: Optional[float] = Field(default=None, description="CPU time used")
    
    def calculate_success_rate(self) -> float:
        """Calculate task success rate
        
        Returns:
            Success rate as percentage (0.0-100.0)
        """
        if self.total_tasks == 0:
            return 0.0
        return (self.successful_tasks / self.total_tasks) * 100.0
    
    def calculate_error_rate(self) -> float:
        """Calculate data error rate
        
        Returns:
            Error rate as percentage (0.0-100.0)
        """
        if self.total_items_processed == 0:
            return 0.0
        return (self.total_errors / self.total_items_processed) * 100.0