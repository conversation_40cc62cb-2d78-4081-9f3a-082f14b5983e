"""ETL Pipeline Manager for orchestrating and monitoring data workflows

Provides centralized management of ETL pipelines with scheduling,
monitoring, error handling, and version management capabilities.
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from uuid import uuid4
import json
from pathlib import Path
from loguru import logger

from prefect import get_client
from prefect.client.schemas import FlowRun, TaskRun

from .base import ETLMetrics, PipelineStatus
from .knowledge_processor import knowledge_base_etl_pipeline
from .script_processor import style_corpus_etl_pipeline
from ...core.exceptions import ServiceError
from ...core.config import cfg


class PipelineType(Enum):
    """Available ETL pipeline types"""
    KNOWLEDGE_BASE = "knowledge_base"
    STYLE_CORPUS = "style_corpus"
    FULL_REFRESH = "full_refresh"


class PipelineSchedule(Enum):
    """Pipeline execution schedules"""
    MANUAL = "manual"
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"


class PipelineManager:
    """Manages ETL pipeline execution and monitoring"""
    
    def __init__(self) -> None:
        """Initialize pipeline manager"""
        self.logger = logger.bind(component="pipeline_manager")
        self.active_runs: Dict[str, Dict[str, Any]] = {}
        
        # Pipeline configurations
        self.pipeline_configs = {
            PipelineType.KNOWLEDGE_BASE: {
                "flow_function": knowledge_base_etl_pipeline,
                "default_timeout_minutes": 120,
                "default_retries": 1,
                "description": "Knowledge base document processing pipeline"
            },
            PipelineType.STYLE_CORPUS: {
                "flow_function": style_corpus_etl_pipeline,
                "default_timeout_minutes": 90,
                "default_retries": 1,
                "description": "Style Corpus script processing pipeline"
            }
        }
    
    async def run_pipeline(
        self,
        pipeline_type: PipelineType,
        source_config: Dict[str, Any],
        target_config: Optional[Dict[str, Any]] = None,
        run_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Execute ETL pipeline
        
        Args:
            pipeline_type: Type of pipeline to run
            source_config: Source configuration parameters
            target_config: Target configuration parameters
            run_config: Runtime configuration (timeout, retries, etc.)
            
        Returns:
            Pipeline run ID
            
        Raises:
            ServiceError: If pipeline execution fails
        """
        run_id = str(uuid4())
        
        try:
            self.logger.info(f"Starting pipeline {pipeline_type.value} with run ID {run_id}")
            
            # Prepare configurations
            if target_config is None:
                target_config = self._get_default_target_config(pipeline_type)
            
            if run_config is None:
                run_config = {}
            
            # Get pipeline configuration
            pipeline_config = self.pipeline_configs[pipeline_type]
            flow_function = pipeline_config["flow_function"]
            
            # Track run start
            run_info = {
                "run_id": run_id,
                "pipeline_type": pipeline_type.value,
                "status": PipelineStatus.RUNNING,
                "started_at": datetime.utcnow(),
                "source_config": source_config,
                "target_config": target_config,
                "run_config": run_config
            }
            self.active_runs[run_id] = run_info
            
            # Execute pipeline
            if pipeline_type == PipelineType.FULL_REFRESH:
                result = await self._run_full_refresh_pipeline(source_config, target_config)
            else:
                result = await flow_function(source_config, target_config)
            
            # Update run status
            run_info["completed_at"] = datetime.utcnow()
            run_info["result"] = result
            run_info["status"] = PipelineStatus.COMPLETED
            
            # Generate metrics
            metrics = await self._generate_pipeline_metrics(run_id, result)
            run_info["metrics"] = metrics
            
            self.logger.info(f"Pipeline {pipeline_type.value} completed successfully: {run_id}")
            return run_id
            
        except Exception as e:
            # Mark run as failed
            if run_id in self.active_runs:
                self.active_runs[run_id]["status"] = PipelineStatus.FAILED
                self.active_runs[run_id]["error"] = str(e)
                self.active_runs[run_id]["completed_at"] = datetime.utcnow()
            
            self.logger.error(f"Pipeline {pipeline_type.value} failed: {str(e)}")
            raise ServiceError(
                f"Pipeline execution failed: {str(e)}",
                "pipeline_manager",
                "PIPELINE_EXECUTION_ERROR"
            )
    
    async def _run_full_refresh_pipeline(
        self,
        source_config: Dict[str, Any],
        target_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run full refresh pipeline (both knowledge base and style corpus)
        
        Args:
            source_config: Source configuration for both pipelines
            target_config: Target configuration for both pipelines
            
        Returns:
            Combined results from both pipelines
        """
        self.logger.info("Starting full refresh pipeline")
        
        # Prepare configurations for each pipeline
        kb_source_config = source_config.get("knowledge_base", {})
        kb_target_config = target_config.get("knowledge_base", {})
        
        style_source_config = source_config.get("style_corpus", {})
        style_target_config = target_config.get("style_corpus", {})
        
        # Run pipelines in parallel for efficiency
        kb_task = knowledge_base_etl_pipeline(kb_source_config, kb_target_config)
        style_task = style_corpus_etl_pipeline(style_source_config, style_target_config)
        
        try:
            kb_result, style_result = await asyncio.gather(kb_task, style_task)
            
            return {
                "knowledge_base": kb_result,
                "style_corpus": style_result,
                "pipeline_type": "full_refresh"
            }
            
        except Exception as e:
            self.logger.error(f"Full refresh pipeline failed: {str(e)}")
            raise
    
    def _get_default_target_config(self, pipeline_type: PipelineType) -> Dict[str, Any]:
        """Get default target configuration for pipeline type
        
        Args:
            pipeline_type: Type of pipeline
            
        Returns:
            Default target configuration
        """
        return {
            "create_new_version": True,
            "index_version": None,  # Auto-increment
            "enable_alias_switch": True
        }
    
    async def _generate_pipeline_metrics(
        self,
        run_id: str,
        pipeline_result: Dict[str, Any]
    ) -> ETLMetrics:
        """Generate metrics for pipeline run
        
        Args:
            run_id: Pipeline run ID
            pipeline_result: Pipeline execution result
            
        Returns:
            ETL metrics object
        """
        run_info = self.active_runs.get(run_id, {})
        
        # Calculate basic metrics
        started_at = run_info.get("started_at", datetime.utcnow())
        completed_at = run_info.get("completed_at", datetime.utcnow())
        total_time = (completed_at - started_at).total_seconds()
        
        # Count tasks and items
        total_tasks = 0
        successful_tasks = 0
        failed_tasks = 0
        total_items = 0
        total_errors = 0
        task_times = []
        
        # Process results based on pipeline type
        if isinstance(pipeline_result, dict):
            for task_name, task_result in pipeline_result.items():
                if hasattr(task_result, 'status'):
                    total_tasks += 1
                    if task_result.status == PipelineStatus.COMPLETED:
                        successful_tasks += 1
                    else:
                        failed_tasks += 1
                    
                    total_items += getattr(task_result, 'processed_count', 0)
                    total_errors += getattr(task_result, 'error_count', 0)
                    task_times.append(getattr(task_result, 'execution_time_seconds', 0))
        
        # Create metrics object
        metrics = ETLMetrics(
            pipeline_name=run_info.get("pipeline_type", "unknown"),
            flow_run_id=run_id,
            started_at=started_at,
            completed_at=completed_at,
            total_tasks=total_tasks,
            successful_tasks=successful_tasks,
            failed_tasks=failed_tasks,
            total_items_processed=total_items,
            total_errors=total_errors,
            total_execution_time_seconds=total_time,
            average_task_time_seconds=sum(task_times) / len(task_times) if task_times else 0.0
        )
        
        return metrics
    
    async def get_pipeline_status(self, run_id: str) -> Dict[str, Any]:
        """Get status of pipeline run
        
        Args:
            run_id: Pipeline run ID
            
        Returns:
            Pipeline status information
        """
        if run_id not in self.active_runs:
            raise ServiceError(
                f"Pipeline run not found: {run_id}",
                "pipeline_manager",
                "RUN_NOT_FOUND"
            )
        
        run_info = self.active_runs[run_id]
        
        # Calculate duration
        started_at = run_info["started_at"]
        completed_at = run_info.get("completed_at")
        
        if completed_at:
            duration_seconds = (completed_at - started_at).total_seconds()
        else:
            duration_seconds = (datetime.utcnow() - started_at).total_seconds()
        
        status_info = {
            "run_id": run_id,
            "pipeline_type": run_info["pipeline_type"],
            "status": run_info["status"].value if isinstance(run_info["status"], PipelineStatus) else run_info["status"],
            "started_at": started_at.isoformat(),
            "duration_seconds": duration_seconds
        }
        
        if completed_at:
            status_info["completed_at"] = completed_at.isoformat()
        
        if "error" in run_info:
            status_info["error"] = run_info["error"]
        
        if "metrics" in run_info:
            status_info["metrics"] = run_info["metrics"].model_dump()
        
        return status_info
    
    async def list_pipeline_runs(
        self,
        pipeline_type: Optional[PipelineType] = None,
        status: Optional[PipelineStatus] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """List pipeline runs with optional filtering
        
        Args:
            pipeline_type: Filter by pipeline type
            status: Filter by status
            limit: Maximum number of runs to return
            
        Returns:
            List of pipeline run information
        """
        runs = []
        
        for run_id, run_info in self.active_runs.items():
            # Apply filters
            if pipeline_type and run_info["pipeline_type"] != pipeline_type.value:
                continue
            
            if status and run_info["status"] != status:
                continue
            
            # Create summary info
            run_summary = {
                "run_id": run_id,
                "pipeline_type": run_info["pipeline_type"],
                "status": run_info["status"].value if isinstance(run_info["status"], PipelineStatus) else run_info["status"],
                "started_at": run_info["started_at"].isoformat()
            }
            
            if "completed_at" in run_info:
                run_summary["completed_at"] = run_info["completed_at"].isoformat()
                run_summary["duration_seconds"] = (
                    run_info["completed_at"] - run_info["started_at"]
                ).total_seconds()
            
            if "metrics" in run_info:
                metrics = run_info["metrics"]
                run_summary["items_processed"] = metrics.total_items_processed
                run_summary["success_rate"] = metrics.calculate_success_rate()
            
            runs.append(run_summary)
        
        # Sort by start time (most recent first)
        runs.sort(key=lambda x: x["started_at"], reverse=True)
        
        return runs[:limit]
    
    async def cancel_pipeline_run(self, run_id: str) -> bool:
        """Cancel a running pipeline
        
        Args:
            run_id: Pipeline run ID to cancel
            
        Returns:
            True if cancellation was successful
        """
        if run_id not in self.active_runs:
            return False
        
        run_info = self.active_runs[run_id]
        
        if run_info["status"] not in [PipelineStatus.RUNNING, PipelineStatus.PENDING]:
            return False
        
        try:
            # Update status
            run_info["status"] = PipelineStatus.CANCELLED
            run_info["completed_at"] = datetime.utcnow()
            
            self.logger.info(f"Cancelled pipeline run: {run_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel pipeline run {run_id}: {str(e)}")
            return False
    
    async def cleanup_old_runs(self, retention_days: int = 30) -> int:
        """Clean up old pipeline run records
        
        Args:
            retention_days: Number of days to retain run records
            
        Returns:
            Number of runs cleaned up
        """
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        cleaned_count = 0
        
        runs_to_remove = []
        for run_id, run_info in self.active_runs.items():
            if run_info["started_at"] < cutoff_date:
                runs_to_remove.append(run_id)
        
        for run_id in runs_to_remove:
            del self.active_runs[run_id]
            cleaned_count += 1
        
        if cleaned_count > 0:
            self.logger.info(f"Cleaned up {cleaned_count} old pipeline runs")
        
        return cleaned_count
    
    async def get_pipeline_health_summary(self) -> Dict[str, Any]:
        """Get overall pipeline health summary
        
        Returns:
            Health summary with statistics and status
        """
        total_runs = len(self.active_runs)
        if total_runs == 0:
            return {
                "status": "healthy",
                "total_runs": 0,
                "success_rate": 100.0,
                "recent_failures": 0
            }
        
        # Calculate statistics
        running_count = 0
        completed_count = 0
        failed_count = 0
        recent_failures = 0
        
        recent_cutoff = datetime.utcnow() - timedelta(hours=24)
        
        for run_info in self.active_runs.values():
            status = run_info["status"]
            started_at = run_info["started_at"]
            
            if status == PipelineStatus.RUNNING:
                running_count += 1
            elif status == PipelineStatus.COMPLETED:
                completed_count += 1
            elif status == PipelineStatus.FAILED:
                failed_count += 1
                if started_at > recent_cutoff:
                    recent_failures += 1
        
        success_rate = (completed_count / total_runs) * 100.0 if total_runs > 0 else 100.0
        
        # Determine overall health
        if recent_failures > 5:
            health_status = "critical"
        elif recent_failures > 2 or success_rate < 80:
            health_status = "degraded"
        elif running_count > 10:
            health_status = "busy"
        else:
            health_status = "healthy"
        
        return {
            "status": health_status,
            "total_runs": total_runs,
            "running_count": running_count,
            "completed_count": completed_count,
            "failed_count": failed_count,
            "success_rate": success_rate,
            "recent_failures": recent_failures
        }


# Utility functions for pipeline configuration

def create_pipeline_source_config(
    knowledge_base_dir: Optional[str] = None,
    script_dir: Optional[str] = None,
    document_types: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create source configuration for pipelines
    
    Args:
        knowledge_base_dir: Directory containing knowledge base documents
        script_dir: Directory containing script files
        document_types: List of valid document types
        
    Returns:
        Source configuration dictionary
    """
    config = {}
    
    if knowledge_base_dir:
        config["knowledge_base"] = {
            "input_directory": knowledge_base_dir,
            "document_types": document_types or ["spec", "faq", "policy", "story"],
            "recursive": True
        }
    
    if script_dir:
        config["style_corpus"] = {
            "script_directory": script_dir,
            "file_patterns": ["*.md", "*.txt"]
        }
    
    return config


def create_pipeline_target_config(
    create_new_versions: bool = True,
    knowledge_base_version: Optional[int] = None,
    style_corpus_version: Optional[int] = None
) -> Dict[str, Any]:
    """Create target configuration for pipelines
    
    Args:
        create_new_versions: Whether to create new index versions
        knowledge_base_version: Specific version for knowledge base index
        style_corpus_version: Specific version for style corpus index
        
    Returns:
        Target configuration dictionary
    """
    base_config = {
        "create_new_version": create_new_versions,
        "enable_alias_switch": True
    }
    
    config = {
        "knowledge_base": {
            **base_config,
            "index_version": knowledge_base_version
        },
        "style_corpus": {
            **base_config,
            "index_version": style_corpus_version
        }
    }
    
    return config