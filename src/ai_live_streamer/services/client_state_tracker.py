"""客户端状态追踪器

管理和追踪所有客户端的播放状态、缓冲健康度和网络性能指标。

Author: Claude Code
Date: 2025-08-08
"""

import asyncio
import time
from typing import Dict, Optional, List, Any, Set
from datetime import datetime, timedelta
from loguru import logger

from ..core.streaming_config import StreamingConfig
from ..models.client_state_models import (
    ClientState, ClientBufferHealth, ConnectionStatus, ClientStateHistory,
    AggregatedClientStats, NetworkMetrics, ClientPerformanceStats
)


class ClientStateTracker:
    """
    客户端状态追踪器
    
    核心职责：
    1. 维护所有客户端的实时状态
    2. 监控缓冲健康度和网络性能
    3. 支持断线重连和状态恢复
    4. 提供聚合统计和分析功能
    """
    
    def __init__(self, config: StreamingConfig):
        self.config = config
        
        # 客户端状态存储
        self._states: Dict[str, ClientState] = {}
        self._state_history: Dict[str, ClientStateHistory] = {}
        
        # 并发控制
        self._lock = asyncio.Lock()
        
        # 清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._cleanup_running = False
        
        # 统计信息
        self._total_clients_seen = 0
        self._start_time = datetime.utcnow()
        
        logger.info("客户端状态追踪器初始化完成")
        
    async def start(self) -> None:
        """启动状态追踪器"""
        self._cleanup_running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("客户端状态追踪器已启动")
        
    async def stop(self) -> None:
        """停止状态追踪器"""
        self._cleanup_running = False
        
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
                
        logger.info("客户端状态追踪器已停止")
        
    async def register_client(self, client_id: str, session_id: Optional[str] = None,
                            initial_data: Optional[Dict[str, Any]] = None) -> ClientState:
        """
        注册新客户端
        
        Args:
            client_id: 客户端标识符
            session_id: 会话标识符
            initial_data: 初始数据
            
        Returns:
            客户端状态
        """
        logger.info(f"Starting client registration: {client_id}, session: {session_id}")
        try:
            logger.debug(f"Attempting to acquire lock for: {client_id}")
            # Add timeout to lock acquisition to detect deadlock
            try:
                async with asyncio.timeout(5.0):  # 5 second timeout
                    async with self._lock:
                        logger.debug(f"Lock acquired for client registration: {client_id}")
                        if client_id in self._states:
                            logger.warning(f"客户端已存在，将更新状态: {client_id}")
                            existing_state = self._states[client_id]
                            existing_state.mark_connected()
                            return existing_state
                            
                        # 创建新的客户端状态
                        client_state = ClientState(
                            client_id=client_id,
                            current_playing_index=0,
                            buffered_until_index=0,
                            remaining_buffer_ms=0,
                            buffer_health=ClientBufferHealth.HEALTHY,
                            session_id=session_id
                        )
                        
                        # 如果有初始数据，更新状态
                        if initial_data:
                            client_state.update_from_request(initial_data)
                            
                        # 更新缓冲健康度
                        client_state.update_buffer_health(
                            self.config.client_buffer_healthy_ms,
                            self.config.client_buffer_at_risk_ms
                        )
                    
                        self._states[client_id] = client_state
                        self._state_history[client_id] = ClientStateHistory(client_id=client_id)
                        self._total_clients_seen += 1
                        
                        logger.info(f"客户端已注册: {client_id}, 会话: {session_id}")
                        return client_state
            except asyncio.TimeoutError:
                logger.error(f"Lock acquisition timeout for client {client_id} - possible deadlock!")
                raise
        except Exception as e:
            logger.error(f"Client registration failed: {e}")
            raise
            
    async def update_client_state(self, client_id: str, request_data: Dict[str, Any]) -> bool:
        """
        更新客户端状态
        
        Args:
            client_id: 客户端标识符
            request_data: 请求数据
            
        Returns:
            是否更新成功
        """
        try:
            async with self._lock:
                # 获取或创建客户端状态
                client_state = self._states.get(client_id)
                
                if not client_state:
                    # 自动注册新客户端
                    logger.info(f"自动注册新客户端: {client_id}")
                    client_state = await self.register_client(client_id, None, request_data)
                else:
                    # 更新现有状态
                    old_health = client_state.buffer_health
                    client_state.update_from_request(request_data)
                    
                    # 更新缓冲健康度
                    health_changed = client_state.update_buffer_health(
                        self.config.client_buffer_healthy_ms,
                        self.config.client_buffer_at_risk_ms
                    )
                    
                    # 记录健康度变化
                    if health_changed:
                        history = self._state_history.get(client_id)
                        if history:
                            history.add_health_record(client_state.buffer_health)
                            
                        logger.debug(f"客户端 {client_id} 缓冲健康度变化: "
                                   f"{old_health.value} -> {client_state.buffer_health.value}")
                        
                        # 记录严重情况
                        if client_state.buffer_health == ClientBufferHealth.DEPLETED:
                            logger.warning(f"客户端 {client_id} 缓冲耗尽: "
                                         f"剩余={client_state.remaining_buffer_ms:.1f}ms, "
                                         f"播放位置={client_state.current_playing_index}")
                            client_state.record_buffer_underrun()
                            
                # 确保状态为已连接
                if client_state.connection_status != ConnectionStatus.CONNECTED:
                    client_state.mark_connected()
                    
                return True
                
        except Exception as e:
            logger.error(f"更新客户端状态失败: {client_id}, 错误: {e}")
            return False
            
    async def get_client_state(self, client_id: str) -> Optional[ClientState]:
        """获取客户端状态"""
        async with self._lock:
            return self._states.get(client_id)
            
    async def get_active_clients(self) -> List[str]:
        """获取活跃客户端列表"""
        async with self._lock:
            active_clients = []
            current_time = datetime.utcnow()
            timeout_seconds = self.config.websocket_message_timeout
            
            for client_id, state in self._states.items():
                # 检查是否活跃
                if state.connection_status.is_active:
                    time_since_update = (current_time - state.last_update_time).total_seconds()
                    if time_since_update < timeout_seconds:
                        active_clients.append(client_id)
                        
            return active_clients
            
    async def handle_client_disconnect(self, client_id: str) -> None:
        """处理客户端断开连接"""
        async with self._lock:
            client_state = self._states.get(client_id)
            if client_state:
                client_state.mark_disconnected()
                
                history = self._state_history.get(client_id)
                if history:
                    history.add_connection_record(ConnectionStatus.DISCONNECTED)
                    
                logger.info(f"客户端断开连接: {client_id}")
                
    async def handle_client_reconnect(self, client_id: str, 
                                    last_played_index: int) -> Dict[str, Any]:
        """
        处理客户端重连
        
        Args:
            client_id: 客户端标识符
            last_played_index: 最后播放的索引
            
        Returns:
            重连响应信息
        """
        async with self._lock:
            client_state = self._states.get(client_id)
            
            if client_state:
                # 更新重连状态
                client_state.record_reconnect()
                client_state.current_playing_index = last_played_index
                client_state.mark_connected()
                
                history = self._state_history.get(client_id)
                if history:
                    history.add_connection_record(ConnectionStatus.CONNECTED)
                    
                logger.info(f"客户端 {client_id} 重连成功，从索引 {last_played_index} 恢复，"
                          f"这是第 {client_state.reconnect_count} 次重连")
                
                return {
                    "status": "reconnected",
                    "resume_from_index": last_played_index + 1,
                    "reconnect_count": client_state.reconnect_count,
                    "session_duration_minutes": client_state.session_duration_minutes
                }
            else:
                # 新客户端连接
                await self.register_client(client_id)
                
                logger.info(f"新客户端连接: {client_id}")
                return {
                    "status": "new_client",
                    "start_from_index": 0,
                    "welcome": True
                }
                
    async def calculate_safe_insertion_point(self, client_id: str) -> int:
        """
        计算安全的QA插入点
        
        Args:
            client_id: 客户端标识符
            
        Returns:
            安全插入点索引
        """
        async with self._lock:
            client_state = self._states.get(client_id)
            
            if not client_state:
                logger.warning(f"客户端不存在: {client_id}")
                return 0
                
            return client_state.calculate_safe_insertion_point(self.config)
            
    async def get_aggregated_stats(self) -> AggregatedClientStats:
        """获取聚合统计信息"""
        async with self._lock:
            active_clients = await self.get_active_clients()
            
            # 健康度分布
            health_distribution = {
                "healthy": 0,
                "at_risk": 0,
                "depleted": 0
            }
            
            # 其他统计
            total_latency = 0.0
            playing_indices = []
            total_requests = 0
            total_bytes = 0
            session_durations = []
            
            for client_id in active_clients:
                state = self._states.get(client_id)
                if state:
                    health_distribution[state.buffer_health.value] += 1
                    total_latency += state.network_metrics.latency_ms
                    playing_indices.append(state.current_playing_index)
                    total_requests += state.performance_stats.total_requests
                    total_bytes += state.performance_stats.total_bytes_received
                    session_durations.append(state.session_duration_minutes)
                    
            # 计算范围
            if playing_indices:
                min_index = min(playing_indices)
                max_index = max(playing_indices)
            else:
                min_index = max_index = 0
                
            # 计算平均值
            avg_latency = total_latency / len(active_clients) if active_clients else 0.0
            avg_session_duration = (sum(session_durations) / len(session_durations) 
                                  if session_durations else 0.0)
            
            return AggregatedClientStats(
                active_clients=len(active_clients),
                total_clients=len(self._states),
                health_distribution=health_distribution,
                average_latency_ms=avg_latency,
                playing_index_range=(min_index, max_index),
                total_requests=total_requests,
                total_bytes_served=total_bytes,
                average_session_duration_minutes=avg_session_duration
            )
            
    async def get_client_history(self, client_id: str, hours: int = 1) -> Dict[str, Any]:
        """
        获取客户端历史数据
        
        Args:
            client_id: 客户端标识符
            hours: 历史时间范围（小时）
            
        Returns:
            历史数据
        """
        async with self._lock:
            history = self._state_history.get(client_id)
            state = self._states.get(client_id)
            
            if not history or not state:
                return {}
                
            return {
                "client_id": client_id,
                "current_state": state.to_dict(),
                "health_distribution": history.get_health_distribution(hours),
                "connection_uptime": history.get_connection_uptime(hours),
                "health_history_size": len(history.health_history),
                "connection_history_size": len(history.connection_history)
            }
            
    async def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        async with self._lock:
            stats = await self.get_aggregated_stats()
            
            # 计算系统级指标
            uptime_minutes = (datetime.utcnow() - self._start_time).total_seconds() / 60
            
            # 缓冲健康度警报
            alerts = []
            if stats.depleted_percentage > 10:
                alerts.append(f"严重警报: {stats.depleted_percentage:.1f}% 客户端缓冲耗尽")
            elif stats.at_risk_percentage > 30:
                alerts.append(f"警告: {stats.at_risk_percentage:.1f}% 客户端缓冲风险")
                
            # 网络性能警报
            if stats.average_latency_ms > 500:
                alerts.append(f"网络延迟过高: {stats.average_latency_ms:.1f}ms")
                
            return {
                "system_uptime_minutes": round(uptime_minutes, 1),
                "total_clients_seen": self._total_clients_seen,
                "current_stats": stats.to_dict(),
                "alerts": alerts,
                "performance_rating": self._calculate_performance_rating(stats),
                "memory_usage_estimate_mb": self._estimate_memory_usage() / 1024 / 1024
            }
            
    def _calculate_performance_rating(self, stats: AggregatedClientStats) -> str:
        """计算性能评级"""
        if stats.healthy_percentage >= 90 and stats.average_latency_ms < 200:
            return "优秀"
        elif stats.healthy_percentage >= 75 and stats.average_latency_ms < 400:
            return "良好"
        elif stats.healthy_percentage >= 50 and stats.average_latency_ms < 600:
            return "一般"
        else:
            return "需要关注"
            
    def _estimate_memory_usage(self) -> int:
        """估算内存使用量（字节）"""
        # 粗略估算
        base_size = 0
        
        # 客户端状态
        for state in self._states.values():
            base_size += 2048  # 每个客户端状态大约2KB
            
        # 历史记录
        for history in self._state_history.values():
            base_size += len(history.health_history) * 32
            base_size += len(history.connection_history) * 32
            
        return base_size
        
    async def cleanup_inactive_clients(self) -> int:
        """清理不活跃的客户端"""
        async with self._lock:
            timeout_seconds = self.config.websocket_message_timeout * 3  # 3倍超时时间
            current_time = datetime.utcnow()
            
            clients_to_remove = []
            
            for client_id, state in self._states.items():
                # 检查最后更新时间
                time_since_update = (current_time - state.last_update_time).total_seconds()
                
                if time_since_update > timeout_seconds:
                    clients_to_remove.append(client_id)
                    
            # 移除不活跃的客户端
            for client_id in clients_to_remove:
                del self._states[client_id]
                
                # 保留有限的历史记录
                history = self._state_history.get(client_id)
                if history and len(history.health_history) < 10:
                    # 如果历史记录很少，也删除
                    del self._state_history[client_id]
                    
                logger.info(f"清理不活跃客户端: {client_id}")
                
            return len(clients_to_remove)
            
    async def _cleanup_loop(self) -> None:
        """清理任务循环"""
        cleanup_interval = 300  # 5分钟清理一次
        
        while self._cleanup_running:
            try:
                await asyncio.sleep(cleanup_interval)
                
                if not self._cleanup_running:
                    break
                    
                # 执行清理
                removed_count = await self.cleanup_inactive_clients()
                
                if removed_count > 0:
                    logger.info(f"清理完成，移除 {removed_count} 个不活跃客户端")
                    
                # 清理历史记录
                async with self._lock:
                    for history in self._state_history.values():
                        # 保持历史记录大小合理
                        if len(history.health_history) > history.max_entries:
                            history.health_history = history.health_history[-history.max_entries//2:]
                        if len(history.connection_history) > history.max_entries:
                            history.connection_history = history.connection_history[-history.max_entries//2:]
                            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理任务错误: {e}")
                
        logger.info("客户端状态清理循环已退出")
        
    async def get_debug_info(self) -> Dict[str, Any]:
        """获取调试信息"""
        async with self._lock:
            active_clients = await self.get_active_clients()
            
            return {
                "total_clients": len(self._states),
                "active_clients": len(active_clients),
                "inactive_clients": len(self._states) - len(active_clients),
                "total_clients_seen": self._total_clients_seen,
                "history_entries": len(self._state_history),
                "cleanup_running": self._cleanup_running,
                "memory_usage_estimate_mb": round(self._estimate_memory_usage() / 1024 / 1024, 2),
                "uptime_minutes": round(
                    (datetime.utcnow() - self._start_time).total_seconds() / 60, 1
                ),
                "sample_clients": [
                    {
                        "client_id": client_id[:8] + "...",
                        "buffer_health": state.buffer_health.value,
                        "playing_index": state.current_playing_index,
                        "last_update": state.last_update_time.isoformat()
                    }
                    for client_id, state in list(self._states.items())[:5]
                ]
            }
            
    async def reset(self) -> None:
        """重置状态追踪器"""
        async with self._lock:
            self._states.clear()
            self._state_history.clear()
            self._total_clients_seen = 0
            self._start_time = datetime.utcnow()
            
        logger.info("客户端状态追踪器已重置")


# 工厂函数

def create_client_state_tracker(config: StreamingConfig) -> ClientStateTracker:
    """创建客户端状态追踪器实例"""
    return ClientStateTracker(config)