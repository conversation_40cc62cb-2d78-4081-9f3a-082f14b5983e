"""Elasticsearch hybrid search service with BM25 + kNN + RRF fusion

Implements the core RAG retrieval system with hybrid search capabilities,
index versioning, and performance monitoring according to design specifications.
"""

from typing import Dict, List, Optional, Any, Tuple
import asyncio
import json
from datetime import datetime
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError
from loguru import logger
from sentence_transformers import SentenceTransformer

from ..core.config import cfg
from ..core.exceptions import ServiceError, TimeoutError
from ..models.constants import (
    MAX_RETRIEVAL_CANDIDATES,
    RRF_K_PARAMETER, 
    DEFAULT_EMBEDDING_DIMENSION,
    MAX_RETRIEVAL_LATENCY_MS
)


class DocumentSchema:
    """Document schema for Elasticsearch indices"""
    
    @staticmethod
    def get_knowledge_base_mapping() -> Dict[str, Any]:
        """Get mapping for knowledge base documents"""
        return {
            "mappings": {
                "properties": {
                    "doc_id": {"type": "keyword"},
                    "type": {"type": "keyword"},  # spec/faq/policy/story
                    "sku": {"type": "keyword"},
                    "brand": {"type": "keyword"},
                    "title": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart"
                    },
                    "chunk_text": {
                        "type": "text", 
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart"
                    },
                    "embedding": {
                        "type": "dense_vector",
                        "dims": DEFAULT_EMBEDDING_DIMENSION,
                        "index": True,
                        "similarity": "cosine"
                    },
                    "metadata": {"type": "object"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "version": {"type": "integer"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "index.knn": True,
                "analysis": {
                    "analyzer": {
                        "ik_max_word": {
                            "type": "ik_max_word"
                        },
                        "ik_smart": {
                            "type": "ik_smart"
                        }
                    }
                }
            }
        }
    
    @staticmethod
    def get_style_corpus_mapping() -> Dict[str, Any]:
        """Get mapping for Style Corpus (script sections)"""
        return {
            "mappings": {
                "properties": {
                    "section_id": {"type": "keyword"},
                    "section_type": {"type": "keyword"},  # opening/selling_point/objection_handling/closing/cta
                    "content": {
                        "type": "text",
                        "analyzer": "ik_max_word", 
                        "search_analyzer": "ik_smart"
                    },
                    "embedding": {
                        "type": "dense_vector",
                        "dims": DEFAULT_EMBEDDING_DIMENSION,
                        "index": True,
                        "similarity": "cosine"
                    },
                    "tone_tags": {"type": "keyword"},
                    "style_features": {"type": "object"},
                    "suitable_contexts": {"type": "keyword"},
                    "time_contexts": {"type": "keyword"},
                    "usage_count": {"type": "integer"},
                    "effectiveness_score": {"type": "float"},
                    "source_document": {"type": "keyword"},
                    "extracted_at": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "index.knn": True,
                "analysis": {
                    "analyzer": {
                        "ik_max_word": {
                            "type": "ik_max_word"
                        },
                        "ik_smart": {
                            "type": "ik_smart"
                        }
                    }
                }
            }
        }


class ElasticsearchService:
    """Elasticsearch service with connection management and basic operations"""
    
    def __init__(self) -> None:
        """Initialize Elasticsearch service"""
        self.client: Optional[AsyncElasticsearch] = None
        self.embedding_model: Optional[SentenceTransformer] = None
        
    async def initialize(self) -> None:
        """Initialize Elasticsearch client and embedding model"""
        try:
            # Initialize Elasticsearch client
            es_config = {
                "hosts": [cfg.elasticsearch_url],
                "timeout": 30,
                "max_retries": 3,
                "retry_on_timeout": True,
            }
            
            # Add authentication if configured
            if cfg.elasticsearch_username and cfg.elasticsearch_password:
                es_config["basic_auth"] = (cfg.elasticsearch_username, cfg.elasticsearch_password)
            
            self.client = AsyncElasticsearch(**es_config)
            
            # Test connection
            await self.client.ping()
            logger.info("Elasticsearch connection established")
            
            # Initialize embedding model
            self.embedding_model = SentenceTransformer('text-embedding-ada-002')
            logger.info("Embedding model loaded")
            
        except Exception as e:
            raise ServiceError(
                f"Failed to initialize Elasticsearch service: {str(e)}",
                "elasticsearch",
                "INIT_ERROR"
            )
    
    async def close(self) -> None:
        """Close Elasticsearch connection"""
        if self.client:
            await self.client.close()
            logger.info("Elasticsearch connection closed")
    
    async def create_index(self, index_name: str, mapping: Dict[str, Any]) -> bool:
        """Create index with specified mapping
        
        Args:
            index_name: Name of index to create
            mapping: Index mapping and settings
            
        Returns:
            True if created successfully
            
        Raises:
            ServiceError: If index creation fails
        """
        if not self.client:
            raise ServiceError("Elasticsearch client not initialized", "elasticsearch", "CLIENT_ERROR")
        
        try:
            # Delete existing index if present
            if await self.client.indices.exists(index=index_name):
                await self.client.indices.delete(index=index_name)
                logger.info(f"Deleted existing index: {index_name}")
            
            # Create new index
            await self.client.indices.create(index=index_name, body=mapping)
            logger.info(f"Created index: {index_name}")
            return True
            
        except Exception as e:
            raise ServiceError(
                f"Failed to create index {index_name}: {str(e)}",
                "elasticsearch",
                "INDEX_CREATE_ERROR"
            )
    
    async def index_document(self, index_name: str, doc_id: str, document: Dict[str, Any]) -> bool:
        """Index a single document
        
        Args:
            index_name: Target index name
            doc_id: Document ID
            document: Document data
            
        Returns:
            True if indexed successfully
        """
        if not self.client:
            raise ServiceError("Elasticsearch client not initialized", "elasticsearch", "CLIENT_ERROR")
        
        try:
            await self.client.index(
                index=index_name,
                id=doc_id,
                document=document
            )
            return True
            
        except Exception as e:
            raise ServiceError(
                f"Failed to index document {doc_id}: {str(e)}",
                "elasticsearch",
                "INDEX_DOC_ERROR"
            )
    
    async def bulk_index_documents(self, index_name: str, documents: List[Dict[str, Any]]) -> int:
        """Bulk index multiple documents
        
        Args:
            index_name: Target index name
            documents: List of documents with _id field
            
        Returns:
            Number of successfully indexed documents
        """
        if not self.client:
            raise ServiceError("Elasticsearch client not initialized", "elasticsearch", "CLIENT_ERROR")
        
        if not documents:
            return 0
        
        try:
            # Prepare bulk request
            bulk_body = []
            for doc in documents:
                action = {"index": {"_index": index_name, "_id": doc["_id"]}}
                bulk_body.append(action)
                
                doc_copy = doc.copy()
                del doc_copy["_id"]
                bulk_body.append(doc_copy)
            
            # Execute bulk request
            response = await self.client.bulk(body=bulk_body)
            
            # Count successful operations
            success_count = 0
            for item in response["items"]:
                if item["index"]["status"] in [200, 201]:
                    success_count += 1
            
            logger.info(f"Bulk indexed {success_count}/{len(documents)} documents to {index_name}")
            return success_count
            
        except Exception as e:
            raise ServiceError(
                f"Failed to bulk index documents: {str(e)}",
                "elasticsearch",
                "BULK_INDEX_ERROR"
            )
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text
        
        Args:
            text: Input text
            
        Returns:
            Embedding vector
        """
        if not self.embedding_model:
            raise ServiceError("Embedding model not initialized", "elasticsearch", "MODEL_ERROR")
        
        try:
            embedding = self.embedding_model.encode(text).tolist()
            return embedding
            
        except Exception as e:
            raise ServiceError(
                f"Failed to generate embedding: {str(e)}",
                "elasticsearch",
                "EMBEDDING_ERROR"
            )


class HybridSearchEngine:
    """Hybrid search engine with BM25 + kNN + RRF fusion"""
    
    def __init__(self, es_service: ElasticsearchService) -> None:
        """Initialize hybrid search engine
        
        Args:
            es_service: Elasticsearch service instance
        """
        self.es_service = es_service
        
    async def hybrid_search(
        self,
        index_name: str,
        query_text: str,
        field_filters: Optional[Dict[str, Any]] = None,
        max_candidates: int = MAX_RETRIEVAL_CANDIDATES,
        k_parameter: int = RRF_K_PARAMETER
    ) -> List[Dict[str, Any]]:
        """Perform hybrid search with BM25 + kNN + RRF fusion
        
        Args:
            index_name: Elasticsearch index to search
            query_text: Search query text
            field_filters: Optional field-based filters (type, sku, brand)
            max_candidates: Maximum candidates to retrieve
            k_parameter: RRF k parameter for fusion
            
        Returns:
            List of search results with RRF scores
            
        Raises:
            TimeoutError: If search exceeds latency threshold
            ServiceError: If search fails
        """
        start_time = datetime.utcnow()
        
        try:
            # Generate query embedding
            query_embedding = self.es_service.generate_embedding(query_text)
            
            # Build base filter
            base_filter = []
            if field_filters:
                for field, value in field_filters.items():
                    if isinstance(value, list):
                        base_filter.append({"terms": {field: value}})
                    else:
                        base_filter.append({"term": {field: value}})
            
            # Execute BM25 and kNN searches in parallel
            bm25_results, knn_results = await asyncio.gather(
                self._bm25_search(index_name, query_text, base_filter, max_candidates),
                self._knn_search(index_name, query_embedding, base_filter, max_candidates)
            )
            
            # Apply RRF fusion
            fused_results = self._apply_rrf_fusion(bm25_results, knn_results, k_parameter)
            
            # Check latency
            elapsed_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
            if elapsed_ms > MAX_RETRIEVAL_LATENCY_MS:
                logger.warning(f"Search latency {elapsed_ms:.0f}ms exceeds threshold {MAX_RETRIEVAL_LATENCY_MS}ms")
            
            logger.info(f"Hybrid search completed in {elapsed_ms:.0f}ms, found {len(fused_results)} results")
            return fused_results
            
        except Exception as e:
            elapsed_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
            if elapsed_ms > MAX_RETRIEVAL_LATENCY_MS:
                raise TimeoutError(
                    f"Search timeout after {elapsed_ms:.0f}ms",
                    "hybrid_search",
                    MAX_RETRIEVAL_LATENCY_MS / 1000
                )
            
            raise ServiceError(
                f"Hybrid search failed: {str(e)}",
                "elasticsearch",
                "SEARCH_ERROR"
            )
    
    async def _bm25_search(
        self,
        index_name: str,
        query_text: str,
        base_filter: List[Dict[str, Any]],
        size: int
    ) -> List[Tuple[str, float]]:
        """Execute BM25 text search
        
        Args:
            index_name: Index to search
            query_text: Query text
            base_filter: Base filters to apply
            size: Number of results to return
            
        Returns:
            List of (doc_id, score) tuples
        """
        query = {
            "size": size,
            "query": {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": query_text,
                                "fields": ["title^2", "chunk_text"],
                                "type": "best_fields",
                                "operator": "or"
                            }
                        }
                    ],
                    "filter": base_filter
                }
            },
            "_source": False
        }
        
        response = await self.es_service.client.search(index=index_name, body=query)
        
        results = []
        for hit in response["hits"]["hits"]:
            results.append((hit["_id"], hit["_score"]))
        
        return results
    
    async def _knn_search(
        self,
        index_name: str,
        query_embedding: List[float],
        base_filter: List[Dict[str, Any]],
        size: int
    ) -> List[Tuple[str, float]]:
        """Execute kNN vector search
        
        Args:
            index_name: Index to search
            query_embedding: Query embedding vector
            base_filter: Base filters to apply
            size: Number of results to return
            
        Returns:
            List of (doc_id, score) tuples
        """
        query = {
            "size": size,
            "knn": {
                "field": "embedding",
                "query_vector": query_embedding,
                "k": size,
                "num_candidates": size * 2,
                "filter": {
                    "bool": {
                        "filter": base_filter
                    }
                } if base_filter else None
            },
            "_source": False
        }
        
        response = await self.es_service.client.search(index=index_name, body=query)
        
        results = []
        for hit in response["hits"]["hits"]:
            results.append((hit["_id"], hit["_score"]))
        
        return results
    
    def _apply_rrf_fusion(
        self,
        bm25_results: List[Tuple[str, float]],
        knn_results: List[Tuple[str, float]],
        k: int
    ) -> List[Dict[str, Any]]:
        """Apply Reciprocal Rank Fusion to combine results
        
        Args:
            bm25_results: BM25 search results
            knn_results: kNN search results  
            k: RRF k parameter
            
        Returns:
            Fused results sorted by RRF score
        """
        # Create rank mappings
        bm25_ranks = {doc_id: rank + 1 for rank, (doc_id, _) in enumerate(bm25_results)}
        knn_ranks = {doc_id: rank + 1 for rank, (doc_id, _) in enumerate(knn_results)}
        
        # Calculate RRF scores
        all_docs = set(bm25_ranks.keys()) | set(knn_ranks.keys())
        rrf_scores = {}
        
        for doc_id in all_docs:
            rrf_score = 0.0
            
            if doc_id in bm25_ranks:
                rrf_score += 1.0 / (k + bm25_ranks[doc_id])
            
            if doc_id in knn_ranks:
                rrf_score += 1.0 / (k + knn_ranks[doc_id])
            
            rrf_scores[doc_id] = rrf_score
        
        # Sort by RRF score descending
        sorted_results = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Format results
        fused_results = []
        for doc_id, rrf_score in sorted_results:
            result = {
                "doc_id": doc_id,
                "rrf_score": rrf_score,
                "bm25_rank": bm25_ranks.get(doc_id),
                "knn_rank": knn_ranks.get(doc_id)
            }
            fused_results.append(result)
        
        return fused_results