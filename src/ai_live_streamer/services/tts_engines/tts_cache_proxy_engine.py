"""TTS Cache Proxy Engine - 企业级版本
包含熔断器、健康检查、流式缓存等高级特性
"""

import asyncio
import aiohttp
import time
from typing import Dict, Any, Optional, List, AsyncGenerator
from datetime import datetime, timedelta
from collections import deque
from dataclasses import dataclass
from enum import Enum
from loguru import logger
import hashlib

from .base import BaseTTSEngine
from ...models.audio import AudioChunk, AudioFormat, AudioStatus, AudioPriority
from ...models.streaming_audio import StreamingChunk, AudioStream, StreamingStatus
from ...core.exceptions import ServiceError

class CircuitState(Enum):
    """熔断器状态"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

@dataclass
class CircuitStats:
    """熔断器统计"""
    failure_count: int = 0
    success_count: int = 0
    last_failure_time: Optional[datetime] = None
    consecutive_successes: int = 0
    error_rate: float = 0.0

class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: int = 60,
                 success_threshold: int = 3,
                 error_rate_threshold: float = 0.25,
                 monitoring_window: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold
        self.error_rate_threshold = error_rate_threshold
        self.monitoring_window = monitoring_window
        
        self.state = CircuitState.CLOSED
        self.stats = CircuitStats()
        self.request_history = deque(maxlen=100)  # 最近100个请求
        self._lock = asyncio.Lock()
        
    async def call(self, func, *args, **kwargs):
        """通过熔断器调用函数"""
        async with self._lock:
            if self.state == CircuitState.OPEN:
                # 检查是否可以进入半开状态
                if self.stats.last_failure_time and \
                   datetime.now() - self.stats.last_failure_time > timedelta(seconds=self.recovery_timeout):
                    self.state = CircuitState.HALF_OPEN
                    logger.info("Circuit breaker entering HALF_OPEN state")
                else:
                    raise ServiceError("Circuit breaker is OPEN")
        
        # 执行调用
        start_time = time.time()
        error_occurred = None
        try:
            result = await func(*args, **kwargs)
            await self._on_success()
            return result
        except Exception as e:
            error_occurred = e
            await self._on_failure()
            raise
        finally:
            # 记录请求历史
            self.request_history.append({
                'timestamp': datetime.now(),
                'duration': time.time() - start_time,
                'success': error_occurred is None
            })
    
    async def _on_success(self):
        """成功调用处理"""
        async with self._lock:
            self.stats.success_count += 1
            self.stats.consecutive_successes += 1
            
            if self.state == CircuitState.HALF_OPEN:
                if self.stats.consecutive_successes >= self.success_threshold:
                    self.state = CircuitState.CLOSED
                    self.stats.failure_count = 0
                    self.stats.consecutive_successes = 0
                    logger.info("Circuit breaker is now CLOSED")
    
    async def _on_failure(self):
        """失败调用处理"""
        async with self._lock:
            self.stats.failure_count += 1
            self.stats.consecutive_successes = 0
            self.stats.last_failure_time = datetime.now()
            
            # 计算错误率
            recent_requests = [r for r in self.request_history 
                             if datetime.now() - r['timestamp'] < timedelta(seconds=self.monitoring_window)]
            if recent_requests:
                error_count = sum(1 for r in recent_requests if not r['success'])
                self.stats.error_rate = error_count / len(recent_requests)
            
            # 检查是否需要打开熔断器
            if self.state == CircuitState.CLOSED:
                if (self.stats.failure_count >= self.failure_threshold or 
                    self.stats.error_rate >= self.error_rate_threshold):
                    self.state = CircuitState.OPEN
                    logger.warning(f"Circuit breaker is now OPEN (failures: {self.stats.failure_count}, error_rate: {self.stats.error_rate:.2%})")
            elif self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
                logger.warning("Circuit breaker returned to OPEN state")

class StreamingCacheManager:
    """流式响应缓存管理器"""
    
    def __init__(self, cache_service_url: str):
        self.cache_service_url = cache_service_url
        self.pending_streams: Dict[str, List[bytes]] = {}
        self._lock = asyncio.Lock()
    
    async def start_caching(self, cache_key: str):
        """开始缓存流式数据"""
        async with self._lock:
            self.pending_streams[cache_key] = []
    
    async def append_chunk(self, cache_key: str, chunk: bytes):
        """追加流数据块"""
        async with self._lock:
            if cache_key in self.pending_streams:
                self.pending_streams[cache_key].append(chunk)
    
    async def finalize_cache(self, cache_key: str, text: str, voice: str, session: aiohttp.ClientSession):
        """完成缓存并上传"""
        async with self._lock:
            if cache_key not in self.pending_streams:
                return
            
            chunks = self.pending_streams.pop(cache_key)
            if not chunks:
                return
        
        # 合并所有音频块
        audio_data = b''.join(chunks)
        
        # 异步上传到缓存服务
        try:
            async with session.post(
                f"{self.cache_service_url}/cache/upload",
                data={
                    'key': cache_key,
                    'text': text,
                    'voice': voice,
                    'audio': audio_data
                }
            ) as response:
                if response.status == 200:
                    logger.info(f"Successfully cached streaming audio: {cache_key}")
                else:
                    logger.warning(f"Failed to cache streaming audio: {response.status}")
        except Exception as e:
            logger.error(f"Error caching streaming audio: {e}")

class TTSCacheProxyEngine(BaseTTSEngine):
    """增强版 TTS 缓存代理引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # 基础配置
        self.cache_service_url = config.get('cache_service_url', 'http://localhost:22243')
        # Cache miss engine - required for handling cache misses
        self.cache_miss_engine_config = config.get('cache_miss_engine')
        if not self.cache_miss_engine_config:
            raise ServiceError("cache_miss_engine must be configured for tts_cache_proxy")
        self.timeout_seconds = config.get('timeout_seconds', 10)
        self.enable_streaming_cache = config.get('enable_streaming_cache', True)
        
        # 熔断器配置
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=config.get('circuit_failure_threshold', 5),
            recovery_timeout=config.get('circuit_recovery_timeout', 60),
            success_threshold=config.get('circuit_success_threshold', 3),
            error_rate_threshold=config.get('circuit_error_rate_threshold', 0.25)
        )
        
        # 组件
        self.cache_miss_engine = None  # Will be initialized in initialize()
        self.session: Optional[aiohttp.ClientSession] = None
        self.streaming_cache_manager = StreamingCacheManager(self.cache_service_url)
        
        # 请求合并和负缓存（防止缓存穿透和雪崩）
        self._pending_requests: Dict[str, asyncio.Future] = {}
        self._negative_cache: Dict[str, float] = {}
        self._negative_cache_ttl = config.get('negative_cache_ttl', 60)
        
        # 健康检查
        self.health_check_interval = config.get('health_check_interval', 30)
        self.last_health_check = 0
        self.service_healthy = True
        
        # 统计
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'streaming_requests': 0,
            'cache_miss_engine_uses': 0,  # Track cache miss engine usage
            'circuit_opens': 0,
            'total_requests': 0,
            'total_errors': 0,
            'request_coalescing_hits': 0,  # Track coalesced requests
            'negative_cache_hits': 0  # Track negative cache hits
        }
    
    async def initialize(self) -> None:
        """初始化引擎"""
        # 创建 HTTP 会话
        timeout = aiohttp.ClientTimeout(total=self.timeout_seconds)
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=30,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector
        )
        
        # Initialize cache miss engine
        if self.cache_miss_engine_config:
            from ..factories import TTSEngineFactory
            try:
                self.cache_miss_engine = TTSEngineFactory.create_engine(
                    self.cache_miss_engine_config
                )
                await self.cache_miss_engine.initialize()
                logger.info(f"Cache miss engine initialized: {self.cache_miss_engine_config}")
            except Exception as e:
                logger.error(f"Failed to initialize cache_miss_engine: {e}")
                raise ServiceError(f"cache_miss_engine initialization failed: {e}")
        
        # 执行初始健康检查
        await self._health_check()
        
        self.is_initialized = True
        logger.info(f"TTS Cache Proxy Engine initialized - URL: {self.cache_service_url}")
    
    async def synthesize(self, text: str, voice: str = None, **kwargs) -> AudioChunk:
        """合成音频 - 支持熔断器"""
        if not self.is_initialized:
            await self.initialize()
        
        self.stats['total_requests'] += 1
        voice = voice or self.config.get('default_voice', 'longanran')
        
        # 定期健康检查
        await self._periodic_health_check()
        
        # 如果服务不健康或熔断器打开，快速失败
        if not self.service_healthy or self.circuit_breaker.state == CircuitState.OPEN:
            raise ServiceError(
                f"TTS cache service unavailable: healthy={self.service_healthy}, circuit={self.circuit_breaker.state.value}",
                service_name="tts_cache_proxy"
            )
        
        # 尝试使用缓存服务
        try:
            return await self.circuit_breaker.call(
                self._synthesize_with_cache,
                text, voice, **kwargs
            )
        except ServiceError as e:
            if "Circuit breaker is OPEN" in str(e):
                self.stats['circuit_opens'] += 1
                logger.error("Circuit breaker triggered - failing fast")
            else:
                self.stats['total_errors'] += 1
                logger.error(f"Cache service error: {e}")
            
            # Fail fast - no fallback
            raise
    
    async def _synthesize_with_cache(self, text: str, voice: str, **kwargs) -> AudioChunk:
        """使用缓存服务合成"""
        request_data = {
            'text': text,
            'language': 'zh',
            'voice': voice,
            'format': 'pcm',
            'sample_rate': 24000
        }
        
        async with self.session.post(
            f"{self.cache_service_url}/speech/",
            json=request_data
        ) as response:
            if response.status != 200:
                raise ServiceError(f"Cache service returned {response.status}")
            
            audio_data = await response.read()
            
            # 检查缓存命中
            if response.headers.get('X-Cache-Hit') == 'true':
                self.stats['cache_hits'] += 1
                logger.debug(f"Cache HIT: {text[:30]}...")
            else:
                self.stats['cache_misses'] += 1
                logger.debug(f"Cache MISS: {text[:30]}...")
            
            # 创建音频块
            return AudioChunk(
                chunk_id=f"cache_{hash(text)}",
                text=text,
                audio_data=audio_data,
                format=AudioFormat.PCM,
                status=AudioStatus.READY,
                priority=kwargs.get('priority', AudioPriority.NORMAL),
                metadata={
                    'voice': voice,
                    'sample_rate': 24000,
                    'cache_hit': response.headers.get('X-Cache-Hit') == 'true',
                    'response_time': response.headers.get('X-Response-Time')
                }
            )
    
    async def synthesize_streaming(self, text: str, voice: str = None, **kwargs) -> AsyncGenerator[StreamingChunk, None]:
        """流式合成 - 智能缓存代理策略"""
        if not self.is_initialized:
            await self.initialize()
        
        self.stats['streaming_requests'] += 1
        voice = voice or self.config.get('default_voice', 'longanran')
        cache_key = self._generate_cache_key(text, voice)
        
        # 1. 检查负缓存（防止缓存穿透）
        if cache_key in self._negative_cache:
            if time.time() - self._negative_cache[cache_key] < self._negative_cache_ttl:
                self.stats['negative_cache_hits'] += 1
                raise ServiceError("Content marked as invalid (negative cache)")
        
        # 2. 尝试从缓存获取
        try:
            full_audio = await self._try_get_cached_audio(text, voice)
            if full_audio:
                # 将缓存的音频分块返回，模拟流式
                chunk_size = 4800  # 100ms @ 24kHz, 16-bit
                for i in range(0, len(full_audio), chunk_size):
                    chunk_data = full_audio[i:i+chunk_size]
                    yield StreamingChunk(
                        chunk_id=f"cached_{hash(text)}_{i}",
                        stream_id=f"cache_stream_{hash(text)}",
                        audio_stream=AudioStream(
                            stream_id=f"cache_stream_{hash(text)}_{i}",
                            audio_data=chunk_data,
                            sample_rate=24000,
                            channels=1,
                            bit_depth=16,
                            duration_ms=len(chunk_data) // 48,  # 24kHz, 16bit, mono
                            sequence_number=i // chunk_size,
                            is_final=(i + chunk_size >= len(full_audio)),
                            text_content=text if i == 0 else None,
                            voice_profile=None
                        ),
                        priority=kwargs.get('priority', AudioPriority.NORMAL),
                        status=StreamingStatus.STREAMING,
                        context_type=kwargs.get('context_type', 'narration')
                    )
                return
        except Exception as e:
            logger.warning(f"Cache check failed: {e}")
        
        # 3. 请求合并（防止缓存雪崩）
        if cache_key in self._pending_requests:
            self.stats['request_coalescing_hits'] += 1
            logger.info(f"Waiting for pending request: {cache_key[:8]}...")
            try:
                # 等待已有请求完成
                result = await self._pending_requests[cache_key]
                # 流式返回结果
                async for chunk in self._stream_cached_audio(result, text, voice, **kwargs):
                    yield chunk
                return
            except Exception as e:
                logger.error(f"Pending request failed: {e}")
        
        # 4. 缓存未命中，使用cache_miss_engine
        if not self.cache_miss_engine:
            raise ServiceError("Cache miss and no cache_miss_engine available")
        
        self.stats['cache_miss_engine_uses'] += 1
        logger.info(f"Cache miss, using cache_miss_engine for: {text[:30]}...")
        
        # 创建请求future供其他相同请求等待
        future = asyncio.Future()
        self._pending_requests[cache_key] = future
        
        try:
            audio_chunks = []
            async for chunk in self.cache_miss_engine.synthesize_streaming(text, voice, **kwargs):
                audio_chunks.append(chunk)
                yield chunk  # 实时返回给客户端
            
            # 合并音频数据用于缓存
            audio_data = b''.join([
                chunk.audio_stream.audio_data if chunk.audio_stream else b''
                for chunk in audio_chunks
            ])
            
            # 设置future结果供等待者使用
            future.set_result(audio_data)
            
            # 异步写入缓存（不阻塞响应）
            if audio_data:
                asyncio.create_task(self._cache_audio_async(text, voice, audio_data))
            
        except Exception as e:
            # 记录负缓存（如果是内容无效错误）
            if "invalid" in str(e).lower() or "not found" in str(e).lower():
                self._negative_cache[cache_key] = time.time()
                logger.warning(f"Added to negative cache: {cache_key[:8]}...")
            future.set_exception(e)
            raise
        finally:
            # 清理pending请求
            self._pending_requests.pop(cache_key, None)
    
    async def _stream_cached_audio(self, audio_data: bytes, text: str, voice: str, **kwargs) -> AsyncGenerator[StreamingChunk, None]:
        """将缓存的音频数据转换为流式输出"""
        chunk_size = 4800  # 100ms @ 24kHz, 16-bit
        for i in range(0, len(audio_data), chunk_size):
            chunk_data = audio_data[i:i+chunk_size]
            yield StreamingChunk(
                chunk_id=f"cached_{hash(text)}_{i}",
                stream_id=f"cache_stream_{hash(text)}",
                audio_stream=AudioStream(
                    stream_id=f"cache_stream_{hash(text)}_{i}",
                    audio_data=chunk_data,
                    sample_rate=24000,
                    channels=1,
                    bit_depth=16,
                    duration_ms=len(chunk_data) // 48,
                    sequence_number=i // chunk_size,
                    is_final=(i + chunk_size >= len(audio_data)),
                    text_content=text if i == 0 else None,
                    voice_profile=None
                ),
                priority=kwargs.get('priority', AudioPriority.NORMAL),
                status=StreamingStatus.STREAMING,
                context_type=kwargs.get('context_type', 'narration')
            )
    
    async def _check_cache_exists(self, text: str, voice: str) -> bool:
        """通过试探性请求检查缓存是否存在"""
        try:
            # 使用 /speech/ 端点，但设置极短超时
            # 如果缓存命中会立即返回，否则会超时
            request_data = {
                'text': text,
                'language': 'zh',
                'voice': voice,
                'format': 'pcm',
                'sample_rate': 24000
            }
            
            # 创建一个短超时的session
            quick_timeout = aiohttp.ClientTimeout(total=0.5)  # 500ms超时
            async with aiohttp.ClientSession(timeout=quick_timeout) as quick_session:
                async with quick_session.post(
                    f"{self.cache_service_url}/speech/",
                    json=request_data
                ) as response:
                    # 检查是否是缓存命中
                    if response.status == 200 and response.headers.get('X-Cache-Hit') == 'true':
                        # 缓存命中，但我们不需要音频数据
                        return True
                    return False
        except asyncio.TimeoutError:
            # 超时意味着需要合成，即缓存未命中
            return False
        except Exception:
            # 其他错误，假定缓存未命中
            return False
    
    def _generate_cache_key(self, text: str, voice: str) -> str:
        """生成缓存键 - 与tts-cache-cosyvoice保持一致"""
        # tts-cache服务使用 text:voice:format:sample_rate
        format = "pcm"  # 固定格式
        sample_rate = 24000  # 固定采样率
        cache_string = f"{text}:{voice}:{format}:{sample_rate}"
        return hashlib.sha256(cache_string.encode()).hexdigest()
    
    async def _periodic_health_check(self):
        """定期健康检查"""
        current_time = time.time()
        if current_time - self.last_health_check > self.health_check_interval:
            await self._health_check()
            self.last_health_check = current_time
    
    async def _health_check(self):
        """健康检查"""
        try:
            async with self.session.get(f"{self.cache_service_url}/") as response:
                self.service_healthy = response.status == 200
                if self.service_healthy:
                    data = await response.json()
                    logger.debug(f"Cache service healthy: {data.get('status')}")
                else:
                    logger.warning(f"Cache service unhealthy: {response.status}")
        except Exception as e:
            self.service_healthy = False
            logger.error(f"Health check failed: {e}")
    
    async def _try_get_cached_audio(self, text: str, voice: str) -> Optional[bytes]:
        """尝试从缓存服务获取音频"""
        request_data = {
            'text': text,
            'language': 'zh', 
            'voice': voice,
            'format': 'pcm',
            'sample_rate': 24000
        }
        
        try:
            async with self.session.post(
                f"{self.cache_service_url}/speech/",
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=2)  # 2秒超时
            ) as response:
                if response.status == 200:
                    # 检查是否缓存命中
                    if response.headers.get('X-Cache-Hit') == 'true':
                        self.stats['cache_hits'] += 1
                        logger.info(f"Cache HIT (streaming): {text[:30]}...")
                        return await response.read()
                    else:
                        self.stats['cache_misses'] += 1
                        return None
        except Exception as e:
            logger.debug(f"Cache fetch failed: {e}")
            return None

    async def _cache_audio_async(self, text: str, voice: str, audio_data: bytes):
        """异步缓存音频 - 通过调用缓存服务的/cache/端点上传"""
        try:
            # 使用新的 /cache/ 端点上传音频数据
            form_data = aiohttp.FormData()
            form_data.add_field('text', text)
            form_data.add_field('voice', voice)
            form_data.add_field('format', 'pcm')
            form_data.add_field('sample_rate', '24000')
            form_data.add_field('audio_data', audio_data)
            
            async with self.session.put(
                f"{self.cache_service_url}/cache/",
                data=form_data,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"Audio cached successfully: {result.get('cache_key', '')[:8]}... size: {result.get('size', 0)} bytes")
                else:
                    logger.warning(f"Failed to cache audio: {response.status}")
                    
        except Exception as e:
            logger.error(f"Error in async caching: {e}")
    
    async def get_voices(self) -> List[Dict[str, str]]:
        """获取可用音色"""
        # Return a static list since we don't have a fallback engine
        # This could be enhanced to query the cache service for available voices
        return [
            {"voice": "longanran", "language": "zh"},
            {"voice": "longyuan", "language": "zh"},
            {"voice": "longmiao", "language": "zh"},
            {"voice": "longxiaocheng", "language": "zh"},
            {"voice": "longjing", "language": "zh"},
        ]
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.session:
            await self.session.close()
        # Cleanup cache miss engine
        if self.cache_miss_engine:
            await self.cache_miss_engine.cleanup()
        logger.info("TTS Cache Proxy Engine cleaned up")
    
    @property
    def supports_streaming(self) -> bool:
        return True
    
    @property
    def engine_name(self) -> str:
        return "tts_cache_proxy"
    
    def get_stats(self) -> Dict[str, Any]:
        """获取详细统计信息"""
        total = self.stats['total_requests']
        if total == 0:
            return self.stats
        
        return {
            **self.stats,
            'cache_hit_rate': f"{(self.stats['cache_hits'] / total * 100):.2f}%",
            'cache_miss_engine_rate': f"{(self.stats['cache_miss_engine_uses'] / total * 100):.2f}%",
            'error_rate': f"{(self.stats['total_errors'] / total * 100):.2f}%",
            'circuit_state': self.circuit_breaker.state.value,
            'circuit_error_rate': f"{(self.circuit_breaker.stats.error_rate * 100):.2f}%",
            'service_healthy': self.service_healthy
        }