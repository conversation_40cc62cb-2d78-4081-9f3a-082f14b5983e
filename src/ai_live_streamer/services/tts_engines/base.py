"""Base TTS Engine interface

Defines the interface that all TTS engines must implement to work with
the TTS Manager system.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, AsyncGenerator
from pathlib import Path

from ...models.audio import AudioChunk, AudioFormat, AudioPriority
from ...models.streaming_audio import StreamingChunk


class BaseTTSEngine(ABC):
    """Base class for all TTS engines"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize TTS engine with configuration
        
        Args:
            config: Engine-specific configuration dictionary
        """
        self.config = config
        self.is_initialized = False
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the TTS engine
        
        This should load models, establish connections, etc.
        Must be called before synthesis operations.
        
        Raises:
            ServiceError: If initialization fails
        """
        pass
    
    @abstractmethod
    async def synthesize(self, text: str, voice: str = None, **kwargs) -> AudioChunk:
        """Synthesize text to audio
        
        Args:
            text: Text to synthesize
            voice: Voice identifier (optional, uses engine default)
            **kwargs: Additional synthesis parameters
            
        Returns:
            AudioChunk containing synthesized audio
            
        Raises:
            ServiceError: If synthesis fails
        """
        pass
    
    @abstractmethod
    async def synthesize_streaming(
        self, 
        text: str, 
        voice: str = None, 
        **kwargs
    ) -> AsyncGenerator[StreamingChunk, None]:
        """Synthesize text to audio with streaming output
        
        Args:
            text: Text to synthesize
            voice: Voice identifier (optional, uses engine default)
            **kwargs: Additional synthesis parameters
            
        Yields:
            AudioChunk objects as they become available
            
        Raises:
            ServiceError: If synthesis fails
        """
        pass
    
    @abstractmethod
    async def get_voices(self) -> List[Dict[str, str]]:
        """Get list of available voices
        
        Returns:
            List of voice dictionaries with 'id', 'name', 'language' keys
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the engine"""
        pass
    
    @property
    @abstractmethod
    def supports_streaming(self) -> bool:
        """Whether this engine supports streaming synthesis"""
        pass
    
    @property
    @abstractmethod
    def engine_name(self) -> str:
        """Name of this TTS engine"""
        pass
    
    def validate_voice(self, voice: str) -> bool:
        """Validate if a voice is supported by this engine
        
        Args:
            voice: Voice identifier to validate
            
        Returns:
            True if voice is supported, False otherwise
        """
        # Default implementation - engines can override
        return True
    
    def get_default_voice(self) -> str:
        """Get the default voice for this engine
        
        Returns:
            Default voice identifier
        """
        return self.config.get('default_voice', 'default')
    
    def estimate_duration(self, text: str) -> float:
        """Estimate synthesis duration for given text
        
        Args:
            text: Text to estimate duration for
            
        Returns:
            Estimated duration in seconds
        """
        # Simple estimation: ~200 words per minute
        word_count = len(text.split())
        return (word_count / 200.0) * 60.0