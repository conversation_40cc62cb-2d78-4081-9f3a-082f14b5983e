"""CosyVoice Unified Engine - 重构版

基于新架构设计的统一TTS引擎：
- 纯粹的"技术执行者"：只负责音频合成，不处理句子分割
- 接收单句文本，返回高质量音频流  
- 上层播放器使用SemanticSentenceSplitter处理句子分割
- 支持流式合成和实时广播
- 使用 Alibaba Cloud DashScope CosyVoice v2 API

重构日期：2025-08-06
"""

import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List, AsyncGenerator
from datetime import datetime, timedelta
from loguru import logger
import threading

from ...models.streaming_audio import (
    AudioStream, StreamingChunk, VoiceProfile, StreamingStatus
)
from ...models.audio import AudioPriority
from ...core.exceptions import ServiceError, AuthenticationError, InvalidRequestError
import re
from ...utils.retry import retry_with_exponential_backoff, is_throttling_error
from .base import BaseTTSEngine
# 🔧 ARCHITECTURE CHANGE: Remove audio format converter to implement zero-conversion architecture
# from ..audio_format_converter import get_audio_converter


class VoiceManager:
    """Voice profile manager for CosyVoice v2"""
    
    def __init__(self):
        self.voices = {
            "longanran": VoiceProfile(
                voice_id="longanran",
                name="龙安燃", 
                type="female_energetic",
                description="活力女声，适合产品推广",
                language="zh-CN",
                sample_rate=24000,
                latency_ms=150,
                quality_score=0.95,
                suitable_for=["product_intro", "call_to_action", "price_announcement"],
                energy_level="high"
            ),
            "longanxuan": VoiceProfile(
                voice_id="longanxuan",
                name="龙安宣",
                type="female_calm",
                description="温和女声，适合品牌介绍",
                language="zh-CN",
                sample_rate=24000,
                latency_ms=150,
                quality_score=0.93,
                suitable_for=["opening", "closing", "interaction"],
                energy_level="medium"
            )
        }
        
        self.default_voice = "longanran"
        self.fallback_voice = "longanxuan"
    
    def get_voice_profile(self, voice_id: str) -> Optional[VoiceProfile]:
        """Get voice profile by ID"""
        return self.voices.get(voice_id)
    
    def get_default_voice(self) -> VoiceProfile:
        """Get default voice profile"""
        return self.voices[self.default_voice]
    
    def get_fallback_voice(self) -> VoiceProfile:
        """Get fallback voice profile"""
        return self.voices[self.fallback_voice]
    
    def select_voice_for_context(self, context_type: str) -> VoiceProfile:
        """Select best voice for given context"""
        for voice in self.voices.values():
            if context_type in voice.suitable_for:
                return voice
        return self.get_default_voice()
    
    def is_voice_available(self, voice_id: str) -> bool:
        """Check if voice is available"""
        return voice_id in self.voices


class AudioStreamBuffer:
    """Audio stream buffer management"""
    
    def __init__(self, buffer_size_seconds: float = 2.0):
        self.buffer_size_seconds = buffer_size_seconds
        self.chunks: List[bytes] = []
        self.total_duration_ms = 0
        self.max_buffer_size = int(buffer_size_seconds * 24000 * 2)  # 16-bit PCM
    
    def add_chunk(self, audio_data: bytes, duration_ms: int = None) -> bool:
        """Add audio chunk to buffer"""
        if duration_ms is None:
            duration_ms = len(audio_data) / (2 * 24000) * 1000  # Estimate for 16-bit PCM
        
        if self.total_duration_ms + duration_ms > self.buffer_size_seconds * 1000:
            return False  # Buffer full
        
        self.chunks.append(audio_data)
        self.total_duration_ms += duration_ms
        return True
    
    def get_next_chunk(self) -> Optional[bytes]:
        """Get next chunk from buffer"""
        if self.chunks:
            chunk = self.chunks.pop(0)
            # Estimate duration for removed chunk
            chunk_duration_ms = len(chunk) / (2 * 24000) * 1000
            self.total_duration_ms = max(0, self.total_duration_ms - chunk_duration_ms)
            return chunk
        return None
    
    def get_buffer_status(self) -> Dict[str, Any]:
        """Get buffer status information"""
        return {
            "chunks_count": len(self.chunks),
            "total_duration_ms": self.total_duration_ms,
            "buffer_utilization": self.total_duration_ms / (self.buffer_size_seconds * 1000),
            "is_full": self.total_duration_ms >= self.buffer_size_seconds * 1000
        }


class CosyVoiceUnifiedEngine(BaseTTSEngine):
    """CosyVoice v2 cloud TTS engine with streaming support
    
    🔧 ZERO-CONVERSION ARCHITECTURE: Implements direct PCM data pass-through
    based on archived code wisdom. No format conversion = zero latency.
    Frontend must use Web Audio API to handle raw PCM data.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Configuration - API Key 优先来自环境变量，其次来自配置；忽略占位符
        import os
        self.api_key = (
            os.environ.get("COSYVOICE_V2_API_KEY")
            or os.environ.get("DASHSCOPE_API_KEY")
            or ""
        )
        self.default_voice = config.get("default_voice", "longanran")
        self.fallback_voice = config.get("fallback_voice", "longanxuan")
        
        # 🔧 ZERO-CONVERSION MODE: No format conversion, raw PCM pass-through
        # This eliminates the 3.3-second FFmpeg cold-start delay entirely
        self.output_format = "pcm"  # Always raw PCM format
        logger.info("🚀 CosyVoice engine initialized with ZERO-CONVERSION architecture")
        logger.info("   ✨ Raw PCM pass-through mode - maximum performance, minimal latency")
        logger.info("   📋 Frontend must use Web Audio API for PCM playback")
        self.chunk_duration_ms = config.get("chunk_duration_ms", 200)
        self.buffer_size_seconds = config.get("buffer_size_seconds", 2.0)
        
        # Components
        self.voice_manager = VoiceManager()
        # 不再需要 StreamingTTSClient，直接使用 DashScope SDK
        self.audio_buffer = AudioStreamBuffer(self.buffer_size_seconds)
        
        # State
        self.is_initialized = False
        
        self.logger = logger.bind(component="cosyvoice_v2_tts")

    @staticmethod
    def _is_placeholder_value(value: Optional[str]) -> bool:
        if not isinstance(value, str):
            return False
        s = value.strip()
        return s == "***" or (s.startswith("${") and s.endswith("}"))

    def _normalize_api_key_from_config(self, value: Optional[str]) -> str:
        if not isinstance(value, str):
            return ""
        if self._is_placeholder_value(value):
            return ""
        return value.strip()

    @staticmethod
    def _classify_service_error(exc: Exception) -> Exception:
        msg = str(exc).lower()
        # 常见的认证失败指示词
        auth_markers = [
            "401", "403", "unauthorized", "forbidden", "invalid api key",
            "invalid key", "authentication", "permission"
        ]
        bad_request_markers = ["400", "bad request", "invalid parameter", "invalid request"]
        if any(m in msg for m in auth_markers):
            return AuthenticationError(str(exc), "cosyvoice_v2_tts", status_code=_extract_status_code(msg))
        if any(m in msg for m in bad_request_markers):
            return InvalidRequestError(str(exc), "cosyvoice_v2_tts", status_code=_extract_status_code(msg))
        return ServiceError(str(exc), "cosyvoice_v2_tts", "DOWNSTREAM_ERROR")
    
    async def initialize(self) -> None:
        """Initialize the TTS engine"""
        try:
            self.logger.info("Initializing CosyVoice v2 TTS engine")
            
            if not self.api_key:
                raise ServiceError(
                    "CosyVoice v2 API key is required",
                    "cosyvoice_v2_tts",
                    "CONFIG_ERROR"
                )
            
            # Validate API key and check voice availability (Fail-Fast)
            ok = await self._validate_api_configuration()
            if not ok:
                raise AuthenticationError(
                    "API configuration validation failed",
                    "cosyvoice_v2_tts",
                    status_code=None
                )
            
            self.is_initialized = True
            self.logger.info("CosyVoice v2 TTS engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize CosyVoice v2 TTS engine: {str(e)}")
            raise ServiceError(
                f"TTS engine initialization failed: {str(e)}",
                "cosyvoice_v2_tts",
                "INIT_ERROR"
            )
    
    async def _validate_api_configuration(self) -> bool:
        """Validate API key and voice availability"""
        try:
            import dashscope
            from dashscope.audio.tts_v2 import SpeechSynthesizer, AudioFormat, ResultCallback
            
            # Set API key
            dashscope.api_key = self.api_key
            
            # Test API key with default voice first
            self.logger.debug("Testing API key validity...")
            
            # Create a dummy callback for validation
            class ValidationCallback(ResultCallback):
                def on_open(self):
                    pass
                def on_data(self, data: bytes):
                    pass  # We don't need to process the audio for validation
                def on_complete(self):
                    pass
                def on_error(self, message: str):
                    pass
                def on_close(self):
                    pass
                def on_event(self, message):
                    pass
            
            # Try creating a synthesizer with a basic voice
            test_voices = [self.default_voice, self.fallback_voice]
            working_voice = None
            
            for voice_id in test_voices:
                try:
                    synthesizer = SpeechSynthesizer(
                        model="cosyvoice-v2",
                        voice=voice_id,
                        format=AudioFormat.PCM_24000HZ_MONO_16BIT,
                        callback=ValidationCallback()  # Pass callback at creation time
                    )
                    
                    # Perform a minimal streaming call to validate credentials
                    try:
                        # Very short text to avoid costs; should quickly return or raise auth error
                        def _try_min_call():
                            try:
                                synthesizer.streaming_call("ping")
                                synthesizer.streaming_complete()
                            except Exception as e:  # noqa: BLE001
                                raise e

                        thread = threading.Thread(target=_try_min_call)
                        thread.daemon = True
                        thread.start()
                        thread.join(timeout=3.0)
                        # If thread still alive, we consider it inconclusive but not a failure
                        if thread.is_alive():
                            self.logger.debug("Credential validation thread timed out; treating as inconclusive")
                            # We won't fail here; try next voice or accept default
                            working_voice = voice_id
                            break
                        # If no exception, consider it working
                        working_voice = voice_id
                        self.logger.info(f"✅ API Key 验证成功，可用音色: {voice_id}")
                        break
                    except Exception as auth_exc:  # noqa: BLE001
                        # Classify and rethrow authentication errors; try next voice only for non-auth errors
                        classified = self._classify_service_error(auth_exc)
                        if isinstance(classified, AuthenticationError):
                            raise classified
                        else:
                            self.logger.debug(f"音色 {voice_id} 验证失败: {auth_exc}")
                            continue
                    
                except Exception as voice_error:
                    self.logger.debug(f"音色 {voice_id} 不可用: {voice_error}")
                    continue
            
            if working_voice:
                # Update voice configuration if needed
                if working_voice != self.default_voice:
                    self.logger.warning(f"默认音色 {self.default_voice} 不可用，已切换到 {working_voice}")
                    self.default_voice = working_voice
                
                return True
            else:
                self.logger.error("❌ 所有测试音色都不可用")
                return False
                
        except ImportError:
            self.logger.warning("DashScope SDK not available for validation")
            return True  # Continue without validation
        except Exception as e:
            classified = self._classify_service_error(e)
            self.logger.error(f"API configuration validation failed: {classified}")
            if isinstance(classified, AuthenticationError):
                # Propagate authentication error to enforce fail-fast
                raise classified
            return False

    async def synthesize_streaming(
        self,
        text: str,
        voice: str = None,
        **kwargs
    ) -> AsyncGenerator[StreamingChunk, None]:
        """Synthesize text to audio with streaming output using DashScope SDK

        Args:
            text: Text to synthesize
            voice: Voice identifier (optional, uses engine default)
            **kwargs: Additional synthesis parameters

        Yields:
            StreamingChunk objects as they become available
        """
        if not self.is_initialized:
            await self.initialize()

        # Voice selection with enhanced fallback logic
        voice_id = voice or self.default_voice
        voice_profile = self.voice_manager.get_voice_profile(voice_id)
        if not voice_profile:
            self.logger.warning(f"Voice {voice_id} not found, using default")
            voice_profile = self.voice_manager.get_default_voice()
            voice_id = voice_profile.voice_id

        stream_id = f"cosyvoice_v2_stream_{uuid.uuid4().hex[:8]}"
        sequence_number = 0
        total_chunks = 0

        # Voice fallback list
        fallback_voices = [voice_id]
        if voice_id != self.default_voice:
            fallback_voices.append(self.default_voice)
        if self.fallback_voice not in fallback_voices:
            fallback_voices.append(self.fallback_voice)

        synthesis_error = None
        for attempt, current_voice_id in enumerate(fallback_voices):
            try:
                # Update voice profile for current attempt
                current_voice_profile = self.voice_manager.get_voice_profile(current_voice_id)
                if not current_voice_profile:
                    # Create temporary profile for fallback voice
                    current_voice_profile = VoiceProfile(
                        voice_id=current_voice_id,
                        name=f"Fallback-{current_voice_id}",
                        type="fallback",
                        description=f"Fallback voice {current_voice_id}",
                        language="zh-CN",
                        sample_rate=24000,
                        latency_ms=150,
                        quality_score=0.8,
                        suitable_for=["fallback"],
                        energy_level="medium"
                    )

                if attempt > 0:
                    self.logger.warning(f"Retrying with fallback voice {attempt}: {current_voice_profile.name}")
                else:
                    self.logger.trace(f"Starting streaming synthesis with voice: {current_voice_profile.name}")

                start_time = time.time()

                # 使用 DashScope SDK 进行流式合成
                import dashscope
                from dashscope.audio.tts_v2 import SpeechSynthesizer, AudioFormat, ResultCallback
                import asyncio
                import threading

                # 设置 API Key
                dashscope.api_key = self.api_key

                # 创建回调类来收集音频数据
                class StreamingCallback(ResultCallback):
                    def __init__(self):
                        self.audio_chunks = []
                        self.completed = False
                        self.error_message = None
                        self.lock = threading.Lock()

                    def on_open(self):
                        pass

                    def on_data(self, data: bytes):
                        with self.lock:
                            self.audio_chunks.append(data)

                    def on_complete(self):
                        with self.lock:
                            self.completed = True

                    def on_error(self, message: str):
                        with self.lock:
                            self.error_message = message
                            self.completed = True

                    def on_close(self):
                        pass

                    def on_event(self, message):
                        pass

                # 创建回调实例
                callback = StreamingCallback()

                # 创建合成器
                synthesizer = SpeechSynthesizer(
                    model="cosyvoice-v2",
                    voice=current_voice_id,
                    format=AudioFormat.PCM_24000HZ_MONO_16BIT,
                    callback=callback
                )

                # 在线程中执行流式合成（包含重试逻辑）
                def run_synthesis():
                    max_attempts = 3
                    last_error = None
                    
                    for attempt in range(max_attempts):
                        try:
                            # 直接使用传入的文本（已由上层语义分割器处理）
                            synthesizer.streaming_call(text)
                            synthesizer.streaming_complete()
                            return  # 成功则退出
                        except Exception as e:
                            last_error = e
                            # 检查是否为限流错误且还有重试机会
                            if is_throttling_error(e) and attempt < max_attempts - 1:
                                import time
                                delay = 2 ** attempt  # 指数退避: 1, 2, 4 秒
                                logger.warning(f"TTS throttling detected, retrying in {delay}s (attempt {attempt + 1}/{max_attempts})")
                                time.sleep(delay)
                                continue
                            else:
                                # 非限流错误或最后一次尝试
                                callback.error_message = str(last_error)
                                callback.completed = True
                                break

                # 启动合成线程
                synthesis_thread = threading.Thread(target=run_synthesis)
                synthesis_thread.start()

                # 流式返回音频数据
                last_chunk_index = 0
                while True:
                    await asyncio.sleep(0.01)  # 小延迟避免过度轮询

                    with callback.lock:
                        # 检查是否有错误
                        if callback.error_message:
                            synthesis_error = callback.error_message
                            break

                        # 处理新的音频块
                        while last_chunk_index < len(callback.audio_chunks):
                            chunk_data = callback.audio_chunks[last_chunk_index]
                            last_chunk_index += 1

                            # Validate chunk data
                            if not chunk_data or len(chunk_data) == 0:
                                self.logger.warning(f"Empty chunk received at sequence {sequence_number}")
                                continue

                            # 🚀 ZERO-CONVERSION ARCHITECTURE: Direct PCM pass-through
                            # Based on archived code wisdom - eliminate ALL format conversion overhead
                            chunk_duration_ms = len(chunk_data) / (2 * 24000) * 1000  # 16-bit PCM, 24kHz
                            
                            # Use raw PCM data directly - NO CONVERSION!
                            audio_data_to_send = chunk_data  # Direct pass-through like archived code
                            
                            # self.logger.debug(f"✨ Direct PCM pass-through: {len(chunk_data)} bytes, {chunk_duration_ms:.1f}ms duration")

                            # Create audio stream with raw PCM data (following archived code pattern)
                            audio_stream = AudioStream(
                                stream_id=f"{stream_id}_chunk_{sequence_number}",
                                audio_data=audio_data_to_send,  # 🚀 Direct PCM data - zero conversion!
                                sample_rate=24000,
                                channels=1,
                                bit_depth=16,
                                duration_ms=int(chunk_duration_ms),
                                timestamp=datetime.utcnow(),
                                sequence_number=sequence_number,
                                is_final=False,
                                text_content=text if sequence_number == 0 else None,
                                voice_profile=current_voice_profile,
                                synthesis_latency_ms=int((time.time() - start_time) * 1000) if sequence_number == 0 else None
                            )

                            # Create streaming chunk with enhanced metadata
                            chunk = StreamingChunk(
                                chunk_id=f"{stream_id}_chunk_{sequence_number}",
                                stream_id=stream_id,
                                audio_stream=audio_stream,
                                priority=kwargs.get('priority', AudioPriority.NORMAL),
                                status=StreamingStatus.STREAMING,
                                context_type=kwargs.get('context_type', 'narration'),
                                segment_title=kwargs.get('segment_title'),
                                segment_type=kwargs.get('segment_type'),
                                play_after=datetime.utcnow(),
                                expires_at=datetime.utcnow() + timedelta(seconds=30)
                            )

                            sequence_number += 1
                            total_chunks += 1
                            yield chunk

                        # 检查是否完成
                        if callback.completed:
                            break

                # 等待合成线程完成
                synthesis_thread.join(timeout=5.0)

                # 检查是否有错误
                if callback.error_message:
                    # 检查是否是限流错误
                    error_msg = callback.error_message
                    if is_throttling_error(Exception(error_msg)):
                        logger.warning(f"TTS throttling detected, will retry: {error_msg}")
                        # 抛出异常让重试装饰器处理
                        raise ServiceError(
                            f"Synthesis throttled: {error_msg}",
                            "cosyvoice_v2_tts",
                            "THROTTLING_ERROR"
                        )
                    else:
                        # 其他错误直接失败
                        raise self._classify_service_error(Exception(error_msg))

                # Create a final marker chunk if we had any audio
                if total_chunks > 0:
                    final_chunk = StreamingChunk(
                        chunk_id=f"{stream_id}_final",
                        stream_id=stream_id,
                        audio_stream=AudioStream(
                            stream_id=f"{stream_id}_final",
                            audio_data=b'',  # Empty final chunk
                            sample_rate=24000,
                            channels=1,
                            bit_depth=16,
                            duration_ms=0,
                            timestamp=datetime.utcnow(),
                            sequence_number=sequence_number,
                            is_final=True,
                            text_content=None,
                            voice_profile=current_voice_profile
                        ),
                        priority=kwargs.get('priority', AudioPriority.NORMAL),
                        status=StreamingStatus.COMPLETED,
                        context_type=kwargs.get('context_type', 'narration')
                    )
                    yield final_chunk

                synthesis_duration = time.time() - start_time
                # self.logger.info(
                #     f"Streaming synthesis completed: {total_chunks} chunks in {synthesis_duration:.2f}s with voice {current_voice_profile.name}"
                # )

                # Success! Break out of fallback loop
                return

            except ServiceError as e:
                synthesis_error = e
                error_str = str(e)

                # Check if this is a voice-related error that might benefit from fallback
                if "418" in error_str or "SYNTHESIZER_INIT_ERROR" in error_str or "STREAMING_CALL_ERROR" in error_str:
                    self.logger.warning(f"Voice {current_voice_id} failed with error 418, trying next fallback...")
                    continue  # Try next voice
                else:
                    # Other errors should not be retried
                    raise

            except Exception as e:
                synthesis_error = e
                self.logger.warning(f"Voice {current_voice_id} failed: {str(e)}")
                continue  # Try next voice

        # If we get here, all voices failed
        if synthesis_error:
            self.logger.error(f"All voice fallbacks failed. Last error: {synthesis_error}")
            if isinstance(synthesis_error, ServiceError):
                raise synthesis_error
            else:
                raise ServiceError(
                    f"All voice fallbacks failed: {synthesis_error}",
                    "cosyvoice_v2_tts",
                    "ALL_VOICES_FAILED"
                )
        else:
            raise ServiceError(
                "All voice fallbacks failed with unknown error",
                "cosyvoice_v2_tts",
                "ALL_VOICES_FAILED"
            )


    @property
    def supports_streaming(self) -> bool:
        """CosyVoice v2 supports streaming"""
        return True

    @property
    def engine_name(self) -> str:
        """Engine name"""
        return "cosyvoice_v2"

    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available voices"""
        return [
            {
                "voice_id": voice.voice_id,
                "name": voice.name,
                "type": voice.type,
                "language": voice.language,
                "description": voice.description
            }
            for voice in self.voice_manager.voices.values()
        ]

    def select_voice_for_segment(self, segment_type: str) -> str:
        """Select appropriate voice for segment type

        Args:
            segment_type: Type of content segment

        Returns:
            Voice ID to use
        """
        voice_profile = self.voice_manager.select_voice_for_context(segment_type)
        return voice_profile.voice_id

    def get_voice_profile(self, voice_id: str) -> Optional[VoiceProfile]:
        """Get voice profile by ID"""
        return self.voice_manager.get_voice_profile(voice_id)

    async def cleanup(self) -> None:
        """Clean up resources"""
        self.is_initialized = False
        self.logger.info("CosyVoice v2 TTS engine cleaned up")
    
    async def synthesize(self, text: str, voice: str = None, **kwargs):
        """Non-streaming synthesis - wrapper around streaming synthesis
        
        Args:
            text: Text to synthesize
            voice: Voice identifier (optional)
            **kwargs: Additional synthesis parameters
            
        Returns:
            AudioChunk containing complete synthesized audio
        """
        # Collect all streaming chunks into a single AudioChunk
        all_audio_data = b''
        total_duration_ms = 0
        voice_profile = None
        
        async for chunk in self.synthesize_streaming(text, voice, **kwargs):
            if chunk.audio_stream and chunk.audio_stream.audio_data:
                all_audio_data += chunk.audio_stream.audio_data
                total_duration_ms += chunk.audio_stream.duration_ms
                if voice_profile is None:
                    voice_profile = chunk.audio_stream.voice_profile
        
        # Import AudioChunk from models
        from ...models.audio import AudioChunk
        
        return AudioChunk(
            audio_data=all_audio_data,
            sample_rate=24000,
            channels=1,
            duration_ms=total_duration_ms,
            format="pcm"
        )
    
    async def get_voices(self) -> List[Dict[str, str]]:
        """Get list of available voices
        
        Returns:
            List of voice dictionaries with 'id', 'name', 'language' keys
        """
        voices = []
        for voice_id, voice_profile in self.voice_manager.voices.items():
            voices.append({
                'id': voice_profile.voice_id,
                'name': voice_profile.name,
                'language': voice_profile.language,
                'type': voice_profile.type,
                'description': voice_profile.description
            })
        return voices


def _extract_status_code(message_lower: str) -> Optional[int]:
    try:
        match = re.search(r"\b(400|401|403|404|405|409|415|418|429|5\d{2})\b", message_lower)
        if match:
            return int(match.group(1))
    except Exception:  # noqa: BLE001
        return None
    return None
