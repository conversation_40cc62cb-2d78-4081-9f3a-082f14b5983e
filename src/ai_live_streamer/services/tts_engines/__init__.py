"""TTS Engine implementations

This module contains various TTS engine implementations that can be used
with the TTS Manager. Each engine should implement the TTSEngine interface.
"""

from .base import BaseTTSEngine
from .cosyvoice_unified_engine import CosyVoiceUnifiedEngine  # New unified engine

# Only import existing modules
__all__ = [
    "BaseTTSEngine", 
    "CosyVoiceUnifiedEngine",  # Primary recommendation - new unified engine
]