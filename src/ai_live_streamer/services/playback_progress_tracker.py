#!/usr/bin/env python3
"""
播放进度追踪器 - 基于时间估算用户实际听到的内容

解决"TTS完成≠播放完成"的核心问题，实现：
1. 记录每个句子的发送时间和音频时长
2. 基于时间估算用户当前听到的句子
3. 为QA插入提供准确的播放进度信息
4. 处理暂停/恢复对时间线的影响

Author: Claude Code
Date: 2025-08-08
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from bisect import bisect_left
from loguru import logger


@dataclass
class SentenceTimeline:
    """句子播放时间线记录"""
    index: int                          # 句子索引
    text: str                          # 句子文本（用于调试）
    dispatch_time_ms: float            # 发送时间（毫秒）
    audio_duration_ms: float           # 音频时长（毫秒）
    estimated_start_ms: float          # 预计开始播放时间
    estimated_completion_ms: float     # 预计播放完成时间
    is_qa_insertion: bool = False      # 是否为QA插入的句子


class PlaybackProgressTracker:
    """播放进度追踪器 - 双进度管理的核心组件
    
    职责：
    1. 维护播放时间线，记录每个句子的发送和预计播放时间
    2. 基于当前时间估算用户正在听的句子
    3. 为QA插入提供准确的时机判断
    4. 处理暂停/恢复对时间的影响
    """
    
    def __init__(self, timing_config: Dict[str, Any]):
        """初始化播放进度追踪器
        
        Args:
            timing_config: 播放时序配置，包含：
                - network_latency_ms: 网络延迟估算
                - client_buffer_ms: 客户端缓冲时间
                - fallback_duration_per_char_ms: 字符时长估算
        """
        self.timing_config = timing_config
        self.timeline: List[SentenceTimeline] = []
        
        # 暂停相关
        self.is_paused = False
        self.pause_start_time_ms: Optional[float] = None
        self.total_pause_duration_ms = 0.0
        
        # 性能优化：缓存最后查询结果
        self._last_query_time_ms: Optional[float] = None
        self._last_query_result: Optional[int] = None
        
        # 安全边际（毫秒）
        self.SAFETY_MARGIN_MS = 100
        
        # 内存管理：最多保留的历史记录数
        self.MAX_TIMELINE_SIZE = 1000
        
        logger.info(f"✅ PlaybackProgressTracker initialized with config: {timing_config}")
    
    def record_sentence_dispatch(self, index: int, text: str, 
                               audio_duration_ms: Optional[float] = None,
                               is_qa_insertion: bool = False) -> SentenceTimeline:
        """记录句子发送，计算预计播放时间
        
        Args:
            index: 句子索引
            text: 句子文本
            audio_duration_ms: 音频时长（毫秒），None时使用字符数估算
            is_qa_insertion: 是否为QA插入的句子
            
        Returns:
            创建的时间线记录
        """
        dispatch_time = self._get_current_time_ms()
        
        # 音频时长估算
        if audio_duration_ms is None or audio_duration_ms <= 0:
            # Fallback: 基于字符数估算
            char_count = len(text.strip())
            fallback_rate = self.timing_config.get('fallback_duration_per_char_ms', 80)
            audio_duration_ms = char_count * fallback_rate
            logger.debug(f"📊 使用字符数估算音频时长: {char_count}字 * {fallback_rate}ms = {audio_duration_ms}ms")
        
        # 计算预计播放时间
        network_latency = self.timing_config.get('network_latency_ms', 150)
        client_buffer = self.timing_config.get('client_buffer_ms', 400)
        
        # 如果有前一个句子，基于其完成时间计算
        if self.timeline:
            last_sentence = self.timeline[-1]
            estimated_start = max(
                dispatch_time + network_latency,
                last_sentence.estimated_completion_ms
            )
        else:
            estimated_start = dispatch_time + network_latency
        
        # 保守估算完成时间
        estimated_completion = (estimated_start + 
                              audio_duration_ms + 
                              client_buffer + 
                              self.SAFETY_MARGIN_MS)
        
        # 创建时间线记录
        timeline_entry = SentenceTimeline(
            index=index,
            text=text[:50] + "..." if len(text) > 50 else text,
            dispatch_time_ms=dispatch_time,
            audio_duration_ms=audio_duration_ms,
            estimated_start_ms=estimated_start,
            estimated_completion_ms=estimated_completion,
            is_qa_insertion=is_qa_insertion
        )
        
        self.timeline.append(timeline_entry)
        
        # 内存管理：清理旧记录
        self._cleanup_old_records()
        
        logger.info(f"📝 记录句子发送 #{index}: "
                   f"发送时间={dispatch_time:.0f}ms, "
                   f"音频时长={audio_duration_ms:.0f}ms, "
                   f"预计完成={estimated_completion:.0f}ms, "
                   f"QA插入={is_qa_insertion}")
        
        return timeline_entry
    
    def get_current_playing_index(self) -> int:
        """获取用户当前正在听的句子索引
        
        Returns:
            当前播放的句子索引，如果都播放完返回总句子数
        """
        current_time = self._get_current_time_ms()
        
        # 性能优化：缓存检查
        if (self._last_query_time_ms is not None and 
            abs(current_time - self._last_query_time_ms) < 50):  # 50ms内使用缓存
            return self._last_query_result or 0
        
        # 使用二分查找优化性能
        playing_index = self._binary_search_playing_index(current_time)
        
        # 更新缓存
        self._last_query_time_ms = current_time
        self._last_query_result = playing_index
        
        logger.trace(f"🎵 当前播放位置: 句子 #{playing_index} (时间: {current_time:.0f}ms)")
        
        return playing_index
    
    def get_estimated_completion_time(self, sentence_index: int) -> Optional[float]:
        """获取指定句子的预计播放完成时间
        
        Args:
            sentence_index: 句子索引
            
        Returns:
            预计完成时间（毫秒），如果句子不存在返回None
        """
        for entry in self.timeline:
            if entry.index == sentence_index:
                return entry.estimated_completion_ms
        return None
    
    def is_sentence_playing_completed(self, sentence_index: int) -> bool:
        """检查指定句子是否已经播放完成
        
        Args:
            sentence_index: 句子索引
            
        Returns:
            是否播放完成
        """
        completion_time = self.get_estimated_completion_time(sentence_index)
        if completion_time is None:
            return False
        
        current_time = self._get_current_time_ms()
        return current_time >= completion_time
    
    def find_qa_insertion_point(self, qa_ready_time_ms: Optional[float] = None) -> int:
        """找到QA应该插入的位置
        
        Args:
            qa_ready_time_ms: QA准备完成的时间，None表示当前时间
            
        Returns:
            QA应该插入的句子索引（在该索引处插入）
        """
        if qa_ready_time_ms is None:
            qa_ready_time_ms = self._get_current_time_ms()
        
        # 估算QA准备好时用户正在听的句子
        playing_index = self._binary_search_playing_index(qa_ready_time_ms)
        
        # QA应该在当前播放句子完成后插入
        insertion_point = playing_index + 1
        
        logger.info(f"🎯 QA插入点计算: "
                   f"QA准备时间={qa_ready_time_ms:.0f}ms, "
                   f"预计播放位置=#{playing_index}, "
                   f"插入位置=#{insertion_point}")
        
        return insertion_point
    
    def handle_pause(self) -> None:
        """处理暂停事件"""
        if self.is_paused:
            logger.warning("⚠️ 已经处于暂停状态，忽略重复暂停")
            return
        
        self.is_paused = True
        self.pause_start_time_ms = self._get_current_time_ms()
        
        logger.info(f"⏸️ 播放暂停，记录暂停时间: {self.pause_start_time_ms:.0f}ms")
    
    def handle_resume(self) -> None:
        """处理恢复事件"""
        if not self.is_paused:
            logger.warning("⚠️ 未处于暂停状态，忽略恢复操作")
            return
        
        if self.pause_start_time_ms is None:
            logger.error("❌ 暂停开始时间丢失，无法正确恢复")
            self.is_paused = False
            return
        
        # 计算暂停时长
        resume_time = self._get_current_time_ms()
        pause_duration = resume_time - self.pause_start_time_ms
        self.total_pause_duration_ms += pause_duration
        
        # 调整所有未完成句子的预计时间
        for entry in self.timeline:
            if entry.estimated_completion_ms > self.pause_start_time_ms:
                entry.estimated_start_ms += pause_duration
                entry.estimated_completion_ms += pause_duration
        
        self.is_paused = False
        self.pause_start_time_ms = None
        
        logger.info(f"▶️ 播放恢复，暂停时长: {pause_duration:.0f}ms, "
                   f"累计暂停: {self.total_pause_duration_ms:.0f}ms")
    
    def get_playback_stats(self) -> Dict[str, Any]:
        """获取播放统计信息"""
        current_time = self._get_current_time_ms()
        playing_index = self.get_current_playing_index()
        
        total_sentences = len(self.timeline)
        completed_sentences = sum(1 for entry in self.timeline 
                                if current_time >= entry.estimated_completion_ms)
        
        # 计算预计总播放时长
        if self.timeline:
            first_start = self.timeline[0].estimated_start_ms
            last_completion = self.timeline[-1].estimated_completion_ms
            total_duration = last_completion - first_start
        else:
            total_duration = 0
        
        return {
            "current_playing_index": playing_index,
            "total_sentences": total_sentences,
            "completed_sentences": completed_sentences,
            "total_duration_ms": total_duration,
            "total_pause_duration_ms": self.total_pause_duration_ms,
            "is_paused": self.is_paused,
            "timeline_size": len(self.timeline)
        }
    
    def _get_current_time_ms(self) -> float:
        """获取当前时间（毫秒）
        
        考虑暂停状态，返回调整后的时间
        """
        actual_time = time.time() * 1000
        
        # 如果正在暂停，返回暂停开始的时间
        if self.is_paused and self.pause_start_time_ms is not None:
            return self.pause_start_time_ms
        
        # 否则返回实际时间减去累计暂停时长
        return actual_time - self.total_pause_duration_ms
    
    def _binary_search_playing_index(self, target_time_ms: float) -> int:
        """使用二分查找找到当前播放的句子索引
        
        Args:
            target_time_ms: 目标时间
            
        Returns:
            正在播放的句子索引
        """
        if not self.timeline:
            return 0
        
        # 构建完成时间列表用于二分查找
        completion_times = [entry.estimated_completion_ms for entry in self.timeline]
        
        # 找到第一个完成时间大于目标时间的位置
        pos = bisect_left(completion_times, target_time_ms)
        
        # pos 就是当前正在播放的句子索引
        return pos
    
    def _cleanup_old_records(self) -> None:
        """清理已经播放完成很久的记录，避免内存泄漏"""
        if len(self.timeline) <= self.MAX_TIMELINE_SIZE:
            return
        
        current_time = self._get_current_time_ms()
        
        # 保留最近的记录和未播放的记录
        cutoff_time = current_time - 60000  # 保留最近1分钟的记录
        
        new_timeline = []
        for entry in self.timeline:
            if (entry.estimated_completion_ms > cutoff_time or 
                entry.estimated_completion_ms > current_time):
                new_timeline.append(entry)
        
        # 确保至少保留一些记录
        if len(new_timeline) < 10 and len(self.timeline) > 10:
            new_timeline = self.timeline[-10:]
        
        old_size = len(self.timeline)
        self.timeline = new_timeline
        
        if old_size > len(self.timeline):
            logger.debug(f"🧹 清理旧记录: {old_size} → {len(self.timeline)}")
    
    def reset(self) -> None:
        """重置追踪器状态"""
        self.timeline.clear()
        self.is_paused = False
        self.pause_start_time_ms = None
        self.total_pause_duration_ms = 0.0
        self._last_query_time_ms = None
        self._last_query_result = None
        logger.info("🔄 PlaybackProgressTracker 已重置")


# 工具函数
def create_playback_tracker(timing_config: Dict[str, Any]) -> PlaybackProgressTracker:
    """创建播放进度追踪器的工厂函数"""
    return PlaybackProgressTracker(timing_config)


# 导出
__all__ = [
    "PlaybackProgressTracker",
    "SentenceTimeline", 
    "create_playback_tracker"
]