"""过渡话术管理器

管理进入和退出QA环节的过渡话术，支持配置驱动、防重复选择等特性。
这是状态驱动架构中负责生成自然过渡语句的关键组件。

Author: Claude Code  
Date: 2025-08-07
"""

import random
from typing import List, Dict, Any, Optional
from loguru import logger


class TransitionManager:
    """过渡话术管理器 - 精炼版
    
    主要特性：
    1. 配置驱动：话术存储在config.yml中，运营可直接修改
    2. 防重复机制：避免连续使用相同的过渡语句
    3. 清晰语义：disabled时返回None而不是空字符串
    4. 健壮性：完善的兜底机制
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化过渡话术管理器
        
        Args:
            config: transitions配置字典，包含enabled/to_qa/from_qa等字段
        """
        # 默认兜底话术，确保系统在配置缺失时仍能工作
        default_phrases = {
            'to_qa': ["我们来看一个问题。"],
            'from_qa': ["好的，我们继续。"]
        }
        
        # 从配置中读取设置
        self.enabled = config.get('enabled', True)
        self.to_qa_phrases: List[str] = config.get('to_qa', default_phrases['to_qa'])
        self.from_qa_phrases: List[str] = config.get('from_qa', default_phrases['from_qa'])
        
        # 防重复机制：记录上次使用的话术
        self._last_to_qa_phrase: Optional[str] = None
        self._last_from_qa_phrase: Optional[str] = None
        
        # 验证配置有效性
        if not self.to_qa_phrases:
            logger.warning("⚠️ No to_qa phrases configured, using default")
            self.to_qa_phrases = default_phrases['to_qa']
            
        if not self.from_qa_phrases:
            logger.warning("⚠️ No from_qa phrases configured, using default")
            self.from_qa_phrases = default_phrases['from_qa']
        
        logger.info(f"✅ TransitionManager初始化完成: "
                   f"enabled={self.enabled}, "
                   f"{len(self.to_qa_phrases)}个进入话术, "
                   f"{len(self.from_qa_phrases)}个退出话术")

    def get_to_qa_phrase(self, context: Dict[str, Any] = None) -> Optional[str]:
        """获取进入QA的过渡语句
        
        Args:
            context: 上下文信息，用于未来的智能选择（暂未使用）
            
        Returns:
            Optional[str]: 过渡语句，如果功能禁用则返回None
        """
        if not self.enabled:
            logger.debug("🎭 Transition disabled, skipping to_qa phrase")
            return None
            
        if not self.to_qa_phrases:
            logger.warning("⚠️ No to_qa phrases available")
            return None
            
        # 防重复选择逻辑
        available_phrases = [
            phrase for phrase in self.to_qa_phrases 
            if phrase != self._last_to_qa_phrase
        ]
        
        # 如果所有话术都被过滤掉了（只有一句话的情况），就用全部话术
        if not available_phrases:
            available_phrases = self.to_qa_phrases
            
        # 随机选择一个话术
        selected_phrase = random.choice(available_phrases)
        self._last_to_qa_phrase = selected_phrase
        
        logger.debug(f"🎭 选择进入QA过渡语: {selected_phrase[:20]}...")
        return selected_phrase

    def get_from_qa_phrase(self, context: Dict[str, Any] = None) -> Optional[str]:
        """获取退出QA的过渡语句
        
        Args:
            context: 上下文信息，用于未来的智能选择（暂未使用）
            
        Returns:
            Optional[str]: 过渡语句，如果功能禁用则返回None
        """
        if not self.enabled:
            logger.debug("🎭 Transition disabled, skipping from_qa phrase")
            return None
            
        if not self.from_qa_phrases:
            logger.warning("⚠️ No from_qa phrases available")
            return None
            
        # 防重复选择逻辑
        available_phrases = [
            phrase for phrase in self.from_qa_phrases 
            if phrase != self._last_from_qa_phrase
        ]
        
        # 如果所有话术都被过滤掉了（只有一句话的情况），就用全部话术
        if not available_phrases:
            available_phrases = self.from_qa_phrases
            
        # 随机选择一个话术
        selected_phrase = random.choice(available_phrases)
        self._last_from_qa_phrase = selected_phrase
        
        logger.debug(f"🎭 选择退出QA过渡语: {selected_phrase[:20]}...")
        return selected_phrase
        
    def is_enabled(self) -> bool:
        """检查过渡功能是否启用
        
        Returns:
            bool: 过渡功能启用状态
        """
        return self.enabled
        
    def get_stats(self) -> Dict[str, Any]:
        """获取管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        return {
            "enabled": self.enabled,
            "to_qa_phrases_count": len(self.to_qa_phrases),
            "from_qa_phrases_count": len(self.from_qa_phrases),
            "last_to_qa_phrase": self._last_to_qa_phrase,
            "last_from_qa_phrase": self._last_from_qa_phrase
        }
        
    def reload_config(self, config: Dict[str, Any]) -> None:
        """重新加载配置（支持热更新）
        
        Args:
            config: 新的配置字典
        """
        old_enabled = self.enabled
        old_to_count = len(self.to_qa_phrases)
        old_from_count = len(self.from_qa_phrases)
        
        # 重新初始化
        self.__init__(config)
        
        logger.info(f"🔄 TransitionManager配置已重载: "
                   f"enabled {old_enabled}->{self.enabled}, "
                   f"to_qa {old_to_count}->{len(self.to_qa_phrases)}, "
                   f"from_qa {old_from_count}->{len(self.from_qa_phrases)}")


def create_transition_manager(config: Dict[str, Any]) -> TransitionManager:
    """工厂函数：创建TransitionManager实例
    
    Args:
        config: 配置字典
        
    Returns:
        TransitionManager: 过渡话术管理器实例
    """
    return TransitionManager(config)