"""文件锁管理器实现 - 单机多进程锁方案

核心特性:
- 使用fcntl.flock实现跨进程锁
- 异步友好: 使用asyncio.to_thread避免阻塞
- 进程崩溃时OS自动释放锁
- 支持超时重试机制

Author: Claude Code  
Date: 2025-01-08
"""

import asyncio
import fcntl
import os
import time
from pathlib import Path
from typing import Dict, Optional
from loguru import logger

from .lock_manager import AbstractLockManager
from ..core.config import cfg


class FileLockManager(AbstractLockManager):
    """文件锁管理器 - 极简实现"""
    
    def __init__(self, lock_dir: Optional[str] = None):
        """初始化文件锁管理器
        
        Args:
            lock_dir: 锁文件存储目录，默认从配置读取
        """
        # 从环境变量或配置读取锁文件目录
        self.lock_dir = lock_dir or cfg.get('LOCK_FILE_DIRECTORY', '/tmp/ai-live-streamer/locks')
        
        # 确保目录存在
        Path(self.lock_dir).mkdir(parents=True, exist_ok=True)
        
        # 保存打开的文件句柄（用于持有锁）
        self._lock_files: Dict[str, any] = {}
        
        logger.info(f"📁 FileLockManager initialized with directory: {self.lock_dir}")
    
    async def acquire_lock(
        self,
        lock_key: str,
        holder_id: str,
        timeout_seconds: int = 60
    ) -> bool:
        """获取文件锁
        
        使用asyncio.to_thread在线程池中执行阻塞操作
        """
        lock_file_path = os.path.join(self.lock_dir, f"{lock_key}.lock")
        
        try:
            # 在线程池中执行阻塞的文件锁操作
            success = await asyncio.to_thread(
                self._acquire_lock_sync,
                lock_file_path,
                lock_key,
                holder_id,
                timeout_seconds
            )
            
            if success:
                logger.info(f"🔒 文件锁获取成功: {lock_key} (holder: {holder_id})")
            else:
                logger.warning(f"⏰ 文件锁获取超时: {lock_key}")
                
            return success
            
        except Exception as e:
            logger.error(f"❌ 文件锁获取异常: {lock_key}, error: {e}")
            return False
    
    def _acquire_lock_sync(
        self,
        file_path: str,
        lock_key: str,
        holder_id: str,
        timeout_seconds: int
    ) -> bool:
        """同步获取文件锁 - 在线程池中执行
        
        使用非阻塞模式 + 重试实现超时机制
        """
        start_time = time.time()
        retry_interval = 0.1  # 100ms重试间隔
        
        # 创建或打开锁文件
        try:
            lock_file = open(file_path, 'w')
        except Exception as e:
            logger.error(f"❌ 无法创建锁文件: {file_path}, error: {e}")
            return False
        
        # 尝试获取锁
        while True:
            try:
                # 尝试非阻塞获取独占锁
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                
                # 写入持有者信息
                lock_file.write(f"{holder_id}\\n")
                lock_file.write(f"acquired_at: {time.time()}\\n")
                lock_file.flush()
                
                # 保存文件句柄以保持锁
                self._lock_files[lock_key] = lock_file
                
                return True
                
            except BlockingIOError:
                # 锁被占用，检查是否超时
                elapsed = time.time() - start_time
                if elapsed >= timeout_seconds:
                    lock_file.close()
                    return False
                
                # 短暂休眠后重试
                time.sleep(retry_interval)
                
            except Exception as e:
                logger.error(f"❌ 文件锁操作失败: {file_path}, error: {e}")
                lock_file.close()
                return False
    
    async def release_lock(
        self,
        lock_key: str,
        holder_id: str
    ) -> bool:
        """释放文件锁"""
        try:
            # 在线程池中执行释放操作
            success = await asyncio.to_thread(
                self._release_lock_sync,
                lock_key,
                holder_id
            )
            
            if success:
                logger.info(f"🔓 文件锁释放成功: {lock_key}")
            else:
                logger.warning(f"⚠️ 文件锁释放失败: {lock_key} (未找到或非持有者)")
                
            return success
            
        except Exception as e:
            logger.error(f"❌ 文件锁释放异常: {lock_key}, error: {e}")
            return False
    
    def _release_lock_sync(self, lock_key: str, holder_id: str) -> bool:
        """同步释放文件锁"""
        if lock_key not in self._lock_files:
            return False
        
        try:
            lock_file = self._lock_files[lock_key]
            
            # 释放锁
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
            
            # 关闭文件
            lock_file.close()
            
            # 删除锁文件
            lock_file_path = os.path.join(self.lock_dir, f"{lock_key}.lock")
            if os.path.exists(lock_file_path):
                os.remove(lock_file_path)
            
            # 从字典中移除
            del self._lock_files[lock_key]
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 释放锁时出错: {lock_key}, error: {e}")
            return False
    
    def cleanup(self):
        """清理所有持有的锁"""
        for lock_key in list(self._lock_files.keys()):
            self._release_lock_sync(lock_key, "cleanup")
        
        logger.info("🧹 FileLockManager cleanup completed")


# Windows兼容性支持
try:
    import msvcrt
    
    class WindowsFileLockManager(FileLockManager):
        """Windows平台的文件锁实现"""
        
        def _acquire_lock_sync(
            self,
            file_path: str,
            lock_key: str,
            holder_id: str,
            timeout_seconds: int
        ) -> bool:
            """Windows版本的锁获取"""
            start_time = time.time()
            retry_interval = 0.1
            
            try:
                lock_file = open(file_path, 'w')
            except Exception as e:
                logger.error(f"❌ 无法创建锁文件: {file_path}, error: {e}")
                return False
            
            while True:
                try:
                    # Windows使用msvcrt.locking
                    msvcrt.locking(lock_file.fileno(), msvcrt.LK_NBLCK, 1)
                    
                    lock_file.write(f"{holder_id}\\n")
                    lock_file.write(f"acquired_at: {time.time()}\\n") 
                    lock_file.flush()
                    
                    self._lock_files[lock_key] = lock_file
                    return True
                    
                except IOError:
                    elapsed = time.time() - start_time
                    if elapsed >= timeout_seconds:
                        lock_file.close()
                        return False
                    
                    time.sleep(retry_interval)
                    
                except Exception as e:
                    logger.error(f"❌ Windows文件锁操作失败: {file_path}, error: {e}")
                    lock_file.close()
                    return False
        
        def _release_lock_sync(self, lock_key: str, holder_id: str) -> bool:
            """Windows版本的锁释放"""
            if lock_key not in self._lock_files:
                return False
            
            try:
                lock_file = self._lock_files[lock_key]
                
                # Windows解锁
                msvcrt.locking(lock_file.fileno(), msvcrt.LK_UNLCK, 1)
                
                lock_file.close()
                
                lock_file_path = os.path.join(self.lock_dir, f"{lock_key}.lock")
                if os.path.exists(lock_file_path):
                    os.remove(lock_file_path)
                
                del self._lock_files[lock_key]
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Windows释放锁时出错: {lock_key}, error: {e}")
                return False
    
except ImportError:
    # Unix/Linux系统，使用标准FileLockManager
    WindowsFileLockManager = FileLockManager