"""Service layer components for AI Live Streamer"""

from .search import ElasticsearchService, HybridSearchEngine
from .tts_manager import T<PERSON><PERSON>ana<PERSON>, get_tts_manager, initialize_tts_manager, cleanup_tts_manager
from .tts_engines.base import BaseTTSEngine
# Removed server-side audio players (simple_audio_player, streaming_audio_player)
# These are now archived in archive/ as they conflict with the streaming architecture

# Script Persistence Services
from .script_persistence_manager import ScriptPersistenceManager
from .script_repository import ScriptRepository
from .script_preview_observer import (
    ScriptPreviewObserver, GlobalScriptObserver, 
    setup_script_persistence, add_persistence_to_previewer
)
from .script_analytics_service import ScriptAnalyticsService
from .script_persistence_setup import quick_setup, integrate_with_existing_previewer, health_check

__all__ = [
    "ElasticsearchService",
    "HybridSearchEngine",
    "TTSManager",
    "BaseTTSEngine", 
    "get_tts_manager",
    "initialize_tts_manager",
    "cleanup_tts_manager",
    # Removed: "SimpleAudioPlayer", "get_simple_audio_player", "initialize_simple_audio_player", "StreamingAudioPlayer"
    # Script Persistence
    "ScriptPersistenceManager",
    "ScriptRepository", 
    "ScriptPreviewObserver",
    "GlobalScriptObserver",
    "ScriptAnalyticsService",
    "setup_script_persistence",
    "add_persistence_to_previewer",
    "quick_setup",
    "integrate_with_existing_previewer",
    "health_check",
]