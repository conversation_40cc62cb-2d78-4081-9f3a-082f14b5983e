"""熔断器模式实现

用于保护TTS服务避免过载

Author: Claude Code
Date: 2025-08-10
"""

import asyncio
import time
import yaml
from typing import Any, Callable, Optional, List
from enum import Enum
from dataclasses import dataclass, field
from loguru import logger


class CircuitBreakerState(Enum):
    """熔断器状态"""
    CLOSED = "CLOSED"      # 正常状态，允许请求通过
    OPEN = "OPEN"          # 熔断状态，拒绝所有请求
    HALF_OPEN = "HALF_OPEN"  # 半开状态，允许少量请求测试


class CircuitBreakerOpenError(Exception):
    """熔断器开启异常"""
    pass


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: float = 0.3  # 失败率阈值
    recovery_timeout: float = 120.0  # 恢复超时（秒）
    window_size: float = 60.0  # 统计窗口大小（秒）
    min_requests: int = 10  # 最小请求数
    half_open_max_requests: int = 3  # 半开状态最大测试请求数
    
    @classmethod
    def from_yaml(cls, path: str = "config/tts_config.yaml"):
        """从YAML文件加载配置"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                cb_data = data.get('tts', {}).get('circuit_breaker', {})
                return cls(
                    failure_threshold=cb_data.get('failure_threshold', 0.3),
                    recovery_timeout=cb_data.get('recovery_timeout_seconds', 120.0),
                    window_size=cb_data.get('window_size_seconds', 60.0),
                    min_requests=cb_data.get('min_requests', 10),
                    half_open_max_requests=cb_data.get('half_open_max_requests', 3)
                )
        except FileNotFoundError:
            logger.warning(f"熔断器配置文件 {path} 不存在，使用默认配置")
            return cls()


@dataclass
class RequestRecord:
    """请求记录"""
    timestamp: float
    success: bool


class TTSCircuitBreaker:
    """TTS服务熔断器"""
    
    def __init__(self, config: Optional[CircuitBreakerConfig] = None):
        self.config = config or CircuitBreakerConfig.from_yaml()
        self.state = CircuitBreakerState.CLOSED
        self.failures: List[RequestRecord] = []
        self.last_open_time: float = 0
        self.half_open_requests: int = 0
        self._lock = asyncio.Lock()
        
        # 统计指标
        self.stats = {
            "total_requests": 0,
            "blocked_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "state_changes": 0,
            "current_failure_rate": 0.0
        }
        
        logger.info(f"熔断器初始化: 失败率阈值={self.config.failure_threshold}, "
                   f"恢复超时={self.config.recovery_timeout}s")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """通过熔断器调用函数"""
        async with self._lock:
            self.stats["total_requests"] += 1
            
            # 检查熔断器状态
            if self.state == CircuitBreakerState.OPEN:
                if time.time() - self.last_open_time > self.config.recovery_timeout:
                    # 超时后进入半开状态
                    self._transition_to_half_open()
                else:
                    # 仍在熔断期，拒绝请求
                    self.stats["blocked_requests"] += 1
                    logger.warning(f"⚡ 熔断器开启，拒绝请求 (已阻止 {self.stats['blocked_requests']} 个请求)")
                    raise CircuitBreakerOpenError("TTS服务熔断器开启，暂时拒绝请求")
            
            elif self.state == CircuitBreakerState.HALF_OPEN:
                if self.half_open_requests >= self.config.half_open_max_requests:
                    # 半开状态测试请求数已满，拒绝
                    self.stats["blocked_requests"] += 1
                    raise CircuitBreakerOpenError("熔断器半开状态测试中，请稍后重试")
                self.half_open_requests += 1
        
        # 执行实际调用
        try:
            result = await func(*args, **kwargs)
            await self._record_success()
            return result
        except Exception as e:
            await self._record_failure()
            raise
    
    async def _record_success(self):
        """记录成功请求"""
        async with self._lock:
            self.stats["successful_requests"] += 1
            self.failures.append(RequestRecord(time.time(), success=True))
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                # 半开状态下成功，可以恢复到关闭状态
                self._transition_to_closed()
            
            self._cleanup_old_records()
    
    async def _record_failure(self):
        """记录失败请求"""
        async with self._lock:
            self.stats["failed_requests"] += 1
            self.failures.append(RequestRecord(time.time(), success=False))
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                # 半开状态下失败，立即回到开启状态
                self._transition_to_open()
            elif self.state == CircuitBreakerState.CLOSED:
                # 检查是否需要熔断
                if self._should_trip():
                    self._transition_to_open()
            
            self._cleanup_old_records()
    
    def _should_trip(self) -> bool:
        """判断是否应该触发熔断"""
        recent_records = self._get_recent_records()
        
        if len(recent_records) < self.config.min_requests:
            return False  # 请求数太少，不触发熔断
        
        failures = sum(1 for r in recent_records if not r.success)
        failure_rate = failures / len(recent_records)
        self.stats["current_failure_rate"] = failure_rate
        
        should_trip = failure_rate > self.config.failure_threshold
        if should_trip:
            logger.warning(f"🔥 失败率 {failure_rate:.1%} 超过阈值 {self.config.failure_threshold:.1%}")
        
        return should_trip
    
    def _get_recent_records(self) -> List[RequestRecord]:
        """获取时间窗口内的记录"""
        cutoff_time = time.time() - self.config.window_size
        return [r for r in self.failures if r.timestamp > cutoff_time]
    
    def _cleanup_old_records(self):
        """清理过期记录"""
        cutoff_time = time.time() - self.config.window_size * 2  # 保留2倍窗口的记录
        self.failures = [r for r in self.failures if r.timestamp > cutoff_time]
    
    def _transition_to_open(self):
        """转换到开启状态"""
        if self.state != CircuitBreakerState.OPEN:
            self.state = CircuitBreakerState.OPEN
            self.last_open_time = time.time()
            self.stats["state_changes"] += 1
            logger.error(f"⚡ 熔断器触发：TTS服务失败率过高，进入熔断状态")
    
    def _transition_to_closed(self):
        """转换到关闭状态"""
        if self.state != CircuitBreakerState.CLOSED:
            self.state = CircuitBreakerState.CLOSED
            self.half_open_requests = 0
            self.stats["state_changes"] += 1
            logger.info(f"✅ 熔断器恢复：服务正常，熔断器关闭")
    
    def _transition_to_half_open(self):
        """转换到半开状态"""
        if self.state != CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.HALF_OPEN
            self.half_open_requests = 0
            self.stats["state_changes"] += 1
            logger.info(f"🔌 熔断器进入半开状态，开始测试服务恢复")
    
    def get_state(self) -> str:
        """获取当前状态"""
        return self.state.value
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        return {
            **self.stats,
            "state": self.state.value,
            "recent_records": len(self._get_recent_records())
        }
    
    def reset(self):
        """重置熔断器"""
        self.state = CircuitBreakerState.CLOSED
        self.failures.clear()
        self.last_open_time = 0
        self.half_open_requests = 0
        self.stats = {
            "total_requests": 0,
            "blocked_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "state_changes": 0,
            "current_failure_rate": 0.0
        }
        logger.info("熔断器已重置")