"""
Capsule Selector for Microcycle Narrative Model

Intelligently selects the next capsule based on weights, cooldowns, and context.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import random
from loguru import logger

from .capsule_templates import CapsuleType, CapsuleTemplate


@dataclass
class CapsuleState:
    """State of a capsule for tracking and selection"""
    capsule_type: CapsuleType
    template: CapsuleTemplate
    last_played_at: Optional[datetime] = None
    play_count: int = 0
    effectiveness_score: float = 1.0
    custom_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_data is None:
            self.custom_data = {}
    
    def is_available(self, current_time: datetime) -> bool:
        """Check if capsule is available for selection"""
        if self.last_played_at is None:
            return True
        
        cooldown_delta = timedelta(seconds=self.template.cooldown_seconds)
        return current_time - self.last_played_at >= cooldown_delta
    
    def get_weight(self, base_weight_multiplier: float = 1.0) -> float:
        """Calculate current weight for selection"""
        weight = self.template.weight * base_weight_multiplier
        
        # Adjust by effectiveness score
        weight *= self.effectiveness_score
        
        # Reduce weight if recently played
        if self.last_played_at:
            time_since_played = (datetime.utcnow() - self.last_played_at).total_seconds()
            recency_factor = min(1.0, time_since_played / self.template.cooldown_seconds)
            weight *= recency_factor
        
        return max(0.01, weight)  # Ensure minimum weight


@dataclass
class SelectionContext:
    """Context for capsule selection"""
    current_time: datetime
    played_capsules: List[str]  # List of played capsule IDs
    pending_capsules: List[str]  # List of pending capsule IDs
    recent_summary: str
    next_topic_preview: str
    audience_signals: Dict[str, Any]
    is_closing_requested: bool = False
    priority_type: Optional[CapsuleType] = None


class CapsuleSelector:
    """Intelligent selector for content capsules"""
    
    def __init__(self, templates_library):
        self.templates_library = templates_library
        self.capsule_states: Dict[str, CapsuleState] = {}
        self.selection_history: List[Tuple[datetime, str, CapsuleType]] = []
        self.logger = logger.bind(component="capsule_selector")
        
        # Selection strategy parameters
        self.config = {
            "diversity_weight": 0.3,  # Weight for type diversity
            "recency_penalty": 0.5,   # Penalty for recently played types
            "audience_signal_weight": 0.2,  # Weight for audience signals
            "max_history_length": 100,
            "opening_capsule_threshold": 5,  # Minutes before inserting opening for new users
            "halftime_interval": 600,  # Seconds between halftime summaries
        }
        
        self._initialize_capsule_states()
    
    def _initialize_capsule_states(self):
        """Initialize states for all available capsules"""
        for capsule_type in CapsuleType:
            templates = self.templates_library.get_templates(capsule_type)
            for i, template in enumerate(templates):
                state_id = f"{capsule_type.value}_{i}"
                self.capsule_states[state_id] = CapsuleState(
                    capsule_type=capsule_type,
                    template=template
                )
        
        self.logger.info(f"Initialized {len(self.capsule_states)} capsule states")
    
    def select_next_capsule(self, context: SelectionContext) -> Optional[Tuple[str, CapsuleState]]:
        """
        Select the next capsule based on context and strategy
        
        Returns:
            Tuple of (capsule_id, CapsuleState) or None if no suitable capsule
        """
        # Handle special cases first
        if context.is_closing_requested:
            return self._select_closing_capsule(context)
        
        if context.priority_type:
            return self._select_priority_type(context, context.priority_type)
        
        # Check if we need special capsules based on timing
        if self._should_insert_halftime(context):
            halftime_capsule = self._select_priority_type(context, CapsuleType.HALF_TIME_SUMMARY)
            if halftime_capsule:
                return halftime_capsule
        
        # Normal selection with weighted random
        available_capsules = self._get_available_capsules(context)
        if not available_capsules:
            self.logger.warning("No available capsules for selection")
            return None
        
        # Calculate weights for each available capsule
        weighted_capsules = []
        for capsule_id, state in available_capsules:
            weight = self._calculate_selection_weight(state, context)
            weighted_capsules.append((capsule_id, state, weight))
        
        # Sort by weight and add randomness for diversity
        weighted_capsules.sort(key=lambda x: x[2], reverse=True)
        
        # Use weighted random selection from top candidates
        top_candidates = weighted_capsules[:min(5, len(weighted_capsules))]
        if top_candidates:
            weights = [w for _, _, w in top_candidates]
            selected = random.choices(top_candidates, weights=weights, k=1)[0]
            capsule_id, state = selected[0], selected[1]
            
            self._record_selection(capsule_id, state, context.current_time)
            return capsule_id, state
        
        return None
    
    def _get_available_capsules(self, context: SelectionContext) -> List[Tuple[str, CapsuleState]]:
        """Get all capsules available for selection"""
        available = []
        for capsule_id, state in self.capsule_states.items():
            if state.is_available(context.current_time):
                # Skip capsules that were just played
                if capsule_id not in context.played_capsules[-3:]:
                    available.append((capsule_id, state))
        return available
    
    def _calculate_selection_weight(self, state: CapsuleState, context: SelectionContext) -> float:
        """Calculate selection weight for a capsule"""
        base_weight = state.get_weight()
        
        # Apply diversity bonus
        diversity_bonus = self._calculate_diversity_bonus(state.capsule_type, context)
        base_weight *= (1 + diversity_bonus * self.config["diversity_weight"])
        
        # Apply audience signal adjustments
        if context.audience_signals:
            signal_adjustment = self._calculate_audience_adjustment(state, context.audience_signals)
            base_weight *= (1 + signal_adjustment * self.config["audience_signal_weight"])
        
        # Apply context-based adjustments
        if state.capsule_type == CapsuleType.PRICE_VALUE:
            # Increase weight if we haven't talked about price recently
            if "price" not in context.recent_summary.lower():
                base_weight *= 1.5
        
        elif state.capsule_type == CapsuleType.PROOF_DEMO:
            # Increase weight after selling points
            recent_types = [t for _, _, t in self.selection_history[-3:]]
            if CapsuleType.SELLING_POINT in recent_types:
                base_weight *= 1.3
        
        return base_weight
    
    def _calculate_diversity_bonus(self, capsule_type: CapsuleType, context: SelectionContext) -> float:
        """Calculate diversity bonus to avoid repetition"""
        if not self.selection_history:
            return 0.0
        
        # Check recent history for same type
        recent_types = [t for _, _, t in self.selection_history[-5:]]
        same_type_count = recent_types.count(capsule_type)
        
        # Penalize if same type was used recently
        if same_type_count > 0:
            return -0.5 * same_type_count
        
        # Bonus for types not used recently
        return 0.5
    
    def _calculate_audience_adjustment(self, state: CapsuleState, signals: Dict[str, Any]) -> float:
        """Adjust weight based on audience signals"""
        adjustment = 0.0
        
        # Example adjustments based on signals
        if signals.get("new_users_spike", False):
            if state.capsule_type in [CapsuleType.OPENING, CapsuleType.PRODUCT_INTRO]:
                adjustment += 1.0
        
        if signals.get("high_engagement", False):
            if state.capsule_type in [CapsuleType.PRICE_VALUE, CapsuleType.CALL_TO_ACTION]:
                adjustment += 0.8
        
        if signals.get("questions_pending", 0) > 0:
            if state.capsule_type == CapsuleType.QA:
                adjustment += 1.5
        
        return adjustment
    
    def _should_insert_halftime(self, context: SelectionContext) -> bool:
        """Check if we should insert a halftime summary"""
        if not self.selection_history:
            return False
        
        # Check time since last halftime
        halftime_history = [
            (time, cid) for time, cid, ctype in self.selection_history
            if ctype == CapsuleType.HALF_TIME_SUMMARY
        ]
        
        if halftime_history:
            last_halftime = halftime_history[-1][0]
            time_since = (context.current_time - last_halftime).total_seconds()
            return time_since >= self.config["halftime_interval"]
        
        # Insert first halftime after enough content
        return len(self.selection_history) >= 10
    
    def _select_closing_capsule(self, context: SelectionContext) -> Optional[Tuple[str, CapsuleState]]:
        """Select a closing capsule"""
        return self._select_priority_type(context, CapsuleType.CLOSING)
    
    def _select_priority_type(self, context: SelectionContext, 
                            capsule_type: CapsuleType) -> Optional[Tuple[str, CapsuleState]]:
        """Select a capsule of a specific type"""
        candidates = [
            (cid, state) for cid, state in self.capsule_states.items()
            if state.capsule_type == capsule_type
        ]
        
        if candidates:
            # Choose the least recently used variant
            candidates.sort(key=lambda x: x[1].last_played_at or datetime.min)
            capsule_id, state = candidates[0]
            self._record_selection(capsule_id, state, context.current_time)
            return capsule_id, state
        
        return None
    
    def _record_selection(self, capsule_id: str, state: CapsuleState, current_time: datetime):
        """Record a capsule selection"""
        state.last_played_at = current_time
        state.play_count += 1
        
        self.selection_history.append((current_time, capsule_id, state.capsule_type))
        
        # Trim history if too long
        if len(self.selection_history) > self.config["max_history_length"]:
            self.selection_history = self.selection_history[-self.config["max_history_length"]:]
        
        self.logger.info(f"Selected capsule: {capsule_id} (type: {state.capsule_type.value})")
    
    def update_effectiveness(self, capsule_id: str, score: float):
        """Update effectiveness score for a capsule based on performance"""
        if capsule_id in self.capsule_states:
            state = self.capsule_states[capsule_id]
            # Exponential moving average
            state.effectiveness_score = 0.7 * state.effectiveness_score + 0.3 * score
            self.logger.debug(f"Updated effectiveness for {capsule_id}: {state.effectiveness_score:.2f}")
    
    def get_selection_stats(self) -> Dict[str, Any]:
        """Get statistics about capsule selection"""
        stats = {
            "total_selections": len(self.selection_history),
            "unique_capsules_played": len(set(cid for _, cid, _ in self.selection_history)),
            "type_distribution": {},
            "average_effectiveness": 0.0
        }
        
        # Calculate type distribution
        for capsule_type in CapsuleType:
            count = sum(1 for _, _, t in self.selection_history if t == capsule_type)
            stats["type_distribution"][capsule_type.value] = count
        
        # Calculate average effectiveness
        effectiveness_scores = [state.effectiveness_score for state in self.capsule_states.values()]
        if effectiveness_scores:
            stats["average_effectiveness"] = sum(effectiveness_scores) / len(effectiveness_scores)
        
        return stats