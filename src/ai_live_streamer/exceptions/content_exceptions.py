"""内容相关的异常定义

Author: Claude Code
Date: 2025-08-11
"""


class ContentGenerationError(Exception):
    """内容生成失败异常"""
    pass


class ContentNotReadyError(Exception):
    """内容尚未准备好异常 - 用于超时后的快速响应"""
    def __init__(self, message: str = "Content is being generated", 
                 error_code: str = "CONTENT_NOT_READY",
                 retry_after_ms: int = 1000):
        super().__init__(message)
        self.error_code = error_code
        self.retry_after_ms = retry_after_ms