"""
客户端连接相关异常定义

这些异常用于在系统各组件之间传递清晰的客户端连接状态信息，
支持熔断机制和智能决策。
"""

from typing import Optional, List


class ClientConnectionError(Exception):
    """客户端连接相关的异常基类
    
    所有客户端连接相关的异常都应继承此类，
    用于建立清晰的异常契约。
    """
    
    def __init__(self, message: str, client_id: Optional[str] = None):
        """
        Args:
            message: 错误信息
            client_id: 相关的客户端ID（如果适用）
        """
        super().__init__(message)
        self.client_id = client_id
        self.message = message


class NoClientsConnectedError(ClientConnectionError):
    """没有客户端连接
    
    当系统检测到没有任何活跃的客户端连接时抛出此异常。
    这是触发播放熔断的主要信号。
    """
    
    def __init__(self, message: str = "No clients connected to receive audio"):
        super().__init__(message)


class AllClientsDisconnectedError(ClientConnectionError):
    """所有客户端都断开了
    
    当系统检测到最后一个客户端断开连接时抛出此异常。
    与 NoClientsConnectedError 的区别在于这表示一个状态变化。
    """
    
    def __init__(self, 
                 message: str = "All clients have disconnected",
                 last_client_id: Optional[str] = None,
                 previously_connected_count: int = 0):
        """
        Args:
            message: 错误信息
            last_client_id: 最后断开的客户端ID
            previously_connected_count: 之前连接的客户端数量
        """
        super().__init__(message, last_client_id)
        self.last_client_id = last_client_id
        self.previously_connected_count = previously_connected_count


class ClientHeartbeatTimeoutError(ClientConnectionError):
    """客户端心跳超时
    
    当客户端心跳检测超时时抛出此异常。
    表示客户端可能已经失去响应但TCP连接还未断开。
    """
    
    def __init__(self,
                 client_id: str,
                 timeout_seconds: float,
                 last_heartbeat_time: Optional[float] = None):
        """
        Args:
            client_id: 超时的客户端ID
            timeout_seconds: 超时阈值（秒）
            last_heartbeat_time: 最后一次心跳时间戳
        """
        message = f"Client {client_id} heartbeat timeout after {timeout_seconds}s"
        super().__init__(message, client_id)
        self.timeout_seconds = timeout_seconds
        self.last_heartbeat_time = last_heartbeat_time