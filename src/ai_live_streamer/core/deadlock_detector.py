"""简化的超时检测机制

符合新架构设计原则：
- 单一职责：专注于基本超时检测
- 简单并发：最小化状态管理
- 明确控制流：清晰的超时逻辑

Author: Claude Code  
Date: 2025-08-05
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from loguru import logger


class SimpleTimeoutDetector:
    """简化的超时检测器
    
    职责：
    1. 基本的操作超时检测
    2. 简单的统计信息
    """
    
    def __init__(self):
        self.operation_timeouts: Dict[str, float] = {}
        self.timeout_stats = {
            "total_operations": 0,
            "total_timeouts": 0,
        }
        logger.debug("🔍 SimpleTimeoutDetector initialized")
    
    def start_operation(self, operation_id: str, timeout_seconds: float = 30.0):
        """开始一个需要超时检测的操作"""
        self.operation_timeouts[operation_id] = time.time() + timeout_seconds
        self.timeout_stats["total_operations"] += 1
        logger.debug(f"⏰ Started operation {operation_id} with {timeout_seconds}s timeout")
    
    def finish_operation(self, operation_id: str):
        """完成操作"""
        if operation_id in self.operation_timeouts:
            del self.operation_timeouts[operation_id]
            logger.debug(f"✅ Operation {operation_id} completed")
    
    def check_timeout(self, operation_id: str) -> bool:
        """检查操作是否超时"""
        if operation_id not in self.operation_timeouts:
            return False
            
        if time.time() > self.operation_timeouts[operation_id]:
            logger.warning(f"⏰ Operation {operation_id} timed out")
            self.timeout_stats["total_timeouts"] += 1
            del self.operation_timeouts[operation_id]
            return True
        
        return False
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        return {
            "active_operations": len(self.operation_timeouts),
            **self.timeout_stats
        }
    
    # ====== 兼容性方法：锁管理接口 ======
    
    def register_lock_acquired(self, lock_name: str, holder_task: str, acquired_at: Optional[str] = None, 
                               component: Optional[str] = None, lock_timeout: Optional[float] = None, 
                               metadata: Optional[Dict[str, Any]] = None):
        """注册锁获取事件（兼容性方法）
        
        Args:
            lock_name: 锁名称
            holder_task: 持有锁的任务名
            acquired_at: 获取时间（可选）
            component: 组件名称（可选）
            lock_timeout: 锁超时时间（可选）
            metadata: 额外元数据（可选）
        """
        # 简化实现：仅记录日志，不维护复杂状态
        logger.debug(f"🔒 Lock acquired: {lock_name} by {holder_task}")
        
        # 可选：基于锁名开始超时检测
        self.start_operation(f"lock_{lock_name}", timeout_seconds=60.0)
    
    def register_lock_released(self, lock_name: str):
        """注册锁释放事件（兼容性方法）
        
        Args:
            lock_name: 锁名称
        """
        logger.debug(f"🔓 Lock released: {lock_name}")
        
        # 结束对应的超时检测
        self.finish_operation(f"lock_{lock_name}")
    
    def register_lock_timeout(self, lock_name: str, waiting_task: str):
        """注册锁超时事件（兼容性方法）
        
        Args:
            lock_name: 锁名称  
            waiting_task: 等待锁的任务名
        """
        logger.warning(f"⏰ Lock timeout: {waiting_task} failed to acquire {lock_name}")
        
        # 增加超时统计
        self.timeout_stats["total_timeouts"] += 1
    
    # ====== 兼容性方法：监控接口 ======
    
    def get_lock_status(self) -> Dict[str, Any]:
        """获取锁状态统计（兼容性方法）
        
        Returns:
            Dict containing lock status and statistics
        """
        return {
            "total_active_locks": len([op for op in self.operation_timeouts.keys() if op.startswith("lock_")]),
            "monitoring_active": True,
            "stats": {
                "total_operations": self.timeout_stats["total_operations"],
                "total_timeouts": self.timeout_stats["total_timeouts"],
                "active_operations": len(self.operation_timeouts)
            }
        }
    
    def get_deadlock_alerts(self, resolved: bool = False) -> List[Dict[str, Any]]:
        """获取死锁警报（兼容性方法）
        
        Args:
            resolved: 是否包含已解决的警报
            
        Returns:
            List of deadlock alerts
        """
        # 简化实现：基于超时统计生成模拟警报
        alerts = []
        
        # 如果有超时，生成警报
        if self.timeout_stats["total_timeouts"] > 0:
            alerts.append({
                "id": f"timeout_alert_{int(time.time())}",
                "type": "timeout",
                "severity": "warning",
                "message": f"Detected {self.timeout_stats['total_timeouts']} timeout(s)",
                "timestamp": datetime.utcnow().isoformat(),
                "resolved": resolved,
                "details": {
                    "total_timeouts": self.timeout_stats["total_timeouts"],
                    "active_operations": len(self.operation_timeouts)
                }
            })
        
        return alerts
    
    def cleanup(self):
        """清理资源"""
        self.operation_timeouts.clear()
        logger.debug("🧹 SimpleTimeoutDetector cleanup completed")


# 全局实例
_timeout_detector: Optional[SimpleTimeoutDetector] = None


def get_deadlock_detector() -> SimpleTimeoutDetector:
    """获取全局超时检测器实例（保持接口兼容性）"""
    global _timeout_detector
    if _timeout_detector is None:
        _timeout_detector = SimpleTimeoutDetector()
    return _timeout_detector


# 兼容性函数
def get_timeout_detector() -> SimpleTimeoutDetector:
    """获取超时检测器"""
    return get_deadlock_detector()