"""Dynamic Script Generator Component (V2)

Implements dynamic script generation with async generator support,
intelligent preloading, and context continuity.

Follows new architecture principles:
- Simple async patterns
- Clear separation of concerns
- Fail-fast error handling
- Efficient resource management

Author: Claude Code
Date: 2025-08-05
"""

import asyncio
from typing import AsyncGenerator, Optional, List, Any, Dict
from datetime import datetime
from dataclasses import dataclass
from collections import deque

from loguru import logger

from ..core.exceptions import ServiceError
from ..models.persona import PersonaConfig
from ..core.semantic_sentence_splitter import SemanticSentenceSplitter


@dataclass
class GenerationConfig:
    """Configuration for dynamic script generation"""
    buffer_size: int = 10
    preload_threshold: int = 3
    max_generation_time_sec: float = 30.0
    context_window_size: int = 5
    min_content_length: int = 50
    max_content_length: int = 300


class ContentBuffer:
    """Thread-safe content buffer for preloading"""
    
    def __init__(self, max_size: int = 10):
        self.max_size = max_size
        self._queue = asyncio.Queue(maxsize=max_size)
        
    def is_empty(self) -> bool:
        return self._queue.empty()
    
    def is_full(self) -> bool:
        return self._queue.full()
    
    def size(self) -> int:
        return self._queue.qsize()
    
    async def add(self, content: str) -> None:
        if not self.is_full():
            await self._queue.put(content)
    
    def add_nowait(self, content: str) -> None:
        if not self.is_full():
            self._queue.put_nowait(content)
    
    async def get(self) -> Optional[str]:
        try:
            return await asyncio.wait_for(self._queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
    
    def get_nowait(self) -> Optional[str]:
        try:
            return self._queue.get_nowait()
        except asyncio.QueueEmpty:
            return None
    
    def clear(self) -> None:
        while not self.is_empty():
            try:
                self._queue.get_nowait()
            except asyncio.QueueEmpty:
                break


class ScriptGenerator:
    """Base script generator with async generation support"""
    
    def __init__(
        self,
        llm_adapter: Any,
        sentence_splitter: Optional[SemanticSentenceSplitter] = None,
        persona: Optional[PersonaConfig] = None,
        config: Optional[GenerationConfig] = None
    ):
        self.llm_adapter = llm_adapter
        self.sentence_splitter = sentence_splitter or SemanticSentenceSplitter()
        self.persona = persona
        self.config = config or GenerationConfig()
        
        # Generation state
        self._generation_history: deque = deque(maxlen=config.context_window_size * 3 if config else 15)
        self._is_active = False
        
        logger.info(f"✅ ScriptGenerator initialized with {type(llm_adapter).__name__}")
    
    async def generate_content(self, topic: str) -> List[str]:
        """Generate a single batch of content"""
        prompt = self._build_prompt(topic)
        
        try:
            # Generate content using LLM
            response = await asyncio.wait_for(
                self.llm_adapter.generate(prompt),
                timeout=self.config.max_generation_time_sec
            )
            
            generated_text = response.get("text", "").strip()
            
            if not generated_text:
                raise ServiceError(
                    "LLM returned empty response",
                    "script_generator",
                    "EMPTY_RESPONSE"
                )
            
            # Split into sentences
            sentences = self.sentence_splitter.split_into_sentences(generated_text)
            
            # Update history
            self._generation_history.extend(sentences)
            
            return sentences
            
        except asyncio.TimeoutError:
            raise ServiceError(
                f"Generation timeout after {self.config.max_generation_time_sec}s",
                "script_generator",
                "GENERATION_TIMEOUT"
            )
        except Exception as e:
            logger.error(f"Generation error: {str(e)}")
            raise ServiceError(
                f"Failed to generate content: {str(e)}",
                "script_generator",
                "GENERATION_ERROR"
            )
    
    async def generate_stream(self, topic: str) -> AsyncGenerator[str, None]:
        """Generate content stream dynamically"""
        self._is_active = True
        
        try:
            while self._is_active:
                # Generate next batch
                sentences = await self.generate_content(topic)
                
                # Yield each sentence
                for sentence in sentences:
                    if not self._is_active:
                        break
                    yield sentence
                    
                # Small delay between batches
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error(f"Stream generation error: {str(e)}")
            raise
        finally:
            self._is_active = False
    
    async def stop(self) -> None:
        """Stop generation"""
        self._is_active = False
    
    def _build_prompt(self, topic: str) -> str:
        """Build generation prompt with context"""
        prompt_parts = [
            f"主题：{topic}",
            f"风格：直播解说，自然流畅"
        ]
        
        if self.persona:
            prompt_parts.append(f"角色：{self.persona.persona_id}")
            prompt_parts.append(f"语气：{self.persona.tone}")
        
        # Add context from history
        if self._generation_history:
            recent = list(self._generation_history)[-self.config.context_window_size:]
            context_text = " ".join(recent)
            prompt_parts.append(f"\n最近内容：{context_text}")
        
        prompt_parts.extend([
            "\n请继续生成直播内容：",
            "1. 与前文自然衔接",
            "2. 保持话题连贯性",
            f"3. 长度控制在{self.config.min_content_length}-{self.config.max_content_length}字",
            "4. 只返回内容，不要说明",
            "\n内容："
        ])
        
        return "\n".join(prompt_parts)


class SmartScriptGenerator(ScriptGenerator):
    """Script generator with intelligent preloading"""
    
    def __init__(
        self,
        llm_adapter: Any,
        sentence_splitter: Optional[SemanticSentenceSplitter] = None,
        persona: Optional[PersonaConfig] = None,
        config: Optional[GenerationConfig] = None
    ):
        super().__init__(llm_adapter, sentence_splitter, persona, config)
        
        # Preloading components
        self._content_buffer = ContentBuffer(config.buffer_size if config else 10)
        self._preload_task: Optional[asyncio.Task] = None
        
        logger.info("✅ SmartScriptGenerator initialized with preloading")
    
    async def generate_stream(self, topic: str) -> AsyncGenerator[str, None]:
        """Generate stream with intelligent preloading"""
        self._is_active = True
        
        try:
            # Initialize buffer
            await self._initialize_buffer(topic)
            
            # Start preload task
            self._preload_task = asyncio.create_task(
                self._preload_loop(topic)
            )
            
            # Stream from buffer
            while self._is_active:
                content = await self._get_from_buffer()
                if content:
                    yield content
                else:
                    # Buffer empty, wait a bit
                    await asyncio.sleep(0.1)
                    
        except Exception as e:
            logger.error(f"Smart stream error: {str(e)}")
            raise
        finally:
            self._is_active = False
            if self._preload_task and not self._preload_task.done():
                self._preload_task.cancel()
                try:
                    await self._preload_task
                except asyncio.CancelledError:
                    pass
    
    async def _initialize_buffer(self, topic: str) -> None:
        """Pre-fill buffer before streaming starts"""
        logger.debug("Initializing content buffer...")
        
        # Generate initial content to fill buffer
        initial_fills = min(3, self.config.buffer_size // 2)
        
        for _ in range(initial_fills):
            try:
                sentences = await self.generate_content(topic)
                for sentence in sentences:
                    await self._content_buffer.add(sentence)
            except Exception as e:
                logger.warning(f"Initial buffer fill error: {str(e)}")
                # Continue with partial buffer
                break
    
    async def _preload_loop(self, topic: str) -> None:
        """Background task to keep buffer filled"""
        logger.debug("Starting preload loop...")
        
        while self._is_active:
            try:
                # Check if buffer needs refilling
                if self._content_buffer.size() < self.config.preload_threshold:
                    # Generate more content
                    sentences = await self.generate_content(topic)
                    
                    # Add to buffer
                    for sentence in sentences:
                        if not self._is_active:
                            break
                        await self._content_buffer.add(sentence)
                
                # Adaptive delay based on buffer status
                if self._content_buffer.is_full():
                    await asyncio.sleep(1.0)  # Buffer full, wait longer
                else:
                    await asyncio.sleep(0.5)  # Need more content
                    
            except Exception as e:
                logger.warning(f"Preload error: {str(e)}")
                await asyncio.sleep(2.0)  # Error recovery delay
    
    async def _get_from_buffer(self) -> Optional[str]:
        """Get content from buffer"""
        return await self._content_buffer.get()
    
    async def stop(self) -> None:
        """Stop generation and cleanup"""
        await super().stop()
        self._content_buffer.clear()