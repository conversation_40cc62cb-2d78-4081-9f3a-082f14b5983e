"""错误处理和恢复策略

实现具体的错误恢复策略，包括：
1. 8 种故障场景的恢复策略
2. FallbackAudioManager 预设音频管理
3. 错误恢复决策树
4. 熔断器模式

符合新架构设计原则：
- 单一职责：专注于错误处理和恢复
- Fail-Fast 原则：不隐藏错误，但提供合理恢复
- 简单并发：使用清晰的异步错误处理

Author: Claude Code
Date: 2025-08-05
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field

from loguru import logger

from ..core.exceptions import ServiceError


class ErrorType(Enum):
    """错误类型枚举"""
    TTS_CONNECTION_FAILED = "tts_connection_failed"
    TTS_GENERATION_FAILED = "tts_generation_failed"
    LLM_SERVICE_UNAVAILABLE = "llm_service_unavailable"
    SCRIPT_GENERATION_FAILED = "script_generation_failed"
    AUDIO_PLAYBACK_FAILED = "audio_playback_failed"
    NETWORK_CONNECTION_LOST = "network_connection_lost"
    RESOURCE_EXHAUSTED = "resource_exhausted"
    UNKNOWN_ERROR = "unknown_error"


class RecoveryAction(Enum):
    """恢复动作枚举"""
    RETRY = "retry"
    FALLBACK = "fallback"
    DEGRADE = "degrade"
    RESTART = "restart"
    SKIP = "skip"
    ABORT = "abort"


@dataclass
class ErrorContext:
    """错误上下文"""
    error_type: ErrorType
    error_message: str
    timestamp: datetime
    component: str
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RecoveryResult:
    """恢复结果"""
    success: bool
    action_taken: RecoveryAction
    message: str
    recovered_data: Any = None
    retry_after: Optional[float] = None


class IErrorRecoveryStrategy(ABC):
    """错误恢复策略接口"""
    
    @abstractmethod
    async def can_handle(self, error_context: ErrorContext) -> bool:
        """判断是否能处理该错误"""
        pass
    
    @abstractmethod
    async def recover(self, error_context: ErrorContext) -> RecoveryResult:
        """执行错误恢复"""
        pass


class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: tuple = (Exception,)
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open
        
        logger.debug(f"CircuitBreaker initialized: threshold={failure_threshold}, timeout={recovery_timeout}")
    
    async def call(self, func: Callable, *args, **kwargs):
        """执行函数调用，应用熔断器模式"""
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half_open"
                logger.info("🔄 CircuitBreaker: Attempting recovery (half-open)")
            else:
                raise ServiceError(
                    "Circuit breaker is open - service temporarily unavailable",
                    "circuit_breaker",
                    "CIRCUIT_OPEN"
                )
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            
            # 成功时重置熔断器
            if self.state == "half_open":
                self.state = "closed"
                self.failure_count = 0
                logger.info("✅ CircuitBreaker: Recovered (closed)")
            
            return result
            
        except self.expected_exception as e:
            self._record_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置熔断器"""
        if self.last_failure_time is None:
            return False
        
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _record_failure(self) -> None:
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            logger.warning(f"⚡ CircuitBreaker: Opened due to {self.failure_count} failures")
    
    def get_status(self) -> Dict[str, Any]:
        """获取熔断器状态"""
        return {
            "state": self.state,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "last_failure_time": self.last_failure_time,
            "recovery_timeout": self.recovery_timeout
        }


class FallbackAudioManager:
    """预设音频管理器"""
    
    def __init__(self):
        self.fallback_audio_pool = self._init_fallback_audio_pool()
        self.current_fallback_index = 0
        
        logger.info(f"📻 FallbackAudioManager initialized with {len(self.fallback_audio_pool)} audio clips")
    
    def _init_fallback_audio_pool(self) -> List[Dict[str, Any]]:
        """初始化预设音频池"""
        return [
            {
                "text": "请稍等，我们正在处理您的请求...",
                "type": "processing",
                "duration": 3.0
            },
            {
                "text": "系统正在恢复中，请稍候片刻。",
                "type": "recovery",
                "duration": 3.5
            },
            {
                "text": "感谢您的耐心等待，我们马上为您继续。",
                "type": "patience",
                "duration": 4.0
            },
            {
                "text": "让我们回到刚才的内容。",
                "type": "continue",
                "duration": 2.5
            },
            {
                "text": "这里有一些技术问题，我们正在解决。",
                "type": "technical",
                "duration": 4.0
            },
            {
                "text": "请大家稍等一下，马上就好。",
                "type": "waiting",
                "duration": 3.0
            }
        ]
    
    def get_fallback_audio(self, error_type: ErrorType) -> Dict[str, Any]:
        """根据错误类型获取合适的预设音频"""
        # 根据错误类型选择合适的音频
        if error_type == ErrorType.TTS_CONNECTION_FAILED:
            audio_type = "technical"
        elif error_type == ErrorType.SCRIPT_GENERATION_FAILED:
            audio_type = "processing"
        elif error_type == ErrorType.NETWORK_CONNECTION_LOST:
            audio_type = "recovery"
        else:
            audio_type = "waiting"
        
        # 寻找匹配类型的音频
        for audio in self.fallback_audio_pool:
            if audio["type"] == audio_type:
                return audio
        
        # 如果没有匹配的，返回轮询音频
        audio = self.fallback_audio_pool[self.current_fallback_index]
        self.current_fallback_index = (self.current_fallback_index + 1) % len(self.fallback_audio_pool)
        
        return audio
    
    def add_fallback_audio(self, text: str, audio_type: str, duration: float) -> None:
        """添加新的预设音频"""
        self.fallback_audio_pool.append({
            "text": text,
            "type": audio_type,
            "duration": duration
        })
        logger.debug(f"📻 Added fallback audio: {text[:30]}...")


# 具体的恢复策略实现

class TTSConnectionRecoveryStrategy(IErrorRecoveryStrategy):
    """TTS 连接错误恢复策略"""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 2.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
    
    async def can_handle(self, error_context: ErrorContext) -> bool:
        return error_context.error_type == ErrorType.TTS_CONNECTION_FAILED
    
    async def recover(self, error_context: ErrorContext) -> RecoveryResult:
        if error_context.retry_count < self.max_retries:
            # 尝试重连
            logger.info(f"🔄 Retrying TTS connection (attempt {error_context.retry_count + 1}/{self.max_retries})")
            await asyncio.sleep(self.retry_delay * (error_context.retry_count + 1))  # 指数退避
            
            return RecoveryResult(
                success=True,
                action_taken=RecoveryAction.RETRY,
                message=f"Retrying TTS connection (attempt {error_context.retry_count + 1})",
                retry_after=self.retry_delay
            )
        else:
            # 切换到预设音频
            logger.warning("🔊 TTS connection failed, switching to fallback audio")
            return RecoveryResult(
                success=True,
                action_taken=RecoveryAction.FALLBACK,
                message="Switched to fallback audio due to TTS connection failure",
                recovered_data={"use_fallback_audio": True}
            )


class LLMServiceRecoveryStrategy(IErrorRecoveryStrategy):
    """LLM 服务错误恢复策略"""
    
    def __init__(self, fallback_providers: List[str] = None):
        self.fallback_providers = fallback_providers or ["mock"]
        self.current_fallback_index = 0
    
    async def can_handle(self, error_context: ErrorContext) -> bool:
        return error_context.error_type == ErrorType.LLM_SERVICE_UNAVAILABLE
    
    async def recover(self, error_context: ErrorContext) -> RecoveryResult:
        if self.current_fallback_index < len(self.fallback_providers):
            fallback_provider = self.fallback_providers[self.current_fallback_index]
            self.current_fallback_index += 1
            
            logger.info(f"🔄 Switching to fallback LLM provider: {fallback_provider}")
            
            return RecoveryResult(
                success=True,
                action_taken=RecoveryAction.FALLBACK,
                message=f"Switched to fallback LLM provider: {fallback_provider}",
                recovered_data={"fallback_provider": fallback_provider}
            )
        else:
            # 降级到预设脚本
            logger.warning("📝 All LLM providers failed, using preset scripts")
            return RecoveryResult(
                success=True,
                action_taken=RecoveryAction.DEGRADE,
                message="Using preset scripts due to LLM service unavailability",
                recovered_data={"use_preset_scripts": True}
            )


class NetworkConnectionRecoveryStrategy(IErrorRecoveryStrategy):
    """网络连接错误恢复策略"""
    
    def __init__(self, max_retries: int = 5, initial_delay: float = 1.0):
        self.max_retries = max_retries
        self.initial_delay = initial_delay
    
    async def can_handle(self, error_context: ErrorContext) -> bool:
        return error_context.error_type == ErrorType.NETWORK_CONNECTION_LOST
    
    async def recover(self, error_context: ErrorContext) -> RecoveryResult:
        if error_context.retry_count < self.max_retries:
            # 指数退避重试
            delay = self.initial_delay * (2 ** error_context.retry_count)
            logger.info(f"🌐 Retrying network connection in {delay:.1f}s (attempt {error_context.retry_count + 1})")
            
            return RecoveryResult(
                success=True,
                action_taken=RecoveryAction.RETRY,
                message=f"Retrying network connection in {delay:.1f}s",
                retry_after=delay
            )
        else:
            # 切换到离线模式
            logger.warning("📡 Network recovery failed, switching to offline mode")
            return RecoveryResult(
                success=True,
                action_taken=RecoveryAction.DEGRADE,
                message="Switched to offline mode due to persistent network issues",
                recovered_data={"offline_mode": True}
            )


class ResourceExhaustedRecoveryStrategy(IErrorRecoveryStrategy):
    """资源耗尽错误恢复策略"""
    
    async def can_handle(self, error_context: ErrorContext) -> bool:
        return error_context.error_type == ErrorType.RESOURCE_EXHAUSTED
    
    async def recover(self, error_context: ErrorContext) -> RecoveryResult:
        # 清理资源并重启服务
        logger.warning("💾 Resource exhausted, initiating cleanup and restart")
        
        # 这里可以添加具体的资源清理逻辑
        await asyncio.sleep(1.0)  # 模拟清理时间
        
        return RecoveryResult(
            success=True,
            action_taken=RecoveryAction.RESTART,
            message="Cleaned up resources and restarting service",
            recovered_data={"restart_required": True}
        )


class ErrorHandler:
    """错误处理器主类"""
    
    def __init__(self, fail_fast: bool = False):
        # Fail-fast 模式配置
        self.fail_fast = fail_fast
        
        # 恢复策略注册表
        self.recovery_strategies: List[IErrorRecoveryStrategy] = []
        self.fallback_audio_manager = FallbackAudioManager()
        
        # 熔断器注册表
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        
        # 错误统计
        self.error_stats = {
            "total_errors": 0,
            "recovered_errors": 0,
            "failed_recoveries": 0,
            "errors_by_type": {},
            "recovery_actions": {}
        }
        
        # 注册默认策略（仅在非 fail-fast 模式下）
        if not self.fail_fast:
            self._register_default_strategies()
        
        mode_desc = "fail-fast" if self.fail_fast else "with recovery strategies"
        logger.info(f"🛡️ ErrorHandler initialized in {mode_desc} mode")
    
    def _register_default_strategies(self) -> None:
        """注册默认的恢复策略"""
        self.recovery_strategies.extend([
            TTSConnectionRecoveryStrategy(),
            LLMServiceRecoveryStrategy(),
            NetworkConnectionRecoveryStrategy(),
            ResourceExhaustedRecoveryStrategy()
        ])
    
    def register_strategy(self, strategy: IErrorRecoveryStrategy) -> None:
        """注册恢复策略"""
        self.recovery_strategies.append(strategy)
        logger.debug(f"Registered recovery strategy: {type(strategy).__name__}")
    
    def get_circuit_breaker(self, service_name: str, **kwargs) -> CircuitBreaker:
        """获取或创建熔断器"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker(**kwargs)
            logger.debug(f"Created circuit breaker for service: {service_name}")
        
        return self.circuit_breakers[service_name]
    
    async def handle_error(
        self,
        error: Exception,
        component: str,
        error_type: Optional[ErrorType] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> RecoveryResult:
        """处理错误并尝试恢复"""
        # 确定错误类型
        if error_type is None:
            error_type = self._classify_error(error)
        
        # 创建错误上下文
        error_context = ErrorContext(
            error_type=error_type,
            error_message=str(error),
            timestamp=datetime.utcnow(),
            component=component,
            metadata=metadata or {}
        )
        
        # 更新统计
        self._update_error_stats(error_type)
        
        logger.warning(f"🚨 Handling error in {component}: {error_type.value} - {str(error)[:100]}")
        
        # Fail-fast 模式：直接抛出异常，不尝试恢复
        if self.fail_fast:
            logger.error(f"💥 Fail-fast mode: Re-raising error without recovery attempts")
            logger.error(f"Error details: {error_context.error_message}")
            logger.error(f"Component: {component}, Type: {error_type.value}")
            if metadata:
                logger.error(f"Metadata: {metadata}")
            
            # 直接重新抛出原始异常，保持完整的堆栈跟踪
            raise error
        
        # 寻找合适的恢复策略
        for strategy in self.recovery_strategies:
            try:
                if await strategy.can_handle(error_context):
                    logger.info(f"🔧 Using recovery strategy: {type(strategy).__name__}")
                    
                    result = await strategy.recover(error_context)
                    
                    if result.success:
                        self.error_stats["recovered_errors"] += 1
                        self._update_recovery_stats(result.action_taken)
                        logger.info(f"✅ Error recovery successful: {result.message}")
                    else:
                        self.error_stats["failed_recoveries"] += 1
                        logger.warning(f"❌ Error recovery failed: {result.message}")
                    
                    return result
                    
            except Exception as recovery_error:
                logger.error(f"Recovery strategy failed: {recovery_error}")
                continue
        
        # 没有合适的恢复策略，返回通用失败结果
        self.error_stats["failed_recoveries"] += 1
        
        logger.error("❌ No suitable recovery strategy found")
        return RecoveryResult(
            success=False,
            action_taken=RecoveryAction.ABORT,
            message=f"No recovery strategy available for {error_type.value}"
        )
    
    def _classify_error(self, error: Exception) -> ErrorType:
        """根据异常类型分类错误"""
        error_message = str(error).lower()
        
        if "tts" in error_message and "connection" in error_message:
            return ErrorType.TTS_CONNECTION_FAILED
        elif "tts" in error_message:
            return ErrorType.TTS_GENERATION_FAILED
        elif "llm" in error_message or "openai" in error_message:
            return ErrorType.LLM_SERVICE_UNAVAILABLE
        elif "script" in error_message and "generation" in error_message:
            return ErrorType.SCRIPT_GENERATION_FAILED
        elif "audio" in error_message and "playback" in error_message:
            return ErrorType.AUDIO_PLAYBACK_FAILED
        elif "network" in error_message or "connection" in error_message:
            return ErrorType.NETWORK_CONNECTION_LOST
        elif "memory" in error_message or "resource" in error_message:
            return ErrorType.RESOURCE_EXHAUSTED
        else:
            return ErrorType.UNKNOWN_ERROR
    
    def _update_error_stats(self, error_type: ErrorType) -> None:
        """更新错误统计"""
        self.error_stats["total_errors"] += 1
        
        type_name = error_type.value
        if type_name not in self.error_stats["errors_by_type"]:
            self.error_stats["errors_by_type"][type_name] = 0
        self.error_stats["errors_by_type"][type_name] += 1
    
    def _update_recovery_stats(self, action: RecoveryAction) -> None:
        """更新恢复动作统计"""
        action_name = action.value
        if action_name not in self.error_stats["recovery_actions"]:
            self.error_stats["recovery_actions"][action_name] = 0
        self.error_stats["recovery_actions"][action_name] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取错误处理统计"""
        total = self.error_stats["total_errors"]
        recovery_rate = (
            self.error_stats["recovered_errors"] / total * 100
            if total > 0 else 0
        )
        
        return {
            **self.error_stats,
            "recovery_rate_percent": recovery_rate,
            "circuit_breakers": {
                name: breaker.get_status()
                for name, breaker in self.circuit_breakers.items()
            }
        }
    
    def get_fallback_audio(self, error_type: ErrorType) -> Dict[str, Any]:
        """获取预设音频"""
        return self.fallback_audio_manager.get_fallback_audio(error_type)


# 全局错误处理器实例
_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例"""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler


def create_configured_error_handler() -> ErrorHandler:
    """创建根据配置设置的错误处理器实例
    
    根据应用配置的 enable_fail_fast 设置来创建相应的 ErrorHandler 实例。
    这个函数应该在应用启动时调用以替换默认的全局实例。
    """
    try:
        from ..core.config import cfg
        fail_fast_enabled = cfg.enable_fail_fast
        logger.info(f"🔧 Creating ErrorHandler with fail_fast={fail_fast_enabled} (env: {cfg.app_env})")
        return ErrorHandler(fail_fast=fail_fast_enabled)
    except Exception as e:
        logger.warning(f"⚠️ Failed to load config for ErrorHandler, using defaults: {e}")
        return ErrorHandler(fail_fast=False)


def initialize_global_error_handler() -> None:
    """初始化全局错误处理器为配置感知的实例
    
    这个函数应该在应用启动时调用，以确保全局 ErrorHandler
    使用正确的配置设置。
    """
    global _error_handler
    _error_handler = create_configured_error_handler()
    logger.info("✅ Global ErrorHandler initialized with configuration")


# 便捷装饰器
def with_error_recovery(component: str, error_type: Optional[ErrorType] = None):
    """错误恢复装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_handler = get_error_handler()
                recovery_result = await error_handler.handle_error(
                    error=e,
                    component=component,
                    error_type=error_type
                )
                
                if recovery_result.success and recovery_result.recovered_data:
                    # 如果有恢复数据，可以在这里处理
                    logger.info(f"Using recovered data: {recovery_result.recovered_data}")
                
                # 重新抛出异常让上层处理
                raise e
        
        return wrapper
    return decorator


# 使用示例
async def example_usage():
    """错误处理器使用示例"""
    error_handler = get_error_handler()
    
    # 模拟各种错误场景
    test_errors = [
        (ConnectionError("TTS connection failed"), "tts_service", ErrorType.TTS_CONNECTION_FAILED),
        (TimeoutError("LLM service timeout"), "llm_service", ErrorType.LLM_SERVICE_UNAVAILABLE),
        (OSError("Network connection lost"), "network", ErrorType.NETWORK_CONNECTION_LOST),
        (MemoryError("Out of memory"), "audio_buffer", ErrorType.RESOURCE_EXHAUSTED)
    ]
    
    for error, component, error_type in test_errors:
        logger.info(f"Testing error recovery for: {error_type.value}")
        
        result = await error_handler.handle_error(
            error=error,
            component=component,
            error_type=error_type
        )
        
        logger.info(f"Recovery result: {result.action_taken.value} - {result.message}")
        
        if result.success and result.recovered_data:
            logger.info(f"Recovered data: {result.recovered_data}")
        
        # 获取预设音频
        fallback_audio = error_handler.get_fallback_audio(error_type)
        logger.info(f"Fallback audio: {fallback_audio['text']}")
        
        print("-" * 50)
    
    # 显示统计信息
    stats = error_handler.get_stats()
    logger.info(f"Error handling stats: {stats}")


if __name__ == "__main__":
    asyncio.run(example_usage())