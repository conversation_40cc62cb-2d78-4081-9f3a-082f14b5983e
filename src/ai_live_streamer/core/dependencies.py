"""依赖注入定义

为FastAPI提供依赖注入，管理核心组件的生命周期。

Author: Claude Code
Date: 2025-08-08
"""

import asyncio
from functools import lru_cache
from typing import Optional
from loguru import logger

from .streaming_config import StreamingConfig, get_streaming_config
from ..services.playlist_manager import PlaylistManager
from ..services.client_state_tracker import ClientStateTracker
from ..services.streaming_content_provider import StreamingContentProvider
from ..services.proactive_synthesizer import ProactiveSynthesizer
from ..services.synthesis_event_bus import get_synthesis_event_bus


# 全局组件实例
_config: Optional[StreamingConfig] = None
_playlist_manager: Optional[PlaylistManager] = None
_client_tracker: Optional[ClientStateTracker] = None
_content_provider: Optional[StreamingContentProvider] = None
_proactive_synthesizer: Optional[ProactiveSynthesizer] = None
_websocket_v2_handler = None  # WebSocketV2Handler instance
_system_monitor = None  # 系统监控器
_state_broadcaster = None  # 状态广播器（架构解耦核心组件）
_tts_engine = None  # 将在集成时注入
_main_content_player = None  # 主内容播放器单例
_llm_adapter = None  # LLM适配器单例
_qa_manager = None  # QA管理器单例
_semantic_splitter = None  # SemanticSentenceSplitter 单例

# QA系统v3组件单例
_question_preprocessor = None  # 问题预处理器单例
_knowledge_base_manager = None  # 知识库管理器单例
_qa_router = None  # QA路由决策器单例
_qa_ranker = None  # 问题排序器单例
_enhanced_qa_manager = None  # 增强QA管理器单例
_enhanced_websocket_v2_handler = None  # 增强WebSocket处理器单例


@lru_cache()
def get_config() -> StreamingConfig:
    """获取配置实例（单例）"""
    global _config
    if _config is None:
        _config = get_streaming_config()
        logger.info("配置实例已创建")
    return _config


def get_playlist_manager() -> PlaylistManager:
    """获取播放列表管理器实例"""
    global _playlist_manager
    if _playlist_manager is None:
        config = get_config()
        main_player = get_main_content_player()  # 先获取 MainContentPlayer 依赖
        semantic_splitter = get_semantic_splitter()  # 获取 SemanticSentenceSplitter 单例
        _playlist_manager = PlaylistManager(config, main_player, semantic_splitter)  # 注入所有依赖
        logger.info("播放列表管理器实例已创建（含MainContentPlayer和SemanticSplitter依赖）")
    return _playlist_manager


def get_client_state_tracker() -> ClientStateTracker:
    """获取客户端状态追踪器实例"""
    global _client_tracker
    if _client_tracker is None:
        config = get_config()
        _client_tracker = ClientStateTracker(config)
        logger.info("客户端状态追踪器实例已创建")
    return _client_tracker


def get_content_provider() -> StreamingContentProvider:
    """获取内容提供器实例"""
    global _content_provider
    if _content_provider is None:
        if _tts_engine is None:
            raise RuntimeError("TTS引擎未初始化，请先调用set_tts_engine()")
            
        config = get_config()
        playlist_manager = get_playlist_manager()
        client_tracker = get_client_state_tracker()
        
        _content_provider = StreamingContentProvider(
            playlist_manager, client_tracker, _tts_engine, config
        )
        logger.info("内容提供器实例已创建")
    return _content_provider


def get_proactive_synthesizer() -> ProactiveSynthesizer:
    """获取主动预合成器实例"""
    global _proactive_synthesizer
    if _proactive_synthesizer is None:
        config = get_config()
        content_provider = get_content_provider()
        client_tracker = get_client_state_tracker()
        playlist_manager = get_playlist_manager()
        
        _proactive_synthesizer = ProactiveSynthesizer(
            content_provider, client_tracker, playlist_manager, config
        )
        logger.info("主动预合成器实例已创建")
    return _proactive_synthesizer


def get_websocket_v2_handler():
    """获取WebSocket v2处理器实例"""
    global _websocket_v2_handler
    if _websocket_v2_handler is None:
        # 延迟导入，避免循环依赖
        from ..api.websocket_v2 import WebSocketV2Handler
        
        # 获取音频代理实例 - 生产环境必需依赖
        from ..services.audio_streaming_proxy import get_audio_proxy
        audio_proxy = get_audio_proxy()
        
        # 确保音频代理可用（Fail-Fast）
        if audio_proxy is None:
            raise RuntimeError("AudioStreamingProxy is required for WebSocketV2Handler")
        
        config = get_config()
        playlist_manager = get_playlist_manager()
        client_tracker = get_client_state_tracker()
        content_provider = get_content_provider()
        proactive_synthesizer = get_proactive_synthesizer()
        
        # 获取状态广播器依赖
        state_broadcaster = get_state_broadcaster()
        
        _websocket_v2_handler = WebSocketV2Handler(
            playlist_manager, client_tracker, content_provider,
            proactive_synthesizer, config,
            audio_proxy,  # 必需参数，不再是可选
            state_broadcaster  # 新增：状态广播器依赖
        )
        logger.info("WebSocket v2处理器实例已创建（含音频代理）")
    return _websocket_v2_handler


def get_system_monitor():
    """获取系统监控器实例"""
    global _system_monitor
    if _system_monitor is None:
        from ..monitoring.system_monitor import create_system_monitor
        config = get_config()
        _system_monitor = create_system_monitor(config)
        logger.info("系统监控器实例已创建")
    return _system_monitor


def get_state_broadcaster():
    """获取状态广播器实例"""
    global _state_broadcaster
    if _state_broadcaster is None:
        from ..services.state_broadcaster import create_state_broadcaster
        config = get_config()
        _state_broadcaster = create_state_broadcaster(config)
        logger.info("状态广播器实例已创建（架构解耦核心组件）")
    return _state_broadcaster


# 生命周期管理

def set_tts_engine(tts_engine) -> None:
    """设置TTS引擎（在应用启动时调用）"""
    global _tts_engine
    _tts_engine = tts_engine
    logger.info("TTS引擎已设置")


def get_main_content_player():
    """获取主内容播放器实例（单例）"""
    global _main_content_player
    if _main_content_player is None:
        from ..services.main_content_player import MainContentPlayer
        _main_content_player = MainContentPlayer()
        logger.info("主内容播放器实例已创建（单例）")
    return _main_content_player


def get_semantic_splitter():
    """获取语义句子拆分器实例（单例）
    
    这个单例确保 NLP 模型只在应用启动时加载一次，
    避免在每次初始化播放列表时重复加载，从而消除启动延迟。
    """
    global _semantic_splitter
    if _semantic_splitter is None:
        from .semantic_sentence_splitter import SemanticSentenceSplitter
        logger.info("开始初始化 SemanticSentenceSplitter 单例...")
        _semantic_splitter = SemanticSentenceSplitter()
        logger.info("✅ SemanticSentenceSplitter 单例已创建，NLP 模型加载完成")
    return _semantic_splitter


async def get_llm_adapter():
    """获取 LLM 适配器实例（单例）"""
    global _llm_adapter
    if _llm_adapter is None:
        from ..services.factories import ServiceFactory
        _llm_adapter = await ServiceFactory.create_llm_adapter()
        logger.info(f"LLM 适配器实例已创建: {_llm_adapter.provider_name}")
    return _llm_adapter


async def get_qa_manager():
    """获取 QA 管理器实例（单例）- 企业级依赖注入"""
    global _qa_manager
    
    if _qa_manager is None:
        try:
            # 显式获取依赖，避免隐式全局状态访问
            llm_adapter = await get_llm_adapter()
            
            # 显式传递配置到子组件
            from ..core.config import cfg
            from ..qa import create_qa_manager
            from ..qa.builder import QAPromptBuilder
            from ..qa.template_manager import get_template_manager
            from ..models.persona import PersonaConfig, PersonaLexicon
            
            # 创建默认人设配置（显式从配置读取）
            default_persona = PersonaConfig(
                persona_id="default_streamer",
                name="默认主播",
                description="用于QA问答的默认主播人设，友好亲切的直播风格",
                persona_type="template_based",
                tone="friendly",
                lexicon=PersonaLexicon(
                    preferred=["亲爱的朋友们", "大家好", "非常感谢", "希望能帮到大家"],
                    banned=["绝对", "肯定", "保证", "一定", "100%"]
                ),
            )
            
            # 创建prompt构建器（显式传递配置）
            prompt_builder = QAPromptBuilder()  # 内部会通过get_template_manager()正确访问配置
            
            # 使用工厂函数创建QA管理器
            _qa_manager = create_qa_manager(llm_adapter, prompt_builder, default_persona)
            
            logger.info(f"✅ QA Manager 实例已创建: {llm_adapter.provider_name}")
            
        except Exception as e:
            logger.error(f"❌ QA Manager 创建失败: {e}")
            raise RuntimeError(f"QA Manager initialization failed: {e}")
            
    return _qa_manager


# QA系统v3依赖注入函数

async def get_question_preprocessor():
    """获取问题预处理器实例（单例）"""
    global _question_preprocessor
    if _question_preprocessor is None:
        from ..qa.preprocessor import QuestionPreprocessor
        from ..core.config import cfg
        
        # 获取Redis配置
        redis_config = {
            'enabled': cfg.qa_v3_redis_enabled,
            'host': cfg.qa_v3_redis_host,
            'port': cfg.qa_v3_redis_port,
            'db': cfg.qa_v3_redis_db,
            'password': cfg.qa_v3_redis_password
        }
        
        _question_preprocessor = QuestionPreprocessor()
        await _question_preprocessor.initialize(redis_config)
        logger.info("✅ 问题预处理器实例已创建")
    return _question_preprocessor


async def get_knowledge_base_manager():
    """获取知识库管理器实例（单例）"""
    global _knowledge_base_manager
    if _knowledge_base_manager is None:
        from ..qa.knowledge_base import KnowledgeBaseManager
        from ..core.config import cfg
        
        # 从配置获取统一的数据库和索引路径
        db_path = cfg.knowledge_base_db_path
        faiss_index_path = cfg.knowledge_base_faiss_index_path
        embedding_model = cfg.knowledge_base_embedding_model
        
        logger.info(f"初始化知识库管理器: db={db_path}, index={faiss_index_path}, model={embedding_model}")
        
        # 确保数据库已初始化
        from pathlib import Path
        import os
        if not Path(db_path).exists():
            # 运行初始化脚本
            logger.warning(f"数据库文件不存在: {db_path}，正在初始化...")
            init_script = Path(__file__).parent.parent.parent.parent / "scripts" / "init_qa_database.py"
            if init_script.exists():
                import subprocess
                result = subprocess.run([os.sys.executable, str(init_script)], capture_output=True, text=True)
                if result.returncode != 0:
                    raise RuntimeError(f"数据库初始化失败: {result.stderr}")
                logger.info("✅ 数据库初始化完成")
            else:
                raise RuntimeError(f"数据库文件不存在且无法找到初始化脚本: {init_script}")
        
        _knowledge_base_manager = KnowledgeBaseManager(
            db_path=db_path,
            faiss_index_path=faiss_index_path,
            embedding_model=embedding_model
        )
        await _knowledge_base_manager.initialize()
        logger.info("✅ 知识库管理器实例已创建")
    return _knowledge_base_manager


async def get_qa_router():
    """获取QA路由决策器实例（单例）"""
    global _qa_router
    if _qa_router is None:
        from ..qa.router import QARouter
        llm_adapter = await get_llm_adapter()
        _qa_router = QARouter(llm_adapter)
        logger.info("✅ QA路由决策器实例已创建")
    return _qa_router


def get_qa_ranker():
    """获取问题排序器实例（单例）"""
    global _qa_ranker
    if _qa_ranker is None:
        from ..qa.ranker import QARanker
        _qa_ranker = QARanker()
        logger.info("✅ 问题排序器实例已创建")
    return _qa_ranker


async def get_enhanced_qa_manager():
    """获取增强QA管理器实例（单例）"""
    global _enhanced_qa_manager
    
    # 检查QA v3是否启用
    from ..core.config import cfg
    if not cfg.qa_v3_enabled:
        logger.warning("QA System v3 is not enabled in config")
        return None
        
    if _enhanced_qa_manager is None:
        # 获取所有必需的依赖
        original_qa_manager = await get_qa_manager()
        preprocessor = await get_question_preprocessor()
        knowledge_base = await get_knowledge_base_manager()
        router = await get_qa_router()
        ranker = get_qa_ranker()
        
        # 获取父类QAManager所需的依赖
        llm_adapter = await get_llm_adapter()
        from ..qa.builder import QAPromptBuilder
        from ..models.persona import PersonaConfig, PersonaLexicon
        
        # 创建默认人设配置
        default_persona = PersonaConfig(
            persona_id="default_streamer",
            name="默认主播",
            description="用于QA问答的默认主播人设，友好亲切的直播风格",
            persona_type="template_based",
            tone="friendly",
            lexicon=PersonaLexicon(
                preferred=["亲爱的朋友们", "大家好", "非常感谢", "希望能帮到大家"],
                banned=["绝对", "肯定", "保证", "一定", "100%"]
            ),
        )
        
        prompt_builder = QAPromptBuilder()
        
        from ..qa.enhanced_manager import EnhancedQAManager
        _enhanced_qa_manager = EnhancedQAManager(
            knowledge_base=knowledge_base,
            router=router,
            ranker=ranker,
            preprocessor=preprocessor,
            # 父类QAManager所需参数
            llm_adapter=llm_adapter,
            prompt_builder=prompt_builder,
            default_persona=default_persona
        )
        
        logger.info("✅ 增强QA管理器实例已创建")
        
    return _enhanced_qa_manager


async def get_enhanced_websocket_v2_handler():
    """获取增强版WebSocket v2处理器实例（单例）"""
    global _enhanced_websocket_v2_handler
    
    # 检查QA v3是否启用
    from ..core.config import cfg
    if not cfg.qa_v3_enabled:
        logger.warning("QA System v3 is not enabled, cannot use enhanced WebSocket handler")
        # 返回普通版handler
        return get_websocket_v2_handler()
        
    if _enhanced_websocket_v2_handler is None:
        # 获取增强QA组件
        enhanced_qa_manager = await get_enhanced_qa_manager()
        qa_ranker = get_qa_ranker()
        
        # 获取原有依赖
        from ..services.audio_streaming_proxy import get_audio_proxy
        audio_proxy = get_audio_proxy()
        
        if audio_proxy is None:
            raise RuntimeError("AudioStreamingProxy is required for EnhancedWebSocketV2Handler")
        
        config = get_config()
        playlist_manager = get_playlist_manager()
        client_tracker = get_client_state_tracker()
        content_provider = get_content_provider()
        proactive_synthesizer = get_proactive_synthesizer()
        state_broadcaster = get_state_broadcaster()
        
        from ..api.enhanced_websocket_v2 import EnhancedWebSocketV2Handler
        _enhanced_websocket_v2_handler = EnhancedWebSocketV2Handler(
            qa_ranker=qa_ranker,
            enhanced_qa_manager=enhanced_qa_manager,
            # 传递原有参数
            playlist_manager=playlist_manager,
            client_tracker=client_tracker, 
            content_provider=content_provider,
            proactive_synthesizer=proactive_synthesizer,
            config=config,
            audio_proxy=audio_proxy,
            state_broadcaster=state_broadcaster
        )
        
        logger.info("✅ 增强版WebSocket v2处理器实例已创建")
        
    return _enhanced_websocket_v2_handler


async def startup_components() -> None:
    """启动所有组件（应用启动时调用）"""
    logger.info("开始启动核心组件...")
    
    # 🎯 预初始化 SemanticSentenceSplitter（避免运行时延迟）
    semantic_splitter = get_semantic_splitter()
    logger.info("✅ NLP 模型预加载完成，避免运行时延迟")
    
    # 启动顺序很重要
    client_tracker = get_client_state_tracker()
    await client_tracker.start()
    
    # 启动事件总线（基础服务，需最先启动）
    event_bus = get_synthesis_event_bus()
    await event_bus.start()
    logger.info("✅ 合成事件总线已启动")
    
    proactive_synthesizer = get_proactive_synthesizer()
    await proactive_synthesizer.start()
    
    # 启动系统监控器
    system_monitor = get_system_monitor()
    await system_monitor.start()
    
    # 📡 初始化状态广播器（架构解耦的核心组件）
    state_broadcaster = get_state_broadcaster()
    logger.info("✅ 状态广播器已初始化，准备接收WebSocket连接注册")
    
    # ✅ QA Manager 快速失败健康检查
    try:
        qa_manager = await get_qa_manager()
        logger.info("✅ QA Manager 启动健康检查通过")
    except Exception as e:
        logger.error(f"❌ QA Manager 启动失败，应用终止: {e}")
        # Fail-Fast: 让应用在启动阶段就失败，而非运行时随机失败
        raise RuntimeError(f"QA Manager startup health check failed: {e}")
    
    # 🚀 QA系统v3组件初始化（可选）
    from ..core.config import cfg
    if cfg.qa_v3_enabled:
        try:
            # 按依赖顺序初始化组件
            logger.info("开始初始化QA系统v3组件...")
            
            preprocessor = await get_question_preprocessor()
            knowledge_base = await get_knowledge_base_manager()
            router = await get_qa_router()
            ranker = get_qa_ranker()
            enhanced_qa_manager = await get_enhanced_qa_manager()
            
            logger.info("✅ QA系统v3组件初始化完成")
            
            # 可选：预初始化增强WebSocket处理器
            # enhanced_ws_handler = await get_enhanced_websocket_v2_handler()
            # logger.info("✅ 增强WebSocket处理器预初始化完成")
            
        except Exception as e:
            logger.error(f"❌ QA系统v3初始化失败: {e}")
            # Fail-Fast: 如果启用了QA v3但初始化失败，抛出异常
            raise RuntimeError(f"QA v3 system initialization failed: {e}")
    else:
        logger.info("💤 QA系统v3未启用（配置中qa_system_v3.enabled=false）")
    
    logger.info("所有核心组件启动完成")


async def shutdown_components() -> None:
    """关闭所有组件（应用关闭时调用）"""
    logger.info("开始关闭核心组件...")
    
    global _websocket_v2_handler, _proactive_synthesizer, _client_tracker
    global _content_provider, _playlist_manager, _system_monitor, _qa_manager, _state_broadcaster
    global _enhanced_websocket_v2_handler, _enhanced_qa_manager, _knowledge_base_manager
    global _question_preprocessor, _qa_router, _qa_ranker
    
    # 关闭顺序与启动相反
    if _system_monitor:
        await _system_monitor.stop()
        
    # 🔄 关闭QA系统v3组件
    if _enhanced_websocket_v2_handler:
        await _enhanced_websocket_v2_handler.cleanup()
        logger.info("增强WebSocket处理器已关闭")
        
    if _enhanced_qa_manager:
        await _enhanced_qa_manager.cleanup()
        logger.info("增强QA管理器已关闭")
        
    if _knowledge_base_manager:
        # KnowledgeBaseManager没有cleanup方法，但有__del__析构函数会自动清理
        logger.info("知识库管理器已关闭")
        
    if _question_preprocessor:
        await _question_preprocessor.cleanup()
        logger.info("问题预处理器已关闭")
        
    if _qa_manager:
        await _qa_manager.cleanup()
        
    # 📡 清理状态广播器（确保所有连接正确关闭）
    if _state_broadcaster:
        await _state_broadcaster.cleanup()
        
    if _websocket_v2_handler:
        await _websocket_v2_handler.cleanup()
        
    if _proactive_synthesizer:
        await _proactive_synthesizer.stop()
        
        
    if _client_tracker:
        await _client_tracker.stop()
        
    if _content_provider:
        await _content_provider.cleanup()
    
    # 停止事件总线（必须在所有使用它的服务停止后）
    # 确保顺序：WebSocket -> ProactiveSynthesizer -> ContentProvider -> EventBus
    try:
        event_bus = get_synthesis_event_bus()
        await event_bus.stop()
        logger.info("合成事件总线已停止")
    except asyncio.CancelledError:
        # 在关闭过程中被取消是正常的
        logger.debug("事件总线关闭被取消（正常行为）")
    except Exception as e:
        logger.warning(f"事件总线关闭时出现错误: {e}")
    
    # 给予所有异步任务一点时间完成清理
    try:
        await asyncio.sleep(0.1)
    except asyncio.CancelledError:
        pass
        
    # 清理全局引用
    _system_monitor = None
    _qa_manager = None
    _state_broadcaster = None
    _websocket_v2_handler = None
    _proactive_synthesizer = None
    _client_tracker = None
    _content_provider = None
    _playlist_manager = None
    
    # 清理QA系统v3组件引用
    _enhanced_websocket_v2_handler = None
    _enhanced_qa_manager = None
    _knowledge_base_manager = None
    _question_preprocessor = None
    _qa_router = None
    _qa_ranker = None
    
    logger.info("所有核心组件关闭完成")
    
    # 确保所有待处理的任务都有机会完成
    # 这有助于避免 uvicorn 的 CancelledError
    try:
        await asyncio.sleep(0.05)
    except asyncio.CancelledError:
        pass


def reset_dependencies() -> None:
    """重置所有依赖（测试时使用）"""
    global _config, _playlist_manager, _client_tracker, _content_provider
    global _proactive_synthesizer, _websocket_v2_handler, _system_monitor, _tts_engine, _qa_manager, _state_broadcaster, _semantic_splitter
    global _question_preprocessor, _knowledge_base_manager, _qa_router, _qa_ranker, _enhanced_qa_manager, _enhanced_websocket_v2_handler
    
    _config = None
    _playlist_manager = None
    _client_tracker = None
    _content_provider = None
    _proactive_synthesizer = None
    _websocket_v2_handler = None
    _system_monitor = None
    _state_broadcaster = None
    _tts_engine = None
    _qa_manager = None
    _semantic_splitter = None
    
    # 重置QA系统v3组件
    _question_preprocessor = None
    _knowledge_base_manager = None
    _qa_router = None
    _qa_ranker = None
    _enhanced_qa_manager = None
    _enhanced_websocket_v2_handler = None
    
    # 清理缓存
    get_config.cache_clear()
    
    # 重置broadcaster的单例实例
    from ..services.state_broadcaster import reset_state_broadcaster
    reset_state_broadcaster()
    
    logger.info("依赖注入已重置")


def get_component_status() -> dict:
    """获取所有组件的状态"""
    event_bus = get_synthesis_event_bus()
    return {
        "config": _config is not None,
        "playlist_manager": _playlist_manager is not None,
        "client_tracker": _client_tracker is not None,
        "content_provider": _content_provider is not None,
        "proactive_synthesizer": _proactive_synthesizer is not None,
        "websocket_v2_handler": _websocket_v2_handler is not None,
        "system_monitor": _system_monitor is not None,
        "state_broadcaster": _state_broadcaster is not None,
        "tts_engine": _tts_engine is not None,
        "qa_manager": _qa_manager is not None,
        "semantic_splitter": _semantic_splitter is not None,
        "event_bus": event_bus._running if event_bus else False,
        
        # QA系统v3组件状态
        "question_preprocessor": _question_preprocessor is not None,
        "knowledge_base_manager": _knowledge_base_manager is not None,
        "qa_router": _qa_router is not None,
        "qa_ranker": _qa_ranker is not None,
        "enhanced_qa_manager": _enhanced_qa_manager is not None,
        "enhanced_websocket_v2_handler": _enhanced_websocket_v2_handler is not None,
    }