# API Contract Document for AI Live Streamer
**Version**: 2.4  
**Date**: 2025-08-10  
**Status**: BINDING CONTRACT

> ⚠️ **IMPORTANT**: This document serves as the Single Source of Truth for all API interfaces between frontend and backend. Any changes MUST be documented here BEFORE implementation.

---

## 1. HTTP REST API Endpoints

### 1.1 Stream Control API

#### Start Stream
**Endpoint**: `POST /api/control/start-stream`  
**Description**: Initialize and start a new live streaming session

**Request Body**:
```json
{
  "mode": "static",  // static, dynamic, smart, script_based
  "content": [  // Required for static mode: list of strings
    "大家好，欢迎来到直播间！",
    "今天我们要介绍一款非常棒的产品。",
    "这个产品有三大特点。"
  ],
  "topic": "产品介绍",  // Required for dynamic/smart modes
  "persona_id": "王哥",  // Optional persona identifier
  "session_id": null,  // Optional custom session ID
  "form_id": null,  // Required for script_based mode
  "prepared_stream_id": null  // Alternative to form_id for script_based
}
```

**Response**:
```json
{
  "status": "success",
  "session_id": "session_1754123456_abc12345",
  "websocket_url": "/ws/v2/stream",
  "message": "Stream started successfully"
}
```

**Error Response**:
```json
{
  "status": "error", 
  "error_code": "STREAM_INIT_FAILED",
  "message": "Failed to initialize stream: [detailed error]"
}
```

#### Stop Stream
**Endpoint**: `POST /api/control/stop-stream/{session_id}`  
**Description**: Stop an active streaming session

**Response**:
```json
{
  "status": "success",
  "message": "Stream stopped"
}
```

#### Get Session Status
**Endpoint**: `GET /api/control/sessions/{session_id}/status`  
**Description**: Get current status of a streaming session

**Response**:
```json
{
  "session_id": "session_1754123456_abc12345",
  "status": "active",  // active, paused, stopped, error
  "current_index": 5,
  "total_segments": 20,
  "connected_clients": 3,
  "start_time": "2025-08-09T10:30:00Z",
  "duration_seconds": 120
}
```

### 1.2 Question Management API

#### Submit Question
**Endpoint**: `POST /api/control/submit-question`  
**Description**: Submit a user question to be answered during the stream

**Request Body**:
```json
{
  "session_id": "session_1754123456_abc12345",
  "question": "这个产品有什么优惠吗？",
  "user_name": "观众123",
  "priority": "normal"  // low, normal, high, urgent
}
```

**Response**:
```json
{
  "status": "success",
  "question_id": "q_1754123456_xyz",
  "position_in_queue": 3,
  "estimated_answer_time": 30  // seconds
}
```

---

## 2. WebSocket v2 Protocol Specification

### 2.1 Connection Protocol

#### Connection URL
```
ws://[host]/ws/v2/stream?client_id={client_id}&session_id={session_id}&protocol_version=2.0
```

**Required Query Parameters**:
- `client_id`: Unique client identifier (string, required)
- `session_id`: Session ID from start-stream API (string, required)  
- `protocol_version`: Must be "2.0" (string, required)

#### Connection Handshake
Upon successful connection, server sends:
```json
{
  "type": "connection_established",
  "client_id": "client_123",
  "session_id": "session_1754123456_abc12345",
  "server_time": 1754123456789,
  "protocol_version": "2.1",
  "server_version": "2.1.0",
  "playlist_info": {  // v2.4: Initial playlist state included
    "version": 1,
    "total_items": 20,
    "start_index": 0
  }
}
```

### 2.2 Message Types

#### Client → Server Messages

##### Content Request
**Purpose**: Request audio content for a specific segment
```json
{
  "type": "content_request",
  "index": 0,  // 0-based index of the segment
  "request_id": "req_1754123456_abc"  // Client-generated unique ID
}
```

##### QA Insert Request
**Purpose**: Request to insert a Q&A segment
```json
{
  "type": "qa_insert",
  "question_id": "q_1754123456_xyz",
  "strategy": "immediate",  // immediate, next_safe_point, queue
  "request_id": "req_1754123456_def"
}
```

##### State Sync Request
**Purpose**: Request current playback state from server
```json
{
  "type": "state_sync",
  "request_id": "req_1754123456_ghi"
}
```

##### Playlist Info Request (v2.4)
**Purpose**: Request full playlist information for synchronization
```json
{
  "type": "playlist_info_request",
  "request_id": "sync_1754123456_jkl",
  "client_version": 3,  // Client's known playlist version
  "sync_reason": "item_inserted",  // Reason for sync: item_inserted, playlist_updated, reconnect, etc.
  "current_playing_index": 5  // Client's current playback position
}
```

##### Interrupt Acknowledgment (v2.2) [DEPRECATED in v2.3]
**Purpose**: ~~Client reports current state in response to interrupt request~~
**Status**: DEPRECATED - Replaced by safe distance insertion strategy in v2.3
```json
{
  "type": "interrupt_ack",
  "qa_id": "qa_1754123456_xyz",
  "playing_index": 31,
  "buffered_indices": [32, 33],
  "audio_cache_keys": {
    "32": "cache_key_32_abc",
    "33": "cache_key_33_def"
  },
  "client_time": 1754123456.890
}
```
**Note**: This message type is no longer used as of v2.3. The system now uses a configurable safe distance insertion strategy that doesn't require client state reporting.

#### Server → Client Messages

##### Audio Content Response
**Purpose**: Deliver requested audio content
```json
{
  "type": "content",
  "index": 0,  // The index that was requested
  "request_id": "req_1754123456_abc",  // Echo back the request_id
  "format": "wav",  // Audio format
  "sample_rate": 24000,
  "channels": 1,
  "duration_ms": 3500,
  "data": "base64_encoded_audio_data",  // Base64 encoded WAV file (header + PCM)
  "metadata": {
    "text": "大家好，欢迎来到直播间！",
    "type": "greeting",
    "is_qa": false
  }
}
```

##### State Update
**Purpose**: Notify client of state changes
```json
{
  "type": "state_update",
  "current_index": 5,
  "total_segments": 20,
  "playback_status": "playing",
  "buffer_health": "good",  // good, low, critical
  "server_time": 1754123456789
}
```

##### Error Message
**Purpose**: Notify client of errors
```json
{
  "type": "error",
  "error_code": "CONTENT_NOT_FOUND",
  "message": "Content for index 99 not found",
  "request_id": "req_1754123456_abc",  // If related to a specific request
  "recoverable": true
}
```

##### Content Not Ready Response (v2.2)
**Purpose**: Notify client that content is being generated
```json
{
  "type": "content_not_ready",
  "error_code": "CONTENT_NOT_READY",
  "message": "Content 'item_123' is being generated, please retry",
  "index": 5,
  "request_id": "req_1754123456_abc",
  "retry_after_ms": 1000,  // Suggested retry delay
  "item_id": "item_123",
  "server_time": 1754123456789
}
```
**Client Behavior**:
- Display "Content generating..." message (optional)
- Auto-retry after the suggested delay
- Do not treat as connection failure

##### QA Notification
**Purpose**: Notify that a Q&A segment is ready
```json
{
  "type": "qa_ready",
  "question_id": "q_1754123456_xyz",
  "insert_after_index": 5,
  "question_text": "这个产品有什么优惠吗？",
  "estimated_duration_ms": 8000
}
```

##### Item Inserted (v2.1)
**Purpose**: Notify client of transactional item insertion into playlist
```json
{
  "type": "item_inserted",
  "schema_version": "1.0",
  "insert_index": 1,
  "items": [
    {
      "id": "qa_001",
      "content": "什么是人工智能？",
      "type": "qa_question", 
      "duration_ms": 2000,
      "content_preview": "什么是人工智能？"
    },
    {
      "id": "qa_002",
      "content": "人工智能是让机器具备类似人类智能的技术...",
      "type": "qa_answer",
      "duration_ms": 5000,
      "content_preview": "人工智能是让机器具备类似人类智能的技术..."
    }
  ],
  "playlist_version": 2,
  "reason": "qa_inserted",
  "server_time": 1754739154.732352,
  "timestamp": "2025-08-09T11:32:34.732353",
  "qa_info": {
    "qa_id": "test_qa_001",
    "question": "什么是人工智能？",
    "answer_preview": "人工智能是让机器具备类似人类智能的技术..."
  }
}
```

##### Playlist Updated (Enhanced v2.1)
**Purpose**: Notify client of playlist state changes with version control
```json
{
  "type": "playlist_updated",
  "playlist_version": 2,
  "update_reason": "qa_inserted",
  "server_time": 1754739154.732352,
  "timestamp": "2025-08-09T11:32:34.732353",
  "total_items": 10,
  "action_required": "client_should_sync",
  "qa_info": {
    "qa_id": "qa_123",
    "question": "用户问题内容",
    "answer_preview": "答案预览...",
    "queue_position": 3
  }
}
```

##### Playlist Version Mismatch (v2.1)
**Purpose**: Notify client of version synchronization issues
```json
{
  "type": "playlist_version_mismatch",
  "current_version": 5,
  "client_known_version": 3,
  "action_required": "client_should_sync",
  "server_time": 1754739154.732352,
  "message": "Client playlist version is outdated"
}
```

##### Playlist Info Response (v2.4)
**Purpose**: Provide full playlist information for client synchronization
```json
{
  "type": "playlist_info_response",
  "request_id": "sync_1754123456_jkl",
  "version": 5,  // Current playlist version
  "total_items": 25,
  "suggested_resume_index": 7,  // Server-calculated optimal resume position
  "version_diff": 2,  // Difference between server and client versions
  "script_items": 20,
  "qa_sequences": 1,
  "checksum": "abc123def456",
  "last_modified": "2025-08-10T12:00:00.000Z",
  "estimated_duration_ms": 180000,
  "server_time": 1754123456789
}
```

##### Interrupt Request (v2.2) [DEPRECATED in v2.3]
**Purpose**: ~~Server requests client to report its current playback and buffer state~~
**Status**: DEPRECATED - Replaced by safe distance insertion strategy in v2.3
```json
{
  "type": "interrupt_request",
  "qa_id": "qa_1754123456_xyz",
  "priority": "immediate",
  "reason": "qa_insertion",
  "server_time": 1754123456.789
}
```

**Note**: This message type is no longer used as of v2.3. The system now uses a simpler, more robust approach:

**Safe Distance QA Insertion Strategy (v2.3)**:
1. Server identifies need for QA insertion
2. Server calculates safe insertion position: `current_playing_index + QA_INSERTION_OFFSET`
   - `QA_INSERTION_OFFSET=2` (default): Ensures safe distance, no content loss
   - `QA_INSERTION_OFFSET=1`: More aggressive, may require client buffer discard
3. Server inserts QA at calculated position, all subsequent content shifts naturally
4. Server broadcasts playlist_updated or item_inserted messages
5. Clients continue normal operation without interruption
6. No client state reporting or complex synchronization required

### 2.3 Audio Format Specification

#### Standard Audio Format
All audio data MUST be delivered in the following format:
- **Container**: WAV with proper RIFF header
- **Encoding**: PCM (Linear PCM)
- **Sample Rate**: 24000 Hz
- **Bit Depth**: 16-bit
- **Channels**: 1 (Mono)
- **Byte Order**: Little-endian

#### WAV Header Structure
```
[RIFF Header - 44 bytes]
  - ChunkID: "RIFF" (4 bytes)
  - ChunkSize: file_size - 8 (4 bytes)
  - Format: "WAVE" (4 bytes)
  - Subchunk1ID: "fmt " (4 bytes)
  - Subchunk1Size: 16 (4 bytes)
  - AudioFormat: 1 (PCM) (2 bytes)
  - NumChannels: 1 (2 bytes)
  - SampleRate: 24000 (4 bytes)
  - ByteRate: 48000 (4 bytes)
  - BlockAlign: 2 (2 bytes)
  - BitsPerSample: 16 (2 bytes)
  - Subchunk2ID: "data" (4 bytes)
  - Subchunk2Size: data_size (4 bytes)
[PCM Data - variable length]
```

---

## 3. State Management Contract

### 3.1 Index Semantics

**Definition**: `current_index` represents the 0-based index of the content segment.

**Client Responsibility**: The client is responsible for managing its own playback progress and requesting the specific index it needs.

- **Initial State**: Client starts by requesting `index = 0`
- **Progression**: Client requests the specific index it needs (e.g., 0, 1, 2, ...)
- **Index Management**: Client tracks which segments have been played and determines the next index to request
- **Server Role**: Server returns the content for the requested index without assumptions about playback order
- **End of Content**: When requested `index >= total_segments`, server returns an error or end-of-content indicator

**Important**: This design ensures stateless server operation and gives clients full control over playback order, seeking, and retry logic.

### 3.2 Request-Response Correlation

1. **Request ID**: Every client request MUST include a unique `request_id`
2. **Response Matching**: Server responses MUST echo the `request_id`
3. **Timeout Handling**: Client should timeout requests after 5000ms
4. **Duplicate Prevention**: Client MUST NOT send duplicate requests with same `request_id`

### 3.3 Connection State Machine

```
DISCONNECTED --connect--> CONNECTING
CONNECTING --success--> CONNECTED
CONNECTING --fail--> DISCONNECTED
CONNECTED --content_request--> REQUESTING
REQUESTING --content_received--> BUFFERING
BUFFERING --buffer_ready--> PLAYING
PLAYING --content_end--> REQUESTING
PLAYING --all_done--> COMPLETED
ANY_STATE --error--> ERROR
ERROR --reconnect--> CONNECTING
```

### 3.4 Playlist Version Control (v2.1)

**Purpose**: Ensure consistent playlist state between server and client through monotonic version numbers.

#### Version Semantics
- **`playlist_version`**: Monotonically increasing integer starting from 1
- **Server Authority**: Server is the single source of truth for version numbers
- **Client Tracking**: Each client MUST track its known playlist version locally
- **Update Trigger**: Version increments on any playlist modification (insertion, deletion, reordering)

#### Client Version Validation Rules
1. **Accept Rule**: Client MUST only process messages where `playlist_version > local_known_version`
2. **Reject Rule**: Client MUST ignore/reject messages where `playlist_version <= local_known_version`
3. **Update Rule**: After successful message processing, client MUST update `local_known_version = message.playlist_version`
4. **Sync Rule**: On version mismatch detection, client SHOULD request full playlist sync

#### Server Version Guarantees
- **Monotonic Increment**: Version numbers MUST strictly increase (never decrease or repeat)
- **Atomic Updates**: Version increment and data modification MUST occur within same transaction
- **Broadcast Ordering**: Messages with higher version numbers MUST NOT be sent before lower version numbers
- **Recovery Support**: Server MUST handle clients with outdated version numbers gracefully

#### Example Version Flow
```
Server State: playlist_version = 3
Client A knows: version = 3
Client B knows: version = 2

1. QA Insertion occurs
   → Server increments: playlist_version = 4
   → Server broadcasts item_inserted: {"playlist_version": 4, ...}

2. Client A receives message:
   → Check: 4 > 3 ✅ → Process message → Update local to 4

3. Client B receives message:  
   → Check: 4 > 2 ✅ → Process message → Update local to 4
```

### 3.5 Schema Version Compatibility (v2.1)

**Purpose**: Enable forward and backward compatibility for message format evolution.

#### Schema Version Field
- **Field Name**: `schema_version` (string, optional)
- **Current Version**: "1.0" for v2.1 API Contract
- **Placement**: Top-level field in all versioned messages

#### Compatibility Rules
1. **Forward Compatibility**: Newer clients SHOULD handle older schema versions gracefully
2. **Backward Compatibility**: Newer schema versions SHOULD include compatibility hints
3. **Unknown Versions**: Clients encountering unknown schema versions SHOULD log warnings but attempt processing
4. **Migration Path**: Schema version changes indicate message format changes requiring client updates

#### Example Schema Handling
```javascript
// Client-side schema version handling
function handleMessage(message) {
    const schemaVersion = message.schema_version || "unknown";
    
    if (schemaVersion !== "1.0") {
        console.warn(`Unsupported schema version: ${schemaVersion}`);
        // Attempt to process with current logic (degraded mode)
    }
    
    // Process message according to current schema understanding
}
```

### 3.6 Transactional Update Guarantees (v2.1)

**Purpose**: Ensure atomic consistency between server state and client notifications.

#### Atomicity Requirements
- **Database + Message**: Playlist modifications and message broadcasting MUST occur within same transaction boundary
- **Rollback Safety**: If message broadcast fails, playlist changes MUST be rolled back
- **All-or-Nothing**: Either both state update and notification succeed, or both fail

#### Transaction Boundaries
```python
# Server-side transaction pattern
async with transaction_lock:
    # 1. Modify playlist state
    playlist.insert(index, item)
    playlist.version += 1
    
    # 2. Broadcast notification (within same transaction)
    await broadcaster.broadcast_item_inserted(...)
    
    # 3. Both operations succeed or both fail atomically
```

#### Ordering Guarantees
1. **Message Order**: Messages MUST be sent in playlist version order
2. **Processing Order**: Clients MUST process messages in version order
3. **Gap Handling**: Clients detecting version gaps MUST request synchronization
4. **Recovery**: Failed transactions MUST NOT leave partial state updates

#### Failure Recovery
- **Broadcast Failure**: Does not affect successful playlist update (fire-and-forget for individual clients)
- **State Failure**: Prevents any message broadcasting (fail-fast)
- **Network Partitions**: Clients reconnecting receive version mismatch notifications for catch-up

---

## 4. Error Codes

| Code | Description | Recoverable |
|------|-------------|-------------|
| `SESSION_NOT_FOUND` | Session ID does not exist | No |
| `CONTENT_NOT_FOUND` | Requested index out of range | No |
| `INVALID_REQUEST` | Malformed request message | No |
| `SYNTHESIS_FAILED` | TTS synthesis error | Yes |
| `BUFFER_OVERFLOW` | Client buffer full | Yes |
| `NETWORK_ERROR` | Network communication error | Yes |
| `INTERNAL_ERROR` | Server internal error | Maybe |
| `VERSION_MISMATCH` | Client playlist version is outdated (v2.1) | Yes |
| `STALE_REQUEST` | Request based on outdated playlist version (v2.1) | No |
| `QA_INSERTION_FAILED` | Failed to insert QA into playlist (v2.1) | Yes |
| `INVALID_QA_REQUEST` | Malformed QA insertion request (v2.1) | No |
| `SCHEMA_VERSION_UNSUPPORTED` | Unsupported schema version in message (v2.1) | Maybe |
| `TRANSACTION_FAILED` | Atomic transaction failed (v2.1) | Yes |
| `CONTENT_NOT_READY` | Content is being generated, client should retry (v2.2) | Yes |
| `INTERRUPT_REQUEST_TIMEOUT` | [DEPRECATED v2.3] Client failed to respond to interrupt request | Yes |
| `INVALID_CLIENT_STATE` | [DEPRECATED v2.3] Client reported invalid state in interrupt_ack | No |

---

## 5. Performance Constraints

### Latency Requirements
- **Content Request → Response**: < 500ms (p95)
- **WebSocket Connection**: < 1000ms
- **QA Insertion**: < 2000ms

### Buffer Management
- **Minimum Buffer**: 3 segments
- **Maximum Buffer**: 10 segments
- **Low Buffer Threshold**: 2 segments (trigger prefetch)

### Concurrent Connections
- **Max Clients per Session**: 100
- **Max Sessions per Server**: 50
- **Max Request Rate**: 10 requests/second per client

---

## 6. Version History

| Version | Date | Changes |
|---------|------|---------|
| 2.0 | 2025-08-09 | Initial contract definition |
| 2.1 | 2025-08-09 | Added QA broadcast messages, version control, transactional guarantees |
| 2.2 | 2025-08-10 | Added interrupt request/ack protocol for smart QA insertion and cache reuse |
| 2.3 | 2025-08-10 | Deprecated interrupt protocol, implemented safe distance insertion strategy with configurable offset |
| 2.4 | 2025-08-10 | Added playlist_info_request/response for full synchronization, enhanced connection_established with initial playlist state |

---

## 7. Contract Enforcement

1. **Breaking Changes**: Any changes to message formats, field names, or semantics constitute a breaking change and require version increment
2. **Deprecation Policy**: Old versions must be supported for at least 30 days after new version release
3. **Testing Requirements**: All endpoints and message types must have corresponding E2E tests
4. **Documentation**: This document must be updated BEFORE any implementation changes

---

**END OF CONTRACT**

_This document is binding for all development activities. Violations will result in build failures._