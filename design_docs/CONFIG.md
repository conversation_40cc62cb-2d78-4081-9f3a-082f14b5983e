# 配置管理指南

## 配置优先级

本系统采用三层配置机制，优先级从高到低：

1. **环境变量** (`.env` 文件或系统环境变量)
   - 用于生产环境的覆盖配置
   - 敏感信息（API密钥等）必须通过环境变量设置
   - 在运行时可覆盖默认配置

2. **YAML 配置** (`config.yml`)
   - 应用的默认配置
   - 功能开关和非敏感配置
   - 详细的引擎参数设置

3. **硬编码默认值**
   - 代码中的最终备选
   - 确保系统在最小配置下也能启动
   - 提供合理的默认行为

## 主要配置项

| 配置项 | 环境变量 | YAML路径 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| TTS引擎 | `TTS_ENGINE` | `tts.default_engine` | `tts_cache_proxy` | TTS服务引擎选择 |
| LLM提供商 | `AI_MODEL_PROVIDER` | `llm.default_provider` | `qwen-plus` | LLM服务提供商 |
| 应用环境 | `APP_ENV` | - | `development` | 运行环境(development/production) |
| 调试模式 | `DEBUG` | - | `false` | 是否启用调试模式 |
| 日志级别 | `LOG_LEVEL` | - | `INFO` | 日志输出级别 |

## 可用的TTS引擎

- `tts_cache_proxy` - 企业级缓存代理引擎（推荐）
- `cosyvoice_unified` - CosyVoice统一引擎
- `cosyvoice_v2` - CosyVoice v2（别名）
- `cozyvoice` - CosyVoice（兼容性别名）

## 可用的LLM提供商

- `qwen-plus` - 阿里云千问Plus
- `qwen-turbo` - 阿里云千问Turbo
- `litellm` - LiteLLM统一网关
- `openai-gpt4` - OpenAI GPT-4
- `anthropic-claude` - Anthropic Claude

## 配置示例

### 1. 使用环境变量覆盖（最高优先级）

```bash
# .env 文件
TTS_ENGINE=tts_cache_proxy
AI_MODEL_PROVIDER=qwen-plus
```

### 2. 使用YAML配置（中等优先级）

```yaml
# config.yml
tts:
  default_engine: "tts_cache_proxy"
  
llm:
  default_provider: "qwen-plus"
```

### 3. 混合配置（推荐）

- 敏感信息（API密钥）放在 `.env`
- 功能配置放在 `config.yml`
- 让环境变量可选择性覆盖YAML配置

## 调试配置问题

### 查看配置摘要

启动应用时，系统会自动打印配置摘要：

```
============================================================
Configuration Summary:
  TTS Engine: 'tts_cache_proxy' (source: ENV)
  LLM Provider: 'qwen-plus' (source: YAML)
  App Environment: 'development' (source: ENV)
  Debug Mode: True
============================================================
```

### 配置来源说明

- `(source: ENV)` - 配置来自环境变量
- `(source: YAML)` - 配置来自config.yml文件
- `(source: DEFAULT)` - 使用了硬编码默认值

### 常见问题

1. **Q: 修改了config.yml但不生效？**
   A: 检查是否有同名的环境变量覆盖了YAML配置。环境变量优先级更高。

2. **Q: 系统使用了意外的默认值？**
   A: 查看启动日志中的配置摘要，确认配置来源。可能是环境变量和YAML都未设置。

3. **Q: 如何临时覆盖配置进行测试？**
   A: 使用环境变量：`TTS_ENGINE=cosyvoice_unified python run_server.py`

## 最佳实践

1. **生产环境**：使用环境变量管理敏感信息和环境特定配置
2. **开发环境**：主要使用config.yml，便于版本控制和团队共享
3. **测试环境**：混合使用，通过环境变量覆盖特定测试场景的配置
4. **配置变更**：修改配置后重启应用，并检查启动日志确认生效

## 配置文件位置

- **环境变量**: `/path/to/project/.env`
- **YAML配置**: `/path/to/project/config.yml`
- **配置类**: `src/ai_live_streamer/core/config.py`