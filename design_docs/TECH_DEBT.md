# 技术债务清单

## 高优先级

### 1. 创建专用的异步生成器重试装饰器
- [ ] **任务**: 创建 `@retry_for_async_generator` 装饰器
- **位置**: `src/ai_live_streamer/utils/streaming_retry.py`
- **目的**: 标准化流式数据的重试策略
- **背景**: 当前的 `@retry_with_exponential_backoff` 不支持异步生成器，导致需要在每个方法内部手动实现重试逻辑
- **预期收益**: 
  - 减少代码重复
  - 统一重试策略
  - 提高代码可维护性

## 中优先级

### 2. 在 CI 中启用 mypy 类型检查
- [ ] **任务**: 配置并启用 mypy 静态类型检查
- **配置文件**: 创建 `mypy.ini`
- **初始范围**: 新代码和修改的模块
- **目的**: 在编译时捕获类型错误，避免运行时错误
- **预期收益**:
  - 早期发现类型不一致问题
  - 提高代码质量
  - 减少运行时错误

### 3. 统一音频数据模型
- [ ] **任务**: 评估是否需要统一 `AudioChunk` 和 `StreamingChunk`
- **当前状态**: 系统中存在两种音频数据模型
- **影响范围**: 所有 TTS 引擎和音频处理组件
- **预期收益**:
  - 简化数据流
  - 减少类型转换
  - 提高系统一致性

## 低优先级

### 4. 优化异步处理模式
- [ ] **任务**: 评估线程与异步的混合使用
- **位置**: `CosyVoiceUnifiedEngine.synthesize_streaming` 中的 `threading.Thread`
- **目的**: 考虑是否可以完全使用异步模式
- **预期收益**:
  - 简化并发模型
  - 提高性能
  - 减少线程管理开销

## 已完成的改进

### ✅ 修复异步生成器装饰器问题
- **日期**: 2025-08-11
- **描述**: 从 `CosyVoiceUnifiedEngine.synthesize_streaming` 移除不兼容的装饰器，改为内部实现重试
- **影响**: 解决了"没有声音"的关键问题

### ✅ 强化接口一致性
- **日期**: 2025-08-11
- **描述**: 更新 `BaseTTSEngine` 抽象基类，确保所有实现返回一致的类型
- **影响**: 提高了系统的类型安全性

---

## 维护说明

1. 新增技术债务时，请标明优先级和预期收益
2. 完成项目后，移至"已完成的改进"部分并标注日期
3. 定期评审并更新优先级