## 连续直播脚本生成"最小改造方案" v1（微循环叙事模型）

### 核心设计理念

**接口一致性原则**：MicrocapsuleGenerator 虽然内部采用"实时生成+缓冲区"架构，但对下游 PlaylistManager 呈现的接口与原 ScriptGenerator 完全一致。PlaylistManager 无需感知上游是预制脚本、实时生成还是缓冲输出——它只关心"请求内容→获得播放项"的统一接口。

**最小修改边界**：
- **改动侧**：仅生成逻辑（MicrocapsuleGenerator + 胶囊模板），专注"如何生成高质量的微胶囊脚本"。
- **不变侧**：播放/队列/TTS/传输等所有下游组件，保持 100% 原有实现。

通过这种"上游替换、下游无感"的设计，确保架构升级的风险可控，开发重心聚焦于内容质量提升。

### 目的与范围
- 目标：在不修改"播放/队列/TTS/传输"等核心链路的前提下，仅通过"生成侧"轻量改造，构建"微循环叙事（Micro-Loop Narrative）"能力，显著提升连续性、对新老用户友好度与转化效率。
- 范围：仅涉及 Prompt/模板、MicrocapsuleGenerator（组合/包装）、极简状态持久化；不改 `PlaylistManager`、`MainContentPlayer`、`StreamingContentProvider`、`TTSManager`、WS 等实现。

---

## 一图概览（最小改动路径）
```mermaid
flowchart LR
  subgraph 客户端
    FE[Web前端播放器]
  end

  subgraph API
    START[/start-stream (DYNAMIC/SMART/SCRIPT_BASED)/]
    CTRL[stop/resume]
  end

  subgraph 生成侧（仅增强，不改接口）
    MCG[MicrocapsuleGenerator\n(带缓冲的实时生成)]
    CAPS[内容胶囊模板库\n(Hook→Unpack→Proof→CTA→Transition)]
  end

  subgraph 播放/合成（保持不变）
    PM[(PlaylistManager)]
    SCP[StreamingContentProvider]
    TTS[TTS Manager/Engines]
    ASP[AudioStreamingProxy]
  end

  FE --> START
  START --> MCG
  MCG --> CAPS
  MCG --> PM
  PM --> SCP --> TTS --> ASP --> FE
  CTRL --> START
```

---

## 微循环叙事（内容胶囊）模型

### 胶囊结构（单个卖点胶囊为例，1-2分钟目标时长）
1. Hook（钩子）：一句话/问题，直指痛点或价值
2. Unpack（展开）：卖点细节/参数/机制
3. Proof（证明）：社会证明/对比/演示/数据
4. CTA（行动号召）：低门槛指令（轻CTA）
5. Transition（过渡）：自然承接下一个胶囊

特点：
- 对新用户：任意时刻进入，都能在1-2分钟内完整理解一个卖点并收到CTA（每个胶囊天然内置微Hook）。
- 对老用户：内容持续推进，无“重复欢迎”打扰；每个胶囊都是新的信息单元。

### 胶囊类型（建议最小集）
- SP（Selling Point）胶囊：围绕单个卖点（多个变体以防疲劳）
- Price/Value 胶囊：价格合理性、优惠权益与价值论证
- Proof/Demo 胶囊：演示/评测/用户评价/数据对比
- Brand/Story 胶囊：品牌理念、设计哲学、背后故事（低频）
- Half-time Summary 胶囊：半场总结/路线复盘（承上启下）
- QA 胶囊：基于 `LiteLLMAdapter.generate_qa_complete` 的过渡+回答+过渡（无需改代码）
- Closing 胶囊：感谢/复盘/最终CTA/预告（优雅终止）

---

## MicrocapsuleGenerator（必做，组合/包装，不改底层）

**接口兼容性**：对外保持与 ScriptGenerator 一致的接口，确保 PlaylistManager 无需任何修改即可接入。内部通过缓冲区解耦实时生成延迟，但这一优化对下游完全透明。

职责（最小必备）：
- 胶囊状态管理：
  - `pending_capsules`: 待播胶囊列表（含类型、主题、权重、冷却时间、模板变量等）
  - `played_capsules`: 已播胶囊列表（含时间、效果指标占位）
- 胶囊选择器（Selector）：
  - 默认策略：在 `pending_capsules` 中按权重/冷却/去重选择下一个胶囊（可从“顺序/轮转”开始）
  - 反重复：同类型胶囊设置冷却窗口；相邻胶囊避免重复主题
  - 可选信号：根据“新用户涌入/峰值”等指标，临时提升 Price/Proof 胶囊权重或插入品牌/半场总结
- Prompt装配器：
  - 将胶囊结构（Hook→Unpack→Proof→CTA→Transition）+ Persona + 产品事实表 + `recent_summary_short` + `next_topic_preview` 装配成最终 Prompt
- 极简持久化（复用会话存储）：优先复用现有 `SessionStorage/FormSession` 等会话管理与存储组件，写入/读取 `recent_summary_short`、`pending/played_capsules` 等关键状态；仅在本地开发或无持久层时，降级为 `state_snapshots/session_{session_id}.json` 快照。

架构边界与"零侵入"定义（澄清）：
- "零侵入"指不修改核心播放链路组件内部实现（如 `PlaylistManager`、`MainContentPlayer` 等）；
- MicrocapsuleGenerator 属于上游编排层的"组合/集成"，通过既有公开接口与下游交互，不属于对核心的"侵入/修改"。

QA 独立供给与协作策略（生成侧隔离）：
- 保持 `SimpleQAHandler` 独立，作为与 MicrocapsuleGenerator 并行的"内容供应方"；
- `PlaylistManager` 作为唯一接入点，接收来自 MicrocapsuleGenerator（主线叙事）与 SimpleQAHandler（问答插入）的两路上游；
- 冲突/并发由既有 QA 安全距离与插入策略解决（保持现状，不更改核心）；
- 可选协作信号：当 QA 频繁插入时，MicrocapsuleGenerator 可临时降低长段胶囊权重，减少碰撞（非必需）。

### 内部架构：带缓冲的实时生成模型

**核心原则**：对外接口保持不变，对内通过缓冲解耦。PlaylistManager 调用 `get_next_content()` 的体验与原 ScriptGenerator 完全相同，但内部通过预生成缓冲区确保响应速度。

MicrocapsuleGenerator 采用"生产者-消费者-缓冲区"架构，解耦 LLM 生成延迟与播放流畅性：

核心组件：
- **内容缓冲区**：使用 `asyncio.Queue`，容量 3-5 个胶囊，存储预生成的播放项。
- **生产者任务**：后台持续运行，选择胶囊→装配 Prompt→调用 LLM→转换播放项→入队缓冲区。
- **消费者任务**：响应 PlaylistManager 需求，从缓冲区取出播放项并推送。

解耦逻辑：
1. 生产者独立于播放节奏，持续保持缓冲区满载。
2. 消费者响应实时需求，优先从缓冲区取现成内容。
3. 当缓冲区为空时，消费者阻塞等待或回退到同步生成。

首播特殊处理：
- 为保证首播低延迟，首次请求绕过缓冲区，直接同步生成并返回。
- 同时启动生产者任务，后续请求优先使用缓冲内容。

架构示意：
```python
class MicrocapsuleGenerator:
    def __init__(self, ...):
        self.content_buffer = asyncio.Queue(maxsize=5)
        self.producer_task = None
        self.is_running = False
    
    async def start_generation(self):
        """启动缓冲式生成"""
        self.is_running = True
        self.producer_task = asyncio.create_task(self._producer_loop())
    
    async def _producer_loop(self):
        """生产者：持续生成胶囊内容"""
        while self.is_running:
            if not self.content_buffer.full():
                capsule = self.select_next_capsule()
                prompt = self.build_prompt(capsule)
                content = await self._generate_content(prompt)
                await self.content_buffer.put(content)
            await asyncio.sleep(0.1)
    
    async def get_next_content(self, force_sync=False):
        """消费者：获取下一个播放内容"""
        if force_sync or self.content_buffer.empty():
            # 首播或缓冲区空时，同步生成
            return await self._sync_generate()
        else:
            # 优先使用缓冲内容
            return await self.content_buffer.get()
```

伪代码（接口层）：
```python
class MicrocapsuleGenerator:
    def __init__(self, session_id: str, product_info: dict, persona_info: dict, templates: dict):
        self.session_id = session_id
        self.product_info = product_info
        self.persona_info = persona_info
        self.templates = templates  # 胶囊模板库
        self.pending_capsules = bootstrap_capsules(product_info)
        self.played_capsules = []
        self.recent_summary_short = ""
        self.next_topic_preview = ""

    def select_next_capsule(self, signals: dict | None = None) -> dict:
        # 依据冷却/权重/反重复选择一个胶囊（可先用简单顺序/轮转实现）
        return simple_selector(self.pending_capsules, self.played_capsules, signals or {})

    def build_prompt(self, capsule: dict) -> str:
        # 用胶囊模板 + persona + 产品事实 + 承上(摘要) + 启下(预告) 拼装最终 Prompt
        return assemble_capsule_prompt(self.templates, capsule, self.persona_info, self.product_info,
                                      self.recent_summary_short, self.next_topic_preview)

    def update_state(self, capsule: dict, generated_text: str) -> None:
        # 基于生成结果更新摘要/预告、移动胶囊至已播、补充新的待播胶囊等
        self.recent_summary_short = summarize_micro(self.recent_summary_short, generated_text)
        self.next_topic_preview = plan_next_preview(self.pending_capsules)
        move_to_played(self.pending_capsules, self.played_capsules, capsule)
        # 持久化状态至 SessionStorage 或降级 JSON
```

---

## 胶囊 Prompt 模板（可复制使用）

通用系统框架（片段生成的“系统指令”）：
```text
[系统角色]
你是一名中文直播主播，风格自然口语化。请以“内容胶囊（1-2分钟）”的形式输出：
Hook → Unpack → Proof → CTA → Transition。

[人设]
- 名称：{{persona.name}}
- 语气/口条：{{persona.tone}}；语速：{{persona.speaking_rate}}倍

[产品事实表（重点提炼）]
- 品类：{{product.category}}
- 目标用户：{{product.target}}
- 关键卖点（Top-N）：{{product.key_points}}
- 核心参数：{{product.specs}}
- 价格与优惠：{{product.price}} / {{product.promo}}
- 常见疑问要点：{{product.faq_short}}

[承上与启下]
- 承上（≤50字）：{{recent_summary_short}}
- 启下预告（≤30字）：{{next_topic_preview}}

[输出要求]
1) 只输出口播内容；禁止Markdown/项目符号/代码块
2) 严格遵循“Hook→Unpack→Proof→CTA→Transition”结构，标点分句自然
3) 总时长建议1-2分钟；每句长度适中，便于句级TTS播报
4) 内容必须与当前胶囊的主题强相关
```

SP（卖点）胶囊模板（示例）：
```text
[胶囊类型]
Selling Point（主题：{{selling_point.title}}）

[Hook]
一句话指出该卖点解决的核心痛点/价值：

[Unpack]
围绕 {{selling_point.title}} 展开细节（参数/机制/场景），3-5句

[Proof]
提供1-2个证据（数据对比/用户评价/演示）

[CTA]
低门槛行动号召（轻CTA，1句）

[Transition]
1句自然过渡，引出下一个主题：{{next_topic_preview}}
```

QA 胶囊模板（基于现有完整回答能力）：
```text
[胶囊类型]
QA（自动含过渡语，调用 generate_qa_complete 即可）

[指引]
使用已有的“开场过渡+核心回答+结束过渡”格式，确保与上下文衔接自然。
```

Opening/Brand/Half-time/Closing 胶囊均按同一结构，替换主题与侧重即可。

---

## 与现有模式的对接（零侵入）

**零侵入核心**：MicrocapsuleGenerator 完全兼容现有接口契约，PlaylistManager 及以下所有组件无需感知生成方式的变化。

- `DYNAMIC/SMART`：MicrocapsuleGenerator 替换原 ScriptGenerator 位置，对外提供相同的"请求→播放项"接口。内部选胶囊→组Prompt→从缓冲区获取或实时生成，但 PlaylistManager 调用体验不变。
- `SCRIPT_BASED`：可用一组预制胶囊（离线生成）直接喂给系统，保持播放链路完全不变。
- QA：沿用 `SimpleQAHandler + LiteLLMAdapter.generate_qa_complete`，与 MicrocapsuleGenerator 并行供给，无需改代码。
- Stop：优雅终止时强制选择"Closing 胶囊"，通过相同接口推送给 PlaylistManager，播完后再停止。

---

## 优雅停止（Closing 胶囊注入）——最小改造设计

目标：在控制面板中新增“停止直播”按钮，触发 LLM 生成“结束语（Closing 胶囊）”，以“插播”形式自然接入当前播放清单，播完后自动结束会话。并同时移除不必要的“测试音频流”按钮，保持操作简洁。

### 前端改动（控制面板）
- 移除测试按钮：删除 `src/ai_live_streamer/api/templates/live_control_panel.html` 中的“🧪 测试音频流”按钮（`testAudioStreaming()` 可保留为控制台调试）。
- 新增停止按钮：在同一按钮区新增“⏹️ 停止直播”，点击后调用 `requestGracefulStop()`。
- UI/UX 细节：
  - 点击后按钮立刻 `disabled`，文案切换为“正在停止中...”，避免重复点击。
  - 状态指示区显示“stopping”，并在收到完成信号后自动恢复初始状态。

前端交互流程：
- 若存在有效 `sessionId`，调用后端 `/api/control/sessions/{session_id}/graceful-stop`。
- 提示“已请求结束语收尾，播完后自动结束”；状态区显示“正在优雅停止...”。
- 收到服务端 `playlist_updated`（含 Closing 项）与 `stream_status: stopping` 或 WS 正常关闭后，UI 复位。

参考前端函数（示意）：
```javascript
async function requestGracefulStop() {
  if (!streamState.sessionId) {
    showNotification('未检测到直播会话，请先开始直播', 'warning');
    return;
  }
  const stopBtn = document.getElementById('gracefulStopBtn');
  if (stopBtn) {
    stopBtn.disabled = true;
    stopBtn.textContent = '正在停止中...';
  }
  try {
    const resp = await fetch(`/api/control/sessions/${streamState.sessionId}/graceful-stop`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ reason: 'user_request_graceful_stop', user_id: 'control_panel' })
    });
    if (resp.status === 202) {
      showNotification('已请求结束语收尾，播完后自动结束', 'info');
    } else if (resp.status === 409) {
      const data = await resp.json().catch(() => ({}));
      showNotification(data.error || '已在进行优雅停止，请勿重复点击', 'warning');
    } else if (!resp.ok) {
      throw new Error('请求失败');
    }
  } catch (e) {
    showNotification(`请求失败：${e.message}`, 'error');
  } finally {
    // 不立刻恢复按钮，由服务端完成信号驱动UI复位（避免重复点击）
  }
}
```

### 后端 API（新增，零侵入播放链路）
- 新增接口：`POST /api/control/sessions/{session_id}/graceful-stop`
  - 入参：`{ reason: string, user_id?: string }`
  - 返回码与幂等：
    - 首次触发：返回 `202 Accepted`（异步处理）。
    - 已在进行中：返回 `409 Conflict`，`{"error": "Graceful stop already in progress."}`。
  - 处理逻辑：
    1) 汇总会话上下文（人设/产品事实/最近摘要）。
    2) 调用 `LLMScriptGenerator._generate_closing_segment` 生成 `ScriptSegmentType.CLOSING`（失败则回退 `form.closing_template` 或硬编码结束语，见“异常与回退”）。
    3) 通过 `script_segment_converter` 转为播放项。
    4) 使用 `PlaylistManager` 按插入策略加入播放清单。
    5) 设置 `stop_after_closing=true`，并广播 `playlist_updated` 与 `stream_status: stopping`。

保留现有硬停止：`POST /api/control/sessions/{session_id}/stop` 仅作紧急终止；`graceful-stop` 用于正常收尾。

### MicrocapsuleGenerator 对接
- **接口一致性**：优雅停止触发后，MicrocapsuleGenerator 通过相同的 `get_next_content()` 接口向 PlaylistManager 提供 Closing 胶囊，无需新增停止专用接口。
- 触发"优雅停止"时，Selector 强制选择 `Closing` 胶囊：
  - Prompt 装配：`Closing` 模板 + Persona + 产品事实 + `recent_summary_short` + `next_topic_preview`（可为空），强调感谢/复盘/最终CTA。
  - 成功后 `update_state()`：记录 `played_capsules`，置 `stop_after_closing` 并持久化至 SessionStorage。

### 播放清单注入策略（PlaylistManager）
- 插入位置：`IMMEDIATE` 策略（当前播放项之后的下一个可播放位置），避免打断当前句/段。
- 队列处理：若队列中有正在处理的 QA，则将 `Closing` 放在该 QA 之后的优先位；设置 `stop_after_index = closing_item_index`，当播放到该项末尾自动结束。
- 广播：通过 `state_broadcaster` 推送 `playlist_updated`（含插入索引与 items 概览）。

伪代码（服务端处理要点）：
```python
@router.post('/sessions/{session_id}/graceful-stop')
async def graceful_stop(session_id: str, req: StreamControlRequest):
    ctx = await get_session_info(session_id)
    segment = await llm_generator._generate_closing_segment(ctx.form, ctx.persona) or build_template_closing(ctx)
    items = script_segment_converter.convert(segment)
    insert_index = await playlist_manager.insert_closing_and_mark_stop(items)
    await broadcaster.broadcast_playlist_updated(session_id, insert_index, items)
    await broadcaster.broadcast_status(session_id, status='stopping')
    return JSONResponse({ 'status': 'accepted', 'insertion_index': insert_index }, status_code=202)
```

### 完成判定与反馈闭环（MicrocapsuleGenerator 监控）
- 状态机：
  - 触发后置 `is_shutting_down = True`，停止生产者任务，Selector 被覆盖为仅选择 `Closing` 胶囊。
  - `Closing` 注入后，进入"等待播放完成"阶段。
- 完成判定：
  - 周期性（例如每 1s）检查 `PlaylistManager`：队列为空或 `current_index` 超过 `stop_after_index`，且播放器状态为“已播完”。
  - 满足条件后，调用底层 `session.stop()` 或 `stop_stream(session_id)`，释放资源并关闭 WS。
- 全局超时：
  - 设置最大等待时间（例如 120s）。超时仍未完成时，记录错误并调用硬停止，确保会话不被悬挂。

闭环示意：
```python
async def monitor_graceful_stop(session_id: str, timeout_seconds: int = 120):
    start = time.time()
    while time.time() - start < timeout_seconds:
        if playlist_manager.has_completed_stop():
            await stop_stream(session_id, StreamControlRequest(reason='graceful_stop_completed'))
            return True
        await asyncio.sleep(1)
    logger.error('Graceful stop timeout, forcing stop')
    await stop_stream(session_id, StreamControlRequest(reason='graceful_stop_timeout'))
    return False
```

### 异常与回退
- 生成失败：
  - 首先回退到 `operational_forms.closing_template`；如仍失败，注入硬编码结束语（例如：“感谢大家的收看，本次直播到此结束，我们下次再见！”）。
  - 仍按相同策略插入，并继续闭环监控。
- 插入失败：回退调用硬停止 `/stop`；UI 提示“收尾失败，已执行安全停止”。
- 幂等：若已处于 `is_shutting_down` 状态或队列已有 `closing` 项，则返回 `409 Conflict`，防止重复注入。

### 监控与SLO（补充）
- 优雅停止触达时延：点击→`closing` 开始播出 < 3s。
- 优雅停止完成时延：点击→WS 正常关闭 < 90s（随结束语长度）；全局超时 120s 兜底。
- 失败率：`closing` 生成失败率 < 2%，插入失败率 < 1%；超时强停率 < 0.5%。
- 幂等保护：重复点击被抑制次数（409 次数）应有限；异常抖动时不影响完成率。

---

## 监控与SLO（最小集）
- 首播延迟：开始生成→第一句播出，目标 < 2-3s
- 句间空窗：两句之间静默时间≈0（依赖Smart预加载与句长控制）
- 胶囊成功率：生成结果满足 Hook→Unpack→Proof→CTA→Transition 结构的占比 > 95%
- 胶囊时长贴合度：1-2分钟目标内占比 > 90%
- QA 响应时延：用户提问→回答播出，目标 < 2-5s（取决于TTS与网络）

（可选）业务指标：CTA触达率/点击率、SP覆盖率（Top-N在前X分钟内覆盖）。

---

## 性能与时延优化：预生成与缓存（新增）
- 预生成策略：对上下文依赖较弱的胶囊（如 `Brand/Story`、部分 `Proof`）可在非高峰时段离线批量生成；
- 缓存策略：将生成结果缓存至 `ScriptPersistence`/`SessionStorage`，MicrocapsuleGenerator 选择器优先命中缓存以降低首帧/收尾时延；
- 失效策略：当产品事实或人设变更时，基于版本号或指纹（hash）使缓存失效；
- 回收策略：采用 LRU + TTL 控制缓存规模，结合业务热度分层（热内容更长 TTL）。

## 风险与回退
- 模式化/僵硬：准备多模版与语气变体；按主题轮换，避免“同构文风疲劳”。
- 质量波动：低于阈值时回退至“模板胶囊”或“微型Hook+简短Unpack”；或切换到离线预制胶囊。
- 漂移/冗长：约束句长与总字数；失败重试降温；必要时做规则截断。

---

## 实施清单（1周）

**核心原则**：确保 PlaylistManager 及以下组件零修改，所有变更聚焦于生成层。

### 生成层改造（核心）
- [ ] 胶囊模板库：SP/Price/Proof/Brand/Half-time/QA/Closing 模板（至少各2个变体）
- [ ] MicrocapsuleGenerator（必做）：
  - [ ] 选择器/装配器/状态管理（pending/played_capsules）
  - [ ] 带缓冲的实时生成架构（asyncio.Queue + 生产者/消费者任务）
  - [ ] **接口兼容层**：确保与 ScriptGenerator 完全相同的外部接口
- [ ] 生成器替换：在配置层将 `ScriptGenerator` 切换为 `MicrocapsuleGenerator`，代码调用方无感知

### 支撑功能（保持接口不变）
- [ ] 持久化复用：接入 `SessionStorage/FormSession`，无持久层时降级 JSON 快照
- [ ] Prompt 接入：在 `SMART` 模式以参数/配置方式注入；不改底层类
- [ ] 优雅停止：新增 `/graceful-stop` 接口 + 前端按钮，复用现有播放闭环
- [ ] 性能看板：首播延迟、句间空窗、胶囊成功率、胶囊时长贴合度、QA时延、优雅停止触达/完成时延
- [ ] 灰度/回退：模板兜底、硬编码结束语应急、参数回滚、预制胶囊应急播放

### 验证要点
- [ ] 接口兼容性测试：确保 PlaylistManager 调用 MicrocapsuleGenerator 与调用 ScriptGenerator 行为一致
- [ ] 播放链路验证：TTS/传输/WebSocket 等下游组件完全无感知生成方式变化

---

### 结束语
"微循环叙事"将 Hook 内化到每个内容单元（胶囊），统一了新老用户体验，且完全可以在"最小改造"的框架下落地：

**接口替换，功能升级**：只需在生成侧用 MicrocapsuleGenerator 替换 ScriptGenerator，保持对外接口完全一致。PlaylistManager 等播放链路组件无需任何修改，即可获得实时生成+缓冲优化的性能提升和微循环叙事的内容品质。

开发重心从"如何修改播放逻辑"转向"如何生成高质量胶囊脚本"，风险可控，价值聚焦。


