### 直播智能问答系统 v3 - 设计方案

**版本:** 1.0
**日期:** 2025-09-15
**作者:** Gemini

#### 1. 概述

为了提升AI直播的互动质量与效率，本项目旨在对现有的问答（QA）系统进行重大升级。当前系统主要依赖LLM直接生成回答，存在召回不足、延迟不可控和回答幻觉等潜在问题。

本方案将引入一个多阶段、分层次的问答处理流程，核心思想是通过 **“快速预处理 -> 混合检索 -> 智能路由 -> 可控生成 -> 持续学习”** 的漏斗模型，最大限度地利用本地知识库，降低对大模型（LLM）的依赖，从而实现低延迟、高准确率且安全可控的智能问答体验。

同时，将在系统后台增加 **“QA话术管理”** 界面，允许运营人员方便地增删改查标准问答对，为整个系统提供高质量的数据基础。

**技术选型:**
*   **本地知识库:** `SQLite` (用于存储QA对、商品信息等结构化文本)
*   **向量检索:** `Faiss` (Facebook AI Similarity Search) (用于高效的语义相似度搜索)
*   **文本嵌入模型:** 选择一个轻量级、高效的中文Embedding模型（如BGE-small-zh, M3E-small等），用于将文本转换为向量。

#### 2. 系统架构与流程

新版QA系统的处理流程将遵循您提出的六阶段最佳实践，并与现有代码结构深度整合。

##### **整体流程图**
```mermaid
graph TD
    A[用户问题] --> B{第一阶段：问题预处理};
    B --> C{第二阶段：混合检索};
    C --> |S ≥ τ_high<br>(高置信度)| D[直接回复标准答案];
    C --> |τ_low ≤ S < τ_high<br>(灰色区域)| E{第三阶段：轻量级LLM判定};
    C --> |S < τ_low<br>(低置信度)| F[忽略/标记不相关];
    E --> |判定为相关| G{第四阶段：LLM兜底回答};
    E --> |判定为不相关| F;
    G --> H{第五阶段：知识库自我进化};
    H --> I[推送给主播/虚拟人];
    D --> I;

    subgraph "第六阶段：问题优先级排序"
        I --> J[动态评分与排序] --> K[最终呈现Top-N];
    end

    subgraph "后台管理"
      M[QA话术管理界面] --> L((SQLite/Faiss知识库));
      H -- 人工审核 --> L;
    end
```

#### 3. 模块化设计与代码实现映射

##### **阶段一：问题预处理与去重**

*   **目标:** 清洗、规范化问题，并过滤近期重复提问。
*   **实现位置:** 扩展 `src/ai_live_streamer/api/websocket_v2.py` 中 `_handle_qa_request` 方法和 `src/ai_live_streamer/api/control.py` 中 `submit_question` 接口的入口部分。

1.  **文本规范化:** 创建一个新的工具函数 `normalize_question(text: str) -> str` 存放于 `src/ai_live_streamer/qa/utils.py` (如无则新建)。
2.  **内容安全审查 (可选):** 在规范化后，可调用第三方内容安全API。
3.  **近期问题去重 (增强版):**
    *   **技术:** 引入 `simhash` 库。
    *   **逻辑:**
        *   在 `src/ai_live_streamer/services/websocket_qa_cache.py` 的 `QACache` 中，除了现有逻辑，增加一个基于 `SimHash` 的指纹缓存（例如，使用Redis存储`question_simhash: answer_id`，设置5分钟过期）。
        *   对新问题计算SimHash指纹，检查与缓存中指纹的汉明距离。
        *   如果距离小于阈值（如3），则视为重复，可直接返回缓存的答案或标记，中断后续流程。

##### **阶段二 & 后台管理：QA知识库与混合检索**

*   **目标:** 建立一个可管理的QA知识库，并实现关键词+向量的混合检索。
*   **实现位置:**
    *   **后台管理API:** `src/ai_live_streamer/api/qa_management.py` (新建)。
    *   **知识库服务:** `src/ai_live_streamer/qa/knowledge_base.py` (新建)。

1.  **QA话术管理界面:**
    *   在 `src/ai_live_streamer/api/` 下新建 `qa_management.py`。
    *   提供RESTful API:
        *   `POST /qa/entries`: 新增一条QA
        *   `GET /qa/entries`: 分页查询所有QA
        *   `GET /qa/entries/{entry_id}`: 获取单条QA详情
        *   `PUT /qa/entries/{entry_id}`: 更新一条QA
        *   `DELETE /qa/entries/{entry_id}`: 删除一条QA
    *   在 `src/ai_live_streamer/api/templates/` 中创建一个简单的HTML页面 `qa_management.html`，使用JavaScript调用上述API，实现前端管理界面。
    *   将此新路由包含进主应用 `app.py` 中。

2.  **知识库服务 (`knowledge_base.py`):**
    *   **`KnowledgeBaseManager` 类:**
        *   `__init__()`: 初始化SQLite连接，加载或创建Faiss索引。
        *   `add_entry(question: str, answer: str, ...)`:
            1.  将QA文本存入SQLite。
            2.  用Embedding模型计算问题的向量。
            3.  将向量和SQLite中的记录ID添加/更新到Faiss索引中。
        *   `update_entry(...)`: 更新SQLite和Faiss。
        *   `delete_entry(...)`: 从SQLite和Faiss中移除。
        *   `hybrid_search(question: str, top_k: int = 5) -> List[Tuple[int, float]]`:
            1.  **关键词检索 (BM25):** (初期可简化) 使用SQLite的 `FTS5` 或 `LIKE` 查询快速召回一个候选集。
            2.  **向量检索重排:** 对用户问题计算向量，在Faiss中查询与候选集最相似的Top-K个结果，返回 `(entry_id, similarity_score)` 列表。

##### **阶段三 & 四：智能路由与可控生成**

*   **目标:** 基于检索结果的置信度，决定是直接回答、交由LLM判定，还是进行LLM兜底生成。
*   **实现位置:** 重构 `src/ai_live_streamer/qa/manager.py` 中的 `QAManager`。

1.  **重构 `QAManager.handle_qa`:**
    ```python
    # src/ai_live_streamer/qa/manager.py

    class QAManager(IQAManager):
        def __init__(self, ..., knowledge_base: KnowledgeBaseManager):
            # ...
            self.knowledge_base = knowledge_base
            self.τ_high = 0.72  # 可配置
            self.τ_low = 0.55   # 可配置

        async def handle_qa(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
            # 1. 问题预处理 (调用qa/utils.py中的函数)
            normalized_question = normalize_question(question_data["text"])
            
            # 2. 混合检索
            search_results = self.knowledge_base.hybrid_search(normalized_question)

            if search_results and search_results[0][1] >= self.τ_high:
                # 3a. 高置信区：直接回答
                best_match_id = search_results[0][0]
                answer = self.knowledge_base.get_answer_by_id(best_match_id)
                # ... 返回标准答案
            
            elif not search_results or search_results[0][1] < self.τ_low:
                # 3b. 低置信区：忽略
                # ... 返回忽略或不相关的标记

            else: # 灰色区域
                # 3c. 轻量级LLM判定
                is_relevant = await self._is_question_relevant_with_llm(normalized_question, context)
                if not is_relevant:
                    # ... 返回忽略标记

                # 4. LLM兜底回答 (无RAG)
                return await self._generate_llm_fallback_answer(normalized_question, context)
    ```

2.  **实现 `_is_question_relevant_with_llm`:**
    *   在 `QAManager` 中新增此私有方法。
    *   它将调用 `QAPromptBuilder` 来构建一个专门用于“相关性判定”的Prompt，如您方案中所示。
    *   使用一个配置为快速、低成本的模型进行调用。

3.  **实现 `_generate_llm_fallback_answer` (原 `_generate_rag_answer`):**
    *   在 `QAManager` 中新增此私有方法。
    *   **不再进行二次文档检索。**
    *   调用 `QAPromptBuilder` 构建带有强护栏（无外部资料，不允许猜测）的生成Prompt。
    *   调用核心LLM生成一个安全、通用的回答。

4.  **更新 `QAPromptBuilder` (`src/ai_live_streamer/qa/builder.py`):**
    *   新增 `build_relevance_check_prompt(...)` 方法。
    *   新增 `build_fallback_generation_prompt(...)` 方法 (原 `build_rag_generation_prompt`)，它将不接收外部文档片段，只负责构建带护栏规则的Prompt。

##### **阶段五：知识库的自我进化**

*   **目标:** 将LLM生成的有效回答沉淀回知识库。
*   **实现:**

1.  **待收录池:** 在SQLite中创建一个新表 `pending_qa_entries`，字段包括 `question`, `generated_answer`, `context`, `hit_count`, `status` (pending, approved, rejected)。
2.  **数据记录:** 当 `_generate_llm_fallback_answer` 成功生成一个答案后，将问题、答案、上下文等信息存入 `pending_qa_entries` 表。
3.  **人工审核:** “QA话术管理”界面需要增加一个“待审核列表”页面，让运营人员可以审核这些条目，并一键“采纳”到主知识库。采纳操作会调用 `KnowledgeBaseManager.add_entry`。

##### **阶段六：问题优先级排序 (可选)**

*   **目标:** 在高并发时，优先回答高价值问题。
*   **实现位置:** 可以在 `QAManager` 返回结果后，或在更上层的 `PlaylistManager` (`src/ai_live_streamer/services/playlist_manager.py`) 插入QA项之前进行。

1.  **评分模块:** 在 `src/ai_live_streamer/qa/ranker.py` (新建) 中创建 `QARanker` 类。
2.  **`rank(...)` 方法:** 输入一个问题列表，根据您方案中的动态评分机制（主题相似度、购买意图、新颖度等）输出排序后的列表。
3.  **集成:** 在 `PlaylistManager` 的 `insert_qa_item` 方法中，不再是来一个处理一个，而是维护一个短暂的候选池，调用 `QARanker` 排序后，再决定下一个要插入播放列表的QA。

#### 4. 需要新增或修改的文件摘要

*   **新增:**
    *   `design_docs/20250915_直播智能问答系统v3_设计方案.md` (本文档)
    *   `src/ai_live_streamer/api/qa_management.py` (后台管理API)
    *   `src/ai_live_streamer/api/templates/qa_management.html` (后台管理前端页面)
    *   `src/ai_live_streamer/qa/knowledge_base.py` (封装SQLite和Faiss的服务)
    *   `src/ai_live_streamer/qa/utils.py` (存放文本处理等工具函数)
    *   `src/ai_live_streamer/qa/ranker.py` (问题排序模块)
*   **修改:**
    *   `src/ai_live_streamer/qa/manager.py`: 核心修改，实现多阶段路由逻辑。
    *   `src/ai_live_streamer/qa/builder.py`: 增加用于相关性判断和LLM兜底生成的Prompt构建方法。
    *   `src/ai_live_streamer/api/websocket_v2.py`: 增强去重逻辑，调用新的QA处理流程。
    *   `src/ai_live_streamer/api/control.py`: 同上，调用新的QA处理流程。
    *   `src/ai_live_streamer/app_v2.py`: 注册 `qa_management` 的API路由。
    *   `src/ai_live_streamer/core/dependencies.py`: 为 `KnowledgeBaseManager` 提供依赖注入。
    *   `pyproject.toml` 或 `requirements.txt`: 添加 `faiss-cpu` (或 `faiss-gpu`), `simhash`, `jieba` (用于Simhash中文处理)等新依赖。

#### 5. 下一步计划

1.  **环境准备:** 安装 `faiss`, `simhash` 等依赖。
2.  **后台先行:** 优先开发 **QA话术管理** 的后台API和前端页面，以及 `KnowledgeBaseManager`。这为后续所有智能功能提供了数据基础。
3.  **改造核心:** 重构 `QAManager` 和 `QAPromptBuilder`，实现核心的路由和生成逻辑。
4.  **联调测试:** 将入口（API/WebSocket）与新的 `QAManager` 对接，进行端到端测试。
5.  **部署上线:** 部署新服务。
