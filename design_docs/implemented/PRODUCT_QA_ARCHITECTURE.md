# 产品-QA-配置架构设计文档

## 文档信息
- **版本**: 1.0.0
- **日期**: 2025-01-12
- **作者**: Claude Code
- **状态**: 已实施

## 1. 概述

本文档描述了AI直播系统中产品管理、QA话术和直播配置之间的架构关系。本次重构的核心目标是建立清晰的职责分离，避免系统间的不必要耦合。

### 1.1 核心设计原则

1. **单一职责原则**: 每个模块只负责一个明确的业务领域
2. **关注点分离**: 产品、QA、配置、脚本生成各自独立
3. **数据流清晰**: 避免循环依赖，建立单向数据流
4. **配置驱动**: 通过配置而非硬编码来控制系统行为

### 1.2 系统关系图

```
┌─────────────┐
│   产品      │ ←──── 一对多 ────→ ┌─────────────┐
│  (Product)  │                    │  QA条目     │
└─────────────┘                    │ (QA Entry)  │
      ↑                            └─────────────┘
      │ 多对一
      │
┌─────────────┐         独立生成
│  直播配置   │ ──────────────────→ ┌─────────────┐
│  (Config)   │                    │  直播脚本   │
└─────────────┘                    │  (Script)   │
                                   └─────────────┘
```

## 2. 数据模型设计

### 2.1 产品模型 (Product)

```sql
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 自增主键，提高索引性能
    sku TEXT UNIQUE NOT NULL,              -- 业务唯一标识
    name TEXT NOT NULL,                    -- 产品名称
    category TEXT NOT NULL,                -- 产品分类
    description TEXT,                       -- 产品描述
    price REAL,                            -- 产品价格
    stock INTEGER,                         -- 库存数量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计决策**：
- 使用自增整数主键而非UUID，提高查询和JOIN性能
- SKU作为业务标识，便于与外部系统集成
- 分类使用枚举值，确保数据一致性

### 2.2 QA条目模型 (QA Entry)

```sql
CREATE TABLE qa_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,           -- 关联产品
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category TEXT,
    tags TEXT,                             -- JSON数组
    hit_count INTEGER DEFAULT 0,
    confidence_score REAL DEFAULT 0.8,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

**设计决策**：
- QA直接关联产品，不关联配置或人设
- 支持全文搜索(FTS5)和向量搜索(Faiss)
- 命中统计用于优化和分析

### 2.3 标签系统

```sql
-- 独立的标签表
CREATE TABLE tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    category TEXT DEFAULT 'other',
    usage_count INTEGER DEFAULT 0
);

-- 产品-标签关联表
CREATE TABLE product_tags (
    product_id INTEGER,
    tag_id INTEGER,
    PRIMARY KEY (product_id, tag_id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (tag_id) REFERENCES tags(id)
);
```

**设计决策**：
- 标签独立管理，支持复杂查询
- 多对多关系，灵活的标签组合
- 使用计数优化热门标签展示

### 2.4 直播配置模型

```sql
CREATE TABLE streaming_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER,                    -- 选择的产品
    config_name TEXT NOT NULL,
    persona_config TEXT,                   -- JSON配置
    streaming_params TEXT,                 -- JSON参数
    script_template TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

**设计决策**：
- 配置选择产品，间接确定QA库
- 人设和参数使用JSON存储，灵活扩展
- 脚本模板独立于QA内容

## 3. API设计

### 3.1 路由规范

```
UI页面路由（返回HTML）：
/console/products              # 产品管理控制台
/console/products/{id}/qa      # 产品QA管理
/console/streaming             # 直播配置控制台

RESTful API路由（返回JSON）：
/api/v1/products               # 产品CRUD
/api/v1/products/{id}/qa       # 产品QA管理
/api/v1/streaming/configs      # 配置管理
/api/v1/streaming/scripts      # 脚本生成
```

**设计决策**：
- 明确分离UI路由(/console)和API路由(/api)
- 版本化API路由(/v1)支持未来升级
- RESTful风格，语义清晰

### 3.2 产品管理API

```python
# 产品CRUD操作
GET    /api/v1/products           # 列表（支持分页、筛选、搜索）
POST   /api/v1/products           # 创建
GET    /api/v1/products/{id}      # 详情
PUT    /api/v1/products/{id}      # 更新
DELETE /api/v1/products/{id}      # 删除

# 产品统计
GET    /api/v1/products/{id}/stats  # 获取统计信息

# SKU查询
GET    /api/v1/products/sku/{sku}   # 根据SKU查询
```

### 3.3 产品QA嵌套API

```python
# QA管理（嵌套在产品下）
GET    /api/v1/products/{product_id}/qa          # QA列表
POST   /api/v1/products/{product_id}/qa          # 添加QA
PUT    /api/v1/products/{product_id}/qa/{qa_id}  # 更新QA
DELETE /api/v1/products/{product_id}/qa/{qa_id}  # 删除QA

# QA搜索
GET    /api/v1/products/{product_id}/qa/search   # 搜索QA
```

## 4. 服务层架构

### 4.1 产品服务 (ProductService)

```python
class ProductService:
    async def create_product(product_data: ProductCreate) -> Product
    async def get_product(product_id: int) -> Product
    async def update_product(product_id: int, updates: ProductUpdate) -> Product
    async def delete_product(product_id: int) -> bool
    async def list_products(filters: ProductFilters, pagination: Pagination) -> Page
    async def get_product_stats(product_id: int) -> ProductStats
```

**职责**：
- 产品的CRUD操作
- 标签管理
- 统计信息计算（带缓存）

### 4.2 QA服务 (QAService)

```python
class QAService:
    async def add_qa_to_product(product_id: int, qa: QACreate) -> QAEntry
    async def get_product_qa_list(product_id: int, pagination: Pagination) -> Page
    async def update_qa(qa_id: int, updates: QAUpdate) -> QAEntry
    async def delete_qa(qa_id: int) -> bool
    async def search_product_qa(product_id: int, query: str) -> List[QAEntry]
```

**职责**：
- 基于产品维度的QA管理
- 全文搜索和向量搜索
- 命中统计更新

### 4.3 脚本生成服务 (ScriptGenerator)

```python
class ScriptGenerator:
    async def generate_script(config_id: int) -> Script:
        # 只使用配置信息生成脚本
        config = await self.get_streaming_config(config_id)
        product = await self.get_product(config.product_id)
        
        # 生成脚本内容（不包含QA）
        script = await self._generate_from_config(
            product_info=product,  # 只使用产品基本信息
            persona=config.persona,
            params=config.streaming_params
        )
        return script
```

**职责**：
- 根据配置生成脚本
- 不依赖QA内容
- 专注于直播流程话术

## 5. 数据流设计

### 5.1 产品创建流程

```
用户创建产品
    ↓
ProductService.create_product()
    ↓
数据库插入产品记录
    ↓
处理标签关联
    ↓
返回产品信息
```

### 5.2 QA管理流程

```
选择产品
    ↓
获取产品ID
    ↓
QAService.add_qa_to_product(product_id, qa_data)
    ↓
数据库插入QA记录（关联product_id）
    ↓
更新FTS索引
    ↓
返回QA信息
```

### 5.3 直播配置流程

```
创建直播配置
    ↓
选择产品（product_id）
    ↓
设置人设和参数
    ↓
保存配置
    ↓
生成脚本（基于配置，不含QA）
```

### 5.4 直播执行时QA查询

```
直播开始
    ↓
加载配置 → 获取product_id
    ↓
用户提问
    ↓
从product_id对应的QA库查询
    ↓
返回答案
```

## 6. 性能优化策略

### 6.1 数据库优化

- **索引策略**：
  - products表：sku, category, name索引
  - qa_entries表：product_id, question索引
  - 全文搜索：FTS5虚拟表

- **查询优化**：
  - 使用预编译语句
  - 批量操作使用事务
  - 分页查询限制数据量

### 6.2 缓存策略

```python
class ProductService:
    def __init__(self):
        self._cache = {}  # 内存缓存
        self._cache_ttl = 300  # 5分钟TTL
    
    async def get_product_stats(self, product_id: int):
        cache_key = f"product_stats_{product_id}"
        if cached := self._get_from_cache(cache_key):
            return cached
        
        stats = await self._calculate_stats(product_id)
        self._set_cache(cache_key, stats, ttl=300)
        return stats
```

### 6.3 并发控制

- 使用连接池管理数据库连接
- 异步IO处理请求
- 适当的锁机制避免竞态条件

## 7. 安全考虑

### 7.1 输入验证

- Pydantic模型验证所有输入
- SQL注入防护（参数化查询）
- XSS防护（HTML转义）

### 7.2 权限控制

- API端点的认证检查
- 产品级别的访问控制
- 敏感操作的审计日志

## 8. 扩展性设计

### 8.1 未来扩展点

1. **多语言QA支持**：在qa_entries表添加language字段
2. **QA版本管理**：添加qa_versions表跟踪历史
3. **产品关联**：支持产品包、产品系列
4. **智能推荐**：基于用户行为推荐QA

### 8.2 API版本管理

- 当前版本：/api/v1/
- 向后兼容：保留旧版本API
- 渐进式迁移：提供迁移指南

## 9. 部署和运维

### 9.1 数据库迁移

```bash
# 初始化数据库
python scripts/init_product_database.py

# 数据迁移（如果有旧数据）
python scripts/migrate_qa_to_products.py
```

### 9.2 监控指标

- API响应时间
- 数据库查询性能
- 缓存命中率
- QA查询准确率

## 10. 总结

本架构设计实现了以下目标：

1. ✅ **清晰的关系链**：产品→QA，配置→产品，脚本←配置
2. ✅ **独立的职责**：脚本生成不依赖QA，QA管理不关心配置
3. ✅ **灵活的复用**：同一产品可用于多个配置，QA自动继承
4. ✅ **直观的UI**：用户能清楚理解产品、配置、QA的关系
5. ✅ **高性能**：合理的索引、缓存和查询优化
6. ✅ **可扩展**：预留扩展点，支持未来需求

## 附录A：数据字典

| 表名 | 字段 | 类型 | 说明 |
|------|------|------|------|
| products | id | INTEGER | 自增主键 |
| products | sku | TEXT | 产品SKU |
| products | name | TEXT | 产品名称 |
| products | category | TEXT | 产品分类 |
| qa_entries | id | INTEGER | 自增主键 |
| qa_entries | product_id | INTEGER | 关联产品 |
| qa_entries | question | TEXT | 问题文本 |
| qa_entries | answer | TEXT | 答案文本 |

## 附录B：API示例

### 创建产品
```bash
curl -X POST http://localhost:8000/api/v1/products \
  -H "Content-Type: application/json" \
  -d '{
    "sku": "PROD-001",
    "name": "智能音箱",
    "category": "electronics",
    "description": "AI驱动的智能音箱",
    "price": 299.00,
    "tags": ["智能", "音箱", "AI"]
  }'
```

### 为产品添加QA
```bash
curl -X POST http://localhost:8000/api/v1/products/1/qa \
  -H "Content-Type: application/json" \
  -d '{
    "question": "这个音箱支持什么音乐平台？",
    "answer": "支持QQ音乐、网易云音乐、Spotify等主流平台",
    "category": "功能",
    "tags": ["音乐", "平台"]
  }'
```

## 修订历史

| 版本 | 日期 | 作者 | 说明 |
|------|------|------|------|
| 1.0.0 | 2025-01-12 | Claude Code | 初始版本，建立产品-QA-配置架构 |