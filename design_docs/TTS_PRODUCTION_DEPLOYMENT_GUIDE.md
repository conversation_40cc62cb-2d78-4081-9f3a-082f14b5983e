# TTS Cache 生产环境部署指南

## 🎯 生产环境部署方案对比

### 方案一：推荐的生产环境部署（Python 守护进程）

```bash
# 部署服务
python scripts/deploy_tts_cache_production.py deploy

# 查看状态
python scripts/deploy_tts_cache_production.py status

# 停止服务
python scripts/deploy_tts_cache_production.py stop
```

**特点**：
- ✅ 完整的进程管理（PID 文件、信号处理）
- ✅ 结构化日志管理
- ✅ 优雅的启动和停止
- ✅ 详细的状态监控

### 方案二：简化的后台部署（Shell 脚本）

```bash
# 启动服务（后台运行）
./scripts/start_tts_cache_daemon.sh start

# 查看状态
./scripts/start_tts_cache_daemon.sh status

# 查看日志
./scripts/start_tts_cache_daemon.sh logs

# 停止服务
./scripts/start_tts_cache_daemon.sh stop
```

**特点**：
- ✅ 简单易用的 Shell 脚本
- ✅ 使用 nohup 后台运行
- ✅ 彩色输出和友好界面
- ✅ 集成日志查看

### 方案三：不推荐的简单方式

```bash
# ❌ 不推荐：会阻塞终端
python scripts/start_tts_cache_standalone.py start

# ❌ 不推荐：nohup 方式（进程管理复杂）
nohup python scripts/start_tts_cache_standalone.py start > logs/tts_cache.log 2>&1 &
```

## 🚀 推荐的生产环境部署流程

### 步骤 1：环境准备

```bash
# 1. 确保 Docker 运行
docker --version

# 2. 创建必要目录
mkdir -p logs run data/minio_data

# 3. 检查 API 密钥
cat secrets/dashscope_api_key.txt
```

### 步骤 2：部署 TTS Cache 服务

```bash
# 使用推荐的生产部署脚本
python scripts/deploy_tts_cache_production.py deploy
```

**输出示例**：
```
🚀 生产环境 TTS Cache 部署
========================================
🗄️  启动 MinIO 存储服务...
✅ MinIO 容器启动成功: a427eefeafd2dc...
⏳ 等待 MinIO 服务启动...
✅ MinIO 服务启动成功
🎤 启动 TTS Cache 服务（守护进程模式）...
✅ TTS Cache 服务已启动 (PID: 12345)
📋 日志文件: /path/to/logs/tts_cache.log
📋 PID 文件: /path/to/run/tts_cache.pid
⏳ 等待 TTS Cache 服务启动...
✅ TTS Cache 服务启动成功

🎉 TTS Cache 生产环境部署完成！
📊 服务地址:
  - TTS Cache API: http://localhost:22243
  - MinIO 控制台: http://localhost:9001

📋 管理命令:
  - 查看状态: python scripts/deploy_tts_cache_production.py status
  - 停止服务: python scripts/deploy_tts_cache_production.py stop
  - 查看日志: tail -f logs/tts_cache.log

✅ 部署成功！服务正在后台运行
```

### 步骤 3：验证部署

```bash
# 检查服务状态
python scripts/deploy_tts_cache_production.py status

# 测试 API 接口
curl http://localhost:22243/metrics

# 查看实时日志
tail -f logs/tts_cache.log
```

### 步骤 4：启动主应用

```bash
# 启动主应用（会自动检测 TTS Cache）
python scripts/run_server.py

# 或指定端口
python scripts/run_server.py --port 8080
```

## 📊 服务监控和管理

### 状态检查

```bash
# 详细状态信息
python scripts/deploy_tts_cache_production.py status
```

**输出示例**：
```
📊 TTS Cache 生产环境状态
========================================
✅ MinIO 服务: 运行中
✅ TTS Cache 服务: 运行中
   📋 进程 PID: 12345
   📊 缓存命中率: 85.2%
   📈 总请求数: 1,234
📋 日志文件: logs/tts_cache.log (2.5M bytes)
```

### 日志管理

```bash
# 查看实时日志
tail -f logs/tts_cache.log

# 查看最近的错误
grep ERROR logs/tts_cache.log | tail -10

# 日志轮转（可选）
logrotate -f /etc/logrotate.d/tts-cache
```

### 进程管理

```bash
# 查看进程信息
ps aux | grep uvicorn

# 检查端口占用
lsof -i :22243

# 查看进程树
pstree -p $(cat run/tts_cache.pid)
```

## 🔄 服务重启和维护

### 优雅重启

```bash
# 推荐方式：使用部署脚本
python scripts/deploy_tts_cache_production.py restart

# 或使用 Shell 脚本
./scripts/start_tts_cache_daemon.sh restart
```

### 手动重启

```bash
# 1. 停止服务
python scripts/deploy_tts_cache_production.py stop

# 2. 等待几秒
sleep 3

# 3. 重新部署
python scripts/deploy_tts_cache_production.py deploy
```

### 清理和重置

```bash
# 停止所有服务
python scripts/deploy_tts_cache_production.py stop

# 清理数据（可选）
./scripts/tts_cache_manager.sh clean

# 重新部署
python scripts/deploy_tts_cache_production.py deploy
```

## 🛡️ 生产环境最佳实践

### 1. 系统服务集成（可选）

创建 systemd 服务文件：

```bash
# /etc/systemd/system/tts-cache.service
[Unit]
Description=TTS Cache Service
After=docker.service
Requires=docker.service

[Service]
Type=forking
User=your-user
WorkingDirectory=/path/to/ai-live-streamer
ExecStart=/path/to/ai-live-streamer/scripts/deploy_tts_cache_production.py deploy
ExecStop=/path/to/ai-live-streamer/scripts/deploy_tts_cache_production.py stop
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 2. 监控和告警

```bash
# 健康检查脚本
#!/bin/bash
if ! curl -s http://localhost:22243/metrics > /dev/null; then
    echo "TTS Cache service is down!" | mail -s "Alert" <EMAIL>
fi
```

### 3. 备份策略

```bash
# 备份 MinIO 数据
tar -czf backup-$(date +%Y%m%d).tar.gz data/minio_data/

# 备份配置
cp -r secrets/ config.yml backup/
```

### 4. 性能优化

```bash
# 调整 uvicorn 工作进程数
# 在 deploy_tts_cache_production.py 中修改：
# "--workers", "4"  # 根据 CPU 核心数调整
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   lsof -i :22243
   kill -9 <PID>
   ```

2. **MinIO 启动失败**
   ```bash
   docker logs minio-standalone
   docker system prune -f
   ```

3. **TTS Cache 启动失败**
   ```bash
   tail -50 logs/tts_cache.log
   python scripts/deploy_tts_cache_production.py stop
   python scripts/deploy_tts_cache_production.py deploy
   ```

4. **磁盘空间不足**
   ```bash
   du -sh data/minio_data/
   ./scripts/tts_cache_manager.sh clean
   ```

## 📋 总结

**推荐的生产环境部署命令**：

```bash
# 🌟 一键部署（推荐）
python scripts/deploy_tts_cache_production.py deploy

# 📊 状态监控
python scripts/deploy_tts_cache_production.py status

# 📋 日志查看
tail -f logs/tts_cache.log

# 🛑 停止服务
python scripts/deploy_tts_cache_production.py stop
```

这种方式提供了：
- ✅ 完整的进程生命周期管理
- ✅ 结构化的日志和监控
- ✅ 优雅的启动和停止
- ✅ 生产环境级别的稳定性

**避免使用**：
- ❌ `nohup python scripts/start_tts_cache_standalone.py start > logs/tts_cache.log 2>&1 &`
- ❌ 直接在前台运行阻塞式命令
