# Claude 开发指南

本文件为 Claude Code (`claude.ai/code`) 在此代码库中工作时提供统一的开发规范和工作流程。所有贡献者必须严格遵循本文档。

## 1. 核心开发原则 (Core Development Principles)

本章节是代码库的“单一事实源”，包含所有必须遵守的设计哲学、编码规范和环境要求。

### 1.1. 设计哲学 (Design Philosophy)

| 编号  | 原则                          | 说明                                                         |
| :---- | :---------------------------- | :----------------------------------------------------------- |
| 1.1.1 | **为正确性设计，而非恢复**    | 设计让失败不可能，而非事后恢复。                             |
| 1.1.2 | **Fail-Fast (快速失败)**      | **最高优先级原则**。任何错误、异常或未达预期的状态都必须**立即抛出异常**并停止执行。**绝对禁止**任何形式的 fallback 机制、降级处理或静默隐藏异常。 |
| 1.1.3 | **单一事实源 (SSoT)**         | 状态机主循环是唯一决策权威，避免状态分散。                   |
| 1.1.4 | **事务性更新**                | 状态变更必须是原子性的，保证一致性，无需事后检查。           |
| 1.1.5 | **纯函数优先**                | 优先编写无副作用的函数，其输出仅由输入决定。                 |
| 1.1.6 | **DRY (Don't Repeat Yourself)** | 避免重复代码，通过抽象和封装提高代码复用性。                 |

### 1.2. 编码规范 (Code Quality)

| 编号  | 规范                           | 说明                                                         |
| :---- | :----------------------------- | :----------------------------------------------------------- |
| 1.2.1 | **单文件行数限制**             | 单个文件代码行数**不得超过 1000 行**。Pylint `too-many-lines (C0302)` 触发时必须拆分。 |
| 1.2.2 | **禁止魔法常量 (No Magic Numbers)** | 所有硬编码值（字符串、数字等）必须提取为大写蛇形命名的常量，并置于文件顶部或 `config.py` 中。 |
| 1.2.3 | **强制类型注解**               | 所有函数签名（参数和返回值）**必须**有完整的类型注解。代码库强制使用 `mypy --strict` 进行检查。 |
| 1.2.4 | **优先使用专业库**             | 避免使用脆弱的正则表达式进行文本处理。进行句子拆分、分词等操作时，应使用 `spacy` (英文) 或 `jieba` (中文) 等专业 NLP 库。 |

### 1.3. 日志与配置 (Logging & Configuration)

| 编号  | 规范                           | 说明                                                         |
| :---- | :----------------------------- | :----------------------------------------------------------- |
| 1.3.1 | **标准化日志**                 | **禁止使用 `print()`** 或 `printf`。**必须**使用标准 `logging` 模块，并根据场景使用 `logger.debug()`, `info()`, `warning()`, `error()`。 |
| 1.3.2 | **日志目录**                   | 所有日志文件必须统一存放到 `logs/` 目录下。                  |
| 1.3.3 | **配置即环境变量**             | 遵循 Twelve-Factor App 原则。所有敏感信息或环境相关配置（如 API 密钥、数据库地址）通过环境变量注入，并由 `config.py` 统一管理。 |
| 1.3.4 | **配置加载失败**               | 当缺少必要的环境变量时，程序启动阶段必须抛出 `ConfigError`，**禁止**在运行时使用默认值或静默失败。 |

### 1.4. API 调用规范 (API Call Standards)

| 编号  | 规范             | 说明                                                         |
| :---- | :--------------- | :----------------------------------------------------------- |
| 1.4.1 | **统一重试机制** | 所有对外部 API 的调用**必须**使用统一的 `@retry` 装饰器封装，并采用指数退避策略。 |
| 1.4.2 | **强制超时设置** | 每个外部 API 调用**必须**设置合理的 `timeout` 值。           |
| 1.4.3 | **API 错误处理** | API 调用失败时（如非 2xx 状态码），**必须**立即抛出自定义的 `APIError` 异常，**禁止**返回 `None` 或默认值。 |

```python
# 示例：符合规范的 API 调用
@retry(policy=exponential_backoff, max_attempts=3)
def call_external_api(url: str, timeout: int = 30) -> dict:
    response = requests.get(url, timeout=timeout)
    if not response.ok:
        raise APIError(f"API call failed to {url}: {response.status_code}")
    return response.json()
```

### 1.5. 项目结构与目录规范 (Project Structure)

| 编号  | 目录/文件               | 职责说明                                                     |
| :---- | :---------------------- | :----------------------------------------------------------- |
| 1.5.1 | `src/`                  | 存放所有核心应用代码。                                       |
| 1.5.2 | `tests/`                | 存放所有测试代码。**禁止**将测试代码散落在项目其他地方。     |
| 1.5.3 | `tests/results/`        | 存放所有测试报告和产物。此目录已被 `.gitignore` 忽略。       |
| 1.5.4 | `logs/`                 | 统一存放所有运行时日志文件。                                 |
| 1.5.5 | `documents/`            | 统一存放所有生成的文档。                                     |
| 1.5.6 | `archive/`              | 存放重构前的代码备份。此目录已被 `.gitignore` 忽略。         |
| 1.5.7 | `design_docs/`          | 存放架构设计、API 契约等重要设计文档。                       |
| 1.5.8 | `scripts/`              | 存放自动化脚本（如测试运行、服务启动等）。                   |
| 1.5.9 | `config.yml` / `.env`   | 配置文件和环境变量。                                         |
| 1.5.10| **中间文件**            | 运行过程中生成的任何中间辅助文件（如临时音频、代码文件）**必须**写入 `tests/` 目录。 |

### 1.6. 环境要求 (Environment Setup)

| 编号  | 规范             | 说明                                                         |
| :---- | :--------------- | :----------------------------------------------------------- |
| 1.6.1 | **Conda 环境**   | **必须**在名为 `aidev` 的 Conda 环境中执行所有 Python 相关命令。 |
| 1.6.2 | **环境激活**     | 在执行任何命令前，**必须**先执行 `source "$HOME/miniconda3/etc/profile.d/conda.sh` 来确保 `conda` 命令可用并激活 `aidev` 环境。 |
| 1.6.3 | **环境验证**     | 通过 `conda info --envs` 检查当前激活的环境是否为 `aidev` (应有 `*` 标记)。 |

```bash
# 标准环境检查流程
source "$HOME/miniconda3/etc/profile.d/conda.sh"
conda info --envs
# 预期输出包含: * aidev
which python3
# 预期输出指向 aidev 环境的 Python 解释器
```

### 1.7. 决策框架 (Decision Framework)

> 决策框架：当有多种方案时，按可测试性 > 可读性 > 一致性 > 简单性的顺序选择。

---

## 2. 强制开发工作流 (Mandatory Development Workflow)

作为AI开发者，你**必须**严格遵循以下分阶段的确定性工作流来完成任何代码修改任务。此工作流引入了**变更范围评估**，以确保验证的有效性和效率。

### 阶段一：代码实现与范围评估 (Code Implementation & Scope Assessment)

1.  **分析需求**: 完全理解用户的修改请求。
2.  **执行修改**: 使用 Edit、MultiEdit、Write 等工具，遵循 **第 1 节** 的所有核心原则，对代码库进行必要的修改。
3.  **评估变更范围**: 在完成代码修改后，**必须**评估你的变更属于以下哪个范围。这将决定后续的验证路径。

    -   **范围 1 (低影响 / 独立组件):**
        -   **定义**: 变更被限制在独立的、不直接影响核心数据流或 API 契约的模块中。
        -   **示例**: 修改 TTS 缓存服务 (`services/tts-cache-cosyvoice/`)、更新文档、优化日志格式、重构某个内部工具函数。
        -   **验证路径**: 进入 **阶段 2-A: 单元/集成测试**。

    -   **范围 2 (核心逻辑 / 集成点):**
        -   **定义**: 变更涉及核心业务逻辑、组件间交互或数据处理，但**不**改变面向客户端的 API 契约。
        -   **示例**: 修改播放列表逻辑 (`PlaylistManager`)、内容提供策略 (`StreamingContentProvider`)、QA 处理流程。
        -   **验证路径**: 进入 **阶段 2-B: 核心 E2E 验证**。

    -   **范围 3 (高影响 / API 契约):**
        -   **定义**: 变更直接修改或扩展了面向客户端的 REST API 或 WebSocket v2 协议，或修改了核心数据模型。
        -   **示例**: 在 WebSocket 中添加新的消息类型、修改 `/api/control/*` 端点的请求/响应格式、更改播放列表的数据结构。
        -   **验证路径**: **必须**依次执行 **阶段 2-B (核心 E2E)** 和 **阶段 3 (全面 E2E 验收)**。

### 阶段二：验证 (Validation)

根据阶段一的范围评估，选择以下相应的验证路径。

> **重要提示**: 所有后续的验证步骤都**必须**在名为 `aidev` 的 Conda 环境中执行。在运行任何测试命令前，请务必通过 `conda activate aidev` 激活该环境。

---

#### 阶段 2-A: 单元/集成测试 (适用于范围 1)

此路径用于快速验证独立组件的功能正确性，成本最低。

1.  **执行测试**:
    -   **指令**: `python -m pytest [path/to/test.py]`
    -   **说明**: 运行指定的单元测试或所有测试（如果未提供路径）。推荐使用 `python -m pytest` 以确保使用环境中正确的 `pytest` 版本。
    -   **环境**: 通常无需启动外部服务。
2.  **分析结果**:
    -   **成功**: 所有测试通过。进入 **阶段 4: 任务完成**。
    -   **失败**: **禁止**进入下一阶段。必须分析失败的测试，返回阶段一修复代码，然后重新运行本阶段测试直至通过。

---

#### 阶段 2-B: 核心 E2E 验证 (适用于范围 2 和 3)

此路径用于快速捕获核心流程中的集成错误，确保基础功能稳定。

1.  **执行测试**:
    -   **指令**: `bash scripts/run_core_test.sh`
    -   **说明**: 此脚本会在一个隔离的 Docker 环境中运行核心路径 E2E 测试，并自动管理服务的启动和关闭，无需手动操作。
2.  **分析结果**:
    -   **成功 (退出码 0)**: 日志显示 "✅ 核心路径测试通过"。
        -   如果变更是**范围 2**，可选择性进入阶段 3（建议使用 `--fast` 模式）或直接进入 **阶段 4**。
        -   如果变更是**范围 3**，**必须**进入 **阶段 3**。
    -   **失败 (退出码非 0)**: 日志显示 "❌ 核心路径测试失败"。**禁止**进入下一阶段。必须分析错误日志，返回阶段一修复代码，然后重新运行本阶段测试直至通过。

---

### 阶段三：全面 E2E 验收 (Comprehensive Acceptance) - (适用于范围 3)

此路径用于对高影响变更进行完整性检查，验证所有相关的业务逻辑和复杂场景。

1.  **执行测试**:
    -   **前提**: 主服务**必须**正在运行（可使用 `python scripts/run_server.py` 启动）。
    -   **指令**: `python scripts/e2e/run_all_e2e_tests.py`
    -   **快速模式 (可选)**: `python scripts/e2e/run_all_e2e_tests.py --fast`
2.  **分析结果**:
    -   **成功 (退出码 0)**: 日志显示 "🎉 All tests passed!"。进入 **阶段 4**。
    -   **失败 (退出码非 0)**: 日志显示 "⚠️ Some tests failed"。**禁止**进入下一阶段。必须分析失败的测试用例（注意 E2E 测试超时通常意味着服务未运行或已崩溃），返回阶段一修复代码，然后依次重新执行阶段 2-B 和本阶段的测试。

### 阶段四：任务完成与汇报 (Task Completion & Reporting)

在成功完成相应的验证路径后，你的编码任务才算真正完成。

1.  **生成报告**: 使用下方的“任务完成报告模板”生成最终报告，**清晰注明所执行的验证路径和范围**。
2.  **版本控制**: Git 操作与此工作流解耦。你的核心职责是交付经过完整验证的代码和报告。

### 📊 测试脚本职责速查表

*注意：下表所有命令均需在 `aidev` Conda 环境中执行。*

| 测试脚本 | 核心职责 | 前提条件 | 适用范围 |
| :--- | :--- | :--- | :--- |
| `python -m pytest [path]` | 单元/集成测试 | 无 | 范围 1 |
| `bash scripts/run_core_test.sh` | 核心集成验证 | Docker | 范围 2, 3 |
| `python scripts/e2e/run_all_e2e_tests.py` | 完整业务逻辑验收 | 手动运行服务 | 范围 3 |

---

## 3. 特定领域指南 (Domain-Specific Guides)

本章节建立在 **第 1 节** 的核心原则之上，为特定开发领域（如 API）提供补充性规范。

### 3.1. API 开发指南 (API Development Guide)

开发任何 API (REST 或 WebSocket) 时，除了必须遵守所有核心原则外，还需遵循以下 API 专属规范。

| 编号  | 规范                   | 说明                                                         |
| :---- | :--------------------- | :----------------------------------------------------------- |
| 3.1.1 | **遵循 API 契约**      | **必须**在开发前仔细阅读 `design_docs/API_CONTRACT.md`。所有 API 的实现（消息格式、字段名、数据类型、错误码）**必须**与此契约**严格一致**。 |
| 3.1.2 | **契约优先**           | 如需修改 API，**必须**先更新 `API_CONTRACT.md` 文档，经过评审后，再进行代码实现。 |
| 3.1.3 | **音频格式规范**       | 所有通过 API 传输的音频**必须**遵循以下格式：WAV 容器, PCM 编码, 24kHz, 16-bit, 单声道 (Mono), 小端字节序 (Little-endian)。详见契约 2.3 节。 |
| 3.1.4 | **E2E 测试集成**       | 新增或修改核心 API 功能时，**必须**参考 `scripts/e2e/test_core_playback_e2e.py`，并添加或更新对应的 E2E 测试用例来覆盖新功能。 |

#### API 开发检查清单

-   **[ ] 契约一致性**:
    -   [ ] WebSocket 消息类型和结构与 `API_CONTRACT.md` 第 2.2 节匹配。
    -   [ ] RESTful API 端点和载荷与契约定义匹配。
    -   [ ] 所有字段名、数据类型均与契约一致（大小写敏感）。
-   **[ ] 错误处理**:
    -   [ ] 使用契约第 4 节定义的标准错误码。
-   **[ ] 音频处理**:
    -   [ ] 输出格式严格为：WAV/PCM, 24000 Hz, 16-bit, Mono, Little-endian。
-   **[ ] 测试覆盖**:
    -   [ ] 核心流程（启动、连接、请求、验证、停止）已通过 E2E 测试。

---

## 4. 任务完成报告模板

*请在完成所有验证阶段后，复制并填充以下内容作为你的最终回复。*

```markdown
### ✅ 任务完成报告

**1. 任务ID/请求描述:**
> (在此处简述你完成的任务)

**2. 代码变更摘要:**
- `path/to/file.py`: (简述修改内容)
- `path/to/another/file.py`: (简述修改内容)

**3. 自动化验证证据:**
- **核心验证**:
  - **命令**: `bash scripts/run_core_test.sh`
  - **结果**: PASS
  - **退出码**: 0
- **全面验收**:
  - **命令**: `python scripts/e2e/run_all_e2e_tests.py`
  - **结果**: PASS
  - **退出码**: 0

**结论:** 所有修改已完成，并通过了所有必需的验证阶段。代码已准备就绪。
```

## 附录 (Appendices)

### A. 重构与归档规范

| 编号 | 规范 | 说明 |
|------|------|------|
| A.1 | 重构前必须备份原代码到 `archive/` 目录 | 确保重构过程中可以回溯和对比 |
| A.2 | 归档文件名格式：`{原文件名}_{YYYYMMDDHH}.{扩展名}` | 标准化归档命名，便于时间排序 |
| A.3 | `archive/` 目录必须加入 `.gitignore` | 避免归档文件污染版本控制 |

**重构示例:**
```bash
# 重构前：备份原文件
cp src/main.py archive/main_2025071114.py

# 重构后：拆分为多个文件
# src/main.py (主入口)
# src/processor.py (核心逻辑)
# src/utils.py (工具函数)
```

### B. E2E 测试验证要点
运行 E2E 测试时，确保以下检查点全部通过：
- **核心功能**:
  - [ ] HTTP API 能成功启动流会话
  - [ ] WebSocket v2 连接建立成功
  - [ ] `content_request` 消息得到正确响应
  - [ ] 音频数据 Base64 解码成功，WAV 头部验证通过
  - [ ] 流会话能正常停止
- **性能要求**:
  - [ ] 内容请求响应时间 < 500ms (p95)
  - [ ] WebSocket 连接时间 < 1000ms
