FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY config/ ./config/

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 22243

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:22243/').raise_for_status()"

# 启动应用
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "22243", "--workers", "2"]