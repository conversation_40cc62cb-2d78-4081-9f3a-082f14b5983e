# TTS-Cache-CosyVoice Service

企业级 TTS 缓存服务，专为阿里云 CosyVoice API 设计。

## 特性

- ✅ 智能缓存：基于内容的自动缓存，大幅降低 API 调用成本
- ✅ 熔断器保护：防止级联故障，确保系统稳定性
- ✅ 流式缓存：支持流式响应的后台缓存
- ✅ MinIO 存储：可靠的分布式对象存储
- ✅ 配额管理：月度字符限制，防止超额使用
- ✅ 监控指标：详细的性能和使用统计
- ✅ Docker 部署：一键部署，易于维护

## 快速开始

1. 准备 API 密钥：
   ```bash
   echo "your-dashscope-api-key" > secrets/dashscope_api_key.txt
   ```

2. 启动服务：
   ```bash
   docker-compose -f docker-compose.tts-cache.yml up -d
   ```

3. 验证服务：
   ```bash
   curl http://localhost:22243/
   ```

## API 文档

服务启动后，访问以下地址查看完整 API 文档：
- Swagger UI: http://localhost:22243/docs
- ReDoc: http://localhost:22243/redoc

### 主要端点

#### 健康检查
```bash
GET /
```

#### 获取指标
```bash
GET /metrics
```

#### 文本转语音
```bash
POST /speech/
Content-Type: application/json

{
  "text": "要转换的文本",
  "voice": "longanran",
  "format": "pcm",
  "sample_rate": 24000
}
```

#### 缓存预热
```bash
POST /cache/warmup
Content-Type: application/json

["文本1", "文本2", "文本3"]
```

## 监控

使用提供的监控脚本查看实时状态：
```bash
./scripts/monitor_tts_cache_dashboard.sh
```

## 配置

主要环境变量：
- `DASHSCOPE_API_KEY`: 阿里云 API 密钥（通过 Docker Secret 传递）
- `MONTHLY_CHAR_LIMIT`: 月度字符限制（默认：1000000）
- `CACHE_EXPIRY_DAYS`: 缓存过期天数（默认：365）
- `CIRCUIT_FAILURE_THRESHOLD`: 熔断器触发阈值（默认：5）
- `CIRCUIT_RECOVERY_TIMEOUT`: 熔断器恢复时间（默认：60秒）

## 架构设计

### 核心组件

1. **FastAPI 应用**：提供 REST API 接口
2. **MinIO 存储**：持久化缓存存储
3. **熔断器**：保护后端 API 服务
4. **统计模块**：实时性能监控

### 数据流

```
客户端请求 → TTS-Cache → 检查缓存
                ↓                ↓
          缓存命中 ←────── 缓存未命中
                ↓                ↓
          返回音频        调用 CosyVoice API
                                 ↓
                            存入 MinIO 缓存
                                 ↓
                              返回音频
```

## 维护

### 日志

日志文件位置：`./logs/tts-cache.log`

查看实时日志：
```bash
docker logs -f tts-cache-cosyvoice
```

### 缓存管理

查看 MinIO 管理界面：
- URL: http://localhost:9001
- 用户名: minioadmin
- 密码: minioadmin123

### 备份

定期备份 MinIO 数据：
```bash
docker exec tts-cache-minio mc mirror local/tts-cache ./backup/
```

## 故障排除

### 服务无法启动

1. 检查端口占用：
   ```bash
   lsof -i :22243
   lsof -i :9000
   ```

2. 检查 Docker 状态：
   ```bash
   docker-compose -f docker-compose.tts-cache.yml ps
   ```

### 缓存未生效

1. 验证 MinIO 连接：
   ```bash
   docker exec tts-cache-minio mc ls local/tts-cache
   ```

2. 检查 API 响应头：
   ```bash
   curl -I -X POST http://localhost:22243/speech/ \
     -H "Content-Type: application/json" \
     -d '{"text": "测试"}'
   ```

### API 错误

1. 检查 API 密钥：
   ```bash
   cat secrets/dashscope_api_key.txt
   ```

2. 查看详细错误日志：
   ```bash
   docker logs tts-cache-cosyvoice --tail 50
   ```

## 性能优化

### 连接池配置

服务已配置优化的连接池参数：
- 最大连接数：100
- 每主机连接数：30
- Keep-Alive 超时：30秒

### 缓存策略

- 使用 SHA256 哈希作为缓存键
- 支持基于文本、音色、格式的精确匹配
- 自动过期清理

### 监控建议

定期检查以下指标：
- 缓存命中率（目标 > 70%）
- 平均响应时间（目标 < 200ms）
- 月度配额使用率
- 存储空间使用情况

## 开发指南

### 本地开发

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行开发服务器：
   ```bash
   uvicorn src.main:app --reload --port 22243
   ```

### 测试

运行集成测试：
```bash
python tests/test_tts_cache_complete.py
```

### 扩展

要添加新的 TTS 提供商：
1. 修改 `call_cosyvoice_api` 函数
2. 更新请求/响应格式
3. 调整音频格式处理

## 许可证

本项目遵循 MIT 许可证。