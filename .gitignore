# Python
__pycache__/
tests/
data/
.kiro/
config/
docs/
logs/
.claude/flags

# Refactoring archive
archive/
archive/


run/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store

# Logs
*.log
logs/

# AI Live Streamer specific
tests/results/
tests/*.wav
tests/*.mp3
#archive/
documents/
config/local.yaml
config/secrets.yaml
secrets/

# Audio files
*.wav
*.mp3
*.flac
*.ogg

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Cache
.cache/
*.cache

# Jupyter Notebooks
.ipynb_checkpoints

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

